/**
 * 渲染处理器
 */
import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import * as path from 'path';
import * as fs from 'fs';
import { RenderService } from './render.service';
import { RenderJobStatus, RenderJobType } from './entities/render-job.entity';

@Processor('render')
export class RenderProcessor {
  private readonly logger = new Logger(RenderProcessor.name);

  constructor(private readonly renderService: RenderService) {}

  @Process('render')
  async handleRender(job: Job) {
    const { jobId } = job.data;
    this.logger.log(`开始处理渲染任务: ${jobId}`);

    try {
      // 更新任务状态为处理中
      const renderJob = await this.renderService.updateStatus(jobId, RenderJobStatus.PROCESSING, 0);

      // 创建输出目录
      const outputDir = path.join(process.cwd(), 'renders', jobId);
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      // 根据任务类型执行不同的渲染逻辑
      if (renderJob.type === RenderJobType.IMAGE) {
        await this.renderImage(renderJob, outputDir);
      } else if (renderJob.type === RenderJobType.VIDEO) {
        await this.renderVideo(renderJob, outputDir);
      } else if (renderJob.type === RenderJobType.ANIMATION) {
        await this.renderAnimation(renderJob, outputDir);
      }

      // 更新任务状态为已完成
      await this.renderService.updateStatus(jobId, RenderJobStatus.COMPLETED, 100);

      this.logger.log(`渲染任务完成: ${jobId}`);
    } catch (error) {
      this.logger.error(`渲染任务失败: ${jobId}`, error.stack);

      // 更新任务状态为失败
      await this.renderService.updateStatus(jobId, RenderJobStatus.FAILED, undefined, error.message);
    }
  }

  /**
   * 渲染图像
   */
  private async renderImage(renderJob, outputDir) {
    // 模拟渲染进度
    for (let i = 0; i <= 100; i += 10) {
      // 检查任务是否被取消
      const updatedJob = await this.renderService.findOne(renderJob.id, renderJob.userId);
      if (updatedJob.status === RenderJobStatus.CANCELED) {
        throw new Error('任务已取消');
      }

      // 更新进度
      await this.renderService.updateStatus(renderJob.id, RenderJobStatus.PROCESSING, i);

      // 模拟渲染时间
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }

    // 生成输出文件路径
    const outputFile = path.join(outputDir, `render.${renderJob.settings.format || 'png'}`);

    // 模拟创建输出文件
    fs.writeFileSync(outputFile, 'This is a simulated render output');

    // 添加渲染结果
    await this.renderService.addResult(renderJob.id, {
      fileUrl: outputFile,
      thumbnailUrl: outputFile,
      fileSize: fs.statSync(outputFile).size,
      width: renderJob.settings.width,
      height: renderJob.settings.height,
      format: renderJob.settings.format || 'png',
      metadata: {
        renderTime: 10,
        quality: renderJob.settings.quality,
      },
    });
  }

  /**
   * 渲染视频
   */
  private async renderVideo(renderJob, outputDir) {
    // 模拟渲染进度
    for (let i = 0; i <= 100; i += 5) {
      // 检查任务是否被取消
      const updatedJob = await this.renderService.findOne(renderJob.id, renderJob.userId);
      if (updatedJob.status === RenderJobStatus.CANCELED) {
        throw new Error('任务已取消');
      }

      // 更新进度
      await this.renderService.updateStatus(renderJob.id, RenderJobStatus.PROCESSING, i);

      // 模拟渲染时间
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }

    // 生成输出文件路径
    const outputFile = path.join(outputDir, `render.${renderJob.settings.format || 'mp4'}`);

    // 模拟创建输出文件
    fs.writeFileSync(outputFile, 'This is a simulated video render output');

    // 添加渲染结果
    await this.renderService.addResult(renderJob.id, {
      fileUrl: outputFile,
      thumbnailUrl: outputFile,
      fileSize: fs.statSync(outputFile).size,
      width: renderJob.settings.width,
      height: renderJob.settings.height,
      format: renderJob.settings.format || 'mp4',
      duration: renderJob.settings.frames / renderJob.settings.fps,
      metadata: {
        renderTime: 20,
        quality: renderJob.settings.quality,
        fps: renderJob.settings.fps,
        frames: renderJob.settings.frames,
      },
    });
  }

  /**
   * 渲染动画
   */
  private async renderAnimation(renderJob, outputDir) {
    // 模拟渲染进度
    for (let i = 0; i <= 100; i += 2) {
      // 检查任务是否被取消
      const updatedJob = await this.renderService.findOne(renderJob.id, renderJob.userId);
      if (updatedJob.status === RenderJobStatus.CANCELED) {
        throw new Error('任务已取消');
      }

      // 更新进度
      await this.renderService.updateStatus(renderJob.id, RenderJobStatus.PROCESSING, i);

      // 模拟渲染时间
      await new Promise((resolve) => setTimeout(resolve, 500));
    }

    // 生成输出文件路径
    const outputFile = path.join(outputDir, `render.${renderJob.settings.format || 'gif'}`);

    // 模拟创建输出文件
    fs.writeFileSync(outputFile, 'This is a simulated animation render output');

    // 添加渲染结果
    await this.renderService.addResult(renderJob.id, {
      fileUrl: outputFile,
      thumbnailUrl: outputFile,
      fileSize: fs.statSync(outputFile).size,
      width: renderJob.settings.width,
      height: renderJob.settings.height,
      format: renderJob.settings.format || 'gif',
      duration: renderJob.settings.frames / renderJob.settings.fps,
      metadata: {
        renderTime: 50,
        quality: renderJob.settings.quality,
        fps: renderJob.settings.fps,
        frames: renderJob.settings.frames,
      },
    });
  }
}
