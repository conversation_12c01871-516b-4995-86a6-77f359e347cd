import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

export interface EmbeddingResponse {
  embedding: number[];
  model: string;
  usage: {
    prompt_tokens: number;
    total_tokens: number;
  };
}

@Injectable()
export class EmbeddingService {
  private readonly provider: string;
  private readonly apiKey: string;
  private readonly endpoint: string;
  private readonly model: string;

  constructor(private readonly configService: ConfigService) {
    this.provider = this.configService.get('rag.embedding.provider', 'openai');
    this.apiKey = this.configService.get('rag.embedding.apiKey', '');
    this.endpoint = this.configService.get('rag.embedding.endpoint', 'https://api.openai.com/v1');
    this.model = this.configService.get('rag.embedding.model', 'text-embedding-ada-002');
  }

  /**
   * 生成文本嵌入向量
   */
  async generateEmbedding(_text: string): Promise<number[]> {
    try {
      switch (this.provider) {
        case 'openai':
          return await this.generateOpenAIEmbedding(_text);
        case 'azure':
          return await this.generateAzureEmbedding(_text);
        case 'local':
          return await this.generateLocalEmbedding(_text);
        default:
          throw new Error(`Unsupported embedding provider: ${this.provider}`);
      }
    } catch (error) {
      console.error('Failed to generate embedding:', error);
      throw error;
    }
  }

  /**
   * 批量生成嵌入向量
   */
  async generateBatchEmbeddings(texts: string[]): Promise<number[][]> {
    const embeddings: number[][] = [];

    // 分批处理以避免API限制
    const batchSize = 100;
    for (let i = 0; i < texts.length; i += batchSize) {
      const batch = texts.slice(i, i + batchSize);
      const batchEmbeddings = await Promise.all(batch.map((text) => this.generateEmbedding(text)));
      embeddings.push(...batchEmbeddings);
    }

    return embeddings;
  }

  private async generateOpenAIEmbedding(text: string): Promise<number[]> {
    const response = await axios.post(
      `${this.endpoint}/embeddings`,
      {
        input: text,
        model: this.model,
      },
      {
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
      },
    );

    return response.data.data[0].embedding;
  }

  private async generateAzureEmbedding(text: string): Promise<number[]> {
    const response = await axios.post(
      this.endpoint,
      {
        input: text,
      },
      {
        headers: {
          'api-key': this.apiKey,
          'Content-Type': 'application/json',
        },
      },
    );

    return response.data.data[0].embedding;
  }

  private async generateLocalEmbedding(text: string): Promise<number[]> {
    // 本地嵌入服务实现
    const response = await axios.post(
      `${this.endpoint}/embeddings`,
      {
        text: text,
        model: this.model,
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );

    return response.data.embedding;
  }
}
