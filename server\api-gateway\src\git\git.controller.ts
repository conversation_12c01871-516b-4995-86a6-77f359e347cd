/**
 * Git控制器
 * 提供Git版本控制相关的API接口
 */
import { Controller, Get, Post, Body, HttpException, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { GitService } from './git.service';

@ApiTags('Git版本控制')
@Controller('git')
export class GitController {
  constructor(private readonly gitService: GitService) {}

  @Get('status')
  @ApiOperation({ summary: '获取Git状态' })
  @ApiResponse({ status: 200, description: 'Git状态信息' })
  async getStatus() {
    try {
      return await this.gitService.getStatus();
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to get git status',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('branches')
  @ApiOperation({ summary: '获取分支列表' })
  @ApiResponse({ status: 200, description: '分支列表' })
  async getBranches() {
    try {
      const branches = await this.gitService.getBranches();
      return { branches };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to get branches',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('files')
  @ApiOperation({ summary: '获取文件状态' })
  @ApiResponse({ status: 200, description: '文件状态信息' })
  async getFiles() {
    try {
      return await this.gitService.getFiles();
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to get file status',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('history')
  @ApiOperation({ summary: '获取提交历史' })
  @ApiResponse({ status: 200, description: '提交历史' })
  async getHistory() {
    try {
      const history = await this.gitService.getHistory();
      return { commits: history };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to get commit history',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('log')
  @ApiOperation({ summary: '获取提交日志' })
  @ApiResponse({ status: 200, description: '提交日志' })
  async getLog() {
    try {
      const history = await this.gitService.getHistory();
      return { commits: history };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to get commit log',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('remotes')
  @ApiOperation({ summary: '获取远程仓库' })
  @ApiResponse({ status: 200, description: '远程仓库列表' })
  async getRemotes() {
    try {
      const remotes = await this.gitService.getRemotes();
      return { remotes };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to get remotes',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('commit')
  @ApiOperation({ summary: '提交更改' })
  @ApiResponse({ status: 200, description: '提交成功' })
  async commit(@Body() body: { message: string }) {
    try {
      return await this.gitService.commit(body.message);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to commit',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('add')
  @ApiOperation({ summary: '添加文件到暂存区' })
  @ApiResponse({ status: 200, description: '添加成功' })
  async addFiles(@Body() body: { files: string[] }) {
    try {
      return await this.gitService.addFiles(body.files);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to add files',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('checkout')
  @ApiOperation({ summary: '切换分支' })
  @ApiResponse({ status: 200, description: '切换成功' })
  async checkout(@Body() body: { branch: string }) {
    try {
      return await this.gitService.checkout(body.branch);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to checkout branch',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('pull')
  @ApiOperation({ summary: '拉取远程更新' })
  @ApiResponse({ status: 200, description: '拉取成功' })
  async pull() {
    try {
      return await this.gitService.pull();
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to pull',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('push')
  @ApiOperation({ summary: '推送到远程仓库' })
  @ApiResponse({ status: 200, description: '推送成功' })
  async push() {
    try {
      return await this.gitService.push();
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to push',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('health')
  @ApiOperation({ summary: 'Git服务健康检查' })
  @ApiResponse({ status: 200, description: '服务状态' })
  async healthCheck() {
    return {
      status: 'ok',
      service: 'git',
      timestamp: new Date().toISOString(),
    };
  }
}
