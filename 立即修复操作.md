# 立即修复操作指南

## 当前状态分析

✅ **正常服务**：
- MySQL数据库
- Redis缓存
- MinIO对象存储
- Elasticsearch搜索引擎
- 服务注册中心
- 资源库服务

❌ **问题服务**：
- AI模型服务（数据库表缺失）
- 用户服务（健康检查失败）
- 项目服务（健康检查失败）
- 渲染服务（健康检查失败）
- API网关（健康检查失败）
- 场景生成服务（健康检查失败）
- 场景模板服务（健康检查失败）

## 立即执行的修复步骤

### 1. 手动创建AI模型服务数据库表

```powershell
# 进入MySQL容器并创建表
docker exec -it dl-engine-mysql-win mysql -u root -pDLEngine2024!@#

# 在MySQL命令行中执行：
USE dl_engine_ai;

CREATE TABLE IF NOT EXISTS ai_models (
  id varchar(36) NOT NULL PRIMARY KEY,
  name varchar(255) NOT NULL UNIQUE,
  displayName varchar(255),
  description text,
  type enum('text','image','audio','video','multimodal','embedding','classification','generation','translation','summarization','qa','chat','code','other') DEFAULT 'other',
  category enum('nlp','cv','speech','multimodal','ml','dl','other') DEFAULT 'other',
  isActive tinyint(1) DEFAULT 1,
  createdAt datetime(6) DEFAULT CURRENT_TIMESTAMP(6),
  updatedAt datetime(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
);

CREATE TABLE IF NOT EXISTS inference_logs (
  id varchar(36) NOT NULL PRIMARY KEY,
  model_id varchar(36) NOT NULL,
  status enum('pending','processing','completed','failed','timeout','cancelled') DEFAULT 'pending',
  created_at datetime(6) DEFAULT CURRENT_TIMESTAMP(6),
  FOREIGN KEY (model_id) REFERENCES ai_models(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS model_versions (
  id varchar(36) NOT NULL PRIMARY KEY,
  model_id varchar(36) NOT NULL,
  version varchar(50) NOT NULL,
  created_at datetime(6) DEFAULT CURRENT_TIMESTAMP(6),
  FOREIGN KEY (model_id) REFERENCES ai_models(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS model_metrics (
  id varchar(36) NOT NULL PRIMARY KEY,
  model_id varchar(36) NOT NULL,
  metricType enum('performance','usage','error','resource','quality','latency','throughput','accuracy','other') DEFAULT 'other',
  metricValue float NOT NULL,
  created_at datetime(6) DEFAULT CURRENT_TIMESTAMP(6),
  FOREIGN KEY (model_id) REFERENCES ai_models(id) ON DELETE CASCADE
);

SHOW TABLES;
exit;
```

### 2. 重启所有问题服务

```powershell
# 重启AI模型服务
docker-compose -f docker-compose.windows.yml restart ai-model-service

# 重启其他问题服务
docker-compose -f docker-compose.windows.yml restart user-service project-service render-service api-gateway scene-generation-service scene-template-service

# 等待服务启动
Start-Sleep -Seconds 60

# 检查服务状态
docker-compose -f docker-compose.windows.yml ps
```

### 3. 验证修复结果

```powershell
# 检查AI模型服务日志
docker-compose -f docker-compose.windows.yml logs --tail=20 ai-model-service

# 检查API网关日志
docker-compose -f docker-compose.windows.yml logs --tail=20 api-gateway

# 测试API网关健康检查
curl http://localhost:3000/api/health

# 测试AI模型服务健康检查
curl http://localhost:3008/api/v1/health
```

## 预期修复结果

修复后应该看到：

1. ✅ AI模型服务不再报"Table doesn't exist"错误
2. ✅ 所有服务状态变为"healthy"
3. ✅ API网关能够连接到所有微服务
4. ✅ 健康检查超时错误消失

## 如果问题持续存在

### 备用方案1：完全重启
```powershell
# 停止所有服务
docker-compose -f docker-compose.windows.yml down

# 使用修复脚本重新启动
.\fix-services-startup.ps1
```

### 备用方案2：检查具体错误
```powershell
# 查看具体服务的详细日志
docker-compose -f docker-compose.windows.yml logs [服务名]

# 检查网络连接
docker network ls
docker network inspect dl-engine-network
```

## 成功指标

- [ ] 所有服务状态为"healthy"
- [ ] AI模型服务无数据库错误
- [ ] API网关健康检查通过
- [ ] 前端可以正常访问（http://localhost）
- [ ] 服务注册中心显示所有服务已注册

## 联系支持

如果按照以上步骤操作后问题仍然存在，请提供：
1. 服务状态输出：`docker-compose -f docker-compose.windows.yml ps`
2. 问题服务的日志：`docker-compose -f docker-compose.windows.yml logs [服务名]`
3. 数据库表状态：MySQL中的`SHOW TABLES`结果
