/**
 * 喷泉预设
 * 提供各种类型的喷泉预设配置
 */
import * as THREE from 'three';
import { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { FountainComponent, FountainConfig, FountainType, FountainMode } from './FountainComponent';
import { Debug } from '../../utils/Debug';

/**
 * 喷泉预设类型
 */
export enum FountainPresetType {
  /** 标准喷泉 */
  STANDARD = 'standard',
  /** 高喷泉 */
  HIGH = 'high',
  /** 宽喷泉 */
  WIDE = 'wide',
  /** 多喷头喷泉 */
  MULTI_JET = 'multi_jet',
  /** 舞蹈喷泉 */
  DANCING = 'dancing',
  /** 音乐喷泉 */
  MUSICAL = 'musical',
  /** 脉冲喷泉 */
  PULSE = 'pulse',
  /** 交替喷泉 */
  ALTERNATING = 'alternating',
  /** 序列喷泉 */
  SEQUENCE = 'sequence',
  /** 随机喷泉 */
  RANDOM = 'random'
}

/**
 * 喷泉预设配置
 */
export interface FountainPresetConfig {
  /** 预设类型 */
  type: FountainPresetType;
  /** 喷泉宽度 */
  width?: number;
  /** 喷泉高度 */
  height?: number;
  /** 喷泉深度 */
  depth?: number;
  /** 喷泉位置 */
  position?: THREE.Vector3;
  /** 喷泉旋转 */
  rotation?: THREE.Euler;
  /** 喷泉颜色 */
  color?: THREE.Color;
  /** 喷泉不透明度 */
  opacity?: number;
  /** 喷泉流速 */
  flowSpeed?: number;
  /** 喷泉湍流强度 */
  turbulenceStrength?: number;
  /** 喷泉湍流频率 */
  turbulenceFrequency?: number;
  /** 喷泉湍流速度 */
  turbulenceSpeed?: number;
  /** 是否启用水雾效果 */
  enableMistEffect?: boolean;
  /** 水雾效果强度 */
  mistEffectStrength?: number;
  /** 是否启用水花效果 */
  enableSplashEffect?: boolean;
  /** 水花效果强度 */
  splashEffectStrength?: number;
  /** 是否启用水滴效果 */
  enableDropletEffect?: boolean;
  /** 水滴效果强度 */
  dropletEffectStrength?: number;
  /** 是否启用声音效果 */
  enableSoundEffect?: boolean;
  /** 声音效果音量 */
  soundEffectVolume?: number;
  /** 是否启用水流动力学 */
  enableFluidDynamics?: boolean;
  /** 喷泉喷射高度 */
  jetHeight?: number;
  /** 喷泉喷射角度 */
  jetAngle?: number;
  /** 喷泉喷射数量 */
  jetCount?: number;
  /** 喷泉喷射间隔 */
  jetInterval?: number;
  /** 喷泉喷射持续时间 */
  jetDuration?: number;
  /** 喷泉喷射延迟 */
  jetDelay?: number;
  /** 喷泉喷射随机性 */
  jetRandomness?: number;
}

/**
 * 喷泉预设
 */
export class FountainPresets {
  /**
   * 创建喷泉预设
   * @param world 世界
   * @param config 预设配置
   * @returns 喷泉实体
   */
  public static createPreset(world: World, config: FountainPresetConfig): Entity {
    // 创建喷泉实体
    const entity = new Entity(`fountain_${config.type}`);

    // 创建喷泉配置
    const fountainConfig: FountainConfig = {};

    // 设置尺寸
    if (config.width !== undefined) fountainConfig.width = config.width;
    if (config.height !== undefined) fountainConfig.height = config.height;
    if (config.depth !== undefined) fountainConfig.depth = config.depth;

    // 设置位置和旋转
    if (config.position !== undefined) fountainConfig.position = config.position;
    if (config.rotation !== undefined) fountainConfig.rotation = config.rotation;

    // 设置颜色和不透明度
    if (config.color !== undefined) fountainConfig.color = config.color;
    if (config.opacity !== undefined) fountainConfig.opacity = config.opacity;

    // 设置流速
    if (config.flowSpeed !== undefined) fountainConfig.flowSpeed = config.flowSpeed;

    // 设置湍流参数
    if (config.turbulenceStrength !== undefined) fountainConfig.turbulenceStrength = config.turbulenceStrength;
    if (config.turbulenceFrequency !== undefined) fountainConfig.turbulenceFrequency = config.turbulenceFrequency;
    if (config.turbulenceSpeed !== undefined) fountainConfig.turbulenceSpeed = config.turbulenceSpeed;

    // 设置效果参数
    if (config.enableMistEffect !== undefined) fountainConfig.enableMistEffect = config.enableMistEffect;
    if (config.mistEffectStrength !== undefined) fountainConfig.mistEffectStrength = config.mistEffectStrength;
    if (config.enableSplashEffect !== undefined) fountainConfig.enableSplashEffect = config.enableSplashEffect;
    if (config.splashEffectStrength !== undefined) fountainConfig.splashEffectStrength = config.splashEffectStrength;
    if (config.enableDropletEffect !== undefined) fountainConfig.enableDropletEffect = config.enableDropletEffect;
    if (config.dropletEffectStrength !== undefined) fountainConfig.dropletEffectStrength = config.dropletEffectStrength;
    if (config.enableSoundEffect !== undefined) fountainConfig.enableSoundEffect = config.enableSoundEffect;
    if (config.soundEffectVolume !== undefined) fountainConfig.soundEffectVolume = config.soundEffectVolume;
    if (config.enableFluidDynamics !== undefined) fountainConfig.enableFluidDynamics = config.enableFluidDynamics;

    // 设置喷泉特有参数
    if (config.jetHeight !== undefined) fountainConfig.jetHeight = config.jetHeight;
    if (config.jetAngle !== undefined) fountainConfig.jetAngle = config.jetAngle;
    if (config.jetCount !== undefined) fountainConfig.jetCount = config.jetCount;
    if (config.jetInterval !== undefined) fountainConfig.jetInterval = config.jetInterval;
    if (config.jetDuration !== undefined) fountainConfig.jetDuration = config.jetDuration;
    if (config.jetDelay !== undefined) fountainConfig.jetDelay = config.jetDelay;
    if (config.jetRandomness !== undefined) fountainConfig.jetRandomness = config.jetRandomness;

    // 根据预设类型应用特定配置
    switch (config.type) {
      case FountainPresetType.STANDARD:
        this.applyStandardPreset(fountainConfig);
        break;
      case FountainPresetType.HIGH:
        this.applyHighPreset(fountainConfig);
        break;
      case FountainPresetType.WIDE:
        this.applyWidePreset(fountainConfig);
        break;
      case FountainPresetType.MULTI_JET:
        this.applyMultiJetPreset(fountainConfig);
        break;
      case FountainPresetType.DANCING:
        this.applyDancingPreset(fountainConfig);
        break;
      case FountainPresetType.MUSICAL:
        this.applyMusicalPreset(fountainConfig);
        break;
      case FountainPresetType.PULSE:
        this.applyPulsePreset(fountainConfig);
        break;
      case FountainPresetType.ALTERNATING:
        this.applyAlternatingPreset(fountainConfig);
        break;
      case FountainPresetType.SEQUENCE:
        this.applySequencePreset(fountainConfig);
        break;
      case FountainPresetType.RANDOM:
        this.applyRandomPreset(fountainConfig);
        break;
      default:
        this.applyStandardPreset(fountainConfig);
        break;
    }

    // 创建喷泉组件
    entity.addComponent(new FountainComponent(entity, fountainConfig));

    // 添加到世界
    world.addEntity(entity);

    Debug.log('FountainPresets', `创建${config.type}喷泉预设完成`);

    return entity;
  }

  /**
   * 应用标准喷泉预设
   * @param config 喷泉配置
   */
  private static applyStandardPreset(config: FountainConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 5;
    if (!config.height) config.height = 0.5;
    if (!config.depth) config.depth = 5;

    // 设置默认颜色和不透明度
    if (!config.color) config.color = new THREE.Color(0xc0e0ff);
    if (config.opacity === undefined) config.opacity = 0.7;

    // 设置默认流速
    if (config.flowSpeed === undefined) config.flowSpeed = 2.0;

    // 设置默认湍流参数
    if (config.turbulenceStrength === undefined) config.turbulenceStrength = 0.8;
    if (config.turbulenceFrequency === undefined) config.turbulenceFrequency = 1.5;
    if (config.turbulenceSpeed === undefined) config.turbulenceSpeed = 1.0;

    // 设置默认效果参数
    if (config.enableMistEffect === undefined) config.enableMistEffect = true;
    if (config.mistEffectStrength === undefined) config.mistEffectStrength = 0.8;
    if (config.enableSplashEffect === undefined) config.enableSplashEffect = true;
    if (config.splashEffectStrength === undefined) config.splashEffectStrength = 0.8;
    if (config.enableDropletEffect === undefined) config.enableDropletEffect = true;
    if (config.dropletEffectStrength === undefined) config.dropletEffectStrength = 0.8;
    if (config.enableSoundEffect === undefined) config.enableSoundEffect = true;
    if (config.soundEffectVolume === undefined) config.soundEffectVolume = 0.8;
    if (config.enableFluidDynamics === undefined) config.enableFluidDynamics = true;

    // 设置默认喷泉特有参数
    if (config.fountainType === undefined) config.fountainType = FountainType.STANDARD;
    if (config.fountainMode === undefined) config.fountainMode = FountainMode.CONTINUOUS;
    if (config.jetHeight === undefined) config.jetHeight = 10.0;
    if (config.jetAngle === undefined) config.jetAngle = 0.0;
    if (config.jetCount === undefined) config.jetCount = 1;
    if (config.jetInterval === undefined) config.jetInterval = 1.0;
    if (config.jetDuration === undefined) config.jetDuration = 2.0;
    if (config.jetDelay === undefined) config.jetDelay = 0.0;
    if (config.jetRandomness === undefined) config.jetRandomness = 0.2;
  }

  /**
   * 应用高喷泉预设
   * @param config 喷泉配置
   */
  private static applyHighPreset(config: FountainConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 5;
    if (!config.height) config.height = 0.5;
    if (!config.depth) config.depth = 5;

    // 设置默认颜色和不透明度
    if (!config.color) config.color = new THREE.Color(0xb0d0ff);
    if (config.opacity === undefined) config.opacity = 0.7;

    // 设置默认流速
    if (config.flowSpeed === undefined) config.flowSpeed = 3.0;

    // 设置默认湍流参数
    if (config.turbulenceStrength === undefined) config.turbulenceStrength = 1.0;
    if (config.turbulenceFrequency === undefined) config.turbulenceFrequency = 1.8;
    if (config.turbulenceSpeed === undefined) config.turbulenceSpeed = 1.2;

    // 设置默认效果参数
    if (config.enableMistEffect === undefined) config.enableMistEffect = true;
    if (config.mistEffectStrength === undefined) config.mistEffectStrength = 1.0;
    if (config.enableSplashEffect === undefined) config.enableSplashEffect = true;
    if (config.splashEffectStrength === undefined) config.splashEffectStrength = 1.0;
    if (config.enableDropletEffect === undefined) config.enableDropletEffect = true;
    if (config.dropletEffectStrength === undefined) config.dropletEffectStrength = 1.0;
    if (config.enableSoundEffect === undefined) config.enableSoundEffect = true;
    if (config.soundEffectVolume === undefined) config.soundEffectVolume = 1.0;
    if (config.enableFluidDynamics === undefined) config.enableFluidDynamics = true;

    // 设置默认喷泉特有参数
    if (config.fountainType === undefined) config.fountainType = FountainType.STANDARD;
    if (config.fountainMode === undefined) config.fountainMode = FountainMode.CONTINUOUS;
    if (config.jetHeight === undefined) config.jetHeight = 20.0;
    if (config.jetAngle === undefined) config.jetAngle = 0.0;
    if (config.jetCount === undefined) config.jetCount = 1;
    if (config.jetInterval === undefined) config.jetInterval = 1.0;
    if (config.jetDuration === undefined) config.jetDuration = 2.0;
    if (config.jetDelay === undefined) config.jetDelay = 0.0;
    if (config.jetRandomness === undefined) config.jetRandomness = 0.2;
  }

  /**
   * 应用宽喷泉预设
   * @param config 喷泉配置
   */
  private static applyWidePreset(config: FountainConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 10;
    if (!config.height) config.height = 0.5;
    if (!config.depth) config.depth = 10;

    // 设置默认颜色和不透明度
    if (!config.color) config.color = new THREE.Color(0xc0e0ff);
    if (config.opacity === undefined) config.opacity = 0.7;

    // 设置默认流速
    if (config.flowSpeed === undefined) config.flowSpeed = 2.0;

    // 设置默认湍流参数
    if (config.turbulenceStrength === undefined) config.turbulenceStrength = 0.8;
    if (config.turbulenceFrequency === undefined) config.turbulenceFrequency = 1.5;
    if (config.turbulenceSpeed === undefined) config.turbulenceSpeed = 1.0;

    // 设置默认效果参数
    if (config.enableMistEffect === undefined) config.enableMistEffect = true;
    if (config.mistEffectStrength === undefined) config.mistEffectStrength = 0.8;
    if (config.enableSplashEffect === undefined) config.enableSplashEffect = true;
    if (config.splashEffectStrength === undefined) config.splashEffectStrength = 0.8;
    if (config.enableDropletEffect === undefined) config.enableDropletEffect = true;
    if (config.dropletEffectStrength === undefined) config.dropletEffectStrength = 0.8;
    if (config.enableSoundEffect === undefined) config.enableSoundEffect = true;
    if (config.soundEffectVolume === undefined) config.soundEffectVolume = 0.8;
    if (config.enableFluidDynamics === undefined) config.enableFluidDynamics = true;

    // 设置默认喷泉特有参数
    if (config.fountainType === undefined) config.fountainType = FountainType.STANDARD;
    if (config.fountainMode === undefined) config.fountainMode = FountainMode.CONTINUOUS;
    if (config.jetHeight === undefined) config.jetHeight = 12.0;
    if (config.jetAngle === undefined) config.jetAngle = 0.0;
    if (config.jetCount === undefined) config.jetCount = 1;
    if (config.jetInterval === undefined) config.jetInterval = 1.0;
    if (config.jetDuration === undefined) config.jetDuration = 2.0;
    if (config.jetDelay === undefined) config.jetDelay = 0.0;
    if (config.jetRandomness === undefined) config.jetRandomness = 0.2;
  }

  /**
   * 应用多喷头喷泉预设
   * @param config 喷泉配置
   */
  private static applyMultiJetPreset(config: FountainConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 8;
    if (!config.height) config.height = 0.5;
    if (!config.depth) config.depth = 8;

    // 设置默认颜色和不透明度
    if (!config.color) config.color = new THREE.Color(0xc0e0ff);
    if (config.opacity === undefined) config.opacity = 0.7;

    // 设置默认流速
    if (config.flowSpeed === undefined) config.flowSpeed = 2.0;

    // 设置默认湍流参数
    if (config.turbulenceStrength === undefined) config.turbulenceStrength = 0.8;
    if (config.turbulenceFrequency === undefined) config.turbulenceFrequency = 1.5;
    if (config.turbulenceSpeed === undefined) config.turbulenceSpeed = 1.0;

    // 设置默认效果参数
    if (config.enableMistEffect === undefined) config.enableMistEffect = true;
    if (config.mistEffectStrength === undefined) config.mistEffectStrength = 0.8;
    if (config.enableSplashEffect === undefined) config.enableSplashEffect = true;
    if (config.splashEffectStrength === undefined) config.splashEffectStrength = 0.8;
    if (config.enableDropletEffect === undefined) config.enableDropletEffect = true;
    if (config.dropletEffectStrength === undefined) config.dropletEffectStrength = 0.8;
    if (config.enableSoundEffect === undefined) config.enableSoundEffect = true;
    if (config.soundEffectVolume === undefined) config.soundEffectVolume = 0.8;
    if (config.enableFluidDynamics === undefined) config.enableFluidDynamics = true;

    // 设置默认喷泉特有参数
    if (config.fountainType === undefined) config.fountainType = FountainType.MULTI_JET;
    if (config.fountainMode === undefined) config.fountainMode = FountainMode.CONTINUOUS;
    if (config.jetHeight === undefined) config.jetHeight = 10.0;
    if (config.jetAngle === undefined) config.jetAngle = 15.0;
    if (config.jetCount === undefined) config.jetCount = 5;
    if (config.jetInterval === undefined) config.jetInterval = 1.0;
    if (config.jetDuration === undefined) config.jetDuration = 2.0;
    if (config.jetDelay === undefined) config.jetDelay = 0.0;
    if (config.jetRandomness === undefined) config.jetRandomness = 0.2;
  }

  /**
   * 应用舞蹈喷泉预设
   * @param config 喷泉配置
   */
  private static applyDancingPreset(config: FountainConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 10;
    if (!config.height) config.height = 0.5;
    if (!config.depth) config.depth = 10;

    // 设置默认颜色和不透明度
    if (!config.color) config.color = new THREE.Color(0xc0e0ff);
    if (config.opacity === undefined) config.opacity = 0.7;

    // 设置默认流速
    if (config.flowSpeed === undefined) config.flowSpeed = 2.5;

    // 设置默认湍流参数
    if (config.turbulenceStrength === undefined) config.turbulenceStrength = 1.0;
    if (config.turbulenceFrequency === undefined) config.turbulenceFrequency = 1.8;
    if (config.turbulenceSpeed === undefined) config.turbulenceSpeed = 1.2;

    // 设置默认效果参数
    if (config.enableMistEffect === undefined) config.enableMistEffect = true;
    if (config.mistEffectStrength === undefined) config.mistEffectStrength = 0.8;
    if (config.enableSplashEffect === undefined) config.enableSplashEffect = true;
    if (config.splashEffectStrength === undefined) config.splashEffectStrength = 0.8;
    if (config.enableDropletEffect === undefined) config.enableDropletEffect = true;
    if (config.dropletEffectStrength === undefined) config.dropletEffectStrength = 0.8;
    if (config.enableSoundEffect === undefined) config.enableSoundEffect = true;
    if (config.soundEffectVolume === undefined) config.soundEffectVolume = 0.8;
    if (config.enableFluidDynamics === undefined) config.enableFluidDynamics = true;

    // 设置默认喷泉特有参数
    if (config.fountainType === undefined) config.fountainType = FountainType.DANCING;
    if (config.fountainMode === undefined) config.fountainMode = FountainMode.CONTINUOUS;
    if (config.jetHeight === undefined) config.jetHeight = 12.0;
    if (config.jetAngle === undefined) config.jetAngle = 0.0;
    if (config.jetCount === undefined) config.jetCount = 8;
    if (config.jetInterval === undefined) config.jetInterval = 0.5;
    if (config.jetDuration === undefined) config.jetDuration = 1.0;
    if (config.jetDelay === undefined) config.jetDelay = 0.0;
    if (config.jetRandomness === undefined) config.jetRandomness = 0.5;
  }

  /**
   * 应用音乐喷泉预设
   * @param config 喷泉配置
   */
  private static applyMusicalPreset(config: FountainConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 12;
    if (!config.height) config.height = 0.5;
    if (!config.depth) config.depth = 12;

    // 设置默认颜色和不透明度
    if (!config.color) config.color = new THREE.Color(0xb0d0ff);
    if (config.opacity === undefined) config.opacity = 0.7;

    // 设置默认流速
    if (config.flowSpeed === undefined) config.flowSpeed = 3.0;

    // 设置默认湍流参数
    if (config.turbulenceStrength === undefined) config.turbulenceStrength = 1.0;
    if (config.turbulenceFrequency === undefined) config.turbulenceFrequency = 2.0;
    if (config.turbulenceSpeed === undefined) config.turbulenceSpeed = 1.5;

    // 设置默认效果参数
    if (config.enableMistEffect === undefined) config.enableMistEffect = true;
    if (config.mistEffectStrength === undefined) config.mistEffectStrength = 1.0;
    if (config.enableSplashEffect === undefined) config.enableSplashEffect = true;
    if (config.splashEffectStrength === undefined) config.splashEffectStrength = 1.0;
    if (config.enableDropletEffect === undefined) config.enableDropletEffect = true;
    if (config.dropletEffectStrength === undefined) config.dropletEffectStrength = 1.0;
    if (config.enableSoundEffect === undefined) config.enableSoundEffect = true;
    if (config.soundEffectVolume === undefined) config.soundEffectVolume = 1.0;
    if (config.enableFluidDynamics === undefined) config.enableFluidDynamics = true;

    // 设置默认喷泉特有参数
    if (config.fountainType === undefined) config.fountainType = FountainType.MUSICAL;
    if (config.fountainMode === undefined) config.fountainMode = FountainMode.CONTINUOUS;
    if (config.jetHeight === undefined) config.jetHeight = 15.0;
    if (config.jetAngle === undefined) config.jetAngle = 0.0;
    if (config.jetCount === undefined) config.jetCount = 12;
    if (config.jetInterval === undefined) config.jetInterval = 0.2;
    if (config.jetDuration === undefined) config.jetDuration = 0.5;
    if (config.jetDelay === undefined) config.jetDelay = 0.0;
    if (config.jetRandomness === undefined) config.jetRandomness = 0.3;
  }

  /**
   * 应用脉冲喷泉预设
   * @param config 喷泉配置
   */
  private static applyPulsePreset(config: FountainConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 6;
    if (!config.height) config.height = 0.5;
    if (!config.depth) config.depth = 6;

    // 设置默认颜色和不透明度
    if (!config.color) config.color = new THREE.Color(0xc0e0ff);
    if (config.opacity === undefined) config.opacity = 0.7;

    // 设置默认流速
    if (config.flowSpeed === undefined) config.flowSpeed = 2.5;

    // 设置默认湍流参数
    if (config.turbulenceStrength === undefined) config.turbulenceStrength = 0.8;
    if (config.turbulenceFrequency === undefined) config.turbulenceFrequency = 1.5;
    if (config.turbulenceSpeed === undefined) config.turbulenceSpeed = 1.0;

    // 设置默认效果参数
    if (config.enableMistEffect === undefined) config.enableMistEffect = true;
    if (config.mistEffectStrength === undefined) config.mistEffectStrength = 0.8;
    if (config.enableSplashEffect === undefined) config.enableSplashEffect = true;
    if (config.splashEffectStrength === undefined) config.splashEffectStrength = 0.8;
    if (config.enableDropletEffect === undefined) config.enableDropletEffect = true;
    if (config.dropletEffectStrength === undefined) config.dropletEffectStrength = 0.8;
    if (config.enableSoundEffect === undefined) config.enableSoundEffect = true;
    if (config.soundEffectVolume === undefined) config.soundEffectVolume = 0.8;
    if (config.enableFluidDynamics === undefined) config.enableFluidDynamics = true;

    // 设置默认喷泉特有参数
    if (config.fountainType === undefined) config.fountainType = FountainType.STANDARD;
    if (config.fountainMode === undefined) config.fountainMode = FountainMode.PULSE;
    if (config.jetHeight === undefined) config.jetHeight = 12.0;
    if (config.jetAngle === undefined) config.jetAngle = 0.0;
    if (config.jetCount === undefined) config.jetCount = 1;
    if (config.jetInterval === undefined) config.jetInterval = 3.0;
    if (config.jetDuration === undefined) config.jetDuration = 1.5;
    if (config.jetDelay === undefined) config.jetDelay = 0.0;
    if (config.jetRandomness === undefined) config.jetRandomness = 0.2;
  }

  /**
   * 应用交替喷泉预设
   * @param config 喷泉配置
   */
  private static applyAlternatingPreset(config: FountainConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 8;
    if (!config.height) config.height = 0.5;
    if (!config.depth) config.depth = 8;

    // 设置默认颜色和不透明度
    if (!config.color) config.color = new THREE.Color(0xc0e0ff);
    if (config.opacity === undefined) config.opacity = 0.7;

    // 设置默认流速
    if (config.flowSpeed === undefined) config.flowSpeed = 2.0;

    // 设置默认湍流参数
    if (config.turbulenceStrength === undefined) config.turbulenceStrength = 0.8;
    if (config.turbulenceFrequency === undefined) config.turbulenceFrequency = 1.5;
    if (config.turbulenceSpeed === undefined) config.turbulenceSpeed = 1.0;

    // 设置默认效果参数
    if (config.enableMistEffect === undefined) config.enableMistEffect = true;
    if (config.mistEffectStrength === undefined) config.mistEffectStrength = 0.8;
    if (config.enableSplashEffect === undefined) config.enableSplashEffect = true;
    if (config.splashEffectStrength === undefined) config.splashEffectStrength = 0.8;
    if (config.enableDropletEffect === undefined) config.enableDropletEffect = true;
    if (config.dropletEffectStrength === undefined) config.dropletEffectStrength = 0.8;
    if (config.enableSoundEffect === undefined) config.enableSoundEffect = true;
    if (config.soundEffectVolume === undefined) config.soundEffectVolume = 0.8;
    if (config.enableFluidDynamics === undefined) config.enableFluidDynamics = true;

    // 设置默认喷泉特有参数
    if (config.fountainType === undefined) config.fountainType = FountainType.MULTI_JET;
    if (config.fountainMode === undefined) config.fountainMode = FountainMode.ALTERNATING;
    if (config.jetHeight === undefined) config.jetHeight = 10.0;
    if (config.jetAngle === undefined) config.jetAngle = 10.0;
    if (config.jetCount === undefined) config.jetCount = 6;
    if (config.jetInterval === undefined) config.jetInterval = 0.5;
    if (config.jetDuration === undefined) config.jetDuration = 1.0;
    if (config.jetDelay === undefined) config.jetDelay = 0.0;
    if (config.jetRandomness === undefined) config.jetRandomness = 0.2;
  }

  /**
   * 应用序列喷泉预设
   * @param config 喷泉配置
   */
  private static applySequencePreset(config: FountainConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 10;
    if (!config.height) config.height = 0.5;
    if (!config.depth) config.depth = 10;

    // 设置默认颜色和不透明度
    if (!config.color) config.color = new THREE.Color(0xc0e0ff);
    if (config.opacity === undefined) config.opacity = 0.7;

    // 设置默认流速
    if (config.flowSpeed === undefined) config.flowSpeed = 2.0;

    // 设置默认湍流参数
    if (config.turbulenceStrength === undefined) config.turbulenceStrength = 0.8;
    if (config.turbulenceFrequency === undefined) config.turbulenceFrequency = 1.5;
    if (config.turbulenceSpeed === undefined) config.turbulenceSpeed = 1.0;

    // 设置默认效果参数
    if (config.enableMistEffect === undefined) config.enableMistEffect = true;
    if (config.mistEffectStrength === undefined) config.mistEffectStrength = 0.8;
    if (config.enableSplashEffect === undefined) config.enableSplashEffect = true;
    if (config.splashEffectStrength === undefined) config.splashEffectStrength = 0.8;
    if (config.enableDropletEffect === undefined) config.enableDropletEffect = true;
    if (config.dropletEffectStrength === undefined) config.dropletEffectStrength = 0.8;
    if (config.enableSoundEffect === undefined) config.enableSoundEffect = true;
    if (config.soundEffectVolume === undefined) config.soundEffectVolume = 0.8;
    if (config.enableFluidDynamics === undefined) config.enableFluidDynamics = true;

    // 设置默认喷泉特有参数
    if (config.fountainType === undefined) config.fountainType = FountainType.MULTI_JET;
    if (config.fountainMode === undefined) config.fountainMode = FountainMode.SEQUENCE;
    if (config.jetHeight === undefined) config.jetHeight = 12.0;
    if (config.jetAngle === undefined) config.jetAngle = 5.0;
    if (config.jetCount === undefined) config.jetCount = 8;
    if (config.jetInterval === undefined) config.jetInterval = 0.2;
    if (config.jetDuration === undefined) config.jetDuration = 1.0;
    if (config.jetDelay === undefined) config.jetDelay = 0.0;
    if (config.jetRandomness === undefined) config.jetRandomness = 0.2;
  }

  /**
   * 应用随机喷泉预设
   * @param config 喷泉配置
   */
  private static applyRandomPreset(config: FountainConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 8;
    if (!config.height) config.height = 0.5;
    if (!config.depth) config.depth = 8;

    // 设置默认颜色和不透明度
    if (!config.color) config.color = new THREE.Color(0xc0e0ff);
    if (config.opacity === undefined) config.opacity = 0.7;

    // 设置默认流速
    if (config.flowSpeed === undefined) config.flowSpeed = 2.5;

    // 设置默认湍流参数
    if (config.turbulenceStrength === undefined) config.turbulenceStrength = 1.0;
    if (config.turbulenceFrequency === undefined) config.turbulenceFrequency = 1.8;
    if (config.turbulenceSpeed === undefined) config.turbulenceSpeed = 1.2;

    // 设置默认效果参数
    if (config.enableMistEffect === undefined) config.enableMistEffect = true;
    if (config.mistEffectStrength === undefined) config.mistEffectStrength = 0.8;
    if (config.enableSplashEffect === undefined) config.enableSplashEffect = true;
    if (config.splashEffectStrength === undefined) config.splashEffectStrength = 0.8;
    if (config.enableDropletEffect === undefined) config.enableDropletEffect = true;
    if (config.dropletEffectStrength === undefined) config.dropletEffectStrength = 0.8;
    if (config.enableSoundEffect === undefined) config.enableSoundEffect = true;
    if (config.soundEffectVolume === undefined) config.soundEffectVolume = 0.8;
    if (config.enableFluidDynamics === undefined) config.enableFluidDynamics = true;

    // 设置默认喷泉特有参数
    if (config.fountainType === undefined) config.fountainType = FountainType.MULTI_JET;
    if (config.fountainMode === undefined) config.fountainMode = FountainMode.RANDOM;
    if (config.jetHeight === undefined) config.jetHeight = 15.0;
    if (config.jetAngle === undefined) config.jetAngle = 10.0;
    if (config.jetCount === undefined) config.jetCount = 10;
    if (config.jetInterval === undefined) config.jetInterval = 0.5;
    if (config.jetDuration === undefined) config.jetDuration = 1.0;
    if (config.jetDelay === undefined) config.jetDelay = 0.0;
    if (config.jetRandomness === undefined) config.jetRandomness = 0.5;
  }
}
