import { EventEmitter } from '../../utils/EventEmitter';
import { type Entity } from '../../core/Entity';
import { AnimationState, StateTransition } from './MultiActionFusionTypes';
/**
 * 状态机配置
 */
export interface StateMachineConfig {
    /** 默认状态 */
    defaultState?: string;
    /** 是否启用调试 */
    debug?: boolean;
    /** 最大转换时间 */
    maxTransitionTime?: number;
}
/**
 * 转换选项
 */
export interface TransitionOptions {
    /** 淡入时间 */
    fadeTime?: number;
    /** 是否循环 */
    loop?: boolean;
    /** 播放速度 */
    speed?: number;
}
/**
 * 状态机参数
 */
export interface StateMachineParameter {
    /** 参数名称 */
    name: string;
    /** 参数值 */
    value: number;
    /** 参数类型 */
    type: 'float' | 'int' | 'bool' | 'trigger';
}
/**
 * 动画状态机
 */
export declare class AnimationStateMachine extends EventEmitter {
    /** 关联的实体 */
    private entity;
    /** 配置 */
    private config;
    /** 状态映射 */
    private states;
    /** 转换映射 */
    private transitions;
    /** 当前状态 */
    private currentState;
    /** 目标状态 */
    private targetState;
    /** 转换进度 */
    private transitionProgress;
    /** 转换时间 */
    private transitionDuration;
    /** 是否正在转换 */
    private isTransitioning;
    /** 状态机参数 */
    private parameters;
    /** 动画混合器 */
    private mixer;
    /** 动画动作映射 */
    private actions;
    /**
     * 构造函数
     * @param entity 关联实体
     * @param config 配置
     */
    constructor(entity: Entity, config?: StateMachineConfig);
    /**
     * 设置状态列表
     * @param states 状态列表
     */
    setStates(states: AnimationState[]): void;
    /**
     * 设置转换列表
     * @param transitions 转换列表
     */
    setTransitions(transitions: StateTransition[]): void;
    /**
     * 转换到指定状态
     * @param stateName 状态名称
     * @param options 转换选项
     */
    transitionTo(stateName: string, options?: TransitionOptions): Promise<void>;
    /**
     * 更新状态机
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void;
    /**
     * 更新转换
     * @param deltaTime 时间增量
     */
    private updateTransition;
    /**
     * 完成转换
     */
    private completeTransition;
    /**
     * 更新混合权重
     */
    private updateBlendWeights;
    /**
     * 检查自动转换
     */
    private checkAutoTransitions;
    /**
     * 评估转换条件
     * @param conditions 条件列表
     * @returns 是否满足条件
     */
    private evaluateTransitionConditions;
    /**
     * 评估单个条件
     * @param value 参数值
     * @param comparison 比较类型
     * @param threshold 阈值
     * @returns 是否满足条件
     */
    private evaluateCondition;
    /**
     * 设置参数值
     * @param name 参数名称
     * @param value 参数值
     */
    setParameter(name: string, value: number): void;
    /**
     * 获取参数值
     * @param name 参数名称
     * @returns 参数值
     */
    getParameter(name: string): number | undefined;
    /**
     * 获取当前状态
     * @returns 当前状态名称
     */
    getCurrentState(): string | null;
    /**
     * 是否正在转换
     * @returns 转换状态
     */
    getIsTransitioning(): boolean;
    /**
     * 转换动画片段为Three.js格式
     * @param clip 动画片段
     * @returns Three.js动画片段
     */
    private convertToThreeClip;
    /**
     * 销毁状态机
     */
    dispose(): void;
}
