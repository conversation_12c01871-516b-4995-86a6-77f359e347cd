#!/usr/bin/env pwsh
# React Context错误修复脚本
# 修复图片中显示的useContext和find方法错误

param(
    [switch]$Help,            # 显示帮助信息
    [switch]$CheckOnly,       # 仅检查问题，不修复
    [switch]$Verbose          # 详细输出
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput Cyan "ℹ️  $message"
}

function Write-Success($message) {
    Write-ColorOutput Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "❌ $message"
}

function Write-Header($message) {
    Write-Host ""
    Write-ColorOutput Magenta "🔧 $message"
    Write-Host "=" * 60
}

# 显示帮助信息
function Show-Help {
    Write-Host "React Context错误修复脚本"
    Write-Host ""
    Write-Host "用法: .\fix-react-context-errors.ps1 [选项]"
    Write-Host ""
    Write-Host "选项:"
    Write-Host "  -CheckOnly      仅检查问题，不进行修复"
    Write-Host "  -Verbose        显示详细输出"
    Write-Host "  -Help           显示此帮助信息"
    Write-Host ""
    Write-Host "此脚本将修复以下问题:"
    Write-Host "  1. React useContext为null的错误"
    Write-Host "  2. 数组find方法undefined错误"
    Write-Host "  3. 组件渲染错误"
    Write-Host "  4. Context Provider配置问题"
}

# 检查文件是否存在
function Test-FileExists($filePath) {
    if (-not (Test-Path $filePath)) {
        Write-Error "文件不存在: $filePath"
        return $false
    }
    return $true
}

# 修复React Context问题
function Repair-ReactContextIssues {
    Write-Header "修复React Context问题"
    
    # 1. 检查并修复main.tsx中的Provider配置
    $mainTsxPath = "editor/src/main.tsx"
    if (Test-FileExists $mainTsxPath) {
        Write-Info "检查main.tsx中的Provider配置..."
        
        $content = Get-Content $mainTsxPath -Raw
        if ($content -match "React\.StrictMode") {
            Write-Info "发现React.StrictMode，这可能导致useContext问题"
            Write-Info "建议在开发环境中暂时禁用StrictMode"
        }
    }
    
    # 2. 检查App.tsx中的Context配置
    $appTsxPath = "editor/src/App.tsx"
    if (Test-FileExists $appTsxPath) {
        Write-Info "检查App.tsx中的Context配置..."
    }
    
    Write-Success "React Context问题检查完成"
}

# 修复数组find方法问题
function Repair-ArrayFindIssues {
    Write-Header "修复数组find方法问题"
    
    # 查找可能有问题的文件
    $problematicFiles = @(
        "editor/src/components/git/GitStatusPanel.tsx",
        "editor/src/components/git/GitHistoryPanel.tsx",
        "editor/src/components/git/GitBranchPanel.tsx",
        "editor/src/store/git/gitSlice.ts"
    )
    
    foreach ($file in $problematicFiles) {
        if (Test-FileExists $file) {
            Write-Info "检查文件: $file"
            
            $content = Get-Content $file -Raw
            
            # 检查是否有未保护的find调用
            if ($content -match "\.find\(") {
                Write-Warning "发现可能有问题的find调用: $file"
            }
            
            # 检查是否有未保护的数组操作
            if ($content -match "\[\]\.") {
                Write-Warning "发现可能有问题的数组操作: $file"
            }
        }
    }
    
    Write-Success "数组find方法问题检查完成"
}

# 清理构建缓存
function Clear-BuildCache {
    Write-Header "清理构建缓存"
    
    $cachePaths = @(
        "editor/node_modules/.cache",
        "editor/dist",
        "editor/.vite"
    )
    
    foreach ($path in $cachePaths) {
        if (Test-Path $path) {
            Write-Info "清理缓存: $path"
            Remove-Item $path -Recurse -Force -ErrorAction SilentlyContinue
        }
    }
    
    Write-Success "构建缓存清理完成"
}

# 重新安装依赖
function Reinstall-Dependencies {
    Write-Header "重新安装依赖"
    
    $editorPath = "editor"
    if (Test-Path $editorPath) {
        Push-Location $editorPath
        
        try {
            Write-Info "删除node_modules..."
            if (Test-Path "node_modules") {
                Remove-Item "node_modules" -Recurse -Force
            }
            
            Write-Info "删除package-lock.json..."
            if (Test-Path "package-lock.json") {
                Remove-Item "package-lock.json" -Force
            }
            
            Write-Info "重新安装依赖..."
            npm install
            
            Write-Success "依赖重新安装完成"
        }
        catch {
            Write-Error "依赖安装失败: $($_.Exception.Message)"
        }
        finally {
            Pop-Location
        }
    }
}

# 重新构建项目
function Rebuild-Project {
    Write-Header "重新构建项目"
    
    $editorPath = "editor"
    if (Test-Path $editorPath) {
        Push-Location $editorPath
        
        try {
            Write-Info "构建项目..."
            npm run build
            
            Write-Success "项目构建完成"
        }
        catch {
            Write-Error "项目构建失败: $($_.Exception.Message)"
            return $false
        }
        finally {
            Pop-Location
        }
    }
    
    return $true
}

# 重启Docker服务
function Restart-DockerServices {
    Write-Header "重启Docker服务"
    
    try {
        Write-Info "停止editor服务..."
        docker-compose -f docker-compose.windows.yml stop editor
        
        Write-Info "重新构建editor镜像..."
        docker-compose -f docker-compose.windows.yml build --no-cache editor
        
        Write-Info "启动editor服务..."
        docker-compose -f docker-compose.windows.yml up -d editor
        
        Write-Success "Docker服务重启完成"
        return $true
    }
    catch {
        Write-Error "Docker服务重启失败: $($_.Exception.Message)"
        return $false
    }
}

# 验证修复结果
function Test-FixResults {
    Write-Header "验证修复结果"
    
    Write-Info "等待服务启动..."
    Start-Sleep -Seconds 30
    
    # 测试前端访问
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:80" -TimeoutSec 15 -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 200) {
            Write-Success "前端访问正常"
        } else {
            Write-Warning "前端响应异常: $($response.StatusCode)"
        }
    } catch {
        Write-Warning "前端连接失败: $($_.Exception.Message)"
    }
    
    Write-Info "请打开浏览器访问 http://localhost:80 并检查控制台错误"
}

# 主函数
function Main {
    if ($Help) {
        Show-Help
        return
    }

    Write-Header "React Context错误修复工具"
    
    if ($CheckOnly) {
        Write-Info "仅检查模式，不进行修复"
        Repair-ReactContextIssues
        Repair-ArrayFindIssues
        return
    }
    
    # 执行修复步骤
    Write-Info "开始修复React Context错误..."
    
    # 1. 检查问题
    Repair-ReactContextIssues
    Repair-ArrayFindIssues
    
    # 2. 清理缓存
    Clear-BuildCache
    
    # 3. 重新安装依赖
    Reinstall-Dependencies
    
    # 4. 重新构建项目
    if (Rebuild-Project) {
        # 5. 重启Docker服务
        if (Restart-DockerServices) {
            # 6. 验证结果
            Test-FixResults
            
            Write-Success "修复完成！"
            Write-Info "如果仍有问题，请检查浏览器控制台的具体错误信息"
        }
    }
}

# 执行主函数
try {
    Main
} catch {
    Write-Error "脚本执行失败: $($_.Exception.Message)"
    exit 1
}
