import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, TreeRepository, DataSource } from 'typeorm';
import { TemplateCategory } from './entities/template-category.entity';
import { CreateCategoryDto, UpdateCategoryDto } from './dto/create-category.dto';
import { CacheService } from '../../common/services/cache.service';
import { LoggerService } from '../../common/services/logger.service';

@Injectable()
export class CategoriesService {
  private readonly categoryTreeRepository: TreeRepository<TemplateCategory>;

  constructor(
    @InjectRepository(TemplateCategory)
    private readonly categoryRepository: Repository<TemplateCategory>,
    private readonly dataSource: DataSource,
    private readonly cacheService: CacheService,
    private readonly logger: LoggerService,
  ) {
    this.categoryTreeRepository = this.dataSource.getTreeRepository(TemplateCategory);
  }

  /**
   * 创建分类
   */
  async create(createCategoryDto: CreateCategoryDto): Promise<TemplateCategory> {
    const { parentId, ...categoryData } = createCategoryDto;

    // 检查slug是否重复
    const existingCategory = await this.categoryRepository.findOne({
      where: { slug: categoryData.slug },
    });

    if (existingCategory) {
      throw new BadRequestException('分类标识已存在');
    }

    let parent: TemplateCategory | null = null;
    if (parentId) {
      parent = await this.categoryRepository.findOne({
        where: { id: parentId },
      });
      if (!parent) {
        throw new NotFoundException('父分类不存在');
      }
    }

    const category = this.categoryTreeRepository.create({
      ...categoryData,
      parent,
      level: parent ? parent.level + 1 : 0,
      path: parent ? `${parent.path}/${categoryData.slug}` : categoryData.slug,
    });

    const savedCategory = await this.categoryTreeRepository.save(category);
    
    // 清除缓存
    await this.clearCache();
    
    this.logger.log(`分类创建成功: ${savedCategory.id}`, 'CategoriesService');
    return savedCategory;
  }

  /**
   * 获取所有分类（树形结构）
   */
  async findAll(): Promise<TemplateCategory[]> {
    const cacheKey = 'categories:tree';
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const categories = await this.categoryTreeRepository.findTrees();
    
    // 缓存结果
    await this.cacheService.set(cacheKey, categories, 1800); // 30分钟缓存
    
    return categories;
  }

  /**
   * 获取扁平化分类列表
   */
  async findFlat(): Promise<TemplateCategory[]> {
    const cacheKey = 'categories:flat';
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const categories = await this.categoryRepository.find({
      where: { isActive: true },
      order: { level: 'ASC', sortOrder: 'ASC', name: 'ASC' },
    });

    // 缓存结果
    await this.cacheService.set(cacheKey, categories, 1800); // 30分钟缓存
    
    return categories;
  }

  /**
   * 根据ID获取分类
   */
  async findOne(id: string): Promise<TemplateCategory> {
    const cacheKey = `category:${id}`;
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const category = await this.categoryRepository.findOne({
      where: { id },
      relations: ['parent', 'children'],
    });

    if (!category) {
      throw new NotFoundException('分类不存在');
    }

    // 缓存结果
    await this.cacheService.set(cacheKey, category, 600); // 10分钟缓存
    
    return category;
  }

  /**
   * 根据slug获取分类
   */
  async findBySlug(slug: string): Promise<TemplateCategory> {
    const cacheKey = `category:slug:${slug}`;
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const category = await this.categoryRepository.findOne({
      where: { slug, isActive: true },
      relations: ['parent', 'children'],
    });

    if (!category) {
      throw new NotFoundException('分类不存在');
    }

    // 缓存结果
    await this.cacheService.set(cacheKey, category, 600); // 10分钟缓存
    
    return category;
  }

  /**
   * 获取根分类
   */
  async findRoots(): Promise<TemplateCategory[]> {
    const cacheKey = 'categories:roots';
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const roots = await this.categoryTreeRepository.findRoots();
    
    // 缓存结果
    await this.cacheService.set(cacheKey, roots, 1800); // 30分钟缓存
    
    return roots;
  }

  /**
   * 获取分类的子分类
   */
  async findChildren(id: string): Promise<TemplateCategory[]> {
    const category = await this.findOne(id);
    return await this.categoryTreeRepository.findDescendants(category);
  }

  /**
   * 获取分类的祖先分类
   */
  async findAncestors(id: string): Promise<TemplateCategory[]> {
    const category = await this.findOne(id);
    return await this.categoryTreeRepository.findAncestors(category);
  }

  /**
   * 更新分类
   */
  async update(id: string, updateCategoryDto: UpdateCategoryDto): Promise<TemplateCategory> {
    const category = await this.findOne(id);
    const { parentId, ...updateData } = updateCategoryDto;

    // 检查slug是否重复（排除自己）
    if (updateData.slug && updateData.slug !== category.slug) {
      const existingCategory = await this.categoryRepository.findOne({
        where: { slug: updateData.slug },
      });

      if (existingCategory && existingCategory.id !== id) {
        throw new BadRequestException('分类标识已存在');
      }
    }

    // 处理父分类变更
    if (parentId !== undefined) {
      if (parentId === null) {
        category.parent = null;
        category.level = 0;
      } else {
        const parent = await this.categoryRepository.findOne({
          where: { id: parentId },
        });
        if (!parent) {
          throw new NotFoundException('父分类不存在');
        }
        
        // 检查是否会形成循环引用
        const ancestors = await this.categoryTreeRepository.findAncestors(parent);
        if (ancestors.some(ancestor => ancestor.id === id)) {
          throw new BadRequestException('不能将分类移动到其子分类下');
        }
        
        category.parent = parent;
        category.level = parent.level + 1;
      }
    }

    // 更新其他字段
    Object.assign(category, updateData);

    // 更新路径
    if (category.parent) {
      category.path = `${category.parent.path}/${category.slug}`;
    } else {
      category.path = category.slug;
    }

    const updatedCategory = await this.categoryTreeRepository.save(category);
    
    // 清除缓存
    await this.clearCache();
    
    this.logger.log(`分类更新成功: ${updatedCategory.id}`, 'CategoriesService');
    return updatedCategory;
  }

  /**
   * 删除分类
   */
  async remove(id: string): Promise<void> {
    const category = await this.findOne(id);

    // 检查是否有子分类
    const children = await this.categoryTreeRepository.findDescendants(category);
    if (children.length > 1) { // 包含自己，所以大于1表示有子分类
      throw new BadRequestException('请先删除子分类');
    }

    // 检查是否有关联的模板
    if (category.templateCount > 0) {
      throw new BadRequestException('该分类下还有模板，无法删除');
    }

    await this.categoryTreeRepository.remove(category);
    
    // 清除缓存
    await this.clearCache();
    
    this.logger.log(`分类删除成功: ${id}`, 'CategoriesService');
  }

  /**
   * 更新模板数量
   */
  async updateTemplateCount(id: string, increment: number = 1): Promise<void> {
    await this.categoryRepository.increment({ id }, 'templateCount', increment);
    await this.cacheService.del(`category:${id}`);
  }

  /**
   * 清除缓存
   */
  private async clearCache(): Promise<void> {
    const patterns = [
      'categories:tree',
      'categories:flat',
      'categories:roots',
      'category:*',
    ];

    for (const pattern of patterns) {
      await this.cacheService.del(pattern);
    }
  }

  /**
   * 批量更新排序
   */
  async updateOrder(orders: { id: string; sortOrder: number }[]): Promise<void> {
    for (const order of orders) {
      await this.categoryRepository.update(
        { id: order.id },
        { sortOrder: order.sortOrder },
      );
    }

    // 清除缓存
    await this.clearCache();
    
    this.logger.log('分类排序更新成功', 'CategoriesService');
  }

  /**
   * 启用/禁用分类
   */
  async toggleActive(id: string): Promise<TemplateCategory> {
    const category = await this.findOne(id);
    category.isActive = !category.isActive;
    
    const updatedCategory = await this.categoryTreeRepository.save(category);
    
    // 清除缓存
    await this.clearCache();
    
    this.logger.log(`分类状态切换成功: ${id}`, 'CategoriesService');
    return updatedCategory;
  }
}
