#!/usr/bin/env node
/**
 * 检查后端服务状态脚本
 * 简单检查各个微服务是否正在运行
 */

const http = require('http');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'cyan');
}

function logHeader(message) {
  log(`\n🚀 ${message}`, 'magenta');
  log('='.repeat(60), 'magenta');
}

// 简单的HTTP请求函数
function checkService(host, port, path = '/') {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: host,
      port: port,
      path: path,
      method: 'GET',
      timeout: 3000
    }, (res) => {
      resolve({
        status: res.statusCode,
        success: res.statusCode < 500
      });
    });
    
    req.on('error', () => {
      resolve({ success: false, error: 'Connection failed' });
    });
    
    req.on('timeout', () => {
      req.destroy();
      resolve({ success: false, error: 'Timeout' });
    });
    
    req.end();
  });
}

// 服务配置
const services = [
  { name: 'API网关', host: 'localhost', port: 3000, path: '/api/health' },
  { name: '用户服务', host: 'localhost', port: 3001, path: '/api/v1/health' },
  { name: '项目服务', host: 'localhost', port: 3002, path: '/api/v1/health' },
  { name: '游戏服务器', host: 'localhost', port: 3003, path: '/api/v1/health' },
  { name: '资产服务', host: 'localhost', port: 3004, path: '/api/v1/health' },
  { name: '渲染服务', host: 'localhost', port: 3005, path: '/api/v1/health' },
  { name: '知识库服务', host: 'localhost', port: 3006, path: '/api/v1/health' },
  { name: 'AI服务', host: 'localhost', port: 3007, path: '/api/v1/health' },
  { name: '资产库服务', host: 'localhost', port: 3008, path: '/api/v1/health' },
  { name: '绑定服务', host: 'localhost', port: 3009, path: '/api/v1/health' },
  { name: '场景生成服务', host: 'localhost', port: 3010, path: '/api/v1/health' },
  { name: '场景模板服务', host: 'localhost', port: 3011, path: '/api/v1/health' },
  { name: '监控服务', host: 'localhost', port: 3012, path: '/api/v1/health' }
];

// 数据库服务
const databases = [
  { name: 'MySQL数据库', host: 'localhost', port: 3306 },
  { name: 'Redis缓存', host: 'localhost', port: 6379 }
];

// 检查所有服务
async function checkAllServices() {
  logHeader('检查后端微服务状态');
  
  let runningServices = 0;
  let totalServices = services.length;
  
  for (const service of services) {
    logInfo(`检查 ${service.name} (${service.host}:${service.port})...`);
    
    const result = await checkService(service.host, service.port, service.path);
    
    if (result.success) {
      logSuccess(`${service.name} - 运行正常 (状态码: ${result.status})`);
      runningServices++;
    } else {
      logError(`${service.name} - 服务不可用 (${result.error || '未知错误'})`);
    }
  }
  
  logHeader('检查数据库服务状态');
  
  for (const db of databases) {
    logInfo(`检查 ${db.name} (${db.host}:${db.port})...`);
    
    const result = await checkService(db.host, db.port);
    
    if (result.success) {
      logSuccess(`${db.name} - 连接正常`);
    } else {
      logError(`${db.name} - 连接失败`);
    }
  }
  
  logHeader('服务状态总结');
  
  log(`微服务状态: ${runningServices}/${totalServices} 正在运行`, 'cyan');
  
  if (runningServices === 0) {
    logError('没有微服务在运行！');
    logWarning('请启动后端服务：');
    logInfo('Windows: .\\start-windows.ps1');
    logInfo('或者使用 Docker: docker-compose -f docker-compose.windows.yml up -d');
  } else if (runningServices < totalServices) {
    logWarning(`部分微服务未运行 (${totalServices - runningServices} 个服务离线)`);
    logInfo('建议检查服务启动状态或日志');
  } else {
    logSuccess('🎉 所有微服务都在正常运行！');
    logInfo('现在可以进行前后端联调测试');
  }
  
  return runningServices;
}

// 检查前端开发服务器
async function checkFrontendServer() {
  logHeader('检查前端开发服务器');
  
  const result = await checkService('localhost', 3000, '/');
  
  if (result.success) {
    logSuccess('前端开发服务器正在运行 (http://localhost:3000)');
  } else {
    logWarning('前端开发服务器未运行');
    logInfo('启动前端服务器: cd editor && npm start');
  }
}

// 提供启动建议
function provideStartupAdvice() {
  logHeader('启动建议');
  
  logInfo('1. 启动后端服务:');
  logInfo('   .\\start-windows.ps1');
  logInfo('');
  logInfo('2. 等待所有服务启动完成 (约2-3分钟)');
  logInfo('');
  logInfo('3. 检查服务状态:');
  logInfo('   node check-backend-services.js');
  logInfo('');
  logInfo('4. 启动前端开发服务器:');
  logInfo('   cd editor && npm start');
  logInfo('');
  logInfo('5. 进行前后端联调测试:');
  logInfo('   node frontend-backend-integration-test.js');
}

// 主函数
async function main() {
  logHeader('后端服务状态检查工具');
  
  const runningServices = await checkAllServices();
  await checkFrontendServer();
  
  if (runningServices === 0) {
    provideStartupAdvice();
  }
  
  logHeader('检查完成');
}

// 运行检查
if (require.main === module) {
  main().catch(error => {
    logError(`检查失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  checkAllServices,
  checkService,
  checkFrontendServer
};
