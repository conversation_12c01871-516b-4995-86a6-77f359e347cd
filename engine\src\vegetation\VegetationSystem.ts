/**
 * 植被系统
 * 负责植被的生成、更新和渲染
 */
import * as THREE from 'three';
import { System } from '../core/System';
import type { Entity } from '../core/Entity';
import type { Camera } from '../rendering/Camera';
import { Scene } from '../scene/Scene';
import { VegetationComponent, VegetationInstance, VegetationItemConfig } from './components/VegetationComponent';
import { TerrainComponent } from '../terrain/components/TerrainComponent';
import { TerrainInstancedRenderingSystem } from '../terrain/TerrainInstancedRenderingSystem';
import { type Transform  } from '../scene/Transform';
import { PhysicalWindSystem } from './wind/PhysicalWindSystem';
import { WindFieldSystem } from './wind/WindFieldSystem';
import { Debug } from '../utils/Debug';
import { EventEmitter } from '../utils/EventEmitter';
import { Octree } from '../utils/Octree';
import { ModelLoader } from '../loaders/ModelLoader';

/**
 * 植被系统事件类型
 */
export enum VegetationSystemEventType {
  VEGETATION_GENERATED = 'vegetation_generated',
  VEGETATION_UPDATED = 'vegetation_updated',
  VEGETATION_CLEARED = 'vegetation_cleared',
  INSTANCE_ADDED = 'instance_added',
  INSTANCE_REMOVED = 'instance_removed',
  INSTANCE_UPDATED = 'instance_updated'
}

/**
 * 植被系统选项接口
 */
export interface VegetationSystemOptions {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 是否使用实例化渲染 */
  useInstancing?: boolean;
  /** 是否使用LOD */
  useLOD?: boolean;
  /** 是否使用视锥体剔除 */
  useFrustumCulling?: boolean;
  /** 是否使用八叉树 */
  useOctree?: boolean;
  /** 是否使用GPU实例化 */
  useGPUInstancing?: boolean;
  /** 是否使用阴影 */
  useShadow?: boolean;
  /** 是否使用风效果 */
  useWind?: boolean;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
}

/**
 * 植被系统类
 */
export class VegetationSystem extends System {
  /** 系统类型 */
  public static readonly TYPE: string = 'VegetationSystem';

  /** 是否启用植被系统 */
  private vegetationEnabled: boolean;

  /** 是否自动更新 */
  private autoUpdate: boolean;

  /** 更新频率 */
  private updateFrequency: number;

  /** 帧计数器 */
  private frameCount: number;

  /** 是否使用实例化渲染 */
  private useInstancing: boolean;

  /** 是否使用LOD */
  private useLOD: boolean;

  /** 是否使用视锥体剔除 */
  private useFrustumCulling: boolean;

  /** 是否使用八叉树 */
  private useOctree: boolean;

  /** 是否使用GPU实例化 */
  private useGPUInstancing: boolean;

  /** 是否使用阴影 */
  private useShadow: boolean;

  /** 是否使用风效果 */
  private useWind: boolean;

  /** 是否使用调试可视化 */
  private useDebugVisualization: boolean;

  /** 是否使用季节效果 */
  private useSeasonal: boolean;

  /** 植被组件映射 */
  private vegetationEntities: Map<Entity, VegetationComponent>;

  /** 地形组件映射 */
  private terrainEntities: Map<string, { entity: Entity, component: TerrainComponent }>;

  /** 模型缓存 */
  private modelCache: Map<string, THREE.Object3D>;

  /** 实例化渲染系统 */
  private instancedRenderingSystem: TerrainInstancedRenderingSystem | null;

  /** 物理风效果系统 */
  private physicalWindSystem: PhysicalWindSystem | null;

  /** 风场系统 */
  private windFieldSystem: WindFieldSystem | null;

  /** 八叉树 */
  private octree: Octree | null;

  /** 视锥体 */
  private frustum: THREE.Frustum;

  /** 模型加载器 */
  private modelLoader: ModelLoader;

  /** 事件发射器 */
  private eventEmitter: EventEmitter;

  /** 调试网格列表 */
  private debugMeshes: THREE.Mesh[];

  /** 临时矩阵 */
  private tempMatrix: THREE.Matrix4;

  /**
   * 创建植被系统
   * @param options 选项
   */
  constructor(options: VegetationSystemOptions = {}) {
    super();

    // 初始化属性
    this.vegetationEnabled = options.enabled !== undefined ? options.enabled : true;
    this.autoUpdate = options.autoUpdate !== undefined ? options.autoUpdate : true;
    this.updateFrequency = options.updateFrequency || 1;
    this.frameCount = 0;
    this.useInstancing = options.useInstancing !== undefined ? options.useInstancing : true;
    this.useLOD = options.useLOD !== undefined ? options.useLOD : true;
    this.useFrustumCulling = options.useFrustumCulling !== undefined ? options.useFrustumCulling : true;
    this.useOctree = options.useOctree !== undefined ? options.useOctree : true;
    this.useGPUInstancing = options.useGPUInstancing !== undefined ? options.useGPUInstancing : true;
    this.useShadow = options.useShadow !== undefined ? options.useShadow : true;
    this.useWind = options.useWind !== undefined ? options.useWind : false;
    this.useDebugVisualization = options.useDebugVisualization !== undefined ? options.useDebugVisualization : false;
    this.useSeasonal = false; // 默认不启用季节效果

    // 初始化映射
    this.vegetationEntities = new Map();
    this.terrainEntities = new Map();
    this.modelCache = new Map();

    // 初始化系统
    this.instancedRenderingSystem = null;
    this.physicalWindSystem = null;
    this.windFieldSystem = null;
    this.octree = this.useOctree ? new Octree({
      size: 10000,
      maxDepth: 8,
      maxObjects: 100
    }) : null;
    this.frustum = new THREE.Frustum();
    this.modelLoader = new ModelLoader();
    this.eventEmitter = new EventEmitter();
    this.debugMeshes = [];
    this.tempMatrix = new THREE.Matrix4();
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    super.initialize();

    // 查找实例化渲染系统
    if (this.useInstancing) {
      this.instancedRenderingSystem = this.world.getSystem(TerrainInstancedRenderingSystem) as TerrainInstancedRenderingSystem;
      if (!this.instancedRenderingSystem) {
        Debug.warn('VegetationSystem', '找不到TerrainInstancedRenderingSystem，将创建新的实例');
        this.instancedRenderingSystem = new TerrainInstancedRenderingSystem({
          enabled: true,
          autoUpdate: true,
          updateFrequency: this.updateFrequency,
          useFrustumCulling: this.useFrustumCulling,
          useOctree: this.useOctree,
          useInstanceLOD: this.useLOD,
          useInstanceShadow: this.useShadow,
          useDebugVisualization: this.useDebugVisualization
        });
        this.world.addSystem(this.instancedRenderingSystem);
      }
    }

    // 查找物理风效果系统
    if (this.useWind) {
      this.physicalWindSystem = this.world.getSystem(PhysicalWindSystem) as PhysicalWindSystem;
      if (!this.physicalWindSystem) {
        Debug.warn('VegetationSystem', '找不到PhysicalWindSystem，将创建新的实例');
        this.physicalWindSystem = new PhysicalWindSystem({
          enabled: true,
          autoUpdate: true,
          updateFrequency: this.updateFrequency,
          usePhysics: true,
          useGPU: this.useGPUInstancing,
          useWindZones: true,
          useDebugVisualization: this.useDebugVisualization
        });
        this.world.addSystem(this.physicalWindSystem);
      }

      // 查找风场系统
      this.windFieldSystem = this.world.getSystem(WindFieldSystem) as WindFieldSystem;
      if (!this.windFieldSystem) {
        Debug.warn('VegetationSystem', '找不到WindFieldSystem，将创建新的实例');
        this.windFieldSystem = new WindFieldSystem({
          enabled: true,
          autoUpdate: true,
          updateFrequency: this.updateFrequency,
          useDebugVisualization: this.useDebugVisualization,
          useWindFieldGrid: false,
          useWindFieldParticles: false
        });
        this.world.addSystem(this.windFieldSystem);
      }
    }

    // 监听实体添加和移除事件
    this.world.on('entityAdded', this.onEntityAdded.bind(this));
    this.world.on('entityRemoved', this.onEntityRemoved.bind(this));

    // 初始化现有实体
    this.world.getEntities().forEach(entity => {
      if (entity.hasComponent(VegetationComponent.TYPE)) {
        this.addVegetationEntity(entity, entity.getComponent(VegetationComponent.TYPE) as VegetationComponent);
      }
      if (entity.hasComponent(TerrainComponent.TYPE)) {
        const terrainComponent = entity.getComponent(TerrainComponent.TYPE) as TerrainComponent;
        this.terrainEntities.set(entity.id, { entity, component: terrainComponent });
      }
    });
  }

  /**
   * 实体添加事件处理
   * @param entity 实体
   */
  private onEntityAdded(entity: Entity): void {
    if (entity.hasComponent(VegetationComponent.TYPE)) {
      this.addVegetationEntity(entity, entity.getComponent(VegetationComponent.TYPE) as VegetationComponent);
    }
    if (entity.hasComponent(TerrainComponent.TYPE)) {
      const terrainComponent = entity.getComponent(TerrainComponent.TYPE) as TerrainComponent;
      this.terrainEntities.set(entity.id, { entity, component: terrainComponent });
    }
  }

  /**
   * 实体移除事件处理
   * @param entity 实体
   */
  private onEntityRemoved(entity: Entity): void {
    if (entity.hasComponent(VegetationComponent.TYPE)) {
      this.removeVegetationEntity(entity);
    }
    if (entity.hasComponent(TerrainComponent.TYPE)) {
      this.terrainEntities.delete(entity.id);
    }
  }

  /**
   * 添加植被实体
   * @param entity 实体
   * @param component 植被组件
   */
  public addVegetationEntity(entity: Entity, component: VegetationComponent): void {
    this.vegetationEntities.set(entity, component);

    // 如果使用风效果，将植被组件添加到物理风效果系统
    if (this.useWind && this.physicalWindSystem && component.useWind) {
      this.physicalWindSystem.addVegetationComponent(entity, component);
    }

    // 如果自动生成，则生成植被
    if (component.autoGenerate && !component.initialized) {
      this.generateVegetation(entity, component);
    }
  }

  /**
   * 移除植被实体
   * @param entity 实体
   */
  public removeVegetationEntity(entity: Entity): void {
    const component = this.vegetationEntities.get(entity);
    if (component) {
      // 清除植被实例
      this.clearVegetation(entity, component);
      this.vegetationEntities.delete(entity);
    }
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(_deltaTime: number): void {
    if (!this.vegetationEnabled || !this.autoUpdate) {
      return;
    }

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.updateFrequency !== 0) {
      return;
    }

    // 获取相机
    const camera = this.getCamera();
    if (!camera) {
      return;
    }

    // 更新视锥体
    if (this.useFrustumCulling) {
      this.updateFrustum(camera);
    }

    // 更新所有植被
    this.vegetationEntities.forEach((component, entity) => {
      if (!component.initialized && component.autoGenerate) {
        this.generateVegetation(entity, component);
      }
      if (component.needsUpdate) {
        this.updateVegetation(entity, component, camera);
      }
    });

    // 更新调试可视化
    if (this.useDebugVisualization) {
      this.updateDebugVisualization();
    }
  }

  /**
   * 获取相机
   * @returns 相机
   */
  private getCamera(): Camera | null {
    // 遍历所有实体查找相机组件
    const entities = this.world.getEntities();
    for (const [, entity] of entities) {
      if (entity.hasComponent('Camera')) {
        return entity.getComponent('Camera') as any as Camera;
      }
    }
    return null;
  }

  /**
   * 更新视锥体
   * @param camera 相机
   */
  private updateFrustum(camera: Camera): void {
    const threeCamera = camera.getThreeCamera();
    this.tempMatrix.multiplyMatrices(threeCamera.projectionMatrix, threeCamera.matrixWorldInverse);
    this.frustum.setFromProjectionMatrix(this.tempMatrix);
  }

  /**
   * 生成植被
   * @param entity 实体
   * @param component 植被组件
   */
  public generateVegetation(entity: Entity, component: VegetationComponent): void {
    // 获取地形实体
    const terrainData = this.terrainEntities.get(component.terrainEntity);
    if (!terrainData) {
      Debug.warn('VegetationSystem', `找不到地形实体: ${component.terrainEntity}`);
      return;
    }

    const terrainComponent = terrainData.component;

    // 清除现有植被
    this.clearVegetation(entity, component);

    // 创建随机数生成器
    const random = this.createRandomGenerator(component.seed);

    // 为每个植被项生成实例
    for (let itemIndex = 0; itemIndex < component.items.length; itemIndex++) {
      const item = component.items[itemIndex];
      this.generateVegetationItem(entity, component, terrainComponent, itemIndex, item, random);
    }

    // 标记为已初始化
    component.initialized = true;
    component.needsUpdate = false;

    // 发出植被生成事件
    this.eventEmitter.emit(VegetationSystemEventType.VEGETATION_GENERATED, entity, component);
  }

  /**
   * 生成植被项
   * @param entity 实体
   * @param component 植被组件
   * @param terrainComponent 地形组件
   * @param itemIndex 项索引
   * @param item 植被项
   * @param random 随机数生成器
   */
  private generateVegetationItem(
    entity: Entity,
    component: VegetationComponent,
    terrainComponent: TerrainComponent,
    itemIndex: number,
    item: VegetationItemConfig,
    random: () => number
  ): void {
    // 加载模型
    this.loadModel(item.model).then(model => {
      if (!model) {
        Debug.warn('VegetationSystem', `无法加载模型: ${item.model}`);
        return;
      }

      // 计算实例数量
      const terrainWidth = terrainComponent.width;
      const terrainHeight = terrainComponent.height;
      const terrainArea = terrainWidth * terrainHeight;
      const instanceCount = Math.floor(terrainArea * item.density);

      // 生成实例
      for (let i = 0; i < instanceCount; i++) {
        // 生成随机位置
        const x = (random() * terrainWidth) - (terrainWidth / 2);
        const z = (random() * terrainHeight) - (terrainHeight / 2);

        // 获取地形高度和法线
        const height = terrainComponent.getHeight(x, z);
        const normal = this.getTerrainNormal(terrainComponent, x, z);
        const slope = this.calculateSlope(normal);

        // 检查高度和坡度是否在范围内
        if (height < item.minHeight || height > item.maxHeight ||
            slope < item.slopeMin || slope > item.slopeMax) {
          continue;
        }

        // 检查分布图
        if (component.useDistributionMap && component.distributionMap) {
          const distributionValue = this.sampleTexture(
            component.distributionMap,
            (x + terrainWidth / 2) / terrainWidth,
            (z + terrainHeight / 2) / terrainHeight
          );
          if (random() > distributionValue) {
            continue;
          }
        }

        // 检查密度图
        if (component.useDensityMap && component.densityMap) {
          const densityValue = this.sampleTexture(
            component.densityMap,
            (x + terrainWidth / 2) / terrainWidth,
            (z + terrainHeight / 2) / terrainHeight
          );
          if (random() > densityValue) {
            continue;
          }
        }

        // 生成随机缩放
        const scale = item.minScale + random() * (item.maxScale - item.minScale);

        // 生成随机旋转
        const rotation = new THREE.Euler(
          0,
          item.randomRotation ? random() * Math.PI * 2 : 0,
          0
        );

        // 生成随机偏移
        const offset = item.randomOffset || 0;
        const offsetX = offset > 0 ? (random() * 2 - 1) * offset : 0;
        const offsetZ = offset > 0 ? (random() * 2 - 1) * offset : 0;

        // 计算最终位置
        const position = new THREE.Vector3(
          x + offsetX,
          height,
          z + offsetZ
        );

        // 生成颜色变化
        let color = new THREE.Color(0xffffff);
        if (item.colorVariation && item.colorVariation.enabled) {
          const hue = random() * item.colorVariation.hue - item.colorVariation.hue / 2;
          const saturation = random() * item.colorVariation.saturation - item.colorVariation.saturation / 2;
          const lightness = random() * item.colorVariation.lightness - item.colorVariation.lightness / 2;

          color.setHSL(
            0.5 + hue,
            0.5 + saturation,
            0.5 + lightness
          );
        }

        // 创建实例
        this.createVegetationInstance(entity, component, itemIndex, model, position, rotation, new THREE.Vector3(scale, scale, scale), color);
      }
    });
  }

  /**
   * 创建植被实例
   * @param entity 实体
   * @param component 植被组件
   * @param itemIndex 项索引
   * @param model 模型
   * @param position 位置
   * @param rotation 旋转
   * @param scale 缩放
   * @param color 颜色
   * @returns 实例ID
   */
  private createVegetationInstance(
    entity: Entity,
    component: VegetationComponent,
    itemIndex: number,
    model: THREE.Object3D,
    position: THREE.Vector3,
    rotation: THREE.Euler,
    scale: THREE.Vector3,
    color: THREE.Color
  ): string {
    // 生成实例ID
    const instanceId = `vegetation_${entity.id}_${itemIndex}_${component.instances.size}`;

    // 创建实例数据
    const instance: VegetationInstance = {
      id: instanceId,
      itemIndex,
      position: position.clone(),
      rotation: rotation.clone(),
      scale: scale.clone(),
      color: color.clone(),
      visible: true,
      lodLevel: 0,
      userData: {}
    };

    // 添加到实例映射
    component.instances.set(instanceId, instance);

    // 如果使用实例化渲染，添加到实例化渲染系统
    if (this.useInstancing && this.instancedRenderingSystem) {
      // 获取模型的几何体和材质
      const mesh = this.findFirstMesh(model);
      if (mesh) {
        // 确保材质是单个材质而不是数组
        const material = Array.isArray(mesh.material) ? mesh.material[0] : mesh.material;
        this.instancedRenderingSystem.addInstance(
          mesh.geometry,
          material,
          {
            position: instance.position,
            rotation: instance.rotation,
            scale: instance.scale,
            color: instance.color,
            visible: instance.visible,
            userData: { instanceId, entity: entity.id, itemIndex }
          }
        );
      }
    } else {
      // 否则，创建单独的模型实例
      const modelInstance = model.clone();
      modelInstance.position.copy(position);
      modelInstance.rotation.copy(rotation);
      modelInstance.scale.copy(scale);

      // 设置颜色
      this.setModelColor(modelInstance, color);

      // 添加到实体的变换组件
      const transform = entity.getComponent('Transform') as any as Transform;
      if (transform) {
        transform.getObject3D().add(modelInstance);
      }
      instance.userData.model = modelInstance;
    }

    // 如果使用八叉树，添加到八叉树
    if (this.useOctree && this.octree) {
      this.octree.insert(instanceId, position, Math.max(scale.x, scale.y, scale.z));
    }

    // 发出实例添加事件
    this.eventEmitter.emit(VegetationSystemEventType.INSTANCE_ADDED, entity, component, instanceId, instance);

    return instanceId;
  }

  /**
   * 更新植被
   * @param entity 实体
   * @param component 植被组件
   * @param camera 相机
   */
  private updateVegetation(entity: Entity, component: VegetationComponent, camera: Camera): void {
    // 如果使用LOD，更新LOD级别
    if (this.useLOD) {
      this.updateLOD(entity, component, camera);
    }

    // 如果使用风效果，更新风效果
    if (this.useWind && component.useWind) {
      this.updateWindEffect(entity, component);
    }

    // 如果使用季节效果，更新季节效果
    if (this.useSeasonal && component.useSeasonal) {
      this.updateSeasonalEffect(entity, component);
    }

    // 标记为已更新
    component.needsUpdate = false;

    // 发出植被更新事件
    this.eventEmitter.emit(VegetationSystemEventType.VEGETATION_UPDATED, entity, component);
  }

  /**
   * 清除植被
   * @param entity 实体
   * @param component 植被组件
   */
  public clearVegetation(entity: Entity, component: VegetationComponent): void {
    // 移除所有实例
    for (const [instanceId, instance] of component.instances.entries()) {
      // 如果不使用实例化渲染，移除模型
      if (!this.useInstancing && instance.userData.model) {
        const transform = entity.getComponent('Transform') as any as Transform;
        if (transform) {
          transform.getObject3D().remove(instance.userData.model);
        }
      }

      // 如果使用八叉树，从八叉树中移除
      if (this.useOctree && this.octree) {
        this.octree.remove(instanceId);
      }

      // 发出实例移除事件
      this.eventEmitter.emit(VegetationSystemEventType.INSTANCE_REMOVED, entity, component, instanceId, instance);
    }

    // 如果使用物理风效果系统，从物理风效果系统中移除植被组件
    if (this.useWind && this.physicalWindSystem && component.useWind) {
      this.physicalWindSystem.removeVegetationComponent(entity);
    }

    // 清空实例映射
    component.instances.clear();

    // 发出植被清除事件
    this.eventEmitter.emit(VegetationSystemEventType.VEGETATION_CLEARED, entity, component);
  }

  /**
   * 更新LOD级别
   * @param entity 实体
   * @param component 植被组件
   * @param camera 相机
   */
  private updateLOD(_entity: Entity, component: VegetationComponent, camera: Camera): void {
    // 获取相机位置
    const cameraPosition = camera.getThreeCamera().position;

    // 遍历所有实例
    for (const [, instance] of component.instances.entries()) {
      // 计算相机到实例的距离
      const distance = cameraPosition.distanceTo(instance.position);

      // 确定LOD级别
      let lodLevel = 0;
      for (let i = 0; i < component.lodDistances.length; i++) {
        if (distance > component.lodDistances[i]) {
          lodLevel = i + 1;
        }
      }

      // 如果LOD级别发生变化，更新实例
      if (instance.lodLevel !== lodLevel) {
        instance.lodLevel = lodLevel;

        // 如果使用实例化渲染，更新实例化渲染系统
        if (this.useInstancing && this.instancedRenderingSystem) {
          // 更新LOD级别
          // 这里需要实现LOD级别更新逻辑
        } else if (instance.userData.model) {
          // 否则，更新模型的LOD级别
          // 这里需要实现模型LOD级别更新逻辑
        }
      }
    }
  }

  /**
   * 更新风效果
   * @param entity 实体
   * @param component 植被组件
   */
  private updateWindEffect(entity: Entity, component: VegetationComponent): void {
    // 如果没有启用物理风效果系统，使用简单的风效果
    if (!this.physicalWindSystem) {
      this.updateSimpleWindEffect(entity, component);
      return;
    }

    // 将植被组件添加到物理风效果系统
    this.physicalWindSystem.addVegetationComponent(entity, component);

    // 如果有风场系统，创建或更新风区域
    if (this.windFieldSystem) {
      // 获取风力参数
      const { strength, direction, frequency, turbulence } = component.windParams;

      // 创建全局风力参数
      const globalWindParams = {
        fieldType: 'turbulent' as any, // 临时类型断言，避免类型错误
        strength,
        direction: new THREE.Vector3(direction.x, 0, direction.y),
        frequency,
        turbulence
      };

      // 设置全局风力参数
      this.physicalWindSystem.setGlobalWind(globalWindParams);
    }
  }

  /**
   * 更新简单的风效果（不使用物理风效果系统时）
   * @param entity 实体
   * @param component 植被组件
   */
  private updateSimpleWindEffect(_entity: Entity, component: VegetationComponent): void {
    // 获取当前时间
    const time = performance.now() * 0.001;

    // 获取风力参数
    const { strength, direction, frequency, turbulence } = component.windParams;

    // 遍历所有实例
    for (const [, instance] of component.instances.entries()) {
      // 获取植被项
      const item = component.items[instance.itemIndex];
      if (!item.windEffect) {
        continue;
      }

      // 计算风力偏移
      const windOffset = Math.sin(time * frequency + instance.position.x * 0.1 + instance.position.z * 0.1) * strength;
      const turbulenceOffset = (Math.sin(time * frequency * 2 + instance.position.x * 0.2) + Math.cos(time * frequency * 3 + instance.position.z * 0.2)) * 0.5 * turbulence;
      const totalOffset = windOffset + turbulenceOffset;

      // 如果使用实例化渲染，更新实例化渲染系统
      if (this.useInstancing && this.instancedRenderingSystem) {
        // 更新实例数据 - 注意：这里可能需要根据实际的updateInstance方法签名调整
        // const instanceData = {
        //   rotation: new THREE.Euler(
        //     instance.rotation.x + totalOffset * 0.1,
        //     instance.rotation.y,
        //     instance.rotation.z + totalOffset * direction.x * 0.1
        //   )
        // };
        // this.instancedRenderingSystem.updateInstance(instanceId, instanceData);
      } else if (instance.userData.model) {
        // 否则，更新模型的旋转
        const model = instance.userData.model as THREE.Object3D;
        model.rotation.x = instance.rotation.x + totalOffset * 0.1;
        model.rotation.z = instance.rotation.z + totalOffset * direction.x * 0.1;
      }
    }
  }

  /**
   * 更新季节效果
   * @param entity 实体
   * @param component 植被组件
   */
  private updateSeasonalEffect(_entity: Entity, component: VegetationComponent): void {
    // 获取季节参数
    const { season, intensity } = component.seasonalParams;

    // 遍历所有实例
    for (const [, instance] of component.instances.entries()) {
      // 获取植被项
      const item = component.items[instance.itemIndex];
      if (!item.seasonalEffect) {
        continue;
      }

      // 根据季节设置颜色
      let color = new THREE.Color();
      switch (season) {
        case 'spring':
          color.setHSL(0.3, 0.7, 0.5); // 鲜绿色
          break;
        case 'summer':
          color.setHSL(0.25, 0.6, 0.5); // 深绿色
          break;
        case 'autumn':
          color.setHSL(0.08, 0.8, 0.5); // 橙黄色
          break;
        case 'winter':
          color.setHSL(0.0, 0.0, 0.8); // 白色
          break;
      }

      // 混合原始颜色和季节颜色
      const originalColor = new THREE.Color(0xffffff);
      if (item.colorVariation && item.colorVariation.enabled) {
        // 使用原始颜色变化
        originalColor.copy(instance.color);
      }

      // 混合颜色
      color.lerp(originalColor, 1 - intensity);

      // 如果使用实例化渲染，更新实例化渲染系统
      if (this.useInstancing && this.instancedRenderingSystem) {
        // 更新实例颜色
        // 这里需要实现季节效果更新逻辑
      } else if (instance.userData.model) {
        // 否则，更新模型的颜色
        this.setModelColor(instance.userData.model as THREE.Object3D, color);
      }
    }
  }

  /**
   * 更新调试可视化
   */
  private updateDebugVisualization(): void {
    // 清除现有调试网格
    for (const mesh of this.debugMeshes) {
      const scene = this.getActiveScene();
      if (scene) {
        scene.getThreeScene().remove(mesh);
      }
    }
    this.debugMeshes = [];

    // 如果没有活跃场景，则返回
    const scene = this.getActiveScene();
    if (!scene) {
      return;
    }

    // 创建材质
    const visibleMaterial = new THREE.MeshBasicMaterial({ color: 0x00ff00, wireframe: true });
    const hiddenMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000, wireframe: true });

    // 遍历所有植被实体
    for (const [, component] of this.vegetationEntities.entries()) {
      // 遍历所有实例
      for (const [, instance] of component.instances.entries()) {
        // 创建包围球网格
        const radius = Math.max(instance.scale.x, instance.scale.y, instance.scale.z);
        const geometry = new THREE.SphereGeometry(radius, 8, 8);
        const material = instance.visible ? visibleMaterial : hiddenMaterial;
        const mesh = new THREE.Mesh(geometry, material);

        // 设置位置
        mesh.position.copy(instance.position);

        // 添加到场景
        scene.getThreeScene().add(mesh);
        this.debugMeshes.push(mesh);
      }
    }
  }

  /**
   * 获取活跃场景
   * @returns 场景
   */
  private getActiveScene(): Scene | null {
    // 遍历所有实体查找场景组件
    const entities = this.world.getEntities();
    for (const [, entity] of entities) {
      if (entity.hasComponent('Scene')) {
        return entity.getComponent('Scene') as any as unknown as Scene;
      }
    }
    return null;
  }

  /**
   * 加载模型
   * @param url 模型URL
   * @returns 模型
   */
  private async loadModel(url: string): Promise<THREE.Object3D | null> {
    // 检查缓存
    if (this.modelCache.has(url)) {
      return this.modelCache.get(url)!.clone();
    }

    try {
      // 加载模型
      const model = await this.modelLoader.load(url);
      if (model) {
        // 添加到缓存
        this.modelCache.set(url, model);
        return model.clone();
      }
    } catch (error) {
      Debug.error('VegetationSystem', `加载模型失败: ${url}`, error);
    }

    return null;
  }

  /**
   * 查找第一个网格
   * @param object 对象
   * @returns 网格
   */
  private findFirstMesh(object: THREE.Object3D): THREE.Mesh | null {
    if (object instanceof THREE.Mesh) {
      return object;
    }

    for (const child of object.children) {
      const mesh = this.findFirstMesh(child);
      if (mesh) {
        return mesh;
      }
    }

    return null;
  }

  /**
   * 设置模型颜色
   * @param model 模型
   * @param color 颜色
   */
  private setModelColor(model: THREE.Object3D, color: THREE.Color): void {
    model.traverse(object => {
      if (object instanceof THREE.Mesh) {
        const material = object.material as THREE.Material;
        if (material) {
          if (material instanceof THREE.MeshBasicMaterial ||
              material instanceof THREE.MeshLambertMaterial ||
              material instanceof THREE.MeshPhongMaterial ||
              material instanceof THREE.MeshStandardMaterial) {
            material.color.copy(color);
          }
        }
      }
    });
  }

  /**
   * 获取地形法线
   * @param terrainComponent 地形组件
   * @param x X坐标
   * @param z Z坐标
   * @returns 法线
   */
  private getTerrainNormal(terrainComponent: TerrainComponent, x: number, z: number): THREE.Vector3 {
    // 将世界坐标转换为地形坐标
    const terrainX = ((x + terrainComponent.width / 2) / terrainComponent.width) * (terrainComponent.resolution - 1);
    const terrainZ = ((z + terrainComponent.height / 2) / terrainComponent.height) * (terrainComponent.resolution - 1);

    // 获取整数坐标
    const x0 = Math.floor(terrainX);
    const z0 = Math.floor(terrainZ);

    // 确保坐标在有效范围内
    if (x0 < 0 || x0 >= terrainComponent.resolution - 1 || z0 < 0 || z0 >= terrainComponent.resolution - 1) {
      return new THREE.Vector3(0, 1, 0);
    }

    // 计算法线索引
    const index = (z0 * terrainComponent.resolution + x0) * 3;

    // 获取法线
    return new THREE.Vector3(
      terrainComponent.normalData[index],
      terrainComponent.normalData[index + 1],
      terrainComponent.normalData[index + 2]
    );
  }

  /**
   * 计算坡度
   * @param normal 法线
   * @returns 坡度（度）
   */
  private calculateSlope(normal: THREE.Vector3): number {
    // 计算法线与上方向的夹角
    const angle = Math.acos(normal.dot(new THREE.Vector3(0, 1, 0)));
    // 转换为度
    return angle * (180 / Math.PI);
  }

  /**
   * 采样纹理
   * @param texture 纹理
   * @param u U坐标
   * @param v V坐标
   * @returns 采样值
   */
  private sampleTexture(texture: THREE.Texture, u: number, v: number): number {
    // 如果纹理没有图像数据，返回1
    if (!texture.image || !texture.image.data) {
      return 1;
    }

    // 获取纹理尺寸
    const width = texture.image.width;
    const height = texture.image.height;

    // 计算像素坐标
    const x = Math.floor(u * width);
    const y = Math.floor(v * height);

    // 确保坐标在有效范围内
    const clampedX = Math.max(0, Math.min(width - 1, x));
    const clampedY = Math.max(0, Math.min(height - 1, y));

    // 计算像素索引
    const index = (clampedY * width + clampedX) * 4;

    // 获取像素值（假设是灰度图，使用红色通道）
    return texture.image.data[index] / 255;
  }

  /**
   * 创建随机数生成器
   * @param seed 种子
   * @returns 随机数生成器
   */
  private createRandomGenerator(seed: number): () => number {
    // 简单的伪随机数生成器
    let s = seed;
    return () => {
      s = (s * 9301 + 49297) % 233280;
      return s / 233280;
    };
  }
}
