import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  Index,
  Unique,
} from 'typeorm';
import { SceneTemplate } from '../../templates/entities/scene-template.entity';
import { User } from '../../auth/entities/user.entity';

export enum ShareType {
  PUBLIC = 'public',
  PRIVATE = 'private',
  TEAM = 'team',
  ORGANIZATION = 'organization',
}

export enum SharePermission {
  VIEW = 'view',
  DOWNLOAD = 'download',
  EDIT = 'edit',
  ADMIN = 'admin',
}

@Entity('template_shares')
@Unique(['template', 'sharedWith'])
@Index(['template', 'shareType'])
@Index(['sharedBy', 'createdAt'])
export class TemplateShare {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: ShareType,
    default: ShareType.PRIVATE,
  })
  @Index()
  shareType: ShareType;

  @Column({
    type: 'enum',
    enum: SharePermission,
    default: SharePermission.VIEW,
  })
  permission: SharePermission;

  @Column({ name: 'share_token', length: 64, nullable: true })
  @Index()
  shareToken: string; // 分享令牌，用于公开分享

  @Column({ name: 'expires_at', nullable: true })
  expiresAt: Date; // 分享过期时间

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'access_count', default: 0 })
  accessCount: number; // 访问次数

  @Column({ name: 'last_accessed_at', nullable: true })
  lastAccessedAt: Date;

  // 分享配置
  @Column({ name: 'allow_download', default: true })
  allowDownload: boolean;

  @Column({ name: 'allow_clone', default: true })
  allowClone: boolean;

  @Column({ name: 'require_login', default: false })
  requireLogin: boolean;

  @Column({ name: 'password_protected', default: false })
  passwordProtected: boolean;

  @Column({ name: 'share_password', nullable: true })
  sharePassword: string; // 分享密码（加密存储）

  // 关联关系
  @ManyToOne(() => SceneTemplate, template => template.shares, { onDelete: 'CASCADE' })
  template: SceneTemplate;

  @Column({ name: 'template_id' })
  @Index()
  templateId: string;

  @ManyToOne(() => User)
  sharedBy: User; // 分享者

  @Column({ name: 'shared_by_id' })
  @Index()
  sharedById: string;

  @ManyToOne(() => User, { nullable: true })
  sharedWith: User; // 被分享者（私有分享时使用）

  @Column({ name: 'shared_with_id', nullable: true })
  sharedWithId: string;

  // 元数据
  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  // 时间戳
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 计算属性
  get isExpired(): boolean {
    return this.expiresAt ? new Date() > this.expiresAt : false;
  }

  get isPublic(): boolean {
    return this.shareType === ShareType.PUBLIC;
  }

  get canAccess(): boolean {
    return this.isActive && !this.isExpired;
  }

  get shareUrl(): string {
    return this.shareToken ? `/shared/templates/${this.shareToken}` : '';
  }
}
