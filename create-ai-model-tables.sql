-- AI模型服务数据库表创建脚本
-- 使用dl_engine_ai数据库

USE dl_engine_ai;

-- 创建AI模型表
CREATE TABLE IF NOT EXISTS `ai_models` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `displayName` varchar(255) DEFAULT NULL,
  `description` text,
  `type` enum('text','image','audio','video','multimodal','embedding','classification','generation','translation','summarization','qa','chat','code','other') NOT NULL DEFAULT 'other',
  `category` enum('nlp','cv','speech','multimodal','ml','dl','other') NOT NULL DEFAULT 'other',
  `version` varchar(50) DEFAULT NULL,
  `framework` enum('tensorflow','pytorch','onnx','huggingface','openai','custom','other') NOT NULL DEFAULT 'other',
  `modelFormat` enum('bin','onnx','pb','pth','h5','tflite','safetensors','other') NOT NULL DEFAULT 'other',
  `filePath` varchar(500) DEFAULT NULL,
  `fileSize` bigint DEFAULT NULL,
  `fileHash` varchar(64) DEFAULT NULL,
  `configPath` varchar(500) DEFAULT NULL,
  `tokenizerPath` varchar(500) DEFAULT NULL,
  `isLoaded` tinyint(1) NOT NULL DEFAULT '0',
  `isActive` tinyint(1) NOT NULL DEFAULT '1',
  `isPublic` tinyint(1) NOT NULL DEFAULT '0',
  `isDefault` tinyint(1) NOT NULL DEFAULT '0',
  `supportedLanguages` json DEFAULT NULL,
  `inputFormats` json DEFAULT NULL,
  `outputFormats` json DEFAULT NULL,
  `parameters` json DEFAULT NULL,
  `metadata` json DEFAULT NULL,
  `requirements` json DEFAULT NULL,
  `performance` json DEFAULT NULL,
  `limitations` json DEFAULT NULL,
  `tags` json DEFAULT NULL,
  `author` varchar(255) DEFAULT NULL,
  `license` varchar(100) DEFAULT NULL,
  `homepage` varchar(500) DEFAULT NULL,
  `repository` varchar(500) DEFAULT NULL,
  `documentation` varchar(500) DEFAULT NULL,
  `minMemoryMB` int DEFAULT NULL,
  `recommendedMemoryMB` int DEFAULT NULL,
  `minCpuCores` int DEFAULT NULL,
  `recommendedCpuCores` int DEFAULT NULL,
  `requiresGPU` tinyint(1) NOT NULL DEFAULT '0',
  `minGpuMemoryMB` int DEFAULT NULL,
  `supportedGpuTypes` json DEFAULT NULL,
  `maxInputLength` int DEFAULT NULL,
  `maxOutputLength` int DEFAULT NULL,
  `maxBatchSize` int DEFAULT NULL,
  `averageInferenceTime` float DEFAULT NULL,
  `maxInferenceTime` float DEFAULT NULL,
  `usageCount` bigint NOT NULL DEFAULT '0',
  `errorCount` int NOT NULL DEFAULT '0',
  `averageResponseTime` float DEFAULT NULL,
  `createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_ai_models_name` (`name`),
  KEY `IDX_ai_models_type` (`type`),
  KEY `IDX_ai_models_category` (`category`),
  KEY `IDX_ai_models_isActive` (`isActive`),
  KEY `IDX_ai_models_isLoaded` (`isLoaded`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建模型版本表
CREATE TABLE IF NOT EXISTS `model_versions` (
  `id` varchar(36) NOT NULL,
  `model_id` varchar(36) NOT NULL,
  `version` varchar(50) NOT NULL,
  `description` text,
  `filePath` varchar(500) NOT NULL,
  `fileSize` bigint NOT NULL,
  `fileHash` varchar(64) DEFAULT NULL,
  `isCurrent` tinyint(1) NOT NULL DEFAULT '0',
  `isStable` tinyint(1) NOT NULL DEFAULT '0',
  `config` json DEFAULT NULL,
  `performanceMetrics` json DEFAULT NULL,
  `changelog` text,
  `releasedBy` varchar(100) DEFAULT NULL,
  `released_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_model_versions_model_version` (`model_id`,`version`),
  KEY `FK_model_versions_model_id` (`model_id`),
  CONSTRAINT `FK_model_versions_model_id` FOREIGN KEY (`model_id`) REFERENCES `ai_models` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建推理日志表
CREATE TABLE IF NOT EXISTS `inference_logs` (
  `id` varchar(36) NOT NULL,
  `model_id` varchar(36) NOT NULL,
  `requestId` varchar(100) DEFAULT NULL,
  `userId` varchar(100) DEFAULT NULL,
  `status` enum('pending','processing','completed','failed','timeout','cancelled') NOT NULL DEFAULT 'pending',
  `inputData` json DEFAULT NULL,
  `outputData` json DEFAULT NULL,
  `parameters` json DEFAULT NULL,
  `responseTime` int DEFAULT NULL,
  `startTime` timestamp NULL DEFAULT NULL,
  `endTime` timestamp NULL DEFAULT NULL,
  `errorMessage` text,
  `errorStack` text,
  `memoryUsage` float DEFAULT NULL,
  `cpuUsage` float DEFAULT NULL,
  `gpuUsage` float DEFAULT NULL,
  `batchSize` int DEFAULT NULL,
  `inputTokens` int DEFAULT NULL,
  `outputTokens` int DEFAULT NULL,
  `confidenceScore` float DEFAULT NULL,
  `clientIp` varchar(45) DEFAULT NULL,
  `userAgent` varchar(500) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  KEY `IDX_inference_logs_model_created` (`model_id`,`created_at`),
  KEY `IDX_inference_logs_status_created` (`status`,`created_at`),
  KEY `IDX_inference_logs_user_created` (`userId`,`created_at`),
  KEY `FK_inference_logs_model_id` (`model_id`),
  CONSTRAINT `FK_inference_logs_model_id` FOREIGN KEY (`model_id`) REFERENCES `ai_models` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建模型指标表
CREATE TABLE IF NOT EXISTS `model_metrics` (
  `id` varchar(36) NOT NULL,
  `model_id` varchar(36) NOT NULL,
  `metricType` enum('performance','usage','error','resource','quality','latency','throughput','accuracy','other') NOT NULL DEFAULT 'other',
  `metricName` varchar(100) NOT NULL,
  `metricValue` float NOT NULL,
  `metricUnit` varchar(50) DEFAULT NULL,
  `aggregationType` enum('sum','avg','min','max','count','rate','percentile','other') NOT NULL DEFAULT 'other',
  `timeWindow` enum('minute','hour','day','week','month','year','custom') NOT NULL DEFAULT 'hour',
  `tags` json DEFAULT NULL,
  `metadata` json DEFAULT NULL,
  `threshold` json DEFAULT NULL,
  `percentiles` json DEFAULT NULL,
  `metric_date` date NOT NULL,
  `metric_timestamp` timestamp NOT NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  KEY `IDX_model_metrics_model_date` (`model_id`,`metric_date`),
  KEY `IDX_model_metrics_type_date` (`metricType`,`metric_date`),
  KEY `IDX_model_metrics_name_date` (`metricName`,`metric_date`),
  KEY `FK_model_metrics_model_id` (`model_id`),
  CONSTRAINT `FK_model_metrics_model_id` FOREIGN KEY (`model_id`) REFERENCES `ai_models` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 显示创建的表
SHOW TABLES;
