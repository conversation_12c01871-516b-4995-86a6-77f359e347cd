/**
 * 可剔除组件
 * 用于标记可以被视锥体剔除的实体
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import type { Transform } from '../../scene/Transform';

/**
 * 可剔除组件选项接口
 */
export interface CullableComponentOptions {
  /** 包围半径 */
  boundingRadius?: number;
  /** 是否自动计算包围半径 */
  autoComputeBoundingRadius?: boolean;
  /** 是否使用包围盒 */
  useBoundingBox?: boolean;
  /** 是否自动计算包围盒 */
  autoComputeBoundingBox?: boolean;
  /** 是否可见 */
  visible?: boolean;
  /** 是否可剔除 */
  cullable?: boolean;
}

/**
 * 可剔除组件类
 */
export class CullableComponent extends Component {
  /** 组件类型 */
  public static readonly type: string = 'CullableComponent';

  /** 包围半径 */
  private boundingRadius: number;

  /** 是否自动计算包围半径 */
  private autoComputeBoundingRadius: boolean;

  /** 是否使用包围盒 */
  private useBoundingBox: boolean;

  /** 是否自动计算包围盒 */
  private autoComputeBoundingBox: boolean;

  /** 包围盒 */
  private boundingBox: THREE.Box3 | null = null;

  /** 是否可见 */
  private visible: boolean;

  /** 是否可剔除 */
  private cullable: boolean;

  /** 原始可见性 */
  private originalVisibility: Map<THREE.Object3D, boolean> = new Map();

  /**
   * 创建可剔除组件
   * @param options 可剔除组件选项
   */
  constructor(options: CullableComponentOptions = {}) {
    super(CullableComponent.type);

    this.boundingRadius = options.boundingRadius !== undefined ? options.boundingRadius : 1.0;
    this.autoComputeBoundingRadius = options.autoComputeBoundingRadius !== undefined ? options.autoComputeBoundingRadius : true;
    this.useBoundingBox = options.useBoundingBox !== undefined ? options.useBoundingBox : false;
    this.autoComputeBoundingBox = options.autoComputeBoundingBox !== undefined ? options.autoComputeBoundingBox : true;
    this.visible = options.visible !== undefined ? options.visible : true;
    this.cullable = options.cullable !== undefined ? options.cullable : true;

    // 如果使用包围盒，则创建包围盒
    if (this.useBoundingBox) {
      this.boundingBox = new THREE.Box3();
    }
  }

  /**
   * 当组件附加到实体时调用
   */
  protected onAttach(): void {
    if (!this.entity) return;

    // 获取实体的变换组件
    const transform = this.entity.getComponent('Transform') as any as any as any as any as Transform;
    if (!transform) return;

    // 保存原始可见性
    this.saveOriginalVisibility(transform.getObject3D());

    // 如果启用自动计算包围半径，则计算包围半径
    if (this.autoComputeBoundingRadius) {
      this.computeBoundingRadius();
    }

    // 如果使用包围盒并启用自动计算包围盒，则计算包围盒
    if (this.useBoundingBox && this.autoComputeBoundingBox) {
      this.computeBoundingBox();
    }

    // 设置可见性
    this.updateVisibility();
  }

  /**
   * 当组件从实体分离时调用
   */
  protected onDetach(): void {
    if (!this.entity) return;

    // 获取实体的变换组件
    const transform = this.entity.getComponent('Transform') as any as any as any as any as Transform;
    if (!transform) return;

    // 恢复原始可见性
    this.restoreOriginalVisibility(transform.getObject3D());

    // 清空原始可见性
    this.originalVisibility.clear();
  }

  /**
   * 保存原始可见性
   * @param object Three.js对象
   */
  private saveOriginalVisibility(object: THREE.Object3D): void {
    // 保存对象的原始可见性
    this.originalVisibility.set(object, object.visible);

    // 递归保存子对象的原始可见性
    for (const child of object.children) {
      this.saveOriginalVisibility(child);
    }
  }

  /**
   * 恢复原始可见性
   * @param object Three.js对象
   */
  private restoreOriginalVisibility(object: THREE.Object3D): void {
    // 恢复对象的原始可见性
    const originalVisible = this.originalVisibility.get(object);
    if (originalVisible !== undefined) {
      object.visible = originalVisible;
    }

    // 递归恢复子对象的原始可见性
    for (const child of object.children) {
      this.restoreOriginalVisibility(child);
    }
  }

  /**
   * 计算包围半径
   */
  public computeBoundingRadius(): void {
    if (!this.entity) return;

    // 获取实体的变换组件
    const transform = this.entity.getComponent('Transform') as any as any as any as any as Transform;
    if (!transform) return;

    // 计算包围半径
    let maxRadius = 0;
    const object = transform.getObject3D();

    // 遍历所有子对象
    object.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        // 如果网格没有包围球，则计算包围球
        if (!child.geometry.boundingSphere) {
          child.geometry.computeBoundingSphere();
        }

        // 获取包围球半径
        if (child.geometry.boundingSphere) {
          // 考虑缩放
          const scale = child.scale.length();
          const radius = child.geometry.boundingSphere.radius * scale;
          maxRadius = Math.max(maxRadius, radius);
        }
      }
    });

    // 设置包围半径
    this.boundingRadius = maxRadius > 0 ? maxRadius : 1.0;
  }

  /**
   * 计算包围盒
   */
  public computeBoundingBox(): void {
    if (!this.entity || !this.boundingBox) return;

    // 获取实体的变换组件
    const transform = this.entity.getComponent('Transform') as any as any as any as any as Transform;
    if (!transform) return;

    // 重置包围盒
    this.boundingBox.makeEmpty();

    // 计算包围盒
    const object = transform.getObject3D();
    const tempBox = new THREE.Box3();

    // 遍历所有子对象
    object.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        // 如果网格没有包围盒，则计算包围盒
        if (!child.geometry.boundingBox) {
          child.geometry.computeBoundingBox();
        }

        // 获取包围盒
        if (child.geometry.boundingBox) {
          // 考虑变换
          tempBox.copy(child.geometry.boundingBox);
          tempBox.applyMatrix4(child.matrixWorld);
          this.boundingBox!.union(tempBox);
        }
      }
    });

    // 如果包围盒为空，则创建一个默认包围盒
    if (this.boundingBox.isEmpty()) {
      this.boundingBox.set(
        new THREE.Vector3(-1, -1, -1),
        new THREE.Vector3(1, 1, 1)
      );
    }
  }

  /**
   * 更新可见性
   */
  private updateVisibility(): void {
    if (!this.entity) return;

    // 获取实体的变换组件
    const transform = this.entity.getComponent('Transform') as any as any as any as any as Transform;
    if (!transform) return;

    // 设置对象的可见性
    const object = transform.getObject3D();
    this.setObjectVisibility(object, this.visible);
  }

  /**
   * 设置对象的可见性
   * @param object Three.js对象
   * @param visible 是否可见
   */
  private setObjectVisibility(object: THREE.Object3D, visible: boolean): void {
    // 设置对象的可见性
    object.visible = visible;

    // 递归设置子对象的可见性
    for (const child of object.children) {
      this.setObjectVisibility(child, visible);
    }
  }

  /**
   * 设置是否可见
   * @param visible 是否可见
   */
  public setVisible(visible: boolean): void {
    // 如果可见性没有变化，则不更新
    if (this.visible === visible) {
      return;
    }

    this.visible = visible;
    this.updateVisibility();
  }

  /**
   * 获取是否可见
   * @returns 是否可见
   */
  public isVisible(): boolean {
    return this.visible;
  }

  /**
   * 设置是否可剔除
   * @param cullable 是否可剔除
   */
  public setCullable(cullable: boolean): void {
    this.cullable = cullable;
  }

  /**
   * 获取是否可剔除
   * @returns 是否可剔除
   */
  public isCullable(): boolean {
    return this.cullable;
  }

  /**
   * 设置包围半径
   * @param radius 半径
   */
  public setBoundingRadius(radius: number): void {
    this.boundingRadius = radius;
  }

  /**
   * 获取包围半径
   * @returns 包围半径
   */
  public getBoundingRadius(): number {
    return this.boundingRadius;
  }

  /**
   * 设置是否自动计算包围半径
   * @param auto 是否自动计算
   */
  public setAutoComputeBoundingRadius(auto: boolean): void {
    this.autoComputeBoundingRadius = auto;

    // 如果启用自动计算，则立即计算
    if (auto) {
      this.computeBoundingRadius();
    }
  }

  /**
   * 获取是否自动计算包围半径
   * @returns 是否自动计算
   */
  public isAutoComputeBoundingRadius(): boolean {
    return this.autoComputeBoundingRadius;
  }

  /**
   * 设置是否使用包围盒
   * @param use 是否使用
   */
  public setUseBoundingBox(use: boolean): void {
    this.useBoundingBox = use;

    // 如果启用包围盒，则创建包围盒
    if (use && !this.boundingBox) {
      this.boundingBox = new THREE.Box3();

      // 如果启用自动计算包围盒，则计算包围盒
      if (this.autoComputeBoundingBox) {
        this.computeBoundingBox();
      }
    } else if (!use) {
      this.boundingBox = null;
    }
  }

  /**
   * 获取是否使用包围盒
   * @returns 是否使用
   */
  public isUseBoundingBox(): boolean {
    return this.useBoundingBox;
  }

  /**
   * 设置是否自动计算包围盒
   * @param auto 是否自动计算
   */
  public setAutoComputeBoundingBox(auto: boolean): void {
    this.autoComputeBoundingBox = auto;

    // 如果启用自动计算，则立即计算
    if (auto && this.useBoundingBox) {
      this.computeBoundingBox();
    }
  }

  /**
   * 获取是否自动计算包围盒
   * @returns 是否自动计算
   */
  public isAutoComputeBoundingBox(): boolean {
    return this.autoComputeBoundingBox;
  }

  /**
   * 设置包围盒
   * @param box 包围盒
   */
  public setBoundingBox(box: THREE.Box3): void {
    if (!this.useBoundingBox) {
      this.useBoundingBox = true;
    }

    if (!this.boundingBox) {
      this.boundingBox = new THREE.Box3();
    }

    this.boundingBox.copy(box);
  }

  /**
   * 获取包围盒
   * @returns 包围盒
   */
  public getBoundingBox(): THREE.Box3 | null {
    return this.boundingBox;
  }
}
