import { System } from '../../core/System';
import { type World } from '../../core/World';
import * as THREE from 'three';
/**
 * 交互类型
 */
export declare enum InteractionType {
    DRAG = "drag",
    CLICK = "click",
    HOVER = "hover",
    GESTURE = "gesture",
    VOICE = "voice",
    COLLISION = "collision",
    PROXIMITY = "proximity"
}
/**
 * 交互状态
 */
export declare enum InteractionState {
    IDLE = "idle",
    ACTIVE = "active",
    DISABLED = "disabled",
    PROCESSING = "processing"
}
/**
 * 环境类型
 */
export declare enum EnvironmentType {
    INDOOR = "indoor",
    OUTDOOR = "outdoor",
    VIRTUAL = "virtual",
    MIXED = "mixed"
}
/**
 * 行为类型
 */
export declare enum BehaviorType {
    IDLE = "idle",
    FOLLOW = "follow",
    AVOID = "avoid",
    INTERACT = "interact",
    EXPLORE = "explore",
    REACT = "react"
}
/**
 * 交互区域
 */
export interface InteractionZone {
    /** 区域ID */
    id: string;
    /** 区域名称 */
    name: string;
    /** 区域类型 */
    type: 'sphere' | 'box' | 'plane' | 'custom';
    /** 位置 */
    position: THREE.Vector3;
    /** 大小 */
    size: THREE.Vector3;
    /** 旋转 */
    rotation: THREE.Euler;
    /** 是否启用 */
    enabled: boolean;
    /** 交互类型 */
    interactionTypes: InteractionType[];
    /** 触发条件 */
    triggerConditions: Map<string, any>;
    /** 响应行为 */
    responses: InteractionResponse[];
}
/**
 * 交互响应
 */
export interface InteractionResponse {
    /** 响应ID */
    id: string;
    /** 响应类型 */
    type: 'animation' | 'sound' | 'effect' | 'script';
    /** 响应数据 */
    data: any;
    /** 延迟时间 */
    delay: number;
    /** 持续时间 */
    duration: number;
    /** 优先级 */
    priority: number;
}
/**
 * 拖拽控制器
 */
export interface DragController {
    /** 是否启用 */
    enabled: boolean;
    /** 拖拽轴限制 */
    constraints: {
        x: boolean;
        y: boolean;
        z: boolean;
    };
    /** 拖拽范围 */
    bounds?: {
        min: THREE.Vector3;
        max: THREE.Vector3;
    };
    /** 拖拽速度 */
    speed: number;
    /** 阻尼 */
    damping: number;
    /** 吸附点 */
    snapPoints: THREE.Vector3[];
    /** 吸附距离 */
    snapDistance: number;
}
/**
 * 环境适应器
 */
export interface EnvironmentAdapter {
    /** 环境类型 */
    environmentType: EnvironmentType;
    /** 光照适应 */
    lightingAdaptation: {
        enabled: boolean;
        autoAdjust: boolean;
        brightness: number;
        contrast: number;
    };
    /** 物理适应 */
    physicsAdaptation: {
        enabled: boolean;
        gravity: THREE.Vector3;
        friction: number;
        airResistance: number;
    };
    /** 音频适应 */
    audioAdaptation: {
        enabled: boolean;
        reverb: number;
        echo: number;
        volume: number;
    };
}
/**
 * 智能行为
 */
export interface IntelligentBehavior {
    /** 行为ID */
    id: string;
    /** 行为类型 */
    type: BehaviorType;
    /** 是否启用 */
    enabled: boolean;
    /** 优先级 */
    priority: number;
    /** 触发条件 */
    conditions: Array<{
        type: string;
        value: any;
        operator: 'equals' | 'greater' | 'less' | 'contains';
    }>;
    /** 行为参数 */
    parameters: Map<string, any>;
    /** 冷却时间 */
    cooldown: number;
    /** 最后执行时间 */
    lastExecuted: number;
}
/**
 * 场景交互系统配置
 */
export interface SceneInteractionConfig {
    /** 是否启用拖拽控制 */
    enableDragControl: boolean;
    /** 是否启用环境适应 */
    enableEnvironmentAdaptation: boolean;
    /** 是否启用智能行为 */
    enableIntelligentBehavior: boolean;
    /** 是否启用碰撞检测 */
    enableCollisionDetection: boolean;
    /** 是否启用物理模拟 */
    enablePhysicsSimulation: boolean;
    /** 交互检测频率 */
    interactionCheckFrequency: number;
    /** 最大交互距离 */
    maxInteractionDistance: number;
    /** 是否启用调试 */
    debug: boolean;
}
/**
 * 场景交互系统
 * 实现数字人与场景的高级交互，支持拖拽控制、环境适应和智能行为
 */
export declare class SceneInteractionSystem extends System {
    /** 系统类型 */
    static readonly TYPE = "SceneInteractionSystem";
    /** 事件发射器 */
    private eventEmitter;
    /** 系统配置 */
    private config;
    /** 交互区域 */
    private interactionZones;
    /** 拖拽控制器 */
    private dragControllers;
    /** 环境适应器 */
    private environmentAdapters;
    /** 智能行为 */
    private intelligentBehaviors;
    /** 当前交互状态 */
    private interactionStates;
    /** 拖拽状态 */
    private dragStates;
    /** 鼠标/触摸状态 */
    private inputState;
    /** 射线投射器 */
    private raycaster;
    /** 相机引用 */
    private camera?;
    /** 场景引用 */
    private scene?;
    /**
     * 构造函数
     * @param world 世界实例
     * @param config 系统配置
     */
    constructor(world: World, config?: Partial<SceneInteractionConfig>);
    /**
     * 获取系统类型
     * @returns 系统类型
     */
    getType(): string;
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 更新系统
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void;
    /**
     * 销毁系统
     */
    dispose(): void;
    /**
     * 初始化默认行为
     */
    private initializeDefaultBehaviors;
    /**
     * 设置输入事件监听
     */
    private setupInputListeners;
    /**
     * 移除输入事件监听
     */
    private removeInputListeners;
    /**
     * 实体添加事件处理
     * @param entity 实体
     */
    private onEntityAdded;
    /**
     * 实体移除事件处理
     * @param entity 实体
     */
    private onEntityRemoved;
    private defaultBehaviors;
    /**
     * 初始化拖拽控制器
     * @param entityId 实体ID
     */
    private initializeDragController;
    /**
     * 更新拖拽控制
     * @param deltaTime 时间增量
     */
    private updateDragControl;
    /**
     * 应用拖拽移动
     * @param entity 实体
     * @param controller 拖拽控制器
     * @param dragState 拖拽状态
     * @param deltaTime 时间增量
     */
    private applyDragMovement;
    /**
     * 检查吸附点
     * @param position 当前位置
     * @param snapPoints 吸附点列表
     * @param snapDistance 吸附距离
     * @returns 吸附后的位置
     */
    private checkSnapPoints;
    /**
     * 鼠标按下事件
     * @param event 鼠标事件
     */
    private onMouseDown;
    /**
     * 鼠标移动事件
     * @param event 鼠标事件
     */
    private onMouseMove;
    /**
     * 鼠标释放事件
     * @param event 鼠标事件
     */
    private onMouseUp;
    /**
     * 点击事件
     * @param event 鼠标事件
     */
    private onClick;
    /**
     * 触摸开始事件
     * @param event 触摸事件
     */
    private onTouchStart;
    /**
     * 触摸移动事件
     * @param event 触摸事件
     */
    private onTouchMove;
    /**
     * 触摸结束事件
     * @param event 触摸事件
     */
    private onTouchEnd;
    /**
     * 执行射线检测
     * @param screenPosition 屏幕位置
     * @returns 相交的实体
     */
    private performRaycast;
    /**
     * 开始拖拽
     * @param entityId 实体ID
     * @param screenPosition 屏幕位置
     */
    private startDrag;
    /**
     * 更新拖拽
     * @param entityId 实体ID
     * @param screenPosition 屏幕位置
     */
    private updateDrag;
    /**
     * 结束拖拽
     * @param entityId 实体ID
     */
    private endDrag;
    /**
     * 屏幕坐标转世界坐标
     * @param screenPosition 屏幕位置
     * @returns 世界位置
     */
    private screenToWorld;
    /**
     * 设置相机引用
     * @param camera 相机
     */
    setCamera(camera: THREE.Camera): void;
    /**
     * 设置场景引用
     * @param scene 场景
     */
    setScene(scene: THREE.Scene): void;
    /**
     * 启用/禁用实体拖拽
     * @param entityId 实体ID
     * @param enabled 是否启用
     */
    setDragEnabled(entityId: string, enabled: boolean): void;
    /**
     * 设置拖拽约束
     * @param entityId 实体ID
     * @param constraints 约束
     */
    setDragConstraints(entityId: string, constraints: {
        x: boolean;
        y: boolean;
        z: boolean;
    }): void;
    /**
     * 添加交互区域
     * @param zone 交互区域
     */
    addInteractionZone(zone: InteractionZone): void;
    /**
     * 移除交互区域
     * @param zoneId 区域ID
     */
    removeInteractionZone(zoneId: string): boolean;
    /**
     * 触发交互
     * @param entityId 实体ID
     * @param type 交互类型
     * @param data 交互数据
     */
    triggerInteraction(entityId: string, type: InteractionType, data?: any): void;
    /**
     * 初始化智能行为
     * @param entityId 实体ID
     */
    private initializeIntelligentBehaviors;
    /**
     * 更新智能行为
     * @param deltaTime 时间增量
     */
    private updateIntelligentBehaviors;
    /**
     * 检查行为条件
     * @param entityId 实体ID
     * @param behavior 行为
     * @returns 是否满足条件
     */
    private checkBehaviorConditions;
    /**
     * 评估条件
     * @param entityId 实体ID
     * @param condition 条件
     * @returns 是否满足
     */
    private evaluateCondition;
    /**
     * 检查距离条件
     * @param entityId 实体ID
     * @param condition 条件
     * @returns 是否满足
     */
    private checkDistanceCondition;
    /**
     * 检查交互条件
     * @param entityId 实体ID
     * @param condition 条件
     * @returns 是否满足
     */
    private checkInteractionCondition;
    /**
     * 检查时间条件
     * @param condition 条件
     * @returns 是否满足
     */
    private checkTimeCondition;
    /**
     * 执行行为
     * @param entityId 实体ID
     * @param behavior 行为
     */
    private executeBehavior;
    /**
     * 执行空闲行为
     * @param entityId 实体ID
     * @param behavior 行为
     */
    private executeIdleBehavior;
    /**
     * 执行跟随行为
     * @param entityId 实体ID
     * @param behavior 行为
     */
    private executeFollowBehavior;
    /**
     * 执行反应行为
     * @param entityId 实体ID
     * @param behavior 行为
     */
    private executeReactBehavior;
    /**
     * 执行探索行为
     * @param entityId 实体ID
     * @param behavior 行为
     */
    private executeExploreBehavior;
    /**
     * 触发智能行为
     * @param entityId 实体ID
     * @param interactionType 交互类型
     * @param data 交互数据
     */
    private triggerIntelligentBehavior;
    /**
     * 初始化环境适应器
     * @param entityId 实体ID
     */
    private initializeEnvironmentAdapter;
    /**
     * 更新环境适应
     * @param deltaTime 时间增量
     */
    private updateEnvironmentAdaptation;
    /**
     * 更新光照适应
     * @param entityId 实体ID
     * @param adapter 环境适应器
     */
    private updateLightingAdaptation;
    /**
     * 更新物理适应
     * @param entityId 实体ID
     * @param adapter 环境适应器
     * @param deltaTime 时间增量
     */
    private updatePhysicsAdaptation;
    /**
     * 更新音频适应
     * @param entityId 实体ID
     * @param adapter 环境适应器
     */
    private updateAudioAdaptation;
    /**
     * 更新交互检测
     * @param deltaTime 时间增量
     */
    private updateInteractionDetection;
    /**
     * 检测实体交互
     */
    private detectEntityInteractions;
    /**
     * 检测区域交互
     */
    private detectZoneInteractions;
    /**
     * 检查实体是否在区域内
     * @param position 实体位置
     * @param zone 交互区域
     * @returns 是否在区域内
     */
    private isEntityInZone;
    /**
     * 触发区域交互
     * @param entityId 实体ID
     * @param zone 交互区域
     */
    private triggerZoneInteraction;
    /**
     * 执行区域响应
     * @param entityId 实体ID
     * @param zone 交互区域
     * @param response 响应
     */
    private executeZoneResponse;
    /**
     * 检测碰撞
     */
    private detectCollisions;
    /**
     * 检查交互区域
     * @param entityId 实体ID
     * @param type 交互类型
     * @param data 交互数据
     */
    private checkInteractionZones;
}
