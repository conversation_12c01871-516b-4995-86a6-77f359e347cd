import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { NotificationChannelEntity } from '../entities/notification-channel.entity';
import { NotificationHistoryEntity } from '../entities/notification-history.entity';
import { AlertEntity } from '../../alert/entities/alert.entity';

@Injectable()
export class WebhookNotifierService {
  private readonly logger = new Logger(WebhookNotifierService.name);

  constructor(private readonly httpService: HttpService) {}

  /**
   * 发送Webhook通知
   */
  async send(
    channel: NotificationChannelEntity,
    alert: AlertEntity,
    notification: NotificationHistoryEntity,
  ): Promise<void> {
    try {
      // 获取Webhook配置
      const config = channel.config;

      if (!config.url) {
        throw new Error('未配置Webhook URL');
      }

      // 构建请求头
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        ...config.headers,
      };

      // 构建请求体
      const payload = this.buildPayload(alert, notification, config);

      // 发送请求
      const response = await firstValueFrom(this.httpService.post(config.url, payload, { headers, timeout: 10000 }));

      if (response.status < 200 || response.status >= 300) {
        throw new Error(`Webhook请求失败: ${response.status} ${response.statusText}`);
      }

      this.logger.debug(`Webhook通知已发送: ${config.url}`);
    } catch (error) {
      this.logger.error(`发送Webhook通知失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 构建请求负载
   */
  private buildPayload(alert: AlertEntity, notification: NotificationHistoryEntity, config: any): any {
    // 使用配置中的模板，如果没有则使用默认模板
    if (config.template) {
      return this.applyTemplate(config.template, alert, notification);
    }

    // 默认模板
    return {
      id: alert.id,
      name: alert.name,
      description: alert.description,
      severity: alert.severity,
      status: alert.status,
      serviceId: alert.serviceId,
      serviceType: alert.serviceType,
      instanceId: alert.instanceId,
      hostname: alert.hostname,
      labels: alert.labels,
      annotations: alert.annotations,
      value: alert.value,
      startTime: alert.startTime,
      endTime: alert.endTime,
      createdAt: alert.createdAt,
      updatedAt: alert.updatedAt,
    };
  }

  /**
   * 应用模板
   */
  private applyTemplate(template: any, alert: AlertEntity, notification: NotificationHistoryEntity): any {
    // 深拷贝模板
    const result = JSON.parse(JSON.stringify(template));

    // 递归替换模板中的变量
    this.replaceTemplateVariables(result, alert, notification);

    return result;
  }

  /**
   * 递归替换模板变量
   */
  private replaceTemplateVariables(obj: any, alert: AlertEntity, notification: NotificationHistoryEntity): void {
    for (const key in obj) {
      if (typeof obj[key] === 'string') {
        obj[key] = this.replaceVariables(obj[key], alert, notification);
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        this.replaceTemplateVariables(obj[key], alert, notification);
      }
    }
  }

  /**
   * 替换变量
   */
  private replaceVariables(text: string, alert: AlertEntity, notification: NotificationHistoryEntity): string {
    return text
      .replace(/\${alert\.id}/g, alert.id)
      .replace(/\${alert\.name}/g, alert.name)
      .replace(/\${alert\.description}/g, alert.description)
      .replace(/\${alert\.severity}/g, alert.severity)
      .replace(/\${alert\.status}/g, alert.status)
      .replace(/\${alert\.serviceId}/g, alert.serviceId || '')
      .replace(/\${alert\.serviceType}/g, alert.serviceType || '')
      .replace(/\${alert\.instanceId}/g, alert.instanceId || '')
      .replace(/\${alert\.hostname}/g, alert.hostname || '')
      .replace(/\${alert\.startTime}/g, alert.startTime.toISOString())
      .replace(/\${alert\.endTime}/g, alert.endTime ? alert.endTime.toISOString() : '')
      .replace(/\${notification\.subject}/g, notification.subject)
      .replace(/\${notification\.content}/g, notification.content);
  }
}
