/**
 * 动画混合系统
 * 支持多个动画的混合、过渡和重定向
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import { type World } from '../../core/World';
import { StandardBoneType } from '../bip';
/**
 * 动画关键帧
 */
export interface AnimationKeyframe {
    /** 时间戳 */
    time: number;
    /** 骨骼变换 */
    transforms: Map<StandardBoneType, {
        position: THREE.Vector3;
        rotation: THREE.Quaternion;
        scale: THREE.Vector3;
    }>;
}
/**
 * 动画轨道
 */
export interface AnimationTrack {
    /** 轨道ID */
    id: string;
    /** 轨道名称 */
    name: string;
    /** 目标骨骼 */
    targetBone: StandardBoneType;
    /** 关键帧列表 */
    keyframes: AnimationKeyframe[];
    /** 插值类型 */
    interpolation: 'linear' | 'cubic' | 'step';
    /** 是否循环 */
    loop: boolean;
}
/**
 * 动画剪辑
 */
export interface AnimationClip {
    /** 剪辑ID */
    id: string;
    /** 剪辑名称 */
    name: string;
    /** 持续时间 */
    duration: number;
    /** 帧率 */
    frameRate: number;
    /** 动画轨道 */
    tracks: AnimationTrack[];
    /** 动画类型 */
    type: 'idle' | 'walk' | 'run' | 'gesture' | 'expression' | 'custom';
    /** 优先级 */
    priority: number;
    /** 是否可混合 */
    blendable: boolean;
    /** 混合权重 */
    weight: number;
    /** 元数据 */
    metadata: {
        source?: string;
        author?: string;
        tags?: string[];
        description?: string;
    };
}
/**
 * 动画状态
 */
export interface AnimationState {
    /** 状态ID */
    id: string;
    /** 状态名称 */
    name: string;
    /** 关联的动画剪辑 */
    clip: AnimationClip;
    /** 当前时间 */
    currentTime: number;
    /** 播放速度 */
    speed: number;
    /** 权重 */
    weight: number;
    /** 是否启用 */
    enabled: boolean;
    /** 是否循环 */
    loop: boolean;
    /** 淡入时间 */
    fadeInTime: number;
    /** 淡出时间 */
    fadeOutTime: number;
    /** 当前淡入淡出状态 */
    fadeState: 'none' | 'fadeIn' | 'fadeOut';
    /** 淡入淡出进度 */
    fadeProgress: number;
}
/**
 * 动画过渡
 */
export interface AnimationTransition {
    /** 过渡ID */
    id: string;
    /** 源状态 */
    fromState: string;
    /** 目标状态 */
    toState: string;
    /** 过渡时间 */
    duration: number;
    /** 过渡曲线 */
    curve: 'linear' | 'easeIn' | 'easeOut' | 'easeInOut' | 'custom';
    /** 自定义曲线函数 */
    customCurve?: (t: number) => number;
    /** 过渡条件 */
    conditions: AnimationCondition[];
    /** 是否可中断 */
    canBeInterrupted: boolean;
}
/**
 * 动画条件
 */
export interface AnimationCondition {
    /** 条件类型 */
    type: 'parameter' | 'time' | 'event' | 'custom';
    /** 参数名称 */
    parameter?: string;
    /** 比较操作 */
    operator: 'equals' | 'notEquals' | 'greater' | 'less' | 'greaterEqual' | 'lessEqual';
    /** 比较值 */
    value: any;
    /** 自定义条件函数 */
    customCondition?: () => boolean;
}
/**
 * 动画混合器
 */
export interface AnimationBlender {
    /** 混合器ID */
    id: string;
    /** 混合器名称 */
    name: string;
    /** 混合类型 */
    type: 'additive' | 'override' | 'multiply';
    /** 输入状态列表 */
    inputStates: string[];
    /** 输出权重 */
    outputWeight: number;
    /** 混合参数 */
    blendParameters: Map<string, number>;
}
/**
 * 动画混合系统配置
 */
export interface AnimationBlendingSystemConfig {
    /** 最大同时播放动画数量 */
    maxConcurrentAnimations?: number;
    /** 默认过渡时间 */
    defaultTransitionDuration?: number;
    /** 是否启用动画压缩 */
    enableCompression?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 调试模式 */
    debug?: boolean;
}
/**
 * 动画混合系统
 */
export declare class AnimationBlendingSystem extends System {
    /** 系统优先级 */
    static readonly PRIORITY = 4;
    /** 配置 */
    private config;
    /** 事件发射器 */
    private eventEmitter;
    /** 动画剪辑库 */
    private animationClips;
    /** 动画状态机 */
    private animationStates;
    /** 动画过渡 */
    private transitions;
    /** 动画混合器 */
    private blenders;
    /** 实体动画映射 */
    private entityAnimations;
    /** 动画参数 */
    private globalParameters;
    /**
     * 构造函数
     * @param world 世界实例
     * @param config 系统配置
     */
    constructor(world: World, config?: AnimationBlendingSystemConfig);
    /**
     * 系统初始化
     */
    initialize(): void;
    /**
     * 系统更新
     * @param deltaTime 帧间隔时间
     */
    update(deltaTime: number): void;
    /**
     * 注册实体
     * @param entity 实体
     */
    private registerEntity;
    /**
     * 注销实体
     * @param entity 实体
     */
    private unregisterEntity;
    /**
     * 添加动画剪辑
     * @param clip 动画剪辑
     */
    addAnimationClip(clip: AnimationClip): void;
    /**
     * 移除动画剪辑
     * @param clipId 剪辑ID
     */
    removeAnimationClip(clipId: string): void;
    /**
     * 创建动画状态
     * @param stateConfig 状态配置
     * @returns 动画状态
     */
    createAnimationState(stateConfig: {
        id: string;
        name: string;
        clipId: string;
        speed?: number;
        weight?: number;
        loop?: boolean;
        fadeInTime?: number;
        fadeOutTime?: number;
    }): AnimationState;
    /**
     * 添加动画过渡
     * @param transition 过渡配置
     */
    addTransition(transition: AnimationTransition): void;
    /**
     * 播放动画
     * @param entityId 实体ID
     * @param stateId 状态ID
     * @param fadeInTime 淡入时间
     */
    playAnimation(entityId: string, stateId: string, fadeInTime?: number): void;
    /**
     * 停止动画
     * @param entityId 实体ID
     * @param stateId 状态ID
     * @param fadeOutTime 淡出时间
     */
    stopAnimation(entityId: string, stateId: string, fadeOutTime?: number): void;
    /**
     * 设置动画参数
     * @param entityId 实体ID
     * @param parameterName 参数名称
     * @param value 参数值
     */
    setAnimationParameter(entityId: string, parameterName: string, value: any): void;
    /**
     * 设置全局动画参数
     * @param parameterName 参数名称
     * @param value 参数值
     */
    setGlobalParameter(parameterName: string, value: any): void;
    /**
     * 更新实体动画
     * @param entityId 实体ID
     * @param animationData 动画数据
     * @param deltaTime 帧间隔时间
     */
    private updateEntityAnimation;
    /**
     * 更新动画状态
     * @param entityId 实体ID
     * @param stateId 状态ID
     * @param deltaTime 帧间隔时间
     */
    private updateAnimationState;
    /**
     * 更新淡入淡出状态
     * @param state 动画状态
     * @param deltaTime 帧间隔时间
     */
    private updateFadeState;
    /**
     * 更新过渡
     * @param entityId 实体ID
     * @param animationData 动画数据
     * @param deltaTime 帧间隔时间
     */
    private updateTransitions;
    /**
     * 检查过渡条件
     * @param entityId 实体ID
     */
    private checkTransitionConditions;
    /**
     * 混合动画
     * @param entityId 实体ID
     * @param animationData 动画数据
     */
    private blendAnimations;
    /**
     * 销毁系统
     */
    dispose(): void;
}
