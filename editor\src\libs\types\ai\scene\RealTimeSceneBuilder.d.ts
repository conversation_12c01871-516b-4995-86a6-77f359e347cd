import { Engine } from '../../core/Engine';
import { Scene } from '../../scene/Scene';
import { type Entity } from '../../core/Entity';
import { SceneLayout, AssetMatchResult } from './SceneGenerationTypes';
/**
 * 材质应用器
 */
export declare class MaterialApplicator {
    private materialCache;
    /**
     * 应用材质
     */
    applyMaterial(entity: Entity, materialHint?: string): Promise<void>;
    /**
     * 创建材质
     */
    private createMaterial;
}
/**
 * 光照计算器
 */
export declare class LightingCalculator {
    private engine;
    constructor(engine: Engine);
    /**
     * 计算场景光照
     */
    calculateLighting(scene: Scene): Promise<void>;
    /**
     * 移除现有光源
     */
    private removeExistingLights;
    /**
     * 添加环境光
     */
    private addAmbientLight;
    /**
     * 添加主光源
     */
    private addMainLight;
    /**
     * 添加补充光源
     */
    private addFillLights;
}
/**
 * 实时场景构建器
 */
export declare class RealTimeSceneBuilder {
    private engine;
    private assetLoader;
    private materialApplicator;
    private lightingCalculator;
    private buildProgress;
    constructor(engine: Engine);
    /**
     * 构建场景
     */
    buildScene(layout: SceneLayout, assets: AssetMatchResult[]): Promise<Scene>;
    /**
     * 流式构建场景
     */
    buildSceneStream(layout: SceneLayout, assets: AssetMatchResult[]): AsyncGenerator<{
        progress: number;
        scene?: Scene;
    }, Scene, unknown>;
    /**
     * 设置环境
     */
    private setupEnvironment;
    /**
     * 放置资产
     */
    private placeAssets;
    /**
     * 放置单个资产
     */
    private placeSingleAsset;
    /**
     * 加载资产
     */
    private loadAsset;
    /**
     * 创建基础几何体
     */
    private createPrimitive;
    /**
     * 设置变换
     */
    private setupTransform;
    /**
     * 创建占位符
     */
    private createPlaceholder;
    /**
     * 加载天空盒
     */
    private loadSkybox;
    /**
     * 优化场景
     */
    private optimizeScene;
    /**
     * 合并几何体
     */
    private mergeGeometries;
    /**
     * 设置LOD
     */
    private setupLOD;
    /**
     * 优化材质
     */
    private optimizeMaterials;
    /**
     * 获取构建进度
     */
    getBuildProgress(): number;
    /**
     * 更新场景
     */
    updateScene(scene: Scene, newAssets: AssetMatchResult[]): Promise<void>;
    /**
     * 移除场景元素
     */
    removeSceneElement(scene: Scene, elementName: string): Promise<void>;
}
