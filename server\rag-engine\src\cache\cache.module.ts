import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { createClient } from 'redis';
import { CacheService } from './cache.service';

@Global()
@Module({
  imports: [ConfigModule],
  providers: [
    {
      provide: 'REDIS_CLIENT',
      useFactory: async (configService: ConfigService) => {
        try {
          const client = createClient({
            socket: {
              host: configService.get('rag.cache.host', 'localhost'),
              port: configService.get('rag.cache.port', 6379),
              connectTimeout: 5000,
            },
            password: configService.get('rag.cache.password'),
          });

          client.on('error', (err) => {
            console.warn('Redis Client Error (缓存服务不可用，将使用内存缓存):', err.message);
          });

          client.on('connect', () => {
            console.log('Redis Client Connected');
          });

          // 尝试连接，如果失败则返回null
          try {
            await client.connect();
            return client;
          } catch (error) {
            console.warn('Redis连接失败，将使用内存缓存:', error.message);
            return null;
          }
        } catch (error) {
          console.warn('Redis初始化失败，将使用内存缓存:', error.message);
          return null;
        }
      },
      inject: [ConfigService],
    },
    CacheService,
  ],
  exports: ['REDIS_CLIENT', CacheService],
})
export class CacheModule {}
