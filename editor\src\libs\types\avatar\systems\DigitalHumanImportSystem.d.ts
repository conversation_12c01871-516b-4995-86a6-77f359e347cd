/**
 * 数字人导入系统
 * 负责数字人文件的上传、验证、解析和导入功能
 */
import { System } from '../../core/System';
import { Entity } from '../../core/Entity';
import { type World } from '../../core/World';
import { MinIOStorageService } from '../../storage/MinIOStorageService';
/**
 * 支持的文件格式
 */
export declare enum SupportedFileFormat {
    GLTF = "gltf",
    GLB = "glb",
    FBX = "fbx",
    OBJ = "obj",
    DAE = "dae",
    BLEND = "blend",
    VRM = "vrm",
    CUSTOM_DH = "dh"
}
/**
 * 导入配置
 */
export interface ImportConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 最大文件大小 (MB) */
    maxFileSize?: number;
    /** 是否自动修复 */
    autoFix?: boolean;
    /** 是否严格验证 */
    strictValidation?: boolean;
    /** 支持的格式 */
    supportedFormats?: SupportedFileFormat[];
}
/**
 * 文件验证结果
 */
export interface FileValidationResult {
    /** 是否有效 */
    isValid: boolean;
    /** 文件格式 */
    format?: SupportedFileFormat;
    /** 文件大小 */
    fileSize: number;
    /** 错误信息 */
    errors: string[];
    /** 警告信息 */
    warnings: string[];
}
/**
 * 导入结果
 */
export interface ImportResult {
    /** 是否成功 */
    success: boolean;
    /** 导入的数字人实体 */
    digitalHuman?: Entity;
    /** 导入的资产ID列表 */
    assetIds?: string[];
    /** 错误信息 */
    error?: string;
    /** 警告信息 */
    warnings?: string[];
}
/**
 * 导入进度信息
 */
export interface ImportProgress {
    /** 当前阶段 */
    stage: string;
    /** 进度百分比 (0-100) */
    progress: number;
    /** 状态消息 */
    message: string;
}
/**
 * 数字人导入系统
 */
export declare class DigitalHumanImportSystem extends System {
    /** 系统优先级 */
    static readonly PRIORITY = 7;
    /** 配置 */
    private config;
    /** 事件发射器 */
    private eventEmitter;
    /** 存储服务 */
    private storageService;
    /** 导入队列 */
    private importQueue;
    /** 当前导入任务 */
    private activeImports;
    /**
     * 构造函数
     * @param world 世界实例
     * @param storageService 存储服务
     * @param config 配置
     */
    constructor(world: World, storageService: MinIOStorageService, config?: ImportConfig);
    /**
     * 系统初始化
     */
    initialize(): void;
    /**
     * 系统更新
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void;
    /**
     * 导入数字人文件
     * @param file 数字人文件
     * @param options 导入选项
     * @returns 导入结果
     */
    importDigitalHuman(file: File, options?: any): Promise<ImportResult>;
    /**
     * 验证文件
     * @param file 文件
     * @returns 验证结果
     */
    validateFile(file: File): Promise<FileValidationResult>;
    /**
     * 检测文件格式
     * @param file 文件
     * @returns 文件格式
     */
    private detectFileFormat;
    /**
     * 验证文件内容
     * @param file 文件
     * @param format 文件格式
     * @returns 验证结果
     */
    private validateFileContent;
    /**
     * 验证GLTF内容
     * @param file 文件
     * @param result 验证结果
     */
    private validateGLTFContent;
    /**
     * 验证VRM内容
     * @param file 文件
     * @param result 验证结果
     */
    private validateVRMContent;
    /**
     * 验证自定义数字人格式内容
     * @param file 文件
     * @param result 验证结果
     */
    private validateCustomDHContent;
    /**
     * 验证通用内容
     * @param file 文件
     * @param result 验证结果
     */
    private validateGenericContent;
    /**
     * 处理导入队列
     */
    private processImportQueue;
    /**
     * 处理导入任务
     * @param task 导入任务
     * @param taskId 任务ID
     */
    private processImportTask;
    /**
     * 更新进度
     * @param taskId 任务ID
     * @param stage 阶段
     * @param progress 进度
     * @param message 消息
     */
    private updateProgress;
    /**
     * 解析数字人文件
     * @param file 文件
     * @param format 格式
     * @returns 解析结果
     */
    private parseDigitalHumanFile;
    /**
     * 解析自定义数字人文件
     * @param file 文件
     * @returns 解析结果
     */
    private parseCustomDHFile;
    /**
     * 解析VRM文件
     * @param file 文件
     * @returns 解析结果
     */
    private parseVRMFile;
    /**
     * 解析GLTF文件
     * @param file 文件
     * @returns 解析结果
     */
    private parseGLTFFile;
    /**
     * 从数据创建数字人
     * @param data 解析的数据
     * @param assetUrl 资产URL
     * @returns 数字人实体
     */
    private createDigitalHumanFromData;
    /**
     * 销毁系统
     */
    dispose(): void;
}
