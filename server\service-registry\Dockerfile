FROM node:22-alpine AS builder

WORKDIR /app

# 复制共享模块
COPY shared/ ./shared/

# 复制服务注册中心代码
COPY service-registry/package*.json ./
RUN npm install

# 复制服务注册中心源代码
COPY service-registry/src ./src
COPY service-registry/tsconfig.json ./
COPY service-registry/nest-cli.json ./

RUN npm run build

FROM node:22-alpine

# 安装 wget 和 curl 用于健康检查
RUN apk add --no-cache wget curl

WORKDIR /app

COPY --from=builder /app/package*.json ./
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules

EXPOSE 3010 4010

CMD ["node", "dist/main.js"]
