/**
 * 数字人导入系统
 * 负责数字人文件的上传、验证、解析和导入功能
 */
import { System } from '../../core/System';
import { Entity } from '../../core/Entity';
import { type World  } from '../../core/World';
import { EventEmitter } from '../../utils/EventEmitter';
import { MinIOStorageService } from '../../storage/MinIOStorageService';
import { DigitalHumanComponent } from '../components/DigitalHumanComponent';

/**
 * 支持的文件格式
 */
export enum SupportedFileFormat {
  GLTF = 'gltf',
  GLB = 'glb',
  FBX = 'fbx',
  OBJ = 'obj',
  DAE = 'dae',
  BLEND = 'blend',
  VRM = 'vrm',
  CUSTOM_DH = 'dh' // 自定义数字人格式
}

/**
 * 导入配置
 */
export interface ImportConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 最大文件大小 (MB) */
  maxFileSize?: number;
  /** 是否自动修复 */
  autoFix?: boolean;
  /** 是否严格验证 */
  strictValidation?: boolean;
  /** 支持的格式 */
  supportedFormats?: SupportedFileFormat[];
}

/**
 * 文件验证结果
 */
export interface FileValidationResult {
  /** 是否有效 */
  isValid: boolean;
  /** 文件格式 */
  format?: SupportedFileFormat;
  /** 文件大小 */
  fileSize: number;
  /** 错误信息 */
  errors: string[];
  /** 警告信息 */
  warnings: string[];
}

/**
 * 导入结果
 */
export interface ImportResult {
  /** 是否成功 */
  success: boolean;
  /** 导入的数字人实体 */
  digitalHuman?: Entity;
  /** 导入的资产ID列表 */
  assetIds?: string[];
  /** 错误信息 */
  error?: string;
  /** 警告信息 */
  warnings?: string[];
}

/**
 * 导入进度信息
 */
export interface ImportProgress {
  /** 当前阶段 */
  stage: string;
  /** 进度百分比 (0-100) */
  progress: number;
  /** 状态消息 */
  message: string;
}

/**
 * 数字人导入系统
 */
export class DigitalHumanImportSystem extends System {
  /** 系统优先级 */
  public static readonly PRIORITY = 7;

  /** 配置 */
  private config: ImportConfig;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 存储服务 */
  private storageService: MinIOStorageService;

  /** 导入队列 */
  private importQueue: Array<{
    file: File;
    options: any;
    resolve: (result: ImportResult) => void;
    reject: (error: Error) => void;
  }> = [];

  /** 当前导入任务 */
  private activeImports: Map<string, ImportProgress> = new Map();

  /**
   * 构造函数
   * @param world 世界实例
   * @param storageService 存储服务
   * @param config 配置
   */
  constructor(world: World, storageService: MinIOStorageService, config: ImportConfig = {}) {
    super(DigitalHumanImportSystem.PRIORITY);

    this.storageService = storageService;
    this.config = {
      debug: false,
      maxFileSize: 100, // 100MB
      autoFix: true,
      strictValidation: false,
      supportedFormats: [
        SupportedFileFormat.GLTF,
        SupportedFileFormat.GLB,
        SupportedFileFormat.FBX,
        SupportedFileFormat.VRM,
        SupportedFileFormat.CUSTOM_DH
      ],
      ...config
    };
  }

  /**
   * 系统初始化
   */
  public initialize(): void {
    if (this.config.debug) {
      console.log('[DigitalHumanImportSystem] 系统初始化');
    }
  }

  /**
   * 系统更新
   * @param deltaTime 时间增量
   */
  public update(deltaTime: number): void {
    // 处理导入队列
    this.processImportQueue();
  }

  /**
   * 导入数字人文件
   * @param file 数字人文件
   * @param options 导入选项
   * @returns 导入结果
   */
  public async importDigitalHuman(file: File, options: any = {}): Promise<ImportResult> {
    return new Promise((resolve, reject) => {
      // 添加到导入队列
      this.importQueue.push({
        file,
        options,
        resolve,
        reject
      });

      if (this.config.debug) {
        console.log(`[DigitalHumanImportSystem] 添加导入任务: ${file.name}`);
      }
    });
  }

  /**
   * 验证文件
   * @param file 文件
   * @returns 验证结果
   */
  public async validateFile(file: File): Promise<FileValidationResult> {
    const result: FileValidationResult = {
      isValid: true,
      fileSize: file.size,
      errors: [],
      warnings: []
    };

    // 检查文件大小
    const maxSizeBytes = (this.config.maxFileSize || 100) * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      result.errors.push(`文件大小超过限制 (${this.config.maxFileSize}MB)`);
      result.isValid = false;
    }

    // 检查文件格式
    const format = this.detectFileFormat(file);
    if (!format) {
      result.errors.push('不支持的文件格式');
      result.isValid = false;
    } else {
      result.format = format;

      // 检查格式是否在支持列表中
      if (!this.config.supportedFormats?.includes(format)) {
        result.errors.push(`不支持的文件格式: ${format}`);
        result.isValid = false;
      }
    }

    // 文件内容验证
    if (result.isValid && format) {
      const contentValidation = await this.validateFileContent(file, format);
      result.errors.push(...contentValidation.errors);
      result.warnings.push(...contentValidation.warnings);
      result.isValid = result.isValid && contentValidation.isValid;
    }

    return result;
  }

  /**
   * 检测文件格式
   * @param file 文件
   * @returns 文件格式
   */
  private detectFileFormat(file: File): SupportedFileFormat | null {
    const extension = file.name.toLowerCase().split('.').pop();
    
    switch (extension) {
      case 'gltf':
        return SupportedFileFormat.GLTF;
      case 'glb':
        return SupportedFileFormat.GLB;
      case 'fbx':
        return SupportedFileFormat.FBX;
      case 'obj':
        return SupportedFileFormat.OBJ;
      case 'dae':
        return SupportedFileFormat.DAE;
      case 'blend':
        return SupportedFileFormat.BLEND;
      case 'vrm':
        return SupportedFileFormat.VRM;
      case 'dh':
        return SupportedFileFormat.CUSTOM_DH;
      default:
        return null;
    }
  }

  /**
   * 验证文件内容
   * @param file 文件
   * @param format 文件格式
   * @returns 验证结果
   */
  private async validateFileContent(
    file: File,
    format: SupportedFileFormat
  ): Promise<{ isValid: boolean; errors: string[]; warnings: string[] }> {
    const result = {
      isValid: true,
      errors: [] as string[],
      warnings: [] as string[]
    };

    try {
      switch (format) {
        case SupportedFileFormat.GLTF:
        case SupportedFileFormat.GLB:
          await this.validateGLTFContent(file, result);
          break;
        case SupportedFileFormat.VRM:
          await this.validateVRMContent(file, result);
          break;
        case SupportedFileFormat.CUSTOM_DH:
          await this.validateCustomDHContent(file, result);
          break;
        default:
          // 其他格式的基本验证
          await this.validateGenericContent(file, result);
          break;
      }
    } catch (error) {
      result.errors.push(`文件内容验证失败: ${error.message}`);
      result.isValid = false;
    }

    return result;
  }

  /**
   * 验证GLTF内容
   * @param file 文件
   * @param result 验证结果
   */
  private async validateGLTFContent(
    file: File,
    result: { isValid: boolean; errors: string[]; warnings: string[] }
  ): Promise<void> {
    try {
      const text = await file.text();
      const gltf = JSON.parse(text);

      // 检查必需字段
      if (!gltf.asset) {
        result.errors.push('GLTF文件缺少asset字段');
        result.isValid = false;
      }

      // 检查版本
      if (gltf.asset && gltf.asset.version !== '2.0') {
        result.warnings.push('建议使用GLTF 2.0版本');
      }

      // 检查场景
      if (!gltf.scenes || gltf.scenes.length === 0) {
        result.warnings.push('GLTF文件不包含场景');
      }

      // 检查节点
      if (!gltf.nodes || gltf.nodes.length === 0) {
        result.warnings.push('GLTF文件不包含节点');
      }

    } catch (error) {
      result.errors.push('GLTF文件格式错误');
      result.isValid = false;
    }
  }

  /**
   * 验证VRM内容
   * @param file 文件
   * @param result 验证结果
   */
  private async validateVRMContent(
    file: File,
    result: { isValid: boolean; errors: string[]; warnings: string[] }
  ): Promise<void> {
    // VRM是基于GLTF的，先进行GLTF验证
    await this.validateGLTFContent(file, result);

    // TODO: 添加VRM特定的验证逻辑
    if (this.config.debug) {
      console.log('[DigitalHumanImportSystem] 验证VRM内容');
    }
  }

  /**
   * 验证自定义数字人格式内容
   * @param file 文件
   * @param result 验证结果
   */
  private async validateCustomDHContent(
    file: File,
    result: { isValid: boolean; errors: string[]; warnings: string[] }
  ): Promise<void> {
    try {
      const text = await file.text();
      const dhData = JSON.parse(text);

      // 检查版本
      if (!dhData.version) {
        result.errors.push('数字人文件缺少版本信息');
        result.isValid = false;
      }

      // 检查基本信息
      if (!dhData.metadata) {
        result.errors.push('数字人文件缺少元数据');
        result.isValid = false;
      }

      // 检查模型数据
      if (!dhData.model) {
        result.errors.push('数字人文件缺少模型数据');
        result.isValid = false;
      }

      // 检查骨骼数据
      if (!dhData.skeleton) {
        result.warnings.push('数字人文件不包含骨骼数据');
      }

      // 检查动画数据
      if (!dhData.animations) {
        result.warnings.push('数字人文件不包含动画数据');
      }

    } catch (error) {
      result.errors.push('数字人文件格式错误');
      result.isValid = false;
    }
  }

  /**
   * 验证通用内容
   * @param file 文件
   * @param result 验证结果
   */
  private async validateGenericContent(
    file: File,
    result: { isValid: boolean; errors: string[]; warnings: string[] }
  ): Promise<void> {
    // 基本的文件完整性检查
    if (file.size === 0) {
      result.errors.push('文件为空');
      result.isValid = false;
    }

    // TODO: 添加更多通用验证逻辑
  }

  /**
   * 处理导入队列
   */
  private processImportQueue(): void {
    if (this.importQueue.length === 0) return;

    const task = this.importQueue.shift()!;
    const taskId = `${task.file.name}_${Date.now()}`;

    this.processImportTask(task, taskId);
  }

  /**
   * 处理导入任务
   * @param task 导入任务
   * @param taskId 任务ID
   */
  private async processImportTask(
    task: {
      file: File;
      options: any;
      resolve: (result: ImportResult) => void;
      reject: (error: Error) => void;
    },
    taskId: string
  ): Promise<void> {
    try {
      // 设置初始进度
      this.activeImports.set(taskId, {
        stage: '验证文件',
        progress: 0,
        message: '正在验证文件格式和内容...'
      });

      this.emit('importProgress', taskId, this.activeImports.get(taskId));

      // 1. 验证文件
      const validation = await this.validateFile(task.file);
      if (!validation.isValid) {
        throw new Error(`文件验证失败: ${validation.errors.join(', ')}`);
      }

      // 2. 上传文件到存储
      this.updateProgress(taskId, '上传文件', 20, '正在上传文件到存储服务...');
      const uploadResult = await this.storageService.uploadFile(task.file, 'digital-humans');

      // 3. 解析文件
      this.updateProgress(taskId, '解析文件', 40, '正在解析数字人文件...');
      const parseResult = await this.parseDigitalHumanFile(task.file, validation.format!);

      // 4. 创建数字人实体
      this.updateProgress(taskId, '创建数字人', 60, '正在创建数字人实体...');
      const digitalHuman = await this.createDigitalHumanFromData(parseResult, uploadResult.url);

      // 5. 应用到场景
      this.updateProgress(taskId, '应用到场景', 80, '正在将数字人添加到场景...');
      this.world.addEntity(digitalHuman);

      // 6. 完成
      this.updateProgress(taskId, '完成', 100, '数字人导入完成');

      const result: ImportResult = {
        success: true,
        digitalHuman,
        assetIds: [uploadResult.id],
        warnings: validation.warnings
      };

      task.resolve(result);
      this.emit('importCompleted', taskId, result);

    } catch (error) {
      const result: ImportResult = {
        success: false,
        error: error.message
      };

      task.reject(error as Error);
      this.emit('importError', taskId, error);

      if (this.config.debug) {
        console.error('[DigitalHumanImportSystem] 导入失败', task.file.name, error);
      }
    } finally {
      this.activeImports.delete(taskId);
    }
  }

  /**
   * 更新进度
   * @param taskId 任务ID
   * @param stage 阶段
   * @param progress 进度
   * @param message 消息
   */
  private updateProgress(taskId: string, stage: string, progress: number, message: string): void {
    const progressInfo: ImportProgress = { stage, progress, message };
    this.activeImports.set(taskId, progressInfo);
    this.emit('importProgress', taskId, progressInfo);
  }

  /**
   * 解析数字人文件
   * @param file 文件
   * @param format 格式
   * @returns 解析结果
   */
  private async parseDigitalHumanFile(file: File, format: SupportedFileFormat): Promise<any> {
    // TODO: 根据不同格式实现具体的解析逻辑
    switch (format) {
      case SupportedFileFormat.CUSTOM_DH:
        return this.parseCustomDHFile(file);
      case SupportedFileFormat.VRM:
        return this.parseVRMFile(file);
      case SupportedFileFormat.GLTF:
      case SupportedFileFormat.GLB:
        return this.parseGLTFFile(file);
      default:
        throw new Error(`不支持的文件格式: ${format}`);
    }
  }

  /**
   * 解析自定义数字人文件
   * @param file 文件
   * @returns 解析结果
   */
  private async parseCustomDHFile(file: File): Promise<any> {
    const text = await file.text();
    return JSON.parse(text);
  }

  /**
   * 解析VRM文件
   * @param file 文件
   * @returns 解析结果
   */
  private async parseVRMFile(file: File): Promise<any> {
    // TODO: 实现VRM文件解析
    throw new Error('VRM文件解析尚未实现');
  }

  /**
   * 解析GLTF文件
   * @param file 文件
   * @returns 解析结果
   */
  private async parseGLTFFile(file: File): Promise<any> {
    // TODO: 实现GLTF文件解析
    throw new Error('GLTF文件解析尚未实现');
  }

  /**
   * 从数据创建数字人
   * @param data 解析的数据
   * @param assetUrl 资产URL
   * @returns 数字人实体
   */
  private async createDigitalHumanFromData(data: any, assetUrl: string): Promise<Entity> {
    const entity = new Entity('导入的数字人');
    entity.name = data.metadata?.name || '导入的数字人';

    // 创建数字人组件
    const digitalHumanComponent = new DigitalHumanComponent(entity, {
      name: entity.name,
      source: 'upload' as any, // 使用DigitalHumanSource.UPLOAD
      sourceData: {
        originalFile: assetUrl,
        importData: data
      }
    });

    entity.addComponent(digitalHumanComponent);

    // TODO: 根据数据创建其他必要的组件
    // 1. 模型组件
    // 2. 骨骼组件
    // 3. 动画组件
    // 4. 材质组件

    return entity;
  }

  // 移除了重写的EventEmitter方法，直接使用继承的方法

  /**
   * 销毁系统
   */
  public dispose(): void {
    this.importQueue.length = 0;
    this.activeImports.clear();
    this.eventEmitter.removeAllListeners();

    if (this.config.debug) {
      console.log('[DigitalHumanImportSystem] 系统已销毁');
    }
  }
}
