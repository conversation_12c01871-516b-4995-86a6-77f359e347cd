{"tutorials": {"title": "教程", "description": "通过交互式教程学习DL（Digital Learning）引擎编辑器的各项功能。", "allTutorials": "所有教程", "recommended": "推荐", "completed": {"title": "教程完成！", "message": "恭喜！您已成功完成\"{title}\"教程。", "reward": "获得奖励", "badgeReward": "徽章：{badge}", "itemReward": "物品：{item}", "close": "关闭"}, "interactive": "交互式教程", "interactiveTutorials": "交互式教程", "interactiveDescription": "交互式教程会引导您一步步完成任务，并提供实时反馈。", "noInteractiveTutorials": "暂无交互式教程，请稍后再来查看。", "recommendedForYou": "为您推荐", "completedTutorials": "已完成的教程", "start": "开始", "continue": "继续", "restart": "重新开始", "next": "下一步", "previous": "上一步", "complete": "完成", "skip": "跳过", "exit": "退出", "step": "步骤", "duration": "预计时间", "minutes": "分钟", "progressStatus": "进度：{current}/{total}", "taskProgress": "任务：{completed}/{total}", "progress": "步骤 {current}/{total}", "completedTitle": "教程完成", "completedMessage": "恭喜！您已完成\"{title}\"教程。", "achievementUnlocked": "解锁成就！", "prerequisites": "前置教程", "hasPrerequisites": "需要前置教程", "helpTooltip": "需要帮助？", "difficulty": {"beginner": "初级", "intermediate": "中级", "advanced": "高级", "easy": "简单", "medium": "中等", "hard": "困难", "expert": "专家"}, "editorBasics": {"title": "编辑器基础", "description": "学习DL（Digital Learning）引擎编辑器的基本界面和操作。", "steps": {"welcome": {"title": "欢迎", "description": "欢迎使用DL（Digital Learning）引擎编辑器！本教程将帮助您了解编辑器的基本界面和操作。"}, "interfaceOverview": {"title": "界面概览", "description": "DL（Digital Learning）引擎编辑器界面由多个面板组成，包括场景视图、层级面板、属性面板等。您可以根据需要调整这些面板的布局。"}, "navigation": {"title": "场景导航", "description": "学习如何在场景中导航，包括平移、旋转和缩放视图。"}, "selection": {"title": "选择对象", "description": "学习如何选择场景中的对象，以及如何使用多选和框选功能。"}, "transformation": {"title": "变换对象", "description": "学习如何移动、旋转和缩放场景中的对象。"}, "properties": {"title": "编辑属性", "description": "学习如何在属性面板中编辑对象的属性。"}, "conclusion": {"title": "总结", "description": "恭喜！您已经学习了DL（Digital Learning）引擎编辑器的基本操作。现在您可以开始创建自己的场景了。"}}}, "animationSystem": {"title": "动画系统", "description": "学习如何使用DL（Digital Learning）引擎的动画系统创建和编辑动画。", "steps": {"introduction": {"title": "介绍", "description": "DL（Digital Learning）引擎的动画系统允许您创建复杂的角色动画和场景动画。本教程将帮助您了解动画系统的基本概念和用法。"}, "animationTypes": {"title": "动画类型", "description": "了解不同类型的动画，包括关键帧动画、骨骼动画和混合动画。"}, "keyframeAnimation": {"title": "关键帧动画", "description": "学习如何创建和编辑关键帧动画。"}, "animationBlending": {"title": "动画混合", "description": "学习如何混合多个动画以创建平滑的过渡。"}, "stateMachines": {"title": "状态机", "description": "学习如何使用状态机控制角色动画。"}, "conclusion": {"title": "总结", "description": "恭喜！您已经学习了DL（Digital Learning）引擎动画系统的基本用法。现在您可以开始创建自己的动画了。"}}}, "visualScripting": {"title": "视觉脚本入门", "description": "学习如何使用视觉脚本系统创建交互式内容。", "steps": {"introduction": {"title": "介绍", "description": "视觉脚本是一种无需编写代码的方式来创建交互式内容和游戏逻辑。本教程将帮助您了解视觉脚本系统的基本概念和用法。"}, "basicConcepts": {"title": "基本概念", "description": "了解节点、连接和事件等基本概念。"}, "creatingLogic": {"title": "创建逻辑", "description": "学习如何创建简单的游戏逻辑。"}, "variables": {"title": "变量", "description": "学习如何使用变量存储和操作数据。"}, "events": {"title": "事件", "description": "学习如何响应用户输入和其他事件。"}, "conclusion": {"title": "总结", "description": "恭喜！您已经学习了视觉脚本系统的基本用法。现在您可以开始创建自己的交互式内容了。"}}}, "characterCreation": {"title": "角色创建", "description": "学习如何创建和自定义3D角色。", "steps": {"introduction": {"title": "介绍", "description": "本教程将指导您创建一个完整的3D角色，包括模型导入、材质设置、骨骼绑定和动画设置。"}, "modelImport": {"title": "模型导入", "description": "学习如何导入角色模型。"}, "materialSetup": {"title": "材质设置", "description": "学习如何设置角色材质。"}, "rigging": {"title": "骨骼绑定", "description": "学习如何为角色设置骨骼。"}, "animationSetup": {"title": "动画设置", "description": "学习如何为角色设置动画。"}, "conclusion": {"title": "总结", "description": "恭喜！您已经学习了如何创建一个完整的3D角色。"}}}}}