/**
 * BIP骨骼集成系统
 * 负责BIP文件的导入、骨骼映射、动画重定向等功能
 */
import { System } from '../../core/System';
import { Entity } from '../../core/Entity';
import { type World } from '../../core/World';
/**
 * BIP集成系统配置
 */
export interface BIPIntegrationConfig {
    /** 是否启用调试模式 */
    debug?: boolean;
    /** 是否自动修复缺失骨骼 */
    autoFixMissing?: boolean;
    /** 是否严格模式 */
    strictMode?: boolean;
    /** 最大并发处理数 */
    maxConcurrentProcessing?: number;
}
/**
 * BIP导入结果
 */
export interface BIPImportResult {
    /** 是否成功 */
    success: boolean;
    /** 骨骼ID */
    skeletonId?: string;
    /** 骨骼数量 */
    boneCount?: number;
    /** 动画数量 */
    animationCount?: number;
    /** 警告信息 */
    warnings?: string[];
    /** 错误信息 */
    error?: string;
}
/**
 * 验证结果
 */
export interface ValidationResult {
    /** 是否有效 */
    isValid: boolean;
    /** 错误信息 */
    errors: string[];
    /** 警告信息 */
    warnings: string[];
}
/**
 * BIP骨骼集成系统
 */
export declare class BIPIntegrationSystem extends System {
    /** 系统优先级 */
    static readonly PRIORITY = 6;
    /** 配置 */
    private config;
    /** 事件发射器 */
    private eventEmitter;
    /** BIP解析器 */
    private bipParser;
    /** 骨骼映射器 */
    private boneMapper;
    /** 处理队列 */
    private processingQueue;
    /** 当前处理中的任务 */
    private activeProcessing;
    /**
     * 构造函数
     * @param world 世界实例
     * @param config 系统配置
     */
    constructor(world: World, config?: BIPIntegrationConfig);
    /**
     * 设置事件监听器
     */
    private setupEventListeners;
    /**
     * 系统初始化
     */
    initialize(): void;
    /**
     * 系统更新
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void;
    /**
     * 导入BIP骨骼文件
     * @param file BIP文件
     * @param digitalHuman 数字人实体
     * @returns 导入结果
     */
    importBIPSkeleton(file: File, digitalHuman: Entity): Promise<BIPImportResult>;
    /**
     * 处理队列
     */
    private processQueue;
    /**
     * 处理导入任务
     * @param task 导入任务
     * @param taskId 任务ID
     */
    private processImportTask;
    /**
     * 验证BIP数据
     * @param bipData BIP骨骼数据
     * @returns 验证结果
     */
    private validateBIPData;
    /**
     * 验证骨骼层级
     * @param bipData BIP骨骼数据
     * @returns 是否有效
     */
    private validateBoneHierarchy;
    /**
     * 应用骨骼到数字人
     * @param skeleton 标准骨骼数据
     * @param digitalHuman 数字人实体
     */
    private applySkeletonToDigitalHuman;
    /**
     * 重新计算蒙皮权重
     * @param digitalHuman 数字人实体
     * @param skeleton 骨骼数据
     */
    private recalculateSkinWeights;
    /**
     * 导入BIP动画
     * @param bipData BIP骨骼数据
     * @param digitalHuman 数字人实体
     * @returns 导入的动画数量
     */
    private importBIPAnimations;
    /**
     * 销毁系统
     */
    dispose(): void;
}
