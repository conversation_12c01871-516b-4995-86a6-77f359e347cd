import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MarketplaceService } from './marketplace.service';
import { SceneTemplate } from '../templates/entities/scene-template.entity';
import { TemplateRating } from '../ratings/entities/template-rating.entity';
import { TemplateShare } from '../sharing/entities/template-share.entity';
import { CacheService } from '../../common/services/cache.service';
import { LoggerService } from '../../common/services/logger.service';

@Module({
  imports: [TypeOrmModule.forFeature([SceneTemplate, TemplateRating, TemplateShare])],
  providers: [
    MarketplaceService,
    CacheService,
    LoggerService,
  ],
  exports: [MarketplaceService, TypeOrmModule],
})
export class MarketplaceModule {}
