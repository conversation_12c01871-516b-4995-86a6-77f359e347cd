import { Injectable, Logger, LoggerService, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as winston from 'winston';
import * as DailyRotateFile from 'winston-daily-rotate-file';

import * as fs from 'fs';
import * as os from 'os';

/**
 * 日志级别
 */
export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'fatal';

/**
 * 日志条目
 */
export interface LogEntry {
  id: string;
  timestamp: number;
  level: LogLevel;
  message: string;
  context?: string;
  data?: any;
  stack?: string;
  hostname?: string;
  pid?: number;
}

/**
 * 增强日志服务
 * 提供高级日志功能，包括日志轮转、多目标输出和结构化日志
 */
@Injectable()
export class EnhancedLoggerService implements LoggerService, OnModuleInit {
  private readonly logger: winston.Logger;
  private readonly nestLogger = new Logger('EnhancedLogger');
  private readonly logDir: string;
  private readonly maxLogFiles: number;
  private readonly maxLogSize: string;
  private readonly enableConsole: boolean;
  private readonly enableFile: boolean;
  private readonly enableJson: boolean;
  private readonly logLevel: string;
  private readonly hostname: string;
  private readonly pid: number;
  private readonly logEntries: LogEntry[] = [];
  private readonly maxLogEntries: number;

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.logDir = this.configService.get<string>('LOG_DIR', 'logs');
    this.maxLogFiles = this.configService.get<number>('MAX_LOG_FILES', 30);
    this.maxLogSize = this.configService.get<string>('MAX_LOG_SIZE', '10m');
    this.enableConsole = this.configService.get<boolean>('ENABLE_CONSOLE_LOGGING', true);
    this.enableFile = this.configService.get<boolean>('ENABLE_FILE_LOGGING', true);
    this.enableJson = this.configService.get<boolean>('ENABLE_JSON_LOGGING', true);
    this.logLevel = this.configService.get<string>('LOG_LEVEL', 'info');
    this.maxLogEntries = this.configService.get<number>('MAX_LOG_ENTRIES', 1000);
    this.hostname = os.hostname();
    this.pid = process.pid;
    
    // 创建日志目录
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
    
    // 创建Winston日志记录器
    this.logger = this.createWinstonLogger();
  }

  /**
   * 模块初始化
   */
  onModuleInit() {
    this.info('增强日志服务已启动', 'EnhancedLoggerService');
  }

  /**
   * 创建Winston日志记录器
   */
  private createWinstonLogger(): winston.Logger {
    const transports: winston.transport[] = [];
    
    // 控制台输出
    if (this.enableConsole) {
      transports.push(
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.colorize(),
            winston.format.printf(({ timestamp, level, message, context, ...meta }) => {
              return `${timestamp} [${level}] [${context || 'App'}] ${message} ${
                Object.keys(meta).length ? JSON.stringify(meta) : ''
              }`;
            }),
          ),
        }),
      );
    }
    
    // 文件输出
    if (this.enableFile) {
      // 普通日志
      transports.push(
        new DailyRotateFile({
          dirname: this.logDir,
          filename: 'application-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          maxSize: this.maxLogSize,
          maxFiles: this.maxLogFiles,
          format: winston.format.combine(
            winston.format.timestamp(),
            this.enableJson ? winston.format.json() : winston.format.simple(),
          ),
        }),
      );
      
      // 错误日志
      transports.push(
        new DailyRotateFile({
          dirname: this.logDir,
          filename: 'error-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          maxSize: this.maxLogSize,
          maxFiles: this.maxLogFiles,
          level: 'error',
          format: winston.format.combine(
            winston.format.timestamp(),
            this.enableJson ? winston.format.json() : winston.format.simple(),
          ),
        }),
      );
    }
    
    return winston.createLogger({
      level: this.logLevel,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json(),
      ),
      defaultMeta: {
        hostname: this.hostname,
        pid: this.pid,
      },
      transports,
    });
  }

  /**
   * 记录调试日志
   */
  debug(message: any, context?: string, ...args: any[]): void {
    this.log('debug', message, context, ...args);
  }

  /**
   * 记录信息日志
   */
  info(message: any, context?: string, ...args: any[]): void {
    this.log('info', message, context, ...args);
  }

  /**
   * 记录警告日志
   */
  warn(message: any, context?: string, ...args: any[]): void {
    this.log('warn', message, context, ...args);
  }

  /**
   * 记录错误日志
   */
  error(message: any, trace?: string, context?: string, ...args: any[]): void {
    this.log('error', message, context, { stack: trace }, ...args);
  }

  /**
   * 记录致命错误日志
   */
  fatal(message: any, trace?: string, context?: string, ...args: any[]): void {
    this.log('fatal', message, context, { stack: trace }, ...args);
  }

  /**
   * 记录日志
   */
  log(level: LogLevel, message: any, context?: string, ...args: any[]): void {
    // 提取元数据
    const meta: any = {};
    if (args.length > 0) {
      if (typeof args[0] === 'object' && args[0] !== null) {
        Object.assign(meta, args[0]);
      }
    }
    
    // 创建日志条目
    const logEntry: LogEntry = {
      id: this.generateId(),
      timestamp: Date.now(),
      level,
      message: this.formatMessage(message),
      context,
      hostname: this.hostname,
      pid: this.pid,
      ...meta,
    };
    
    // 记录到Winston
    this.logger.log(level, logEntry.message, {
      context,
      ...meta,
    });
    
    // 添加到内存中
    this.addLogEntry(logEntry);
    
    // 触发日志事件
    this.eventEmitter.emit('logging.log', logEntry);
  }

  /**
   * 格式化消息
   */
  private formatMessage(message: any): string {
    if (typeof message === 'string') {
      return message;
    }
    
    if (message instanceof Error) {
      return message.message;
    }
    
    try {
      return JSON.stringify(message);
    } catch (e) {
      return String(message);
    }
  }

  /**
   * 生成ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
  }

  /**
   * 添加日志条目
   */
  private addLogEntry(entry: LogEntry): void {
    this.logEntries.push(entry);
    
    // 如果超过最大条目数，删除最旧的条目
    if (this.logEntries.length > this.maxLogEntries) {
      this.logEntries.shift();
    }
  }

  /**
   * 获取最近的日志条目
   */
  getRecentLogs(limit: number = 100, level?: LogLevel): LogEntry[] {
    if (limit <= 0) {
      return [];
    }
    
    let logs = [...this.logEntries];
    
    // 按级别过滤
    if (level) {
      logs = logs.filter(entry => entry.level === level);
    }
    
    // 按时间降序排序
    logs.sort((a, b) => b.timestamp - a.timestamp);
    
    return logs.slice(0, Math.min(limit, logs.length));
  }

  /**
   * 搜索日志
   */
  searchLogs(query: string, limit: number = 100, level?: LogLevel): LogEntry[] {
    if (!query || limit <= 0) {
      return [];
    }
    
    const queryLower = query.toLowerCase();
    
    let logs = [...this.logEntries];
    
    // 按级别过滤
    if (level) {
      logs = logs.filter(entry => entry.level === level);
    }
    
    // 按查询过滤
    logs = logs.filter(entry => {
      return (
        entry.message.toLowerCase().includes(queryLower) ||
        (entry.context && entry.context.toLowerCase().includes(queryLower)) ||
        (entry.stack && entry.stack.toLowerCase().includes(queryLower))
      );
    });
    
    // 按时间降序排序
    logs.sort((a, b) => b.timestamp - a.timestamp);
    
    return logs.slice(0, Math.min(limit, logs.length));
  }

  /**
   * 获取日志统计信息
   */
  getLogStats(): any {
    const stats = {
      total: this.logEntries.length,
      debug: 0,
      info: 0,
      warn: 0,
      error: 0,
      fatal: 0,
      lastLogTime: this.logEntries.length > 0 ? this.logEntries[this.logEntries.length - 1].timestamp : null,
    };
    
    // 统计各级别日志数量
    for (const entry of this.logEntries) {
      stats[entry.level]++;
    }
    
    return stats;
  }
}
