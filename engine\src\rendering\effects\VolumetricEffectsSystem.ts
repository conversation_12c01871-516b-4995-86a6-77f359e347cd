/**
 * 体积效果系统
 * 用于实现体积光和体积雾等效果
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { Debug } from '../../utils/Debug';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';

/**
 * 体积效果系统配置
 */
export interface VolumetricEffectsSystemConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 是否启用体积光 */
  enableVolumetricLight?: boolean;
  /** 是否启用体积雾 */
  enableVolumetricFog?: boolean;
  /** 是否启用调试可视化 */
  enableDebugVisualization?: boolean;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
}

/**
 * 体积光配置
 */
export interface VolumetricLightConfig {
  /** 位置 */
  position: THREE.Vector3;
  /** 方向 */
  direction: THREE.Vector3;
  /** 颜色 */
  color: THREE.Color;
  /** 强度 */
  intensity: number;
  /** 衰减 */
  decay: number;
  /** 长度 */
  length: number;
  /** 角度 */
  angle: number;
  /** 密度 */
  density: number;
}

/**
 * 体积雾配置
 */
export interface VolumetricFogConfig {
  /** 位置 */
  position: THREE.Vector3;
  /** 大小 */
  size: THREE.Vector3;
  /** 颜色 */
  color: THREE.Color;
  /** 密度 */
  density: number;
  /** 是否启用噪声 */
  enableNoise?: boolean;
  /** 噪声尺度 */
  noiseScale?: number;
  /** 噪声速度 */
  noiseSpeed?: number;
}

/**
 * 体积效果系统
 */
export class VolumetricEffectsSystem extends System {
  /** 配置 */
  private config: VolumetricEffectsSystemConfig;
  /** 体积光列表 */
  private volumetricLights: Map<string, { config: VolumetricLightConfig, mesh: THREE.Mesh }> = new Map();
  /** 体积雾列表 */
  private volumetricFogs: Map<string, { config: VolumetricFogConfig, mesh: THREE.Mesh }> = new Map();
  /** 帧计数器 */
  private frameCount: number = 0;
  /** 性能监视器 */
  private performanceMonitor: PerformanceMonitor = PerformanceMonitor.getInstance();
  /** 调试渲染器 */
  private debugRenderer: THREE.Object3D | null = null;
  /** 时间 */
  private time: number = 0;
  /** 体积光材质 */
  private volumetricLightMaterial: THREE.ShaderMaterial | null = null;
  /** 体积雾材质 */
  private volumetricFogMaterial: THREE.ShaderMaterial | null = null;

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config: VolumetricEffectsSystemConfig = {}) {
    super(0); // 传入优先级参数

    // 设置配置
    this.config = {
      enabled: config.enabled !== undefined ? config.enabled : true,
      autoUpdate: config.autoUpdate !== undefined ? config.autoUpdate : true,
      updateFrequency: config.updateFrequency || 1,
      enableVolumetricLight: config.enableVolumetricLight !== undefined ? config.enableVolumetricLight : true,
      enableVolumetricFog: config.enableVolumetricFog !== undefined ? config.enableVolumetricFog : true,
      enableDebugVisualization: config.enableDebugVisualization !== undefined ? config.enableDebugVisualization : false,
      enablePerformanceMonitoring: config.enablePerformanceMonitoring !== undefined ? config.enablePerformanceMonitoring : false
    };

    // 设置世界
    this.setWorld(world);

    // 创建材质
    this.createMaterials();

    // 创建调试渲染器
    if (this.config.enableDebugVisualization) {
      this.createDebugRenderer();
    }
  }

  /**
   * 创建材质
   */
  private createMaterials(): void {
    // 创建体积光材质
    this.volumetricLightMaterial = new THREE.ShaderMaterial({
      uniforms: {
        lightPosition: { value: new THREE.Vector3(0, 0, 0) },
        lightDirection: { value: new THREE.Vector3(0, -1, 0) },
        lightColor: { value: new THREE.Color(0xffffff) },
        lightIntensity: { value: 1.0 },
        lightDecay: { value: 1.0 },
        lightLength: { value: 10.0 },
        lightAngle: { value: Math.PI / 4 },
        density: { value: 0.1 },
        time: { value: 0.0 }
      },
      vertexShader: `
        varying vec3 vWorldPosition;
        varying vec3 vViewPosition;

        void main() {
          vec4 worldPosition = modelMatrix * vec4(position, 1.0);
          vWorldPosition = worldPosition.xyz;

          vec4 viewPosition = viewMatrix * worldPosition;
          vViewPosition = viewPosition.xyz;

          gl_Position = projectionMatrix * viewPosition;
        }
      `,
      fragmentShader: `
        uniform vec3 lightPosition;
        uniform vec3 lightDirection;
        uniform vec3 lightColor;
        uniform float lightIntensity;
        uniform float lightDecay;
        uniform float lightLength;
        uniform float lightAngle;
        uniform float density;
        uniform float time;

        varying vec3 vWorldPosition;
        varying vec3 vViewPosition;

        void main() {
          // 计算光线方向
          vec3 lightDir = normalize(lightDirection);

          // 计算从光源到当前点的向量
          vec3 lightToPos = vWorldPosition - lightPosition;

          // 计算当前点到光源的距离
          float distance = length(lightToPos);

          // 计算当前点到光源方向的夹角
          float angle = acos(dot(normalize(lightToPos), lightDir));

          // 如果在光锥范围内且距离在有效范围内
          if (angle < lightAngle && distance < lightLength) {
            // 计算衰减
            float attenuation = 1.0 - pow(distance / lightLength, lightDecay);

            // 计算角度衰减
            float angleAttenuation = 1.0 - angle / lightAngle;

            // 计算最终强度
            float finalIntensity = lightIntensity * attenuation * angleAttenuation * density;

            // 添加一些噪声
            float noise = sin(vWorldPosition.x * 0.1 + time) *
                          cos(vWorldPosition.y * 0.1 + time) *
                          sin(vWorldPosition.z * 0.1 + time) * 0.5 + 0.5;

            // 设置最终颜色
            gl_FragColor = vec4(lightColor, finalIntensity * noise);
          } else {
            // 不在光锥范围内，完全透明
            gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);
          }
        }
      `,
      transparent: true,
      blending: THREE.AdditiveBlending,
      depthWrite: false,
      side: THREE.DoubleSide
    });

    // 创建体积雾材质
    this.volumetricFogMaterial = new THREE.ShaderMaterial({
      uniforms: {
        fogColor: { value: new THREE.Color(0xffffff) },
        fogDensity: { value: 0.1 },
        time: { value: 0.0 },
        enableNoise: { value: true },
        noiseScale: { value: 0.1 },
        noiseSpeed: { value: 0.1 }
      },
      vertexShader: `
        varying vec3 vWorldPosition;
        varying vec3 vLocalPosition;

        void main() {
          vec4 worldPosition = modelMatrix * vec4(position, 1.0);
          vWorldPosition = worldPosition.xyz;
          vLocalPosition = position;

          gl_Position = projectionMatrix * viewMatrix * worldPosition;
        }
      `,
      fragmentShader: `
        uniform vec3 fogColor;
        uniform float fogDensity;
        uniform float time;
        uniform bool enableNoise;
        uniform float noiseScale;
        uniform float noiseSpeed;

        varying vec3 vWorldPosition;
        varying vec3 vLocalPosition;

        // 简单的噪声函数
        float noise(vec3 p) {
          return sin(p.x * noiseScale + time * noiseSpeed) *
                 cos(p.y * noiseScale + time * noiseSpeed) *
                 sin(p.z * noiseScale + time * noiseSpeed) * 0.5 + 0.5;
        }

        void main() {
          // 计算从中心到边缘的渐变
          vec3 normalizedPos = vLocalPosition / vec3(0.5, 0.5, 0.5);
          float distanceFromCenter = length(normalizedPos);
          float edgeFade = 1.0 - smoothstep(0.0, 1.0, distanceFromCenter);

          // 计算最终密度
          float finalDensity = fogDensity * edgeFade;

          // 如果启用噪声，添加噪声效果
          if (enableNoise) {
            float noiseValue = noise(vWorldPosition);
            finalDensity *= noiseValue;
          }

          // 设置最终颜色
          gl_FragColor = vec4(fogColor, finalDensity);
        }
      `,
      transparent: true,
      blending: THREE.AdditiveBlending,
      depthWrite: false,
      side: THREE.DoubleSide
    });
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    // 如果启用了性能监控，启动性能监视器
    if (this.config.enablePerformanceMonitoring) {
      // PerformanceMonitor 使用 start() 方法而不是 initialize()
      if (typeof this.performanceMonitor.start === 'function') {
        this.performanceMonitor.start();
      }
    }
  }

  /**
   * 创建调试渲染器
   */
  private createDebugRenderer(): void {
    this.debugRenderer = new THREE.Group();
    this.debugRenderer.name = 'VolumetricEffectsDebugRenderer';

    // 添加到场景
    const activeScene = this.world.getActiveScene();
    if (activeScene) {
      // 获取场景的 THREE.Scene 对象
      const threeScene = (activeScene as any).getThreeScene?.() || (activeScene as any).scene;
      if (threeScene) {
        threeScene.add(this.debugRenderer);
      }
    }
  }

  /**
   * 更新调试渲染器
   */
  private updateDebugRenderer(): void {
    if (!this.debugRenderer) return;

    // 清空调试渲染器
    while (this.debugRenderer.children.length > 0) {
      this.debugRenderer.remove(this.debugRenderer.children[0]);
    }

    // 添加体积光调试可视化
    this.volumetricLights.forEach(({ mesh }, id) => {
      // 创建辅助对象
      const helper = new THREE.BoxHelper(mesh);
      helper.name = `VolumetricLight_${id}_Helper`;
      this.debugRenderer!.add(helper);
    });

    // 添加体积雾调试可视化
    this.volumetricFogs.forEach(({ mesh }, id) => {
      // 创建辅助对象
      const helper = new THREE.BoxHelper(mesh);
      helper.name = `VolumetricFog_${id}_Helper`;
      this.debugRenderer!.add(helper);
    });
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.config.enabled || !this.config.autoUpdate) {
      return;
    }

    // 更新时间
    this.time += deltaTime;

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.config.updateFrequency !== 0) {
      return;
    }

    // 如果启用了性能监控，开始计时
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMonitor.beginMeasure('volumetricEffectsUpdate');
    }

    // 更新体积光
    if (this.config.enableVolumetricLight) {
      this.updateVolumetricLights(deltaTime);
    }

    // 更新体积雾
    if (this.config.enableVolumetricFog) {
      this.updateVolumetricFogs(deltaTime);
    }

    // 如果启用了调试可视化，更新调试渲染器
    if (this.config.enableDebugVisualization && this.debugRenderer) {
      this.updateDebugRenderer();
    }

    // 如果启用了性能监控，结束计时
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMonitor.endMeasure('volumetricEffectsUpdate');
    }
  }

  /**
   * 添加体积光
   * @param id 体积光ID
   * @param config 体积光配置
   * @returns 体积光ID
   */
  public addVolumetricLight(id: string, config: VolumetricLightConfig): string {
    // 创建几何体
    const geometry = new THREE.CylinderGeometry(
      Math.tan(config.angle) * config.length,
      0,
      config.length,
      32,
      1,
      true
    );
    geometry.applyMatrix4(new THREE.Matrix4().makeRotationX(Math.PI / 2));

    // 创建材质
    if (!this.volumetricLightMaterial) {
      throw new Error('体积光材质未初始化');
    }
    const material = this.volumetricLightMaterial.clone();
    material.uniforms.lightPosition.value.copy(config.position);
    material.uniforms.lightDirection.value.copy(config.direction.clone().normalize());
    material.uniforms.lightColor.value.copy(config.color);
    material.uniforms.lightIntensity.value = config.intensity;
    material.uniforms.lightDecay.value = config.decay;
    material.uniforms.lightLength.value = config.length;
    material.uniforms.lightAngle.value = config.angle;
    material.uniforms.density.value = config.density;

    // 创建网格
    const mesh = new THREE.Mesh(geometry, material);
    mesh.position.copy(config.position);
    mesh.lookAt(config.position.clone().add(config.direction));
    mesh.name = `VolumetricLight_${id}`;

    // 添加到场景
    const activeScene = this.world.getActiveScene();
    if (activeScene) {
      // 获取场景的 THREE.Scene 对象
      const threeScene = (activeScene as any).getThreeScene?.() || (activeScene as any).scene;
      if (threeScene) {
        threeScene.add(mesh);
      }
    }

    // 添加到体积光列表
    this.volumetricLights.set(id, { config, mesh });

    return id;
  }

  /**
   * 移除体积光
   * @param id 体积光ID
   */
  public removeVolumetricLight(id: string): void {
    // 获取体积光
    const volumetricLight = this.volumetricLights.get(id);
    if (!volumetricLight) return;

    // 从场景中移除
    const activeScene = this.world.getActiveScene();
    if (activeScene) {
      // 获取场景的 THREE.Scene 对象
      const threeScene = (activeScene as any).getThreeScene?.() || (activeScene as any).scene;
      if (threeScene) {
        threeScene.remove(volumetricLight.mesh);
      }
    }

    // 释放资源
    (volumetricLight.mesh.geometry as any).dispose();
    (volumetricLight.mesh.material as THREE.Material).dispose();

    // 从体积光列表中移除
    this.volumetricLights.delete(id);
  }

  /**
   * 更新体积光
   * @param _deltaTime 帧间隔时间（秒）
   */
  private updateVolumetricLights(_deltaTime: number): void {
    // 遍历所有体积光
    this.volumetricLights.forEach(({ config, mesh }) => {
      // 更新时间
      const material = mesh.material as THREE.ShaderMaterial;
      material.uniforms.time.value = this.time;

      // 更新相机位置
      const activeScene = this.world.getActiveScene();
      const camera = activeScene?.getActiveCamera();
      if (camera) {
        // 使体积光始终面向相机
        const cameraPosition = camera.getPosition();
        const lightPosition = config.position.clone();
        const lightDirection = config.direction.clone().normalize();

        // 计算相机到光源的向量
        const cameraToLight = lightPosition.clone().sub(cameraPosition);

        // 计算相机到光源的距离
        const distanceToLight = cameraToLight.length();

        // 如果相机在光源附近，调整体积光的朝向
        if (distanceToLight < config.length * 2) {
          // 计算相机到光源方向的夹角
          const angle = cameraToLight.normalize().dot(lightDirection);

          // 如果相机在光锥后方，调整体积光的朝向
          if (angle < 0) {
            // 计算垂直于光源方向和相机到光源向量的轴
            const axis = new THREE.Vector3().crossVectors(lightDirection, cameraToLight).normalize();

            // 如果轴有效，旋转体积光
            if (axis.lengthSq() > 0.001) {
              // 旋转体积光，使其始终可见
              mesh.quaternion.setFromAxisAngle(axis, Math.PI / 2);
            }
          }
        }
      }
    });
  }

  /**
   * 添加体积雾
   * @param id 体积雾ID
   * @param config 体积雾配置
   * @returns 体积雾ID
   */
  public addVolumetricFog(id: string, config: VolumetricFogConfig): string {
    // 创建几何体
    const geometry = new THREE.BoxGeometry(
      config.size.x,
      config.size.y,
      config.size.z
    );

    // 创建材质
    if (!this.volumetricFogMaterial) {
      throw new Error('体积雾材质未初始化');
    }
    const material = this.volumetricFogMaterial.clone();
    material.uniforms.fogColor.value.copy(config.color);
    material.uniforms.fogDensity.value = config.density;
    material.uniforms.enableNoise.value = config.enableNoise !== undefined ? config.enableNoise : true;
    material.uniforms.noiseScale.value = config.noiseScale || 0.1;
    material.uniforms.noiseSpeed.value = config.noiseSpeed || 0.1;

    // 创建网格
    const mesh = new THREE.Mesh(geometry, material);
    mesh.position.copy(config.position);
    mesh.name = `VolumetricFog_${id}`;

    // 添加到场景
    const activeScene = this.world.getActiveScene();
    if (activeScene) {
      // 获取场景的 THREE.Scene 对象
      const threeScene = (activeScene as any).getThreeScene?.() || (activeScene as any).scene;
      if (threeScene) {
        threeScene.add(mesh);
      }
    }

    // 添加到体积雾列表
    this.volumetricFogs.set(id, { config, mesh });

    return id;
  }

  /**
   * 移除体积雾
   * @param id 体积雾ID
   */
  public removeVolumetricFog(id: string): void {
    // 获取体积雾
    const volumetricFog = this.volumetricFogs.get(id);
    if (!volumetricFog) return;

    // 从场景中移除
    const activeScene = this.world.getActiveScene();
    if (activeScene) {
      // 获取场景的 THREE.Scene 对象
      const threeScene = (activeScene as any).getThreeScene?.() || (activeScene as any).scene;
      if (threeScene) {
        threeScene.remove(volumetricFog.mesh);
      }
    }

    // 释放资源
    (volumetricFog.mesh.geometry as any).dispose();
    (volumetricFog.mesh.material as THREE.Material).dispose();

    // 从体积雾列表中移除
    this.volumetricFogs.delete(id);
  }

  /**
   * 更新体积雾
   * @param _deltaTime 帧间隔时间（秒）
   */
  private updateVolumetricFogs(_deltaTime: number): void {
    // 遍历所有体积雾
    this.volumetricFogs.forEach(({ mesh }) => {
      // 更新时间
      const material = mesh.material as THREE.ShaderMaterial;
      material.uniforms.time.value = this.time;
    });
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 停止性能监控
    if (this.config.enablePerformanceMonitoring) {
      if (typeof this.performanceMonitor.stop === 'function') {
        this.performanceMonitor.stop();
      }
    }

    // 清理所有体积光
    for (const id of Array.from(this.volumetricLights.keys())) {
      this.removeVolumetricLight(id);
    }

    // 清理所有体积雾
    for (const id of Array.from(this.volumetricFogs.keys())) {
      this.removeVolumetricFog(id);
    }

    // 清理调试渲染器
    if (this.debugRenderer) {
      const activeScene = this.world.getActiveScene();
      if (activeScene) {
        const threeScene = (activeScene as any).getThreeScene?.() || (activeScene as any).scene;
        if (threeScene) {
          threeScene.remove(this.debugRenderer);
        }
      }
      this.debugRenderer = null;
    }

    // 清理材质
    if (this.volumetricLightMaterial) {
      (this.volumetricLightMaterial as any).dispose();
      this.volumetricLightMaterial = null;
    }

    if (this.volumetricFogMaterial) {
      (this.volumetricFogMaterial as any).dispose();
      this.volumetricFogMaterial = null;
    }

    // 调用父类的销毁方法
    super.dispose();
  }
}
