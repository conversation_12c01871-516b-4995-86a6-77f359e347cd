/**
 * 湖泊生成器
 * 用于生成湖泊水体
 */
import * as THREE from 'three';
import { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { WaterBodyComponent } from './WaterBodyComponent';
import { WaterPresets, WaterPresetType } from './WaterPresets';
import { TerrainComponent } from '../../terrain/components/TerrainComponent';
import { Debug } from '../../utils/Debug';

/**
 * 湖泊形状类型
 */
export enum LakeShapeType {
  /** 圆形 */
  CIRCLE = 'circle',
  /** 椭圆形 */
  ELLIPSE = 'ellipse',
  /** 不规则形状 */
  IRREGULAR = 'irregular',
  /** 自定义形状 */
  CUSTOM = 'custom'
}

/**
 * 湖泊生成配置
 */
export interface LakeGeneratorConfig {
  /** 湖泊位置 */
  position: THREE.Vector3;
  /** 湖泊尺寸 */
  size: {
    width: number;
    depth: number;
  };
  /** 湖泊深度 */
  depth: number;
  /** 湖泊形状类型 */
  shapeType?: LakeShapeType;
  /** 湖泊形状参数（用于不规则形状） */
  shapeParams?: {
    /** 不规则度（0-1） */
    irregularity?: number;
    /** 种子 */
    seed?: number;
    /** 控制点数量 */
    controlPoints?: number;
  };
  /** 自定义形状点（用于自定义形状） */
  customShapePoints?: THREE.Vector2[];
  /** 分辨率 */
  resolution?: number;
  /** 是否跟随地形 */
  followTerrain?: boolean;
  /** 地形偏移 */
  terrainOffset?: number;
  /** 是否生成湖岸 */
  generateShore?: boolean;
  /** 湖岸宽度 */
  shoreWidth?: number;
  /** 湖岸高度 */
  shoreHeight?: number;
  /** 是否生成湖床 */
  generateLakeBed?: boolean;
  /** 湖床材质 */
  lakeBedMaterial?: any;
  /** 是否生成水下植被 */
  generateUnderwaterVegetation?: boolean;
  /** 水下植被密度 */
  underwaterVegetationDensity?: number;
  /** 是否生成水下粒子 */
  generateUnderwaterParticles?: boolean;
  /** 水下粒子数量 */
  underwaterParticleCount?: number;
}

/**
 * 湖泊生成器
 */
export class LakeGenerator {
  /** 世界 */
  private world: World;
  /** 配置 */
  private config: LakeGeneratorConfig;
  /** 地形组件 */
  private terrainComponent: TerrainComponent | null = null;
  /** 湖泊实体 */
  private lakeEntity: Entity | null = null;
  /** 湖泊水体组件 */
  private lakeWaterBody: WaterBodyComponent | null = null;
  /** 湖泊几何体 */
  private lakeGeometry: THREE.BufferGeometry | null = null;
  /** 湖岸几何体 */
  private shoreGeometry: THREE.BufferGeometry | null = null;
  /** 湖床几何体 */
  private lakeBedGeometry: THREE.BufferGeometry | null = null;
  /** 形状点 */
  private shapePoints: THREE.Vector2[] = [];
  /** 粒子更新函数 */
  private particleUpdateFunction: ((deltaTime: number) => void) | null = null;

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config: LakeGeneratorConfig) {
    this.world = world;
    this.config = {
      position: config.position,
      size: config.size,
      depth: config.depth,
      shapeType: config.shapeType || LakeShapeType.CIRCLE,
      shapeParams: {
        irregularity: config.shapeParams?.irregularity || 0.3,
        seed: config.shapeParams?.seed || 12345,
        controlPoints: config.shapeParams?.controlPoints || 8
      },
      customShapePoints: config.customShapePoints || [],
      resolution: config.resolution || 32,
      followTerrain: config.followTerrain !== undefined ? config.followTerrain : true,
      terrainOffset: config.terrainOffset || 0.5,
      generateShore: config.generateShore !== undefined ? config.generateShore : true,
      shoreWidth: config.shoreWidth || 5.0,
      shoreHeight: config.shoreHeight || 1.0,
      generateLakeBed: config.generateLakeBed !== undefined ? config.generateLakeBed : true,
      generateUnderwaterVegetation: config.generateUnderwaterVegetation !== undefined ? config.generateUnderwaterVegetation : true,
      underwaterVegetationDensity: config.underwaterVegetationDensity || 0.1,
      generateUnderwaterParticles: config.generateUnderwaterParticles !== undefined ? config.generateUnderwaterParticles : true,
      underwaterParticleCount: config.underwaterParticleCount || 1000
    };

    // 生成形状点
    this.generateShapePoints();
  }

  /**
   * 设置地形组件
   * @param terrainComponent 地形组件
   */
  public setTerrainComponent(terrainComponent: TerrainComponent): void {
    this.terrainComponent = terrainComponent;
  }

  /**
   * 生成湖泊
   * @returns 湖泊实体
   */
  public generate(): Entity {
    // 创建湖泊实体
    this.lakeEntity = new Entity('lake');

    // 创建湖泊几何体
    this.createLakeGeometry();

    // 创建湖泊水体
    this.createLakeWaterBody();

    // 如果需要生成湖岸，创建湖岸
    if (this.config.generateShore) {
      this.createShore();
    }

    // 如果需要生成湖床，创建湖床
    if (this.config.generateLakeBed) {
      this.createLakeBed();
    }

    // 如果需要生成水下植被，创建水下植被
    if (this.config.generateUnderwaterVegetation) {
      this.createUnderwaterVegetation();
    }

    // 如果需要生成水下粒子，创建水下粒子
    if (this.config.generateUnderwaterParticles) {
      this.createUnderwaterParticles();
    }

    // 添加到世界
    this.world.addEntity(this.lakeEntity!);

    return this.lakeEntity!;
  }

  /**
   * 生成形状点
   */
  private generateShapePoints(): void {
    switch (this.config.shapeType) {
      case LakeShapeType.CIRCLE:
        this.generateCircleShapePoints();
        break;
      case LakeShapeType.ELLIPSE:
        this.generateEllipseShapePoints();
        break;
      case LakeShapeType.IRREGULAR:
        this.generateIrregularShapePoints();
        break;
      case LakeShapeType.CUSTOM:
        this.shapePoints = this.config.customShapePoints!;
        break;
    }
  }

  /**
   * 生成圆形形状点
   */
  private generateCircleShapePoints(): void {
    const radius = Math.min(this.config.size.width, this.config.size.depth) / 2;
    const segments = this.config.resolution!;

    for (let i = 0; i < segments; i++) {
      const angle = (i / segments) * Math.PI * 2;
      const x = Math.cos(angle) * radius;
      const y = Math.sin(angle) * radius;

      this.shapePoints.push(new THREE.Vector2(x, y));
    }
  }

  /**
   * 生成椭圆形状点
   */
  private generateEllipseShapePoints(): void {
    const radiusX = this.config.size.width / 2;
    const radiusY = this.config.size.depth / 2;
    const segments = this.config.resolution!;

    for (let i = 0; i < segments; i++) {
      const angle = (i / segments) * Math.PI * 2;
      const x = Math.cos(angle) * radiusX;
      const y = Math.sin(angle) * radiusY;

      this.shapePoints.push(new THREE.Vector2(x, y));
    }
  }

  /**
   * 生成不规则形状点
   */
  private generateIrregularShapePoints(): void {
    // 先生成椭圆形状点
    this.generateEllipseShapePoints();

    // 获取不规则度
    const irregularity = this.config.shapeParams!.irregularity!;

    // 设置随机种子
    const seed = this.config.shapeParams!.seed!;
    const random = this.seededRandom(seed);

    // 扰动形状点
    for (let i = 0; i < this.shapePoints.length; i++) {
      const point = this.shapePoints[i];
      const distance = point.length();

      // 随机扰动
      const disturbance = (random() * 2 - 1) * irregularity * distance * 0.3;

      // 应用扰动
      const direction = point.clone().normalize();
      point.add(direction.multiplyScalar(disturbance));
    }
  }

  /**
   * 基于种子的随机数生成器
   * @param seed 种子
   * @returns 随机数生成函数
   */
  private seededRandom(seed: number): () => number {
    let s = seed;
    return function() {
      s = Math.sin(s) * 10000;
      return s - Math.floor(s);
    };
  }

  /**
   * 创建湖泊几何体
   */
  private createLakeGeometry(): void {
    // 创建几何体
    const geometry = new THREE.BufferGeometry();

    // 创建顶点和索引
    const vertices: number[] = [];
    const indices: number[] = [];
    const uvs: number[] = [];

    // 添加中心点
    const centerX = (this.config as any).getPosition().x;
    const centerY = (this.config as any).getPosition().y;
    const centerZ = (this.config as any).getPosition().z;

    // 如果需要跟随地形，调整高度
    let centerHeight = centerY;
    if (this.config.followTerrain && this.terrainComponent) {
      const height = this.getTerrainHeight(centerX, centerZ);
      if (height !== null) {
        centerHeight = height + this.config.terrainOffset!;
      }
    }

    // 添加中心点
    vertices.push(centerX, centerHeight, centerZ);
    uvs.push(0.5, 0.5);

    // 添加边缘点
    for (let i = 0; i < this.shapePoints.length; i++) {
      const point = this.shapePoints[i];
      const x = centerX + point.x;
      const z = centerZ + point.y;

      // 如果需要跟随地形，调整高度
      let y = centerHeight;
      if (this.config.followTerrain && this.terrainComponent) {
        const height = this.getTerrainHeight(x, z);
        if (height !== null) {
          y = height + this.config.terrainOffset!;
        }
      }

      // 添加顶点
      vertices.push(x, y, z);

      // 添加UV
      const u = point.x / this.config.size.width + 0.5;
      const v = point.y / this.config.size.depth + 0.5;
      uvs.push(u, v);

      // 添加索引（创建三角形）
      if (i < this.shapePoints.length - 1) {
        indices.push(0, i + 1, i + 2);
      } else {
        indices.push(0, i + 1, 1);
      }
    }

    // 设置几何体属性
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
    geometry.setIndex(indices);

    // 计算法线
    geometry.computeVertexNormals();

    // 保存几何体
    this.lakeGeometry = geometry;
  }

  /**
   * 创建湖泊水体
   */
  private createLakeWaterBody(): void {
    if (!this.lakeGeometry) {
      Debug.error('LakeGenerator', '湖泊几何体未创建，无法创建湖泊水体');
      return;
    }

    // 创建湖泊水体组件，使用预设配置
    this.lakeWaterBody = WaterPresets.createPreset(this.lakeEntity!, {
      type: WaterPresetType.LAKE
    });
  }

  /**
   * 创建湖岸
   */
  private createShore(): void {
    if (!this.shapePoints || this.shapePoints.length === 0) {
      Debug.error('LakeGenerator', '形状点未创建，无法创建湖岸');
      return;
    }

    // 创建几何体
    const geometry = new THREE.BufferGeometry();

    // 创建顶点和索引
    const vertices: number[] = [];
    const indices: number[] = [];
    const uvs: number[] = [];

    // 获取中心点
    const centerX = (this.config as any).getPosition().x;
    const centerY = (this.config as any).getPosition().y;
    const centerZ = (this.config as any).getPosition().z;

    // 创建内外环
    const innerPoints: THREE.Vector3[] = [];
    const outerPoints: THREE.Vector3[] = [];

    // 遍历形状点
    for (let i = 0; i < this.shapePoints.length; i++) {
      const point = this.shapePoints[i];

      // 计算内环点
      const innerX = centerX + point.x;
      const innerZ = centerZ + point.y;

      // 如果需要跟随地形，调整高度
      let innerY = centerY;
      if (this.config.followTerrain && this.terrainComponent) {
        const height = this.getTerrainHeight(innerX, innerZ);
        if (height !== null) {
          innerY = height + this.config.terrainOffset!;
        }
      }

      // 添加内环点
      innerPoints.push(new THREE.Vector3(innerX, innerY, innerZ));

      // 计算外环点
      const direction = new THREE.Vector2(point.x, point.y).normalize();
      const outerPoint = point.clone().add(direction.multiplyScalar(this.config.shoreWidth!));
      const outerX = centerX + outerPoint.x;
      const outerZ = centerZ + outerPoint.y;

      // 如果需要跟随地形，调整高度
      let outerY = centerY;
      if (this.config.followTerrain && this.terrainComponent) {
        const height = this.getTerrainHeight(outerX, outerZ);
        if (height !== null) {
          outerY = height;
        }
      }

      // 添加外环点
      outerPoints.push(new THREE.Vector3(outerX, outerY, outerZ));
    }

    // 添加顶点和索引
    for (let i = 0; i < innerPoints.length; i++) {
      const innerPoint = innerPoints[i];
      const outerPoint = outerPoints[i];

      // 添加内环点
      vertices.push(innerPoint.x, innerPoint.y + this.config.shoreHeight!, innerPoint.z);

      // 添加外环点
      vertices.push(outerPoint.x, outerPoint.y, outerPoint.z);

      // 添加UV
      const u = i / innerPoints.length;
      uvs.push(0, u);
      uvs.push(1, u);

      // 添加索引（创建三角形）
      const nextIndex = (i + 1) % innerPoints.length;
      const base = i * 2;
      const nextBase = nextIndex * 2;

      indices.push(base, base + 1, nextBase);
      indices.push(base + 1, nextBase + 1, nextBase);
    }

    // 设置几何体属性
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
    geometry.setIndex(indices);

    // 计算法线
    geometry.computeVertexNormals();

    // 保存几何体
    this.shoreGeometry = geometry;

    // 创建湖岸材质
    const material = new THREE.MeshStandardMaterial({
      color: 0x8B4513,
      roughness: 0.8,
      metalness: 0.1
    });

    // 创建湖岸网格
    const mesh = new THREE.Mesh(this.shoreGeometry, material);

    // 添加到场景（通过渲染组件）
    const renderComponent = this.lakeEntity!.getComponent('RenderComponent') as any as any;
    if (renderComponent) {
      renderComponent.addObject(mesh);
    }
  }

  /**
   * 创建湖床
   */
  private createLakeBed(): void {
    if (!this.shapePoints || this.shapePoints.length === 0) {
      Debug.error('LakeGenerator', '形状点未创建，无法创建湖床');
      return;
    }

    // 创建几何体
    const geometry = new THREE.BufferGeometry();

    // 创建顶点和索引
    const vertices: number[] = [];
    const indices: number[] = [];
    const uvs: number[] = [];

    // 获取中心点
    const centerX = (this.config as any).getPosition().x;
    const centerY = (this.config as any).getPosition().y;
    const centerZ = (this.config as any).getPosition().z;

    // 如果需要跟随地形，调整高度
    let centerHeight = centerY;
    if (this.config.followTerrain && this.terrainComponent) {
      const height = this.getTerrainHeight(centerX, centerZ);
      if (height !== null) {
        centerHeight = height + this.config.terrainOffset!;
      }
    }

    // 添加中心点（底部）
    vertices.push(centerX, centerHeight - this.config.depth, centerZ);
    uvs.push(0.5, 0.5);

    // 添加边缘点
    for (let i = 0; i < this.shapePoints.length; i++) {
      const point = this.shapePoints[i];
      const x = centerX + point.x;
      const z = centerZ + point.y;

      // 如果需要跟随地形，调整高度
      let y = centerHeight;
      if (this.config.followTerrain && this.terrainComponent) {
        const height = this.getTerrainHeight(x, z);
        if (height !== null) {
          y = height + this.config.terrainOffset!;
        }
      }

      // 添加顶点（底部）
      vertices.push(x, y - this.config.depth, z);

      // 添加UV
      const u = point.x / this.config.size.width + 0.5;
      const v = point.y / this.config.size.depth + 0.5;
      uvs.push(u, v);

      // 添加索引（创建三角形）
      if (i < this.shapePoints.length - 1) {
        indices.push(0, i + 1, i + 2);
      } else {
        indices.push(0, i + 1, 1);
      }
    }

    // 设置几何体属性
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
    geometry.setIndex(indices);

    // 计算法线
    geometry.computeVertexNormals();

    // 保存几何体
    this.lakeBedGeometry = geometry;

    // 创建湖床材质
    const material = this.config.lakeBedMaterial || new THREE.MeshStandardMaterial({
      color: 0x7B5E3D,
      roughness: 0.9,
      metalness: 0.1
    });

    // 创建湖床网格
    const mesh = new THREE.Mesh(this.lakeBedGeometry, material);

    // 添加到场景（通过渲染组件）
    const renderComponent = this.lakeEntity!.getComponent('RenderComponent') as any as any;
    if (renderComponent) {
      renderComponent.addObject(mesh);
    }
  }

  /**
   * 创建水下植被
   */
  private createUnderwaterVegetation(): void {
    if (!this.lakeGeometry) {
      Debug.error('LakeGenerator', '湖泊几何体未创建，无法创建水下植被');
      return;
    }

    // 获取中心点
    const centerX = (this.config as any).getPosition().x;
    const centerY = (this.config as any).getPosition().y;
    const centerZ = (this.config as any).getPosition().z;

    // 如果需要跟随地形，调整高度
    let centerHeight = centerY;
    if (this.config.followTerrain && this.terrainComponent) {
      const height = this.getTerrainHeight(centerX, centerZ);
      if (height !== null) {
        centerHeight = height + this.config.terrainOffset!;
      }
    }

    // 计算植被数量
    const area = Math.PI * this.config.size.width * this.config.size.depth / 4;
    const vegetationCount = Math.floor(area * this.config.underwaterVegetationDensity!);

    // 创建随机数生成器
    const random = this.seededRandom(this.config.shapeParams!.seed! + 1);

    // 创建植被
    for (let i = 0; i < vegetationCount; i++) {
      // 随机位置
      const angle = random() * Math.PI * 2;
      const distance = Math.sqrt(random()) * Math.min(this.config.size.width, this.config.size.depth) / 2 * 0.8;

      const x = centerX + Math.cos(angle) * distance;
      const z = centerZ + Math.sin(angle) * distance;

      // 如果需要跟随地形，调整高度
      let y = centerHeight - this.config.depth + random() * this.config.depth * 0.5;
      if (this.config.followTerrain && this.terrainComponent) {
        const height = this.getTerrainHeight(x, z);
        if (height !== null) {
          y = height + this.config.terrainOffset! - this.config.depth + random() * this.config.depth * 0.5;
        }
      }

      // 创建植被几何体
      const geometry = new THREE.CylinderGeometry(0.1, 0.5, 1 + random() * 2, 4, 1);

      // 创建植被材质
      const material = new THREE.MeshStandardMaterial({
        color: new THREE.Color(0.1 + random() * 0.2, 0.5 + random() * 0.3, 0.1 + random() * 0.2),
        roughness: 0.8,
        metalness: 0.1
      });

      // 创建植被网格
      const mesh = new THREE.Mesh(geometry, material);

      // 设置位置
      mesh.position.set(x, y, z);

      // 随机旋转
      (mesh as any).setRotationQuaternion(random() * 0.3, random() * Math.PI * 2, random() * 0.3);

      // 添加到场景（通过渲染组件）
      const renderComponent = this.lakeEntity!.getComponent('RenderComponent') as any as any;
      if (renderComponent) {
        renderComponent.addObject(mesh);
      }
    }
  }

  /**
   * 创建水下粒子
   */
  private createUnderwaterParticles(): void {
    if (!this.lakeWaterBody) {
      Debug.error('LakeGenerator', '湖泊水体未创建，无法创建水下粒子');
      return;
    }

    // 获取中心点
    const centerX = (this.config as any).getPosition().x;
    const centerY = (this.config as any).getPosition().y;
    const centerZ = (this.config as any).getPosition().z;

    // 如果需要跟随地形，调整高度
    let centerHeight = centerY;
    if (this.config.followTerrain && this.terrainComponent) {
      const height = this.getTerrainHeight(centerX, centerZ);
      if (height !== null) {
        centerHeight = height + this.config.terrainOffset!;
      }
    }

    // 创建粒子系统
    const particleSystem = new THREE.Points(
      new THREE.BufferGeometry(),
      new THREE.PointsMaterial({
        color: 0xFFFFFF,
        size: 0.05,
        transparent: true,
        opacity: 0.3,
        map: new THREE.TextureLoader().load('/assets/textures/particle.png'),
        blending: THREE.AdditiveBlending,
        depthWrite: false
      })
    );

    // 创建随机数生成器
    const random = this.seededRandom(this.config.shapeParams!.seed! + 2);

    // 创建粒子位置
    const positions: number[] = [];
    const velocities: number[] = [];
    const lifetimes: number[] = [];

    // 创建粒子
    for (let i = 0; i < this.config.underwaterParticleCount!; i++) {
      // 随机位置
      const angle = random() * Math.PI * 2;
      const distance = Math.sqrt(random()) * Math.min(this.config.size.width, this.config.size.depth) / 2 * 0.8;

      const x = centerX + Math.cos(angle) * distance;
      const z = centerZ + Math.sin(angle) * distance;

      // 随机高度
      const y = centerHeight - this.config.depth + random() * this.config.depth;

      // 添加位置
      positions.push(x, y, z);

      // 添加速度（缓慢上升）
      const vx = (random() * 2 - 1) * 0.01;
      const vy = 0.01 + random() * 0.02;
      const vz = (random() * 2 - 1) * 0.01;
      velocities.push(vx, vy, vz);

      // 添加生命周期
      lifetimes.push(random() * 5 + 5);
    }

    // 设置几何体属性
    particleSystem.geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
    particleSystem.geometry.setAttribute('velocity', new THREE.Float32BufferAttribute(velocities, 3));
    particleSystem.geometry.setAttribute('lifetime', new THREE.Float32BufferAttribute(lifetimes, 1));

    // 添加到场景（通过渲染组件）
    const renderComponent = this.lakeEntity!.getComponent('RenderComponent') as any as any;
    if (renderComponent) {
      renderComponent.addObject(particleSystem);
    }

    // 添加更新函数
    this.particleUpdateFunction = (deltaTime: number) => {
      const positions = particleSystem.geometry.getAttribute('position');
      const velocities = particleSystem.geometry.getAttribute('velocity');
      const lifetimes = particleSystem.geometry.getAttribute('lifetime');

      for (let i = 0; i < positions.count; i++) {
        // 更新位置
        positions.setXYZ(
          i,
          positions.getX(i) + velocities.getX(i) * deltaTime,
          positions.getY(i) + velocities.getY(i) * deltaTime,
          positions.getZ(i) + velocities.getZ(i) * deltaTime
        );

        // 更新生命周期
        lifetimes.setX(i, lifetimes.getX(i) - deltaTime);

        // 如果生命周期结束或者粒子超出水面，重置粒子
        if (lifetimes.getX(i) <= 0 || positions.getY(i) > centerHeight) {
          // 随机位置
          const angle = random() * Math.PI * 2;
          const distance = Math.sqrt(random()) * Math.min(this.config.size.width, this.config.size.depth) / 2 * 0.8;

          const x = centerX + Math.cos(angle) * distance;
          const z = centerZ + Math.sin(angle) * distance;

          // 随机高度
          const y = centerHeight - this.config.depth + random() * this.config.depth * 0.2;

          // 重置位置
          positions.setXYZ(i, x, y, z);

          // 重置速度
          const vx = (random() * 2 - 1) * 0.01;
          const vy = 0.01 + random() * 0.02;
          const vz = (random() * 2 - 1) * 0.01;
          velocities.setXYZ(i, vx, vy, vz);

          // 重置生命周期
          lifetimes.setX(i, random() * 5 + 5);
        }
      }

      // 更新几何体
      positions.needsUpdate = true;
      lifetimes.needsUpdate = true;
    };

    // 注意：粒子更新需要在渲染循环中手动调用 updateParticles 函数
    // 这里暂时注释掉，因为 WaterBodyComponent 没有 addUpdateCallback 方法
    // this.lakeWaterBody.addUpdateCallback(updateParticles);
  }

  /**
   * 获取粒子更新函数
   * @returns 粒子更新函数，如果没有粒子则返回null
   */
  public getParticleUpdateFunction(): ((deltaTime: number) => void) | null {
    return this.particleUpdateFunction;
  }

  /**
   * 获取指定位置的地形高度
   * @param x X坐标
   * @param z Z坐标
   * @returns 地形高度，如果无法获取则返回null
   */
  private getTerrainHeight(x: number, z: number): number | null {
    if (!this.terrainComponent) {
      return null;
    }

    // 调用地形组件的获取高度方法
    return this.terrainComponent.getHeight(x, z);
  }
}
