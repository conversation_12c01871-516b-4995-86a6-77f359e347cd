import { System } from '../../core/System';
import { type World  } from '../../core/World';
import { type Entity  } from '../../core/Entity';
import { EventEmitter } from 'events';
import { FacialAnimationComponent, FacialExpressionType, VisemeType } from '../components/FacialAnimationComponent';
import * as THREE from 'three';

/**
 * 表情预设类型
 */
export enum ExpressionPresetType {
  BASIC = 'basic',
  COMPLEX = 'complex',
  CULTURAL = 'cultural',
  EMOTIONAL = 'emotional',
  MICRO = 'micro',
  DYNAMIC = 'dynamic'
}

/**
 * 表情强度级别
 */
export enum ExpressionIntensity {
  SUBTLE = 'subtle',
  MILD = 'mild',
  MODERATE = 'moderate',
  STRONG = 'strong',
  EXTREME = 'extreme'
}

/**
 * 表情混合模式
 */
export enum ExpressionBlendMode {
  REPLACE = 'replace',
  ADD = 'add',
  MULTIPLY = 'multiply',
  OVERLAY = 'overlay',
  SMOOTH = 'smooth'
}

/**
 * 表情关键帧
 */
export interface ExpressionKeyframe {
  /** 时间 */
  time: number;
  /** 表情类型 */
  expression: FacialExpressionType;
  /** 权重 */
  weight: number;
  /** 持续时间 */
  duration?: number;
  /** 缓动函数 */
  easing?: string;
  /** 额外参数 */
  parameters?: Map<string, number>;
}

/**
 * 表情序列
 */
export interface ExpressionSequence {
  /** 序列ID */
  id: string;
  /** 序列名称 */
  name: string;
  /** 关键帧 */
  keyframes: ExpressionKeyframe[];
  /** 总时长 */
  duration: number;
  /** 是否循环 */
  loop: boolean;
  /** 混合模式 */
  blendMode: ExpressionBlendMode;
  /** 标签 */
  tags: string[];
}

/**
 * 表情预设
 */
export interface ExpressionPreset {
  /** 预设ID */
  id: string;
  /** 预设名称 */
  name: string;
  /** 预设类型 */
  type: ExpressionPresetType;
  /** 描述 */
  description: string;
  /** 表情组合 */
  expressions: Array<{
    type: FacialExpressionType;
    weight: number;
    delay?: number;
    duration?: number;
  }>;
  /** 强度级别 */
  intensity: ExpressionIntensity;
  /** 文化背景 */
  culture?: string;
  /** 标签 */
  tags: string[];
  /** 预览图 */
  thumbnail?: string;
}

/**
 * 动作预设
 */
export interface ActionPreset {
  /** 预设ID */
  id: string;
  /** 预设名称 */
  name: string;
  /** 描述 */
  description: string;
  /** 表情序列 */
  expressionSequence: ExpressionSequence;
  /** 身体动作 */
  bodyAnimation?: any;
  /** 音频 */
  audio?: string;
  /** 持续时间 */
  duration: number;
  /** 标签 */
  tags: string[];
}

/**
 * 实时控制参数
 */
export interface RealTimeControlParams {
  /** 表情强度 */
  intensity: number;
  /** 混合速度 */
  blendSpeed: number;
  /** 随机变化 */
  randomVariation: number;
  /** 微表情频率 */
  microExpressionFrequency: number;
  /** 眨眼频率 */
  blinkFrequency: number;
  /** 呼吸强度 */
  breathingIntensity: number;
}

/**
 * 高级表情和动作系统配置
 */
export interface AdvancedExpressionSystemConfig {
  /** 是否启用微表情 */
  enableMicroExpressions: boolean;
  /** 是否启用自动眨眼 */
  enableAutoBlink: boolean;
  /** 是否启用呼吸动画 */
  enableBreathing: boolean;
  /** 是否启用情感分析 */
  enableEmotionAnalysis: boolean;
  /** 是否启用文化适应 */
  enableCulturalAdaptation: boolean;
  /** 默认混合速度 */
  defaultBlendSpeed: number;
  /** 是否启用调试 */
  debug: boolean;
}

/**
 * 高级表情和动作系统
 * 实现高级表情控制和动作管理系统，支持表情预设、动作混合和实时控制
 */
export class AdvancedExpressionSystem extends System {
  /** 系统类型 */
  public static readonly TYPE = 'AdvancedExpressionSystem';

  /** 事件发射器 */
  private eventEmitter: EventEmitter;

  /** 系统配置 */
  private config: AdvancedExpressionSystemConfig;

  /** 表情预设库 */
  private expressionPresets: Map<string, ExpressionPreset>;

  /** 动作预设库 */
  private actionPresets: Map<string, ActionPreset>;

  /** 表情序列库 */
  private expressionSequences: Map<string, ExpressionSequence>;

  /** 活跃的表情控制 */
  private activeExpressions: Map<string, {
    entity: Entity;
    sequence: ExpressionSequence;
    startTime: number;
    currentKeyframe: number;
  }>;

  /** 实时控制参数 */
  private realTimeParams: Map<string, RealTimeControlParams>;

  /** 微表情定时器 */
  private microExpressionTimers: Map<string, NodeJS.Timeout>;

  /** 眨眼定时器 */
  private blinkTimers: Map<string, NodeJS.Timeout>;

  /** 呼吸动画状态 */
  private breathingStates: Map<string, {
    phase: number;
    intensity: number;
  }>;

  /**
   * 构造函数
   * @param world 世界实例
   * @param config 系统配置
   */
  constructor(world: World, config: Partial<AdvancedExpressionSystemConfig> = {}) {
    super();

    this.eventEmitter = new EventEmitter();
    this.config = {
      enableMicroExpressions: true,
      enableAutoBlink: true,
      enableBreathing: true,
      enableEmotionAnalysis: false,
      enableCulturalAdaptation: false,
      defaultBlendSpeed: 2.0,
      debug: false,
      ...config
    };

    this.expressionPresets = new Map();
    this.actionPresets = new Map();
    this.expressionSequences = new Map();
    this.activeExpressions = new Map();
    this.realTimeParams = new Map();
    this.microExpressionTimers = new Map();
    this.blinkTimers = new Map();
    this.breathingStates = new Map();

    this.initializeDefaultPresets();
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return AdvancedExpressionSystem.TYPE;
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    if (this.config.debug) {
      console.log('AdvancedExpressionSystem: 初始化高级表情系统');
    }

    // 监听实体添加和移除事件
    this.world.on('entityAdded', this.onEntityAdded.bind(this));
    this.world.on('entityRemoved', this.onEntityRemoved.bind(this));
  }

  /**
   * 更新系统
   * @param deltaTime 时间增量
   */
  public update(deltaTime: number): void {
    // 更新活跃的表情序列
    this.updateActiveExpressions(deltaTime);

    // 更新微表情
    if (this.config.enableMicroExpressions) {
      this.updateMicroExpressions(deltaTime);
    }

    // 更新呼吸动画
    if (this.config.enableBreathing) {
      this.updateBreathingAnimation(deltaTime);
    }

    // 更新实时控制
    this.updateRealTimeControl(deltaTime);
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 清理定时器
    for (const timer of Array.from(this.microExpressionTimers.values())) {
      clearTimeout(timer);
    }
    for (const timer of Array.from(this.blinkTimers.values())) {
      clearTimeout(timer);
    }

    // 清理数据
    this.expressionPresets.clear();
    this.actionPresets.clear();
    this.expressionSequences.clear();
    this.activeExpressions.clear();
    this.realTimeParams.clear();
    this.microExpressionTimers.clear();
    this.blinkTimers.clear();
    this.breathingStates.clear();

    // 移除事件监听器
    this.world.off('entityAdded', this.onEntityAdded.bind(this));
    this.world.off('entityRemoved', this.onEntityRemoved.bind(this));

    if (this.config.debug) {
      console.log('AdvancedExpressionSystem: 系统已销毁');
    }
  }

  /**
   * 初始化默认预设
   */
  private initializeDefaultPresets(): void {
    // 基础表情预设
    this.createBasicExpressionPresets();

    // 复杂表情预设
    this.createComplexExpressionPresets();

    // 文化特定预设
    if (this.config.enableCulturalAdaptation) {
      this.createCulturalPresets();
    }

    // 情感预设
    this.createEmotionalPresets();

    // 微表情预设
    if (this.config.enableMicroExpressions) {
      this.createMicroExpressionPresets();
    }

    // 动态预设
    this.createDynamicPresets();
  }

  /**
   * 创建基础表情预设
   */
  private createBasicExpressionPresets(): void {
    const basicExpressions = [
      {
        id: 'neutral',
        name: '中性',
        type: FacialExpressionType.NEUTRAL,
        intensity: ExpressionIntensity.MODERATE
      },
      {
        id: 'happy',
        name: '开心',
        type: FacialExpressionType.HAPPY,
        intensity: ExpressionIntensity.MODERATE
      },
      {
        id: 'sad',
        name: '悲伤',
        type: FacialExpressionType.SAD,
        intensity: ExpressionIntensity.MODERATE
      },
      {
        id: 'angry',
        name: '愤怒',
        type: FacialExpressionType.ANGRY,
        intensity: ExpressionIntensity.MODERATE
      },
      {
        id: 'surprised',
        name: '惊讶',
        type: FacialExpressionType.SURPRISED,
        intensity: ExpressionIntensity.MODERATE
      },
      {
        id: 'fear',
        name: '恐惧',
        type: FacialExpressionType.FEAR,
        intensity: ExpressionIntensity.MODERATE
      },
      {
        id: 'disgust',
        name: '厌恶',
        type: FacialExpressionType.DISGUST,
        intensity: ExpressionIntensity.MODERATE
      }
    ];

    for (const expr of basicExpressions) {
      const preset: ExpressionPreset = {
        id: expr.id,
        name: expr.name,
        type: ExpressionPresetType.BASIC,
        description: `基础${expr.name}表情`,
        expressions: [{
          type: expr.type,
          weight: 1.0
        }],
        intensity: expr.intensity,
        tags: ['basic', expr.id]
      };

      this.expressionPresets.set(preset.id, preset);
    }
  }

  /**
   * 创建复杂表情预设
   */
  private createComplexExpressionPresets(): void {
    // 混合表情
    const complexPresets: ExpressionPreset[] = [
      {
        id: 'bittersweet',
        name: '苦乐参半',
        type: ExpressionPresetType.COMPLEX,
        description: '开心和悲伤的混合表情',
        expressions: [
          { type: FacialExpressionType.HAPPY, weight: 0.6 },
          { type: FacialExpressionType.SAD, weight: 0.4 }
        ],
        intensity: ExpressionIntensity.MODERATE,
        tags: ['complex', 'mixed', 'emotional']
      },
      {
        id: 'nervous_smile',
        name: '紧张的微笑',
        type: ExpressionPresetType.COMPLEX,
        description: '开心和恐惧的混合表情',
        expressions: [
          { type: FacialExpressionType.HAPPY, weight: 0.7 },
          { type: FacialExpressionType.FEAR, weight: 0.3 }
        ],
        intensity: ExpressionIntensity.MILD,
        tags: ['complex', 'nervous', 'social']
      },
      {
        id: 'confused_anger',
        name: '困惑的愤怒',
        type: ExpressionPresetType.COMPLEX,
        description: '愤怒和困惑的混合表情',
        expressions: [
          { type: FacialExpressionType.ANGRY, weight: 0.6 },
          { type: FacialExpressionType.SURPRISED, weight: 0.4 }
        ],
        intensity: ExpressionIntensity.STRONG,
        tags: ['complex', 'confused', 'negative']
      }
    ];

    for (const preset of complexPresets) {
      this.expressionPresets.set(preset.id, preset);
    }
  }

  /**
   * 创建文化特定预设
   */
  private createCulturalPresets(): void {
    // 中国文化表情
    const chinesePresets: ExpressionPreset[] = [
      {
        id: 'chinese_polite_smile',
        name: '中式礼貌微笑',
        type: ExpressionPresetType.CULTURAL,
        description: '含蓄的礼貌微笑',
        expressions: [
          { type: FacialExpressionType.HAPPY, weight: 0.4 }
        ],
        intensity: ExpressionIntensity.SUBTLE,
        culture: 'chinese',
        tags: ['cultural', 'chinese', 'polite']
      },
      {
        id: 'chinese_respect',
        name: '中式尊敬',
        type: ExpressionPresetType.CULTURAL,
        description: '表示尊敬的表情',
        expressions: [
          { type: FacialExpressionType.NEUTRAL, weight: 0.8 },
          { type: FacialExpressionType.HAPPY, weight: 0.2 }
        ],
        intensity: ExpressionIntensity.MILD,
        culture: 'chinese',
        tags: ['cultural', 'chinese', 'respect']
      }
    ];

    for (const preset of chinesePresets) {
      this.expressionPresets.set(preset.id, preset);
    }
  }

  /**
   * 创建情感预设
   */
  private createEmotionalPresets(): void {
    // 情感强度变化
    const emotionalPresets: ExpressionPreset[] = [
      {
        id: 'joy_burst',
        name: '爆发的喜悦',
        type: ExpressionPresetType.EMOTIONAL,
        description: '强烈的喜悦表情',
        expressions: [
          { type: FacialExpressionType.HAPPY, weight: 1.0 }
        ],
        intensity: ExpressionIntensity.EXTREME,
        tags: ['emotional', 'intense', 'positive']
      },
      {
        id: 'deep_sorrow',
        name: '深度悲伤',
        type: ExpressionPresetType.EMOTIONAL,
        description: '深度的悲伤表情',
        expressions: [
          { type: FacialExpressionType.SAD, weight: 1.0 }
        ],
        intensity: ExpressionIntensity.EXTREME,
        tags: ['emotional', 'intense', 'negative']
      }
    ];

    for (const preset of emotionalPresets) {
      this.expressionPresets.set(preset.id, preset);
    }
  }

  /**
   * 创建微表情预设
   */
  private createMicroExpressionPresets(): void {
    const microPresets: ExpressionPreset[] = [
      {
        id: 'micro_doubt',
        name: '微怀疑',
        type: ExpressionPresetType.MICRO,
        description: '轻微的怀疑表情',
        expressions: [
          { type: FacialExpressionType.SURPRISED, weight: 0.2, duration: 0.1 }
        ],
        intensity: ExpressionIntensity.SUBTLE,
        tags: ['micro', 'doubt', 'subtle']
      },
      {
        id: 'micro_pleasure',
        name: '微愉悦',
        type: ExpressionPresetType.MICRO,
        description: '轻微的愉悦表情',
        expressions: [
          { type: FacialExpressionType.HAPPY, weight: 0.15, duration: 0.15 }
        ],
        intensity: ExpressionIntensity.SUBTLE,
        tags: ['micro', 'pleasure', 'subtle']
      }
    ];

    for (const preset of microPresets) {
      this.expressionPresets.set(preset.id, preset);
    }
  }

  /**
   * 创建动态预设
   */
  private createDynamicPresets(): void {
    // 动态表情序列
    const dynamicSequence: ExpressionSequence = {
      id: 'thinking_process',
      name: '思考过程',
      keyframes: [
        { time: 0.0, expression: FacialExpressionType.NEUTRAL, weight: 1.0 },
        { time: 0.5, expression: FacialExpressionType.SURPRISED, weight: 0.3 },
        { time: 1.0, expression: FacialExpressionType.NEUTRAL, weight: 0.8 },
        { time: 1.5, expression: FacialExpressionType.HAPPY, weight: 0.6 },
        { time: 2.0, expression: FacialExpressionType.NEUTRAL, weight: 1.0 }
      ],
      duration: 2.0,
      loop: false,
      blendMode: ExpressionBlendMode.SMOOTH,
      tags: ['dynamic', 'thinking', 'sequence']
    };

    this.expressionSequences.set(dynamicSequence.id, dynamicSequence);

    const dynamicPreset: ExpressionPreset = {
      id: 'thinking',
      name: '思考',
      type: ExpressionPresetType.DYNAMIC,
      description: '动态思考表情序列',
      expressions: [
        { type: FacialExpressionType.NEUTRAL, weight: 1.0 }
      ],
      intensity: ExpressionIntensity.MODERATE,
      tags: ['dynamic', 'thinking', 'sequence']
    };

    this.expressionPresets.set(dynamicPreset.id, dynamicPreset);
  }

  // ==================== 公共API方法 ====================

  /**
   * 应用表情预设
   * @param entityId 实体ID
   * @param presetId 预设ID
   * @param duration 持续时间
   * @param blendMode 混合模式
   * @returns 是否成功应用
   */
  public applyExpressionPreset(
    entityId: string,
    presetId: string,
    duration: number = 1.0,
    blendMode: ExpressionBlendMode = ExpressionBlendMode.SMOOTH
  ): boolean {
    const entity = this.world.getEntity(entityId);
    if (!entity) {
      console.warn(`实体不存在: ${entityId}`);
      return false;
    }

    const preset = this.expressionPresets.get(presetId);
    if (!preset) {
      console.warn(`表情预设不存在: ${presetId}`);
      return false;
    }

    const facialComponent = entity.getComponent<FacialAnimationComponent>(FacialAnimationComponent.TYPE);
    if (!facialComponent) {
      console.warn(`实体没有面部动画组件: ${entityId}`);
      return false;
    }

    // 应用表情组合
    for (const expr of preset.expressions) {
      const delay = expr.delay || 0;
      const exprDuration = expr.duration || duration;

      setTimeout(() => {
        facialComponent.setExpression(expr.type, expr.weight, exprDuration);
      }, delay * 1000);
    }

    // 触发事件
    this.eventEmitter.emit('expressionPresetApplied', entityId, preset);

    if (this.config.debug) {
      console.log(`应用表情预设 ${preset.name} 到实体 ${entityId}`);
    }

    return true;
  }

  /**
   * 播放表情序列
   * @param entityId 实体ID
   * @param sequenceId 序列ID
   * @returns 是否成功播放
   */
  public playExpressionSequence(entityId: string, sequenceId: string): boolean {
    const entity = this.world.getEntity(entityId);
    if (!entity) {
      console.warn(`实体不存在: ${entityId}`);
      return false;
    }

    const sequence = this.expressionSequences.get(sequenceId);
    if (!sequence) {
      console.warn(`表情序列不存在: ${sequenceId}`);
      return false;
    }

    const facialComponent = entity.getComponent<FacialAnimationComponent>(FacialAnimationComponent.TYPE);
    if (!facialComponent) {
      console.warn(`实体没有面部动画组件: ${entityId}`);
      return false;
    }

    // 停止现有序列
    this.stopExpressionSequence(entityId);

    // 开始新序列
    this.activeExpressions.set(entityId, {
      entity,
      sequence,
      startTime: Date.now(),
      currentKeyframe: 0
    });

    // 触发事件
    this.eventEmitter.emit('expressionSequenceStarted', entityId, sequence);

    if (this.config.debug) {
      console.log(`播放表情序列 ${sequence.name} 到实体 ${entityId}`);
    }

    return true;
  }

  /**
   * 停止表情序列
   * @param entityId 实体ID
   */
  public stopExpressionSequence(entityId: string): void {
    if (this.activeExpressions.has(entityId)) {
      const activeExpression = this.activeExpressions.get(entityId)!;
      this.activeExpressions.delete(entityId);

      // 触发事件
      this.eventEmitter.emit('expressionSequenceStopped', entityId, activeExpression.sequence);

      if (this.config.debug) {
        console.log(`停止表情序列 ${activeExpression.sequence.name} 在实体 ${entityId}`);
      }
    }
  }

  /**
   * 设置实时控制参数
   * @param entityId 实体ID
   * @param params 控制参数
   */
  public setRealTimeControlParams(entityId: string, params: Partial<RealTimeControlParams>): void {
    const currentParams = this.realTimeParams.get(entityId) || {
      intensity: 1.0,
      blendSpeed: this.config.defaultBlendSpeed,
      randomVariation: 0.1,
      microExpressionFrequency: 0.2,
      blinkFrequency: 0.3,
      breathingIntensity: 0.5
    };

    const newParams = { ...currentParams, ...params };
    this.realTimeParams.set(entityId, newParams);

    // 更新微表情定时器
    if (this.config.enableMicroExpressions) {
      this.updateMicroExpressionTimer(entityId, newParams.microExpressionFrequency);
    }

    // 更新眨眼定时器
    if (this.config.enableAutoBlink) {
      this.updateBlinkTimer(entityId, newParams.blinkFrequency);
    }

    if (this.config.debug) {
      console.log(`更新实时控制参数 for 实体 ${entityId}`, newParams);
    }
  }

  /**
   * 创建自定义表情预设
   * @param preset 表情预设
   */
  public createCustomExpressionPreset(preset: ExpressionPreset): void {
    this.expressionPresets.set(preset.id, preset);

    // 触发事件
    this.eventEmitter.emit('customPresetCreated', preset);

    if (this.config.debug) {
      console.log(`创建自定义表情预设: ${preset.name}`);
    }
  }

  /**
   * 创建自定义表情序列
   * @param sequence 表情序列
   */
  public createCustomExpressionSequence(sequence: ExpressionSequence): void {
    this.expressionSequences.set(sequence.id, sequence);

    // 触发事件
    this.eventEmitter.emit('customSequenceCreated', sequence);

    if (this.config.debug) {
      console.log(`创建自定义表情序列: ${sequence.name}`);
    }
  }

  /**
   * 获取所有表情预设
   * @param type 预设类型过滤
   * @returns 表情预设列表
   */
  public getExpressionPresets(type?: ExpressionPresetType): ExpressionPreset[] {
    const presets = Array.from(this.expressionPresets.values());
    return type ? presets.filter(p => p.type === type) : presets;
  }

  /**
   * 获取所有表情序列
   * @returns 表情序列列表
   */
  public getExpressionSequences(): ExpressionSequence[] {
    return Array.from(this.expressionSequences.values());
  }

  /**
   * 搜索表情预设
   * @param query 搜索条件
   * @returns 匹配的预设
   */
  public searchExpressionPresets(query: {
    name?: string;
    type?: ExpressionPresetType;
    intensity?: ExpressionIntensity;
    culture?: string;
    tags?: string[];
  }): ExpressionPreset[] {
    const results: ExpressionPreset[] = [];

    for (const preset of Array.from(this.expressionPresets.values())) {
      let matches = true;

      if (query.name && !preset.name.toLowerCase().includes(query.name.toLowerCase())) {
        matches = false;
      }

      if (query.type && preset.type !== query.type) {
        matches = false;
      }

      if (query.intensity && preset.intensity !== query.intensity) {
        matches = false;
      }

      if (query.culture && preset.culture !== query.culture) {
        matches = false;
      }

      if (query.tags && query.tags.length > 0) {
        const hasAllTags = query.tags.every(tag => preset.tags.includes(tag));
        if (!hasAllTags) {
          matches = false;
        }
      }

      if (matches) {
        results.push(preset);
      }
    }

    return results;
  }

  // ==================== 私有方法 ====================

  /**
   * 实体添加事件处理
   * @param entity 实体
   */
  private onEntityAdded(entity: Entity): void {
    const facialComponent = entity.getComponent<FacialAnimationComponent>(FacialAnimationComponent.TYPE);
    if (facialComponent) {
      // 初始化实时控制参数
      this.setRealTimeControlParams(entity.id, {});

      // 初始化呼吸状态
      if (this.config.enableBreathing) {
        this.breathingStates.set(entity.id, {
          phase: 0,
          intensity: 0.5
        });
      }

      if (this.config.debug) {
        console.log(`为实体 ${entity.id} 初始化高级表情系统`);
      }
    }
  }

  /**
   * 实体移除事件处理
   * @param entity 实体
   */
  private onEntityRemoved(entity: Entity): void {
    // 清理相关数据
    this.activeExpressions.delete(entity.id);
    this.realTimeParams.delete(entity.id);
    this.breathingStates.delete(entity.id);

    // 清理定时器
    const microTimer = this.microExpressionTimers.get(entity.id);
    if (microTimer) {
      clearTimeout(microTimer);
      this.microExpressionTimers.delete(entity.id);
    }

    const blinkTimer = this.blinkTimers.get(entity.id);
    if (blinkTimer) {
      clearTimeout(blinkTimer);
      this.blinkTimers.delete(entity.id);
    }

    if (this.config.debug) {
      console.log(`清理实体 ${entity.id} 的高级表情系统数据`);
    }
  }

  /**
   * 更新活跃的表情序列
   * @param deltaTime 时间增量
   */
  private updateActiveExpressions(deltaTime: number): void {
    const currentTime = Date.now();

    for (const [entityId, activeExpression] of Array.from(this.activeExpressions.entries())) {
      const { entity, sequence, startTime, currentKeyframe } = activeExpression;
      const elapsedTime = (currentTime - startTime) / 1000; // 转换为秒

      // 检查是否完成
      if (elapsedTime >= sequence.duration) {
        if (sequence.loop) {
          // 重新开始
          activeExpression.startTime = currentTime;
          activeExpression.currentKeyframe = 0;
        } else {
          // 完成序列
          this.stopExpressionSequence(entityId);
          continue;
        }
      }

      // 更新当前关键帧
      this.updateSequenceKeyframe(entity, sequence, elapsedTime, activeExpression);
    }
  }

  /**
   * 更新序列关键帧
   * @param entity 实体
   * @param sequence 序列
   * @param elapsedTime 已过时间
   * @param activeExpression 活跃表情
   */
  private updateSequenceKeyframe(
    entity: Entity,
    sequence: ExpressionSequence,
    elapsedTime: number,
    activeExpression: any
  ): void {
    const facialComponent = entity.getComponent<FacialAnimationComponent>(FacialAnimationComponent.TYPE);
    if (!facialComponent) return;

    // 找到当前时间对应的关键帧
    let currentKeyframe = 0;
    for (let i = 0; i < sequence.keyframes.length; i++) {
      if (sequence.keyframes[i].time <= elapsedTime) {
        currentKeyframe = i;
      } else {
        break;
      }
    }

    // 如果关键帧发生变化，应用新的表情
    if (currentKeyframe !== activeExpression.currentKeyframe) {
      const keyframe = sequence.keyframes[currentKeyframe];
      const duration = keyframe.duration || 0.5;

      // 应用表情
      facialComponent.setExpression(keyframe.expression, keyframe.weight, duration);

      // 更新当前关键帧
      activeExpression.currentKeyframe = currentKeyframe;

      if (this.config.debug) {
        console.log(`应用关键帧 ${currentKeyframe} 到实体 ${entity.id}`);
      }
    }
  }

  /**
   * 更新微表情
   * @param deltaTime 时间增量
   */
  private updateMicroExpressions(deltaTime: number): void {
    // 微表情通过定时器处理，这里不需要额外逻辑
  }

  /**
   * 更新呼吸动画
   * @param deltaTime 时间增量
   */
  private updateBreathingAnimation(deltaTime: number): void {
    for (const [entityId, breathingState] of Array.from(this.breathingStates.entries())) {
      const entity = this.world.getEntity(entityId);
      if (!entity) continue;

      const facialComponent = entity.getComponent<FacialAnimationComponent>(FacialAnimationComponent.TYPE);
      if (!facialComponent) continue;

      const params = this.realTimeParams.get(entityId);
      if (!params) continue;

      // 更新呼吸相位
      breathingState.phase += deltaTime * 0.5; // 呼吸频率
      if (breathingState.phase > Math.PI * 2) {
        breathingState.phase -= Math.PI * 2;
      }

      // 计算呼吸强度
      const breathingValue = Math.sin(breathingState.phase) * params.breathingIntensity * 0.1;

      // 应用轻微的表情变化来模拟呼吸
      // 这里可以调整鼻孔、嘴唇等部位
      // 简化实现：轻微调整中性表情的权重
      const baseWeight = 1.0 + breathingValue;
      facialComponent.setExpression(FacialExpressionType.NEUTRAL, baseWeight, 0.1);
    }
  }

  /**
   * 更新实时控制
   * @param deltaTime 时间增量
   */
  private updateRealTimeControl(deltaTime: number): void {
    // 实时控制主要通过参数调整和定时器实现
    // 这里可以添加额外的实时逻辑
  }

  /**
   * 更新微表情定时器
   * @param entityId 实体ID
   * @param frequency 频率
   */
  private updateMicroExpressionTimer(entityId: string, frequency: number): void {
    // 清除现有定时器
    const existingTimer = this.microExpressionTimers.get(entityId);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // 创建新定时器
    const interval = (1 / frequency) * 1000; // 转换为毫秒
    const timer = setTimeout(() => {
      this.triggerMicroExpression(entityId);
      this.updateMicroExpressionTimer(entityId, frequency); // 递归调用
    }, interval + Math.random() * interval); // 添加随机变化

    this.microExpressionTimers.set(entityId, timer);
  }

  /**
   * 更新眨眼定时器
   * @param entityId 实体ID
   * @param frequency 频率
   */
  private updateBlinkTimer(entityId: string, frequency: number): void {
    // 清除现有定时器
    const existingTimer = this.blinkTimers.get(entityId);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // 创建新定时器
    const interval = (1 / frequency) * 1000; // 转换为毫秒
    const timer = setTimeout(() => {
      this.triggerBlink(entityId);
      this.updateBlinkTimer(entityId, frequency); // 递归调用
    }, interval + Math.random() * interval * 0.5); // 添加随机变化

    this.blinkTimers.set(entityId, timer);
  }

  /**
   * 触发微表情
   * @param entityId 实体ID
   */
  private triggerMicroExpression(entityId: string): void {
    const entity = this.world.getEntity(entityId);
    if (!entity) return;

    const facialComponent = entity.getComponent<FacialAnimationComponent>(FacialAnimationComponent.TYPE);
    if (!facialComponent) return;

    // 随机选择一个微表情预设
    const microPresets = this.getExpressionPresets(ExpressionPresetType.MICRO);
    if (microPresets.length === 0) return;

    const randomPreset = microPresets[Math.floor(Math.random() * microPresets.length)];

    // 应用微表情
    for (const expr of randomPreset.expressions) {
      const duration = expr.duration || 0.2;
      facialComponent.setExpression(expr.type, expr.weight, duration);

      // 快速恢复到中性
      setTimeout(() => {
        facialComponent.setExpression(FacialExpressionType.NEUTRAL, 1.0, duration);
      }, duration * 1000);
    }

    if (this.config.debug) {
      console.log(`触发微表情 ${randomPreset.name} 在实体 ${entityId}`);
    }
  }

  /**
   * 触发眨眼
   * @param entityId 实体ID
   */
  private triggerBlink(entityId: string): void {
    const entity = this.world.getEntity(entityId);
    if (!entity) return;

    const facialComponent = entity.getComponent<FacialAnimationComponent>(FacialAnimationComponent.TYPE);
    if (!facialComponent) return;

    // 简化的眨眼实现
    // 实际实现需要控制眼睑的混合形状
    // 这里使用轻微的表情变化来模拟

    const blinkDuration = 0.15;

    // 闭眼
    setTimeout(() => {
      // 这里应该控制眼睑闭合的混合形状
      // 简化实现：使用轻微的表情变化
    }, 0);

    // 睁眼
    setTimeout(() => {
      // 恢复正常
    }, blinkDuration * 1000);

    if (this.config.debug) {
      console.log(`触发眨眼在实体 ${entityId}`);
    }
  }

  // 移除了重写的EventEmitter方法，直接使用继承的方法
}
