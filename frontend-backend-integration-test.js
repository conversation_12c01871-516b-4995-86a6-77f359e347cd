#!/usr/bin/env node
/**
 * 前后端联调测试脚本 - 替换Mock数据为真实API调用
 * 测试前端与后端API的真实连接，验证数据流
 */

// 使用Node.js内置的fetch API (Node 18+) 或 http模块
const http = require('http');
const https = require('https');
const fs = require('fs');
const path = require('path');

// 简单的HTTP请求函数
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;

    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 5000
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = data ? JSON.parse(data) : {};
          resolve({
            status: res.statusCode,
            data: jsonData,
            headers: res.headers
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: data,
            headers: res.headers
          });
        }
      });
    });

    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (options.data) {
      req.write(JSON.stringify(options.data));
    }

    req.end();
  });
}

// 配置
const API_BASE_URL = 'http://localhost:3000/api';
const FRONTEND_PATH = 'editor';

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'cyan');
}

function logHeader(message) {
  log(`\n🚀 ${message}`, 'magenta');
  log('='.repeat(60), 'magenta');
}

// 测试状态
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: []
};

// 全局认证令牌
let authToken = null;

// 执行测试并记录结果
async function runTest(testName, testFunction) {
  testResults.total++;
  logInfo(`开始测试: ${testName}`);
  
  try {
    const result = await testFunction();
    if (result) {
      testResults.passed++;
      logSuccess(`${testName} - 通过`);
    } else {
      testResults.failed++;
      logError(`${testName} - 失败`);
    }
    return result;
  } catch (error) {
    testResults.failed++;
    testResults.errors.push({ test: testName, error: error.message });
    logError(`${testName} - 异常: ${error.message}`);
    return false;
  }
}

// 1. 测试API网关健康状态
async function testApiGatewayHealth() {
  try {
    const response = await makeRequest(`${API_BASE_URL}/health`, { timeout: 5000 });
    return response.status === 200;
  } catch (error) {
    return false;
  }
}

// 2. 测试用户注册和登录
async function testUserAuthentication() {
  try {
    // 注册测试用户
    const timestamp = Date.now();
    const userData = {
      username: `testuser_${timestamp}`,
      email: `test_${timestamp}@example.com`,
      password: 'testpassword123',
      displayName: '前后端联调测试用户'
    };

    const registerResponse = await makeRequest(`${API_BASE_URL}/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: userData
    });
    if (registerResponse.status !== 201) {
      return false;
    }

    // 登录测试
    const loginResponse = await makeRequest(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: {
        usernameOrEmail: userData.email,
        password: userData.password
      }
    });

    if (loginResponse.status === 200 && loginResponse.data.access_token) {
      authToken = loginResponse.data.access_token;
      logInfo(`获取到认证令牌: ${authToken.substring(0, 20)}...`);
      return true;
    }

    return false;
  } catch (error) {
    return false;
  }
}

// 3. 测试用户信息获取
async function testUserProfile() {
  if (!authToken) return false;

  try {
    const response = await makeRequest(`${API_BASE_URL}/auth/profile`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    return response.status === 200 && response.data.id;
  } catch (error) {
    return false;
  }
}

// 4. 测试项目管理
async function testProjectManagement() {
  if (!authToken) return false;
  
  try {
    const headers = { Authorization: `Bearer ${authToken}` };
    
    // 创建项目
    const projectData = {
      name: `联调测试项目_${Date.now()}`,
      description: '前后端联调测试项目',
      type: 'scene',
      isPublic: false
    };
    
    const createResponse = await axios.post(`${API_BASE_URL}/projects`, projectData, { headers });
    if (createResponse.status !== 201) return false;
    
    const projectId = createResponse.data.id;
    logInfo(`创建项目成功: ${projectId}`);
    
    // 获取项目列表
    const listResponse = await axios.get(`${API_BASE_URL}/projects`, { headers });
    if (listResponse.status !== 200) return false;
    
    // 获取项目详情
    const detailResponse = await axios.get(`${API_BASE_URL}/projects/${projectId}`, { headers });
    if (detailResponse.status !== 200) return false;
    
    // 更新项目
    const updateResponse = await axios.patch(`${API_BASE_URL}/projects/${projectId}`, {
      description: '更新后的项目描述'
    }, { headers });
    
    return updateResponse.status === 200;
  } catch (error) {
    return false;
  }
}

// 5. 测试资产管理
async function testAssetManagement() {
  if (!authToken) return false;
  
  try {
    const headers = { Authorization: `Bearer ${authToken}` };
    
    // 获取资产列表
    const assetsResponse = await axios.get(`${API_BASE_URL}/assets`, { headers });
    if (assetsResponse.status !== 200) return false;
    
    // 获取模型列表
    const modelsResponse = await axios.get(`${API_BASE_URL}/models`, { headers });
    if (modelsResponse.status !== 200) return false;
    
    // 获取纹理列表
    const texturesResponse = await axios.get(`${API_BASE_URL}/textures`, { headers });
    if (texturesResponse.status !== 200) return false;
    
    // 获取音频列表
    const audioResponse = await axios.get(`${API_BASE_URL}/audio`, { headers });
    
    return audioResponse.status === 200;
  } catch (error) {
    return false;
  }
}

// 6. 测试渲染服务
async function testRenderService() {
  if (!authToken) return false;
  
  try {
    const headers = { Authorization: `Bearer ${authToken}` };
    
    // 获取渲染任务列表
    const tasksResponse = await axios.get(`${API_BASE_URL}/render/tasks`, { headers });
    
    return tasksResponse.status === 200;
  } catch (error) {
    return false;
  }
}

// 7. 测试示例服务（检查是否使用真实API）
async function testExampleService() {
  try {
    const response = await axios.get(`${API_BASE_URL}/examples`);
    return response.status === 200;
  } catch (error) {
    // 如果API不存在，这是正常的，因为可能还没有实现
    return true;
  }
}

// 8. 检查前端配置是否正确
function checkFrontendConfiguration() {
  const configPath = path.join(FRONTEND_PATH, 'src/config/environment.ts');
  
  if (!fs.existsSync(configPath)) {
    logError(`前端配置文件不存在: ${configPath}`);
    return false;
  }
  
  const configContent = fs.readFileSync(configPath, 'utf8');
  
  // 检查Mock数据是否已禁用
  const mockDisabled = configContent.includes('enableMockData: false');
  if (!mockDisabled) {
    logWarning('前端仍在使用Mock数据，建议禁用以使用真实API');
  }
  
  // 检查API地址配置
  const apiConfigCorrect = configContent.includes('http://localhost:3000/api');
  if (!apiConfigCorrect) {
    logError('前端API地址配置错误');
    return false;
  }
  
  return true;
}

// 9. 更新前端配置以禁用Mock数据
function updateFrontendConfig() {
  const configPath = path.join(FRONTEND_PATH, 'src/config/environment.ts');
  
  if (!fs.existsSync(configPath)) {
    logError(`前端配置文件不存在: ${configPath}`);
    return false;
  }
  
  let configContent = fs.readFileSync(configPath, 'utf8');
  
  // 禁用Mock数据
  configContent = configContent.replace(/enableMockData:\s*true/g, 'enableMockData: false');
  
  // 确保API地址正确
  configContent = configContent.replace(
    /apiUrl:\s*['"][^'"]*['"]/g,
    "apiUrl: 'http://localhost:3000/api'"
  );
  
  fs.writeFileSync(configPath, configContent, 'utf8');
  logSuccess('前端配置已更新，Mock数据已禁用');
  
  return true;
}

// 10. 生成测试报告
function generateTestReport() {
  logHeader('测试报告');
  
  log(`总测试数: ${testResults.total}`, 'cyan');
  log(`通过: ${testResults.passed}`, 'green');
  log(`失败: ${testResults.failed}`, 'red');
  log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`, 'yellow');
  
  if (testResults.errors.length > 0) {
    log('\n错误详情:', 'red');
    testResults.errors.forEach(error => {
      log(`  - ${error.test}: ${error.error}`, 'red');
    });
  }
  
  if (testResults.passed === testResults.total) {
    logSuccess('\n🎉 所有测试通过！前后端联调成功！');
    logInfo('现在可以启动前端应用进行实际使用：');
    logInfo('cd editor && npm start');
  } else {
    logWarning('\n⚠️  部分测试失败，请检查后端服务状态');
    logInfo('确保所有微服务都已启动：');
    logInfo('./start-windows.ps1');
  }
}

// 主测试流程
async function runIntegrationTests() {
  logHeader('前后端联调测试 - Mock数据替换为真实API');
  
  // 检查前端配置
  logHeader('1. 检查前端配置');
  const configOk = checkFrontendConfiguration();
  if (!configOk) {
    logInfo('正在更新前端配置...');
    updateFrontendConfig();
  }
  
  // 执行API测试
  logHeader('2. 执行API连接测试');
  
  await runTest('API网关健康检查', testApiGatewayHealth);
  await runTest('用户认证测试', testUserAuthentication);
  await runTest('用户信息获取', testUserProfile);
  await runTest('项目管理测试', testProjectManagement);
  await runTest('资产管理测试', testAssetManagement);
  await runTest('渲染服务测试', testRenderService);
  await runTest('示例服务测试', testExampleService);
  
  // 生成报告
  generateTestReport();
}

// 运行测试
if (require.main === module) {
  runIntegrationTests().catch(error => {
    logError(`测试执行失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  runIntegrationTests,
  testApiGatewayHealth,
  testUserAuthentication,
  testProjectManagement,
  testAssetManagement,
  updateFrontendConfig
};
