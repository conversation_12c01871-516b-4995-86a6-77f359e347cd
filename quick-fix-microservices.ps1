#!/usr/bin/env pwsh
# 快速修复微服务启动问题的脚本
# 用于应用所有必要的修复和优化

param(
    [switch]$Force,         # 强制重新构建
    [switch]$Clean,         # 清理现有容器
    [switch]$Verbose,       # 详细输出
    [switch]$Help          # 显示帮助
)

# 颜色输出函数
function Write-ColorOutput($ForegroundColor, $Message) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    Write-Output $Message
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Success($message) {
    Write-ColorOutput Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "❌ $message"
}

function Write-Info($message) {
    Write-ColorOutput Cyan "ℹ️  $message"
}

function Write-Header($message) {
    Write-Host ""
    Write-ColorOutput Magenta "🔧 $message"
    Write-Host "=" * 60
}

# 显示帮助信息
function Show-Help {
    Write-Host "微服务快速修复脚本"
    Write-Host ""
    Write-Host "用法: .\quick-fix-microservices.ps1 [选项]"
    Write-Host ""
    Write-Host "选项:"
    Write-Host "  -Force      强制重新构建所有镜像"
    Write-Host "  -Clean      清理现有容器和卷"
    Write-Host "  -Verbose    显示详细输出"
    Write-Host "  -Help       显示此帮助信息"
    Write-Host ""
    Write-Host "示例:"
    Write-Host "  .\quick-fix-microservices.ps1                # 标准修复"
    Write-Host "  .\quick-fix-microservices.ps1 -Force         # 强制重新构建"
    Write-Host "  .\quick-fix-microservices.ps1 -Clean -Force  # 完全重新部署"
}

# 检查Docker状态
function Test-DockerStatus {
    Write-Info "检查Docker状态..."
    
    try {
        $dockerInfo = docker info 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Docker运行正常"
            return $true
        } else {
            Write-Error "Docker未运行，请启动Docker Desktop"
            return $false
        }
    } catch {
        Write-Error "无法连接到Docker"
        return $false
    }
}

# 检查必要文件
function Test-RequiredFiles {
    Write-Info "检查必要文件..."
    
    $requiredFiles = @(
        "docker-compose.windows.yml",
        ".env",
        "server/asset-library-service/Dockerfile",
        "server/project-service/Dockerfile",
        "server/user-service/Dockerfile"
    )
    
    $missing = @()
    foreach ($file in $requiredFiles) {
        if (-not (Test-Path $file)) {
            $missing += $file
        }
    }
    
    if ($missing.Count -gt 0) {
        Write-Error "缺少必要文件:"
        foreach ($file in $missing) {
            Write-Error "  $file"
        }
        return $false
    }
    
    Write-Success "所有必要文件存在"
    return $true
}

# 清理现有容器和卷
function Clear-ExistingResources {
    Write-Header "清理现有资源"
    
    Write-Info "停止所有容器..."
    docker-compose -f docker-compose.windows.yml down --remove-orphans
    
    if ($Clean) {
        Write-Info "删除所有卷..."
        docker-compose -f docker-compose.windows.yml down -v
        
        Write-Info "清理Docker系统..."
        docker system prune -f
        
        Write-Info "清理未使用的卷..."
        docker volume prune -f
    }
    
    Write-Success "资源清理完成"
}

# 构建服务镜像
function Build-ServiceImages {
    Write-Header "构建服务镜像"
    
    $services = @(
        "asset-library-service",
        "user-service", 
        "project-service",
        "asset-service"
    )
    
    foreach ($service in $services) {
        Write-Info "构建服务: $service"
        
        if ($Force) {
            docker-compose -f docker-compose.windows.yml build --no-cache $service
        } else {
            docker-compose -f docker-compose.windows.yml build $service
        }
        
        if ($LASTEXITCODE -ne 0) {
            Write-Error "构建 $service 失败"
            return $false
        }
        
        Write-Success "$service 构建完成"
    }
    
    return $true
}

# 启动基础设施服务
function Start-InfrastructureServices {
    Write-Header "启动基础设施服务"
    
    $infraServices = @("mysql", "redis", "minio", "elasticsearch", "chroma")
    
    foreach ($service in $infraServices) {
        Write-Info "启动服务: $service"
        docker-compose -f docker-compose.windows.yml up -d $service
        
        if ($LASTEXITCODE -ne 0) {
            Write-Error "启动 $service 失败"
            return $false
        }
        
        # 等待服务启动
        Start-Sleep -Seconds 15
    }
    
    Write-Info "等待基础设施服务完全启动..."
    Start-Sleep -Seconds 60
    
    Write-Success "基础设施服务启动完成"
    return $true
}

# 启动核心服务
function Start-CoreServices {
    Write-Header "启动核心服务"
    
    # 启动服务注册中心
    Write-Info "启动服务注册中心..."
    docker-compose -f docker-compose.windows.yml up -d service-registry
    Start-Sleep -Seconds 30
    
    # 启动核心业务服务
    $coreServices = @("user-service", "project-service", "asset-service", "asset-library-service")
    
    foreach ($service in $coreServices) {
        Write-Info "启动服务: $service"
        docker-compose -f docker-compose.windows.yml up -d $service
        
        if ($LASTEXITCODE -ne 0) {
            Write-Error "启动 $service 失败"
            return $false
        }
        
        # 等待服务启动
        Start-Sleep -Seconds 30
    }
    
    Write-Success "核心服务启动完成"
    return $true
}

# 启动其他服务
function Start-OtherServices {
    Write-Header "启动其他服务"
    
    Write-Info "启动API网关..."
    docker-compose -f docker-compose.windows.yml up -d api-gateway
    Start-Sleep -Seconds 20
    
    Write-Info "启动其他业务服务..."
    docker-compose -f docker-compose.windows.yml up -d
    
    Write-Success "所有服务启动完成"
}

# 验证服务状态
function Test-ServiceStatus {
    Write-Header "验证服务状态"
    
    Write-Info "检查容器状态..."
    docker-compose -f docker-compose.windows.yml ps
    
    Write-Info "检查服务健康状态..."
    
    # 检查关键服务的健康状态
    $healthChecks = @{
        "MySQL" = "http://localhost:3306"
        "Redis" = "redis://localhost:6379"
        "MinIO" = "http://localhost:9000/minio/health/live"
        "Elasticsearch" = "http://localhost:9200/_cluster/health"
        "Asset Library Service" = "http://localhost:8003/health"
        "User Service" = "http://localhost:4001/health"
        "Project Service" = "http://localhost:4002/health"
        "Asset Service" = "http://localhost:4003/health"
    }
    
    foreach ($service in $healthChecks.GetEnumerator()) {
        if ($service.Value -like "http*") {
            try {
                $response = Invoke-WebRequest -Uri $service.Value -TimeoutSec 5 -UseBasicParsing
                if ($response.StatusCode -eq 200) {
                    Write-Success "$($service.Key) 健康检查通过"
                } else {
                    Write-Warning "$($service.Key) 健康检查失败: $($response.StatusCode)"
                }
            } catch {
                Write-Warning "$($service.Key) 健康检查失败: $($_.Exception.Message)"
            }
        }
    }
}

# 主函数
function Main {
    if ($Help) {
        Show-Help
        return
    }
    
    Write-Header "DL Engine 微服务快速修复工具"
    
    # 检查前置条件
    if (-not (Test-DockerStatus)) {
        exit 1
    }
    
    if (-not (Test-RequiredFiles)) {
        exit 1
    }
    
    try {
        # 清理现有资源
        Clear-ExistingResources
        
        # 构建服务镜像
        if (-not (Build-ServiceImages)) {
            exit 1
        }
        
        # 分阶段启动服务
        if (-not (Start-InfrastructureServices)) {
            exit 1
        }
        
        if (-not (Start-CoreServices)) {
            exit 1
        }
        
        Start-OtherServices
        
        # 验证服务状态
        Test-ServiceStatus
        
        Write-Success "🎉 微服务修复和启动完成！"
        Write-Info "💡 使用 .\health-check-windows.ps1 进行详细健康检查"
        Write-Info "💡 使用 docker-compose -f docker-compose.windows.yml logs -f 查看实时日志"
        
    } catch {
        Write-Error "修复过程中发生错误: $($_.Exception.Message)"
        exit 1
    }
}

# 执行主函数
try {
    Main
} catch {
    Write-Error "脚本执行失败: $($_.Exception.Message)"
    exit 1
}
