/**
 * 增强水面渲染器
 * 用于渲染高质量的水面效果，包括反射、折射、焦散等
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { WaterBodyComponent, WaterBodyType } from '../../physics/water/WaterBodyComponent';
import type { Camera } from '../../rendering/Camera';
import { Scene } from '../../scene/Scene';
import { Debug } from '../../utils/Debug';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';
import { WaterMaterial } from './WaterMaterial';

/**
 * 增强水面渲染器配置
 */
export interface EnhancedWaterSurfaceRendererConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 反射贴图分辨率 */
  reflectionMapResolution?: number;
  /** 折射贴图分辨率 */
  refractionMapResolution?: number;
  /** 是否启用反射 */
  enableReflection?: boolean;
  /** 是否启用折射 */
  enableRefraction?: boolean;
  /** 是否启用焦散 */
  enableCaustics?: boolean;
  /** 是否启用水下雾效 */
  enableUnderwaterFog?: boolean;
  /** 是否启用水下扭曲 */
  enableUnderwaterDistortion?: boolean;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
  /** 是否使用低质量模式（用于低性能设备） */
  useLowQualityMode?: boolean;
}

/**
 * 增强水面渲染器
 */
export class EnhancedWaterSurfaceRenderer extends System {
  /** 配置 */
  private config: EnhancedWaterSurfaceRendererConfig;
  /** 水体组件映射 */
  private waterBodies: Map<string, WaterBodyComponent>;
  /** 水体材质映射 */
  private waterMaterials: Map<string, WaterMaterial>;
  /** 是否启用渲染器 */
  private rendererEnabled: boolean;
  /** 是否自动更新 */
  private autoUpdate: boolean;
  /** 更新频率 */
  private updateFrequency: number;
  /** 帧计数器 */
  private frameCount: number;
  /** 渲染器 */
  private renderer: THREE.WebGLRenderer | null;
  /** 活动相机 */
  private activeCamera: Camera | null;
  /** 活动场景 */
  private activeScene: Scene | null;
  /** 反射渲染目标 */
  private reflectionRenderTarget: THREE.WebGLRenderTarget | null;
  /** 折射渲染目标 */
  private refractionRenderTarget: THREE.WebGLRenderTarget | null;
  /** 焦散渲染目标 */
  private causticsRenderTarget: THREE.WebGLRenderTarget | null;
  /** 反射相机 */
  private reflectionCamera: THREE.PerspectiveCamera | null;
  /** 折射相机 */
  private refractionCamera: THREE.PerspectiveCamera | null;
  /** 反射平面 */
  private reflectionPlane: THREE.Plane;
  /** 折射平面 */
  private refractionPlane: THREE.Plane;
  /** 性能监控器 */
  private performanceMonitor: PerformanceMonitor;

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config: EnhancedWaterSurfaceRendererConfig = {}) {
    // 使用数字ID代替World对象
    super(0);

    // 设置世界
    this.setWorld(world);

    // 设置配置
    this.config = {
      enabled: config.enabled !== undefined ? config.enabled : true,
      autoUpdate: config.autoUpdate !== undefined ? config.autoUpdate : true,
      updateFrequency: config.updateFrequency || 1,
      reflectionMapResolution: config.reflectionMapResolution || 512,
      refractionMapResolution: config.refractionMapResolution || 512,
      enableReflection: config.enableReflection !== undefined ? config.enableReflection : true,
      enableRefraction: config.enableRefraction !== undefined ? config.enableRefraction : true,
      enableCaustics: config.enableCaustics !== undefined ? config.enableCaustics : true,
      enableUnderwaterFog: config.enableUnderwaterFog !== undefined ? config.enableUnderwaterFog : true,
      enableUnderwaterDistortion: config.enableUnderwaterDistortion !== undefined ? config.enableUnderwaterDistortion : true,
      enablePerformanceMonitoring: config.enablePerformanceMonitoring !== undefined ? config.enablePerformanceMonitoring : false,
      useLowQualityMode: config.useLowQualityMode !== undefined ? config.useLowQualityMode : false
    };

    // 初始化属性
    this.waterBodies = new Map();
    this.waterMaterials = new Map();
    this.rendererEnabled = this.config.enabled!;
    this.autoUpdate = this.config.autoUpdate!;
    this.updateFrequency = this.config.updateFrequency!;
    this.frameCount = 0;
    this.renderer = null;
    this.activeCamera = null;
    this.activeScene = null;
    this.reflectionRenderTarget = null;
    this.refractionRenderTarget = null;
    this.causticsRenderTarget = null;
    this.reflectionCamera = null;
    this.refractionCamera = null;
    this.reflectionPlane = new THREE.Plane();
    this.refractionPlane = new THREE.Plane();
    this.performanceMonitor = PerformanceMonitor.getInstance();

    // 设置系统启用状态
    this.setEnabled(this.config.enabled!);

    // 初始化系统
    this.initializeRenderer();
  }

  /**
   * 初始化渲染器
   */
  private initializeRenderer(): void {
    // 创建渲染目标
    this.createRenderTargets();

    // 创建相机
    this.createCameras();

    Debug.log('EnhancedWaterSurfaceRenderer', '增强水面渲染器初始化完成');
  }

  /**
   * 创建渲染目标
   */
  private createRenderTargets(): void {
    // 获取渲染器 - 暂时设为null，需要外部设置
    this.renderer = null;
    if (!this.renderer) {
      Debug.warn('EnhancedWaterSurfaceRenderer', '渲染器未设置，请调用setRenderer方法');
      return;
    }

    // 创建反射渲染目标
    if (this.config.enableReflection) {
      this.reflectionRenderTarget = new THREE.WebGLRenderTarget(
        this.config.reflectionMapResolution!,
        this.config.reflectionMapResolution!,
        {
          minFilter: THREE.LinearFilter,
          magFilter: THREE.LinearFilter,
          format: THREE.RGBAFormat,
          stencilBuffer: false
        }
      );
    }

    // 创建折射渲染目标
    if (this.config.enableRefraction) {
      this.refractionRenderTarget = new THREE.WebGLRenderTarget(
        this.config.refractionMapResolution!,
        this.config.refractionMapResolution!,
        {
          minFilter: THREE.LinearFilter,
          magFilter: THREE.LinearFilter,
          format: THREE.RGBAFormat,
          stencilBuffer: false
        }
      );
    }

    // 创建焦散渲染目标
    if (this.config.enableCaustics) {
      this.causticsRenderTarget = new THREE.WebGLRenderTarget(
        256,
        256,
        {
          minFilter: THREE.LinearFilter,
          magFilter: THREE.LinearFilter,
          format: THREE.RGBAFormat,
          stencilBuffer: false
        }
      );
    }
  }

  /**
   * 创建相机
   */
  private createCameras(): void {
    // 创建反射相机
    this.reflectionCamera = new THREE.PerspectiveCamera(50, 1, 0.1, 1000);

    // 创建折射相机
    this.refractionCamera = new THREE.PerspectiveCamera(50, 1, 0.1, 1000);
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.rendererEnabled || !this.autoUpdate) {
      return;
    }

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.updateFrequency !== 0) {
      return;
    }

    // 如果启用了性能监控，开始计时
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMonitor.beginMeasure('waterSurfaceUpdate');
    }

    // 获取活动相机和场景
    this.updateActiveCamera();
    this.updateActiveScene();

    // 如果没有相机或场景，则不更新
    if (!this.activeCamera || !this.activeScene || !this.renderer) {
      return;
    }

    // 更新所有水体
    for (const [entityId, waterBody] of this.waterBodies) {
      // 如果水体未初始化或未启用，则跳过
      if (!waterBody.isInitialized() || !waterBody.isEnabled()) {
        continue;
      }

      // 更新水体渲染
      this.updateWaterRendering(entityId, waterBody, deltaTime);
    }

    // 如果启用了性能监控，结束计时
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMonitor.endMeasure('waterSurfaceUpdate');
    }
  }

  /**
   * 更新活动相机
   */
  private updateActiveCamera(): void {
    // 暂时设为null，需要外部设置
    // TODO: 实现正确的相机获取逻辑
    this.activeCamera = null;
  }

  /**
   * 更新活动场景
   */
  private updateActiveScene(): void {
    // 暂时设为null，需要外部设置
    // TODO: 实现正确的场景获取逻辑
    this.activeScene = null;
  }

  /**
   * 更新水体渲染
   * @param entityId 实体ID
   * @param waterBody 水体组件
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateWaterRendering(entityId: string, waterBody: WaterBodyComponent, deltaTime: number): void {
    // 获取水体材质
    const material = this.waterMaterials.get(entityId);
    if (!material) {
      return;
    }

    // 更新反射贴图
    if (this.config.enableReflection) {
      this.updateReflectionMap(waterBody);
      material.setReflectionMap(this.reflectionRenderTarget!.texture);
    }

    // 更新折射贴图
    if (this.config.enableRefraction) {
      this.updateRefractionMap(waterBody);
      material.setRefractionMap(this.refractionRenderTarget!.texture);
    }

    // 更新焦散贴图
    if (this.config.enableCaustics) {
      this.updateCausticsMap(waterBody);
      material.setCausticsMap(this.causticsRenderTarget!.texture);
    }

    // 更新水体材质
    material.update(deltaTime, this.activeCamera!.getThreeCamera());
  }

  /**
   * 更新反射贴图
   * @param waterBody 水体组件
   */
  private updateReflectionMap(waterBody: WaterBodyComponent): void {
    if (!this.reflectionRenderTarget || !this.reflectionCamera || !this.activeCamera || !this.activeScene || !this.renderer) {
      return;
    }

    // 获取水体位置和法线
    const waterPosition = waterBody.getPosition();
    const waterNormal = new THREE.Vector3(0, 1, 0); // 假设水面法线向上

    // 设置反射平面
    this.reflectionPlane.setFromNormalAndCoplanarPoint(waterNormal, waterPosition);

    // 复制相机参数
    const threeCamera = this.activeCamera.getThreeCamera();
    if (threeCamera instanceof THREE.PerspectiveCamera) {
      this.reflectionCamera.copy(threeCamera);
    }

    // 应用反射矩阵
    const reflectionMatrix = new THREE.Matrix4();
    reflectionMatrix.set(
      1, 0, 0, 0,
      0, -1, 0, 0,
      0, 0, 1, 0,
      0, 0, 0, 1
    );

    // 应用反射矩阵
    this.reflectionCamera.applyMatrix4(reflectionMatrix);

    // 设置相机位置
    const cameraPosition = this.activeCamera.getThreeCamera().position.clone();
    const cameraTarget = this.activeCamera.getThreeCamera().getWorldDirection(new THREE.Vector3()).clone();

    // 反射相机位置
    cameraPosition.y = -cameraPosition.y + 2 * waterPosition.y;
    this.reflectionCamera.position.copy(cameraPosition);

    // 反射相机目标
    cameraTarget.y = -cameraTarget.y;
    this.reflectionCamera.lookAt(cameraTarget.add(cameraPosition));

    // 保存当前渲染目标
    const currentRenderTarget = this.renderer.getRenderTarget();

    // 设置渲染目标
    this.renderer.setRenderTarget(this.reflectionRenderTarget);

    // 清除渲染目标
    this.renderer.clear();

    // 设置剪裁平面
    const currentClippingPlanes = this.renderer.clippingPlanes;
    this.renderer.clippingPlanes = [this.reflectionPlane];

    // 渲染场景
    this.renderer.render(this.activeScene.getThreeScene(), this.reflectionCamera);

    // 恢复剪裁平面
    this.renderer.clippingPlanes = currentClippingPlanes;

    // 恢复渲染目标
    this.renderer.setRenderTarget(currentRenderTarget);
  }

  /**
   * 更新折射贴图
   * @param waterBody 水体组件
   */
  private updateRefractionMap(waterBody: WaterBodyComponent): void {
    if (!this.refractionRenderTarget || !this.refractionCamera || !this.activeCamera || !this.activeScene || !this.renderer) {
      return;
    }

    // 获取水体位置和法线
    const waterPosition = waterBody.getPosition();
    const waterNormal = new THREE.Vector3(0, 1, 0); // 假设水面法线向上

    // 设置折射平面
    this.refractionPlane.setFromNormalAndCoplanarPoint(waterNormal.clone().negate(), waterPosition);

    // 复制相机参数
    const threeCamera = this.activeCamera.getThreeCamera();
    if (threeCamera instanceof THREE.PerspectiveCamera) {
      this.refractionCamera.copy(threeCamera);
    }

    // 保存当前渲染目标
    const currentRenderTarget = this.renderer.getRenderTarget();

    // 设置渲染目标
    this.renderer.setRenderTarget(this.refractionRenderTarget);

    // 清除渲染目标
    this.renderer.clear();

    // 设置剪裁平面
    const currentClippingPlanes = this.renderer.clippingPlanes;
    this.renderer.clippingPlanes = [this.refractionPlane];

    // 渲染场景
    this.renderer.render(this.activeScene.getThreeScene(), this.refractionCamera);

    // 恢复剪裁平面
    this.renderer.clippingPlanes = currentClippingPlanes;

    // 恢复渲染目标
    this.renderer.setRenderTarget(currentRenderTarget);
  }

  /**
   * 更新焦散贴图
   * @param waterBody 水体组件
   */
  private updateCausticsMap(waterBody: WaterBodyComponent): void {
    if (!this.causticsRenderTarget || !this.renderer) {
      return;
    }

    // 创建焦散材质（如果不存在）
    if (!this.causticsMaterial) {
      this.createCausticsMaterial();
    }

    // 保存当前渲染目标
    const currentRenderTarget = this.renderer.getRenderTarget();

    // 设置渲染目标
    this.renderer.setRenderTarget(this.causticsRenderTarget);

    // 清除渲染目标
    this.renderer.clear();

    // 渲染焦散
    this.renderCaustics(waterBody);

    // 恢复渲染目标
    this.renderer.setRenderTarget(currentRenderTarget);
  }

  /**
   * 创建焦散材质
   */
  private causticsMaterial: THREE.ShaderMaterial | null = null;
  private causticsScene: THREE.Scene | null = null;
  private causticsCamera: THREE.OrthographicCamera | null = null;
  private causticsQuad: THREE.Mesh | null = null;

  private createCausticsMaterial(): void {
    // 创建焦散场景
    this.causticsScene = new THREE.Scene();

    // 创建焦散相机
    this.causticsCamera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1);

    // 创建焦散顶点着色器
    const causticsVertexShader = `
      varying vec2 vUv;

      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `;

    // 创建焦散片段着色器
    const causticsFragmentShader = `
      uniform float time;
      uniform float causticsIntensity;
      uniform sampler2D normalSampler;

      varying vec2 vUv;

      // 扰动UV
      vec2 distort(vec2 uv, float amount) {
        return uv + sin(uv.y * 20.0 + time) * cos(uv.x * 20.0 + time) * amount;
      }

      // 焦散计算函数
      vec3 calculateCaustics(vec2 uv, float time) {
        // 水面法线扰动
        vec2 distortion1 = texture2D(normalSampler, uv * 0.5 + vec2(time * 0.01, time * 0.02)).rg * 2.0 - 1.0;
        vec2 distortion2 = texture2D(normalSampler, uv * 0.4 - vec2(time * 0.02, time * 0.01)).rg * 2.0 - 1.0;
        vec2 distortion = (distortion1 + distortion2) * 0.5;

        // 计算焦散
        float caustic = 0.0;

        // 多层焦散
        for (int i = 0; i < 3; i++) {
          float scale = 1.0 - float(i) * 0.2;
          float intensity = 1.0 / (float(i) + 1.0);

          vec2 causticsUv = uv * scale + distortion * 0.1 * float(i + 1);
          float pattern = texture2D(normalSampler, causticsUv).r;

          // 计算焦散强度
          float causticsIntensity = smoothstep(0.4, 0.6, pattern) * intensity;
          caustic += causticsIntensity;
        }

        // 应用颜色
        vec3 causticsColor = vec3(1.0, 0.95, 0.8) * caustic * causticsIntensity;

        return causticsColor;
      }

      void main() {
        // 计算焦散
        vec3 caustics = calculateCaustics(vUv, time);

        // 输出焦散颜色
        gl_FragColor = vec4(caustics, 1.0);
      }
    `;

    // 加载法线贴图
    const textureLoader = new THREE.TextureLoader();
    const normalTexture = textureLoader.load('/assets/textures/waternormals.jpg', (texture) => {
      texture.wrapS = texture.wrapT = THREE.RepeatWrapping;
    });

    // 创建焦散材质
    this.causticsMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        causticsIntensity: { value: 1.0 },
        normalSampler: { value: normalTexture }
      },
      vertexShader: causticsVertexShader,
      fragmentShader: causticsFragmentShader
    });

    // 创建焦散四边形
    const geometry = new THREE.PlaneGeometry(2, 2);
    this.causticsQuad = new THREE.Mesh(geometry, this.causticsMaterial);
    this.causticsScene.add(this.causticsQuad);
  }

  /**
   * 渲染焦散
   * @param waterBody 水体组件
   */
  private renderCaustics(waterBody: WaterBodyComponent): void {
    if (!this.causticsMaterial || !this.causticsScene || !this.causticsCamera || !this.renderer) {
      return;
    }

    // 更新时间
    this.causticsMaterial.uniforms.time.value += 0.01;

    // 根据水体类型调整焦散强度
    switch (waterBody.getType()) {
      case WaterBodyType.OCEAN:
        this.causticsMaterial.uniforms.causticsIntensity.value = 1.2;
        break;
      case WaterBodyType.LAKE:
        this.causticsMaterial.uniforms.causticsIntensity.value = 1.0;
        break;
      case WaterBodyType.RIVER:
        this.causticsMaterial.uniforms.causticsIntensity.value = 0.8;
        break;
      case WaterBodyType.UNDERGROUND_LAKE:
      case WaterBodyType.UNDERGROUND_RIVER:
        this.causticsMaterial.uniforms.causticsIntensity.value = 0.5;
        break;
      default:
        this.causticsMaterial.uniforms.causticsIntensity.value = 1.0;
        break;
    }

    // 渲染焦散
    this.renderer.render(this.causticsScene, this.causticsCamera);
  }

  /**
   * 添加水体组件
   * @param entity 实体
   * @param component 水体组件
   */
  public addWaterBody(entity: Entity, component: WaterBodyComponent): void {
    this.waterBodies.set(entity.id, component);

    // 创建水体材质
    const material = this.createWaterMaterial(component);
    this.waterMaterials.set(entity.id, material);

    Debug.log('EnhancedWaterSurfaceRenderer', `添加水体组件: ${entity.id}`);
  }

  /**
   * 移除水体组件
   * @param entity 实体
   */
  public removeWaterBody(entity: Entity): void {
    this.waterBodies.delete(entity.id);
    this.waterMaterials.delete(entity.id);

    Debug.log('EnhancedWaterSurfaceRenderer', `移除水体组件: ${entity.id}`);
  }

  /**
   * 创建水体材质
   * @param component 水体组件
   * @returns 水体材质
   */
  private createWaterMaterial(component: WaterBodyComponent): WaterMaterial {
    // 获取水体类型
    const waterType = component.getType();

    // 根据水体类型创建不同的材质
    let color: THREE.Color;
    let depthColor: THREE.Color;
    let shallowColor: THREE.Color;
    let reflectivity: number;
    let refractionRatio: number;
    let waveStrength: number;
    let waveSpeed: number;

    switch (waterType) {
      case WaterBodyType.OCEAN:
        color = new THREE.Color(0x0055aa);
        depthColor = new THREE.Color(0x000033);
        shallowColor = new THREE.Color(0x0077cc);
        reflectivity = 0.6;
        refractionRatio = 0.98;
        waveStrength = 0.2;
        waveSpeed = 0.8;
        break;

      case WaterBodyType.LAKE:
        color = new THREE.Color(0x0066cc);
        depthColor = new THREE.Color(0x003366);
        shallowColor = new THREE.Color(0x0099ff);
        reflectivity = 0.5;
        refractionRatio = 0.97;
        waveStrength = 0.1;
        waveSpeed = 0.5;
        break;

      case WaterBodyType.RIVER:
        color = new THREE.Color(0x0088dd);
        depthColor = new THREE.Color(0x005588);
        shallowColor = new THREE.Color(0x00aaff);
        reflectivity = 0.4;
        refractionRatio = 0.96;
        waveStrength = 0.15;
        waveSpeed = 1.0;
        break;

      case WaterBodyType.UNDERGROUND_LAKE:
      case WaterBodyType.UNDERGROUND_RIVER:
        color = new THREE.Color(0x004488);
        depthColor = new THREE.Color(0x001122);
        shallowColor = new THREE.Color(0x0066aa);
        reflectivity = 0.3;
        refractionRatio = 0.95;
        waveStrength = 0.05;
        waveSpeed = 0.3;
        break;

      default:
        color = new THREE.Color(0x0066cc);
        depthColor = new THREE.Color(0x003366);
        shallowColor = new THREE.Color(0x0099ff);
        reflectivity = 0.5;
        refractionRatio = 0.97;
        waveStrength = 0.1;
        waveSpeed = 0.5;
        break;
    }

    // 创建水体材质
    const material = new WaterMaterial({
      color,
      depthColor,
      shallowColor,
      reflectivity,
      refractionRatio,
      waveStrength,
      waveSpeed,
      opacity: 0.8,
      enableReflection: this.config.enableReflection,
      enableRefraction: this.config.enableRefraction,
      enableCaustics: this.config.enableCaustics,
      enableFoam: true,
      enableUnderwaterFog: this.config.enableUnderwaterFog,
      enableUnderwaterDistortion: this.config.enableUnderwaterDistortion
    });

    return material;
  }

  /**
   * 设置配置
   * @param config 配置
   */
  public setConfig(config: Partial<EnhancedWaterSurfaceRendererConfig>): void {
    // 更新配置
    if (config.enabled !== undefined) {
      this.rendererEnabled = config.enabled;
      this.setEnabled(config.enabled);
      this.config.enabled = config.enabled;
    }

    if (config.autoUpdate !== undefined) {
      this.autoUpdate = config.autoUpdate;
      this.config.autoUpdate = config.autoUpdate;
    }

    if (config.updateFrequency !== undefined) {
      this.updateFrequency = config.updateFrequency;
      this.config.updateFrequency = config.updateFrequency;
    }

    if (config.reflectionMapResolution !== undefined && config.reflectionMapResolution !== this.config.reflectionMapResolution) {
      this.config.reflectionMapResolution = config.reflectionMapResolution;
      // 重新创建反射渲染目标
      if (this.reflectionRenderTarget) {
        (this.reflectionRenderTarget as any).dispose();
        this.reflectionRenderTarget = new THREE.WebGLRenderTarget(
          this.config.reflectionMapResolution,
          this.config.reflectionMapResolution,
          {
            minFilter: THREE.LinearFilter,
            magFilter: THREE.LinearFilter,
            format: THREE.RGBAFormat,
            stencilBuffer: false
          }
        );
      }
    }

    if (config.refractionMapResolution !== undefined && config.refractionMapResolution !== this.config.refractionMapResolution) {
      this.config.refractionMapResolution = config.refractionMapResolution;
      // 重新创建折射渲染目标
      if (this.refractionRenderTarget) {
        (this.refractionRenderTarget as any).dispose();
        this.refractionRenderTarget = new THREE.WebGLRenderTarget(
          this.config.refractionMapResolution,
          this.config.refractionMapResolution,
          {
            minFilter: THREE.LinearFilter,
            magFilter: THREE.LinearFilter,
            format: THREE.RGBAFormat,
            stencilBuffer: false
          }
        );
      }
    }

    if (config.enableReflection !== undefined) {
      this.config.enableReflection = config.enableReflection;
      // 如果启用反射，但没有反射渲染目标，则创建
      if (this.config.enableReflection && !this.reflectionRenderTarget) {
        this.reflectionRenderTarget = new THREE.WebGLRenderTarget(
          this.config.reflectionMapResolution!,
          this.config.reflectionMapResolution!,
          {
            minFilter: THREE.LinearFilter,
            magFilter: THREE.LinearFilter,
            format: THREE.RGBAFormat,
            stencilBuffer: false
          }
        );
      }
    }

    if (config.enableRefraction !== undefined) {
      this.config.enableRefraction = config.enableRefraction;
      // 如果启用折射，但没有折射渲染目标，则创建
      if (this.config.enableRefraction && !this.refractionRenderTarget) {
        this.refractionRenderTarget = new THREE.WebGLRenderTarget(
          this.config.refractionMapResolution!,
          this.config.refractionMapResolution!,
          {
            minFilter: THREE.LinearFilter,
            magFilter: THREE.LinearFilter,
            format: THREE.RGBAFormat,
            stencilBuffer: false
          }
        );
      }
    }

    if (config.enableCaustics !== undefined) {
      this.config.enableCaustics = config.enableCaustics;
      // 如果启用焦散，但没有焦散渲染目标，则创建
      if (this.config.enableCaustics && !this.causticsRenderTarget) {
        this.causticsRenderTarget = new THREE.WebGLRenderTarget(
          256,
          256,
          {
            minFilter: THREE.LinearFilter,
            magFilter: THREE.LinearFilter,
            format: THREE.RGBAFormat,
            stencilBuffer: false
          }
        );
      }
    }

    if (config.enableUnderwaterFog !== undefined) {
      this.config.enableUnderwaterFog = config.enableUnderwaterFog;
    }

    if (config.enableUnderwaterDistortion !== undefined) {
      this.config.enableUnderwaterDistortion = config.enableUnderwaterDistortion;
    }

    if (config.enablePerformanceMonitoring !== undefined) {
      this.config.enablePerformanceMonitoring = config.enablePerformanceMonitoring;
    }

    if (config.useLowQualityMode !== undefined) {
      this.config.useLowQualityMode = config.useLowQualityMode;
    }

    Debug.log('EnhancedWaterSurfaceRenderer', '配置已更新');
  }

  /**
   * 设置渲染器
   * @param renderer 渲染器
   */
  public setRenderer(renderer: THREE.WebGLRenderer): void {
    this.renderer = renderer;
    // 重新创建渲染目标
    this.createRenderTargets();
  }

  /**
   * 设置活动相机
   * @param camera 相机
   */
  public setActiveCamera(camera: Camera): void {
    this.activeCamera = camera;
  }

  /**
   * 设置活动场景
   * @param scene 场景
   */
  public setActiveScene(scene: Scene): void {
    this.activeScene = scene;
  }



  /**
   * 销毁渲染器
   */
  public dispose(): void {
    // 销毁所有材质
    for (const material of this.waterMaterials.values()) {
      (material as any).dispose();
    }
    this.waterMaterials.clear();

    // 清空水体
    this.waterBodies.clear();

    // 销毁渲染目标
    if (this.reflectionRenderTarget) {
      (this.reflectionRenderTarget as any).dispose();
      this.reflectionRenderTarget = null;
    }

    if (this.refractionRenderTarget) {
      (this.refractionRenderTarget as any).dispose();
      this.refractionRenderTarget = null;
    }

    if (this.causticsRenderTarget) {
      (this.causticsRenderTarget as any).dispose();
      this.causticsRenderTarget = null;
    }

    // 销毁焦散材质和场景
    if (this.causticsMaterial) {
      (this.causticsMaterial as any).dispose();
      this.causticsMaterial = null;
    }

    if (this.causticsScene) {
      this.causticsScene.clear();
      this.causticsScene = null;
    }

    this.causticsCamera = null;
    this.causticsQuad = null;

    Debug.log('EnhancedWaterSurfaceRenderer', '增强水面渲染器已销毁');
  }
}