import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { Tag, TagType } from './entities/tag.entity';
import { CacheService } from '../../common/services/cache.service';
import { LoggerService } from '../../common/services/logger.service';

export interface CreateTagDto {
  name: string;
  description?: string;
  type?: TagType;
  color?: string;
}

export interface UpdateTagDto {
  name?: string;
  description?: string;
  type?: TagType;
  color?: string;
  isFeatured?: boolean;
  isActive?: boolean;
}

export interface TagQueryDto {
  search?: string;
  type?: TagType;
  featured?: boolean;
  active?: boolean;
  page?: number;
  limit?: number;
}

@Injectable()
export class TagsService {
  constructor(
    @InjectRepository(Tag)
    private readonly tagRepository: Repository<Tag>,
    private readonly cacheService: CacheService,
    private readonly logger: LoggerService,
  ) {}

  /**
   * 创建标签
   */
  async create(createTagDto: CreateTagDto): Promise<Tag> {
    // 检查标签名是否已存在
    const existingTag = await this.tagRepository.findOne({
      where: { name: createTagDto.name },
    });

    if (existingTag) {
      throw new ConflictException('标签名已存在');
    }

    const tag = this.tagRepository.create(createTagDto);
    const savedTag = await this.tagRepository.save(tag);

    // 清除缓存
    await this.clearCache();

    this.logger.log(`标签创建成功: ${savedTag.id}`, 'TagsService');
    return savedTag;
  }

  /**
   * 查找所有标签
   */
  async findAll(query: TagQueryDto = {}): Promise<{ tags: Tag[]; total: number }> {
    const cacheKey = `tags:list:${JSON.stringify(query)}`;
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const queryBuilder = this.tagRepository.createQueryBuilder('tag');

    // 搜索条件
    if (query.search) {
      queryBuilder.andWhere('tag.name ILIKE :search', {
        search: `%${query.search}%`,
      });
    }

    // 类型过滤
    if (query.type) {
      queryBuilder.andWhere('tag.type = :type', { type: query.type });
    }

    // 特色标签过滤
    if (query.featured !== undefined) {
      queryBuilder.andWhere('tag.isFeatured = :featured', {
        featured: query.featured,
      });
    }

    // 活跃状态过滤
    if (query.active !== undefined) {
      queryBuilder.andWhere('tag.isActive = :active', { active: query.active });
    }

    // 排序
    queryBuilder.orderBy('tag.usageCount', 'DESC').addOrderBy('tag.name', 'ASC');

    // 分页
    const { page = 1, limit = 50 } = query;
    const skip = (page - 1) * limit;

    const [tags, total] = await queryBuilder.skip(skip).take(limit).getManyAndCount();

    const result = { tags, total };

    // 缓存结果
    await this.cacheService.set(cacheKey, result, 600); // 10分钟缓存

    return result;
  }

  /**
   * 根据ID查找标签
   */
  async findOne(id: string): Promise<Tag> {
    const cacheKey = `tag:${id}`;
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const tag = await this.tagRepository.findOne({
      where: { id },
      relations: ['assets'],
    });

    if (!tag) {
      throw new NotFoundException('标签不存在');
    }

    // 缓存结果
    await this.cacheService.set(cacheKey, tag, 600); // 10分钟缓存

    return tag;
  }

  /**
   * 更新标签
   */
  async update(id: string, updateTagDto: UpdateTagDto): Promise<Tag> {
    const tag = await this.findOne(id);

    // 如果更新名称，检查是否冲突
    if (updateTagDto.name && updateTagDto.name !== tag.name) {
      const existingTag = await this.tagRepository.findOne({
        where: { name: updateTagDto.name },
      });

      if (existingTag) {
        throw new ConflictException('标签名已存在');
      }
    }

    Object.assign(tag, updateTagDto);
    const updatedTag = await this.tagRepository.save(tag);

    // 清除缓存
    await this.clearCache();

    this.logger.log(`标签更新成功: ${updatedTag.id}`, 'TagsService');
    return updatedTag;
  }

  /**
   * 删除标签
   */
  async remove(id: string): Promise<void> {
    const tag = await this.findOne(id);

    // 检查是否有关联的资产
    if (tag.usageCount > 0) {
      throw new ConflictException('不能删除有关联资产的标签');
    }

    await this.tagRepository.remove(tag);

    // 清除缓存
    await this.clearCache();

    this.logger.log(`标签删除成功: ${id}`, 'TagsService');
  }

  /**
   * 获取热门标签
   */
  async getPopularTags(limit: number = 20): Promise<Tag[]> {
    const cacheKey = `tags:popular:${limit}`;
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const tags = await this.tagRepository.find({
      where: { isActive: true },
      order: { usageCount: 'DESC' },
      take: limit,
    });

    // 缓存结果
    await this.cacheService.set(cacheKey, tags, 1800); // 30分钟缓存

    return tags;
  }

  /**
   * 获取特色标签
   */
  async getFeaturedTags(): Promise<Tag[]> {
    const cacheKey = 'tags:featured';
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const tags = await this.tagRepository.find({
      where: { isFeatured: true, isActive: true },
      order: { usageCount: 'DESC' },
    });

    // 缓存结果
    await this.cacheService.set(cacheKey, tags, 1800); // 30分钟缓存

    return tags;
  }

  /**
   * 搜索标签
   */
  async searchTags(query: string, limit: number = 10): Promise<Tag[]> {
    return await this.tagRepository.find({
      where: {
        name: Like(`%${query}%`),
        isActive: true,
      },
      order: { usageCount: 'DESC' },
      take: limit,
    });
  }

  /**
   * 增加标签使用次数
   */
  async incrementUsageCount(id: string): Promise<void> {
    await this.tagRepository.increment({ id }, 'usageCount', 1);
    await this.cacheService.del(`tag:${id}`);
  }

  /**
   * 减少标签使用次数
   */
  async decrementUsageCount(id: string): Promise<void> {
    await this.tagRepository.decrement({ id }, 'usageCount', 1);
    await this.cacheService.del(`tag:${id}`);
  }

  /**
   * 清除缓存
   */
  private async clearCache(): Promise<void> {
    await this.cacheService.delPattern('tags:*');
    await this.cacheService.delPattern('tag:*');
  }
}
