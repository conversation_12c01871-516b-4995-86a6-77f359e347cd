import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TemplateShare, ShareType, SharePermission } from './entities/template-share.entity';
import { SceneTemplate } from '../templates/entities/scene-template.entity';
import { LoggerService } from '../../common/services/logger.service';
import * as crypto from 'crypto';

export interface CreateShareDto {
  shareType: ShareType;
  permission: SharePermission;
  sharedWithId?: string;
  expiresAt?: Date;
  allowDownload?: boolean;
  allowClone?: boolean;
  requireLogin?: boolean;
  passwordProtected?: boolean;
  sharePassword?: string;
}

export interface UpdateShareDto {
  permission?: SharePermission;
  expiresAt?: Date;
  allowDownload?: boolean;
  allowClone?: boolean;
  requireLogin?: boolean;
  passwordProtected?: boolean;
  sharePassword?: string;
  isActive?: boolean;
}

@Injectable()
export class SharingService {
  constructor(
    @InjectRepository(TemplateShare)
    private readonly shareRepository: Repository<TemplateShare>,
    @InjectRepository(SceneTemplate)
    private readonly templateRepository: Repository<SceneTemplate>,
    private readonly logger: LoggerService,
  ) {}

  /**
   * 创建分享
   */
  async createShare(
    templateId: string,
    sharedById: string,
    createShareDto: CreateShareDto,
  ): Promise<TemplateShare> {
    // 验证模板是否存在
    const template = await this.templateRepository.findOne({
      where: { id: templateId },
      relations: ['creator'],
    });

    if (!template) {
      throw new NotFoundException('模板不存在');
    }

    // 检查权限
    if (template.creator.id !== sharedById) {
      throw new BadRequestException('只有模板创建者可以分享模板');
    }

    // 生成分享令牌
    const shareToken = this.generateShareToken();

    // 处理密码加密
    let encryptedPassword: string | null = null;
    if (createShareDto.passwordProtected && createShareDto.sharePassword) {
      encryptedPassword = await this.hashPassword(createShareDto.sharePassword);
    }

    const share = this.shareRepository.create({
      ...createShareDto,
      templateId,
      sharedById,
      shareToken,
      sharePassword: encryptedPassword,
    });

    const savedShare = await this.shareRepository.save(share);
    this.logger.log(`模板分享创建成功: ${templateId}`, 'SharingService');
    
    return savedShare;
  }

  /**
   * 获取模板的所有分享
   */
  async findByTemplate(templateId: string): Promise<TemplateShare[]> {
    return await this.shareRepository.find({
      where: { templateId },
      order: { createdAt: 'DESC' },
      relations: ['sharedBy', 'sharedWith'],
    });
  }

  /**
   * 根据分享令牌获取分享
   */
  async findByToken(shareToken: string): Promise<TemplateShare> {
    const share = await this.shareRepository.findOne({
      where: { shareToken },
      relations: ['template', 'sharedBy', 'sharedWith'],
    });

    if (!share) {
      throw new NotFoundException('分享不存在');
    }

    if (!share.canAccess) {
      throw new BadRequestException('分享已过期或被禁用');
    }

    return share;
  }

  /**
   * 访问分享（增加访问次数）
   */
  async accessShare(shareToken: string, password?: string): Promise<TemplateShare> {
    const share = await this.findByToken(shareToken);

    // 验证密码
    if (share.passwordProtected) {
      if (!password) {
        throw new BadRequestException('需要密码');
      }

      const isValidPassword = await this.verifyPassword(password, share.sharePassword);
      if (!isValidPassword) {
        throw new BadRequestException('密码错误');
      }
    }

    // 增加访问次数
    share.accessCount += 1;
    share.lastAccessedAt = new Date();
    
    await this.shareRepository.save(share);
    
    return share;
  }

  /**
   * 更新分享
   */
  async updateShare(
    id: string,
    sharedById: string,
    updateShareDto: UpdateShareDto,
  ): Promise<TemplateShare> {
    const share = await this.shareRepository.findOne({
      where: { id },
      relations: ['template'],
    });

    if (!share) {
      throw new NotFoundException('分享不存在');
    }

    // 检查权限
    if (share.sharedById !== sharedById) {
      throw new BadRequestException('只有分享创建者可以修改分享');
    }

    // 处理密码更新
    if (updateShareDto.passwordProtected && updateShareDto.sharePassword) {
      updateShareDto.sharePassword = await this.hashPassword(updateShareDto.sharePassword);
    }

    Object.assign(share, updateShareDto);
    const updatedShare = await this.shareRepository.save(share);

    this.logger.log(`模板分享更新成功: ${id}`, 'SharingService');
    return updatedShare;
  }

  /**
   * 删除分享
   */
  async removeShare(id: string, sharedById: string): Promise<void> {
    const share = await this.shareRepository.findOne({
      where: { id },
    });

    if (!share) {
      throw new NotFoundException('分享不存在');
    }

    // 检查权限
    if (share.sharedById !== sharedById) {
      throw new BadRequestException('只有分享创建者可以删除分享');
    }

    await this.shareRepository.remove(share);
    this.logger.log(`模板分享删除成功: ${id}`, 'SharingService');
  }

  /**
   * 获取用户创建的分享
   */
  async findByUser(
    userId: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<{ shares: TemplateShare[]; total: number }> {
    const [shares, total] = await this.shareRepository.findAndCount({
      where: { sharedById: userId },
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
      relations: ['template', 'sharedWith'],
    });

    return { shares, total };
  }

  /**
   * 获取分享给用户的模板
   */
  async findSharedWithUser(
    userId: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<{ shares: TemplateShare[]; total: number }> {
    const [shares, total] = await this.shareRepository.findAndCount({
      where: { sharedWithId: userId, isActive: true },
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
      relations: ['template', 'sharedBy'],
    });

    return { shares, total };
  }

  /**
   * 获取公开分享
   */
  async findPublicShares(
    page: number = 1,
    limit: number = 20,
  ): Promise<{ shares: TemplateShare[]; total: number }> {
    const [shares, total] = await this.shareRepository.findAndCount({
      where: { 
        shareType: ShareType.PUBLIC,
        isActive: true,
      },
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
      relations: ['template', 'sharedBy'],
    });

    return { shares, total };
  }

  /**
   * 生成分享令牌
   */
  private generateShareToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * 哈希密码
   */
  private async hashPassword(password: string): Promise<string> {
    const bcrypt = await import('bcrypt');
    return bcrypt.hash(password, 10);
  }

  /**
   * 验证密码
   */
  private async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
    const bcrypt = await import('bcrypt');
    return bcrypt.compare(password, hashedPassword);
  }

  /**
   * 清理过期分享
   */
  async cleanupExpiredShares(): Promise<number> {
    const expiredShares = await this.shareRepository.find({
      where: {
        isActive: true,
      },
    });

    const toDeactivate = expiredShares.filter(share => share.isExpired);
    
    if (toDeactivate.length > 0) {
      await this.shareRepository.update(
        { id: toDeactivate.map(s => s.id) as any },
        { isActive: false },
      );
    }

    this.logger.log(`清理过期分享: ${toDeactivate.length}个`, 'SharingService');
    return toDeactivate.length;
  }
}
