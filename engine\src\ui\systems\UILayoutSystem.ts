/**
 * UILayoutSystem.ts
 *
 * UI布局系统，管理UI元素的布局
 */

import { System } from '../../core/System';
import type { World } from '../../core/World';
import type { Entity } from '../../core/Entity';
import { Vector2 } from 'three';
import { UIComponent } from '../components/UIComponent';
import { UILayoutComponent, GridLayout, FlexLayout, AbsoluteLayout, RelativeLayout, GridLayoutParams, FlexLayoutParams, AbsoluteLayoutParams, RelativeLayoutParams, LayoutItemParams } from '../components/UILayoutComponent';
import { UISystem } from './UISystem';

/**
 * UI布局系统配置
 */
export interface UILayoutSystemConfig {
  // 是否启用调试模式
  debug?: boolean;

  // 是否自动应用布局
  autoApplyLayout?: boolean;

  // 自动应用布局的间隔（毫秒）
  autoApplyInterval?: number;
}

/**
 * UI布局系统
 * 管理UI元素的布局
 */
export class UILayoutSystem extends System {
  // UI系统引用
  private uiSystem: UISystem;

  // 配置
  private config: UILayoutSystemConfig;

  // 布局组件列表
  private layoutComponents: Map<Entity, UILayoutComponent> = new Map();

  // 上次应用布局的时间
  private lastApplyTime: number = 0;

  /**
   * 构造函数
   * @param world 世界实例
   * @param uiSystem UI系统实例
   * @param config UI布局系统配置
   */
  constructor(world: World, uiSystem: UISystem, config: UILayoutSystemConfig = {}) {
    // 调用基类构造函数，传入优先级（默认为0）
    super(0);

    // 设置世界引用（使用基类方法）
    this.setWorld(world);

    this.uiSystem = uiSystem;

    this.config = {
      debug: config.debug || false,
      autoApplyLayout: config.autoApplyLayout !== undefined ? config.autoApplyLayout : true,
      autoApplyInterval: config.autoApplyInterval || 500
    };
  }

  /**
   * 注册布局组件
   * @param entity 实体
   * @param component 布局组件
   */
  registerLayoutComponent(entity: Entity, component: UILayoutComponent): void {
    this.layoutComponents.set(entity, component);
    this.uiSystem.registerUILayoutComponent(entity, component);
  }

  /**
   * 注销布局组件
   * @param entity 实体
   */
  unregisterLayoutComponent(entity: Entity): void {
    this.layoutComponents.delete(entity);
    this.uiSystem.unregisterUILayoutComponent(entity);
  }

  /**
   * 获取或创建布局组件
   * @param entity 实体
   * @param layout 布局
   * @param layoutItemParams 布局项参数
   * @returns 布局组件
   */
  getOrCreateLayoutComponent(entity: Entity, layout: GridLayout | FlexLayout | AbsoluteLayout | RelativeLayout, layoutItemParams?: LayoutItemParams): UILayoutComponent {
    let component = this.layoutComponents.get(entity);

    if (!component) {
      component = new UILayoutComponent(entity, layout, layoutItemParams);
      this.registerLayoutComponent(entity, component);
    } else {
      component.setLayout(layout);
      if (layoutItemParams) {
        component.setLayoutItemParams(layoutItemParams);
      }
    }

    return component;
  }

  /**
   * 创建网格布局
   * @param entity 实体
   * @param uiComponent UI组件
   * @param params 网格布局参数
   * @returns 布局组件
   */
  createGridLayout(entity: Entity, uiComponent: UIComponent, params: GridLayoutParams): UILayoutComponent {
    const layout = new GridLayout(params);
    const component = this.getOrCreateLayoutComponent(entity, layout);

    // 应用布局
    component.applyLayout(uiComponent);

    return component;
  }

  /**
   * 创建弹性布局
   * @param entity 实体
   * @param uiComponent UI组件
   * @param params 弹性布局参数
   * @returns 布局组件
   */
  createFlexLayout(entity: Entity, uiComponent: UIComponent, params: FlexLayoutParams): UILayoutComponent {
    const layout = new FlexLayout(params);
    const component = this.getOrCreateLayoutComponent(entity, layout);

    // 应用布局
    component.applyLayout(uiComponent);

    return component;
  }

  /**
   * 创建绝对布局
   * @param entity 实体
   * @param uiComponent UI组件
   * @param params 绝对布局参数
   * @returns 布局组件
   */
  createAbsoluteLayout(entity: Entity, uiComponent: UIComponent, params: AbsoluteLayoutParams = {}): UILayoutComponent {
    const layout = new AbsoluteLayout(params);
    const component = this.getOrCreateLayoutComponent(entity, layout);

    // 应用布局
    component.applyLayout(uiComponent);

    return component;
  }

  /**
   * 创建相对布局
   * @param entity 实体
   * @param uiComponent UI组件
   * @param params 相对布局参数
   * @returns 布局组件
   */
  createRelativeLayout(entity: Entity, uiComponent: UIComponent, params: RelativeLayoutParams = {}): UILayoutComponent {
    const layout = new RelativeLayout(params);
    const component = this.getOrCreateLayoutComponent(entity, layout);

    // 应用布局
    component.applyLayout(uiComponent);

    return component;
  }

  /**
   * 设置布局项参数
   * @param entity 实体
   * @param params 布局项参数
   */
  setLayoutItemParams(entity: Entity, params: LayoutItemParams): void {
    let component = this.layoutComponents.get(entity);

    if (component) {
      component.setLayoutItemParams(params);
    } else {
      // 如果没有布局组件，创建一个空布局
      component = new UILayoutComponent(entity, new AbsoluteLayout(), params);
      this.registerLayoutComponent(entity, component);
    }
  }

  /**
   * 应用所有布局
   */
  applyAllLayouts(): void {
    for (const [entity, layoutComponent] of this.layoutComponents) {
      // 尝试获取实体上的UI组件
      // 注意：这里假设实体上已经添加了UIComponent组件
      // 实际实现可能需要根据具体的组件获取方式进行调整
      const uiComponent = entity.getComponent('UIComponent') as any as any as any as UIComponent;
      if (uiComponent) {
        layoutComponent.applyLayout(uiComponent);
      }
    }
  }

  /**
   * 创建居中布局
   * @param entity 实体
   * @param uiComponent UI组件
   * @returns 布局组件
   */
  createCenterLayout(entity: Entity, uiComponent: UIComponent): UILayoutComponent {
    // 居中布局实际上是一个特殊的绝对布局
    const layout = new AbsoluteLayout();
    const component = this.getOrCreateLayoutComponent(entity, layout);

    // 设置居中位置
    uiComponent.setPosition(new Vector2(0, 0));

    return component;
  }

  /**
   * 创建网格布局项
   * @param entity 实体
   * @param column 列位置
   * @param row 行位置
   * @param columnSpan 列跨度
   * @param rowSpan 行跨度
   */
  createGridLayoutItem(entity: Entity, column: number, row: number, columnSpan: number = 1, rowSpan: number = 1): void {
    const params: LayoutItemParams = {
      gridColumn: `${column} / span ${columnSpan}`,
      gridRow: `${row} / span ${rowSpan}`
    };

    this.setLayoutItemParams(entity, params);
  }

  /**
   * 创建弹性布局项
   * @param entity 实体
   * @param flexGrow 增长系数
   * @param flexShrink 收缩系数
   * @param flexBasis 基础尺寸
   * @param alignSelf 自对齐方式
   * @param order 顺序
   */
  createFlexLayoutItem(entity: Entity, flexGrow: number = 0, flexShrink: number = 1, flexBasis: number | string = 'auto', alignSelf: 'auto' | 'flex-start' | 'flex-end' | 'center' | 'baseline' | 'stretch' = 'auto', order: number = 0): void {
    const params: LayoutItemParams = {
      flexGrow,
      flexShrink,
      flexBasis,
      alignSelf,
      order
    };

    this.setLayoutItemParams(entity, params);
  }

  /**
   * 创建绝对布局项
   * @param entity 实体
   * @param left 左边距
   * @param top 上边距
   * @param right 右边距
   * @param bottom 下边距
   * @param zIndex 层级
   */
  createAbsoluteLayoutItem(entity: Entity, left?: number, top?: number, right?: number, bottom?: number, zIndex?: number): void {
    const params: LayoutItemParams = {
      left,
      top,
      right,
      bottom,
      zIndex
    };

    this.setLayoutItemParams(entity, params);
  }

  /**
   * 创建相对布局项
   * @param entity 实体
   * @param margin 外边距
   */
  createRelativeLayoutItem(entity: Entity, margin: number | { top?: number, right?: number, bottom?: number, left?: number }): void {
    const params: LayoutItemParams = {
      margin
    };

    this.setLayoutItemParams(entity, params);
  }

  /**
   * 更新系统
   * @param _deltaTime 时间增量 - 未使用，因为使用Date.now()来计算时间间隔
   */
  update(_deltaTime: number): void {
    // 如果启用自动应用布局
    if (this.config.autoApplyLayout) {
      const currentTime = Date.now();

      // 检查是否达到应用间隔
      if (currentTime - this.lastApplyTime >= this.config.autoApplyInterval!) {
        this.applyAllLayouts();
        this.lastApplyTime = currentTime;
      }
    }
  }

  /**
   * 销毁系统
   */
  dispose(): void {
    // 清空布局组件列表
    this.layoutComponents.clear();
  }
}
