/**
 * 创建测试用户脚本
 * 用于在数据库中创建一个测试用户，方便登录测试
 */

const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');

async function createTestUser() {
  let connection;
  
  try {
    // 连接数据库
    connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'DLEngine2024!@#',
      database: 'dl_engine_users'
    });

    console.log('✅ 数据库连接成功');

    // 检查用户表是否存在
    const [tables] = await connection.execute(
      "SHOW TABLES LIKE 'user'"
    );

    if (tables.length === 0) {
      console.log('📝 创建用户表...');
      await connection.execute(`
        CREATE TABLE user (
          id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
          username VARCHAR(50) UNIQUE NOT NULL,
          email VARCHAR(100) UNIQUE NOT NULL,
          password VARCHAR(255) NOT NULL,
          displayName VARCHAR(100),
          avatar VARCHAR(255),
          role ENUM('admin', 'user', 'guest') DEFAULT 'user',
          isActive BOOLEAN DEFAULT true,
          emailVerified BOOLEAN DEFAULT false,
          lastLoginAt DATETIME,
          createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
          updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_username (username),
          INDEX idx_email (email),
          INDEX idx_role (role),
          INDEX idx_active (isActive)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);
      console.log('✅ 用户表创建成功');
    }

    // 检查测试用户是否已存在
    const [existingUsers] = await connection.execute(
      'SELECT id FROM user WHERE email = ? OR username = ?',
      ['<EMAIL>', 'testuser']
    );

    if (existingUsers.length > 0) {
      console.log('⚠️  测试用户已存在，跳过创建');
      return;
    }

    // 创建测试用户
    const hashedPassword = await bcrypt.hash('123456', 10);
    
    await connection.execute(`
      INSERT INTO user (username, email, password, displayName, role, isActive, emailVerified)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      'testuser',
      '<EMAIL>',
      hashedPassword,
      '测试用户',
      'user',
      true,
      true
    ]);

    console.log('✅ 测试用户创建成功');
    console.log('📋 登录信息:');
    console.log('   邮箱: <EMAIL>');
    console.log('   密码: 123456');

    // 创建管理员用户
    const [existingAdmins] = await connection.execute(
      'SELECT id FROM user WHERE email = ? OR username = ?',
      ['<EMAIL>', 'admin']
    );

    if (existingAdmins.length === 0) {
      const adminPassword = await bcrypt.hash('admin123', 10);
      
      await connection.execute(`
        INSERT INTO user (username, email, password, displayName, role, isActive, emailVerified)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        'admin',
        '<EMAIL>',
        adminPassword,
        '系统管理员',
        'admin',
        true,
        true
      ]);

      console.log('✅ 管理员用户创建成功');
      console.log('📋 管理员登录信息:');
      console.log('   邮箱: <EMAIL>');
      console.log('   密码: admin123');
    }

  } catch (error) {
    console.error('❌ 创建测试用户失败:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 请确保MySQL数据库正在运行');
      console.log('💡 可以运行: docker-compose -f docker-compose.windows.yml up mysql');
    }
    
    if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('💡 数据库不存在，请先创建数据库');
      console.log('💡 可以运行: docker exec -it dl-engine-mysql-win mysql -u root -p');
      console.log('💡 然后执行: CREATE DATABASE dl_engine_users;');
    }
    
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行脚本
createTestUser();
