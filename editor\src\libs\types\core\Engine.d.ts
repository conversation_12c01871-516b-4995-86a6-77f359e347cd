/**
 * 引擎核心类
 * 负责管理世界、系统和渲染循环
 */
import { World } from './World';
import { System } from './System';
import { EventEmitter } from '../utils/EventEmitter';
import { Renderer } from '../rendering/Renderer';
import { type Camera } from '../rendering/Camera';
import { AssetManager } from '../assets/AssetManager';
import { I18n } from '../i18n/I18n';
export interface EngineOptions {
    /** 画布元素或ID */
    canvas?: HTMLCanvasElement | string;
    /** 是否自动开始渲染循环 */
    autoStart?: boolean;
    /** 是否启用调试模式 */
    debug?: boolean;
    /** 默认语言 */
    language?: string;
}
export declare class Engine extends EventEmitter {
    /** 世界实例 */
    private world;
    /** 系统列表 */
    private systems;
    /** 渲染器 */
    private renderer;
    /** 活跃相机 */
    private activeCamera;
    /** 资产管理器 */
    private assetManager;
    /** 国际化实例 */
    private i18n;
    /** 是否正在运行 */
    private running;
    /** 是否已初始化 */
    private initialized;
    /** 是否处于调试模式 */
    private debug;
    /** 动画帧ID */
    private animationFrameId;
    /** 上一帧时间戳 */
    private lastFrameTime;
    /** 固定更新累积时间 */
    private fixedUpdateAccumulator;
    /** 固定更新时间步长（秒） */
    private fixedUpdateTimeStep;
    /**
     * 创建引擎实例
     * @param options 引擎选项
     */
    constructor(options?: EngineOptions);
    /**
     * 初始化引擎
     */
    initialize(): void;
    /**
     * 开始渲染循环
     */
    start(): void;
    /**
     * 停止渲染循环
     */
    stop(): void;
    /**
     * 更新循环
     * @param timestamp 当前时间戳
     */
    private update;
    /**
     * 添加系统
     * @param system 系统实例
     * @returns 添加的系统
     */
    addSystem<T extends System>(system: T): T;
    /**
     * 获取系统
     * @param type 系统类型
     * @returns 系统实例，如果不存在则返回null
     */
    getSystem<T extends System>(type: string): T | null;
    /**
     * 移除系统
     * @param system 系统实例或类型
     * @returns 是否成功移除
     */
    removeSystem(system: System | string): boolean;
    /**
     * 获取世界
     * @returns 世界实例
     */
    getWorld(): World;
    /**
     * 获取渲染器
     * @returns 渲染器实例
     */
    getRenderer(): Renderer;
    /**
     * 设置活跃相机
     * @param camera 相机实例
     */
    setActiveCamera(camera: Camera): void;
    /**
     * 获取活跃相机
     * @returns 活跃相机实例
     */
    getActiveCamera(): Camera | null;
    /**
     * 获取资产管理器
     * @returns 资产管理器实例
     */
    getAssetManager(): AssetManager;
    /**
     * 获取国际化实例
     * @returns 国际化实例
     */
    getI18n(): I18n;
    /**
     * 是否处于调试模式
     * @returns 是否处于调试模式
     */
    isDebug(): boolean;
    /**
     * 设置调试模式
     * @param debug 是否启用调试模式
     */
    setDebug(debug: boolean): void;
    /**
     * 是否正在运行
     * @returns 是否正在运行
     */
    isRunning(): boolean;
    /**
     * 销毁引擎
     */
    dispose(): void;
}
