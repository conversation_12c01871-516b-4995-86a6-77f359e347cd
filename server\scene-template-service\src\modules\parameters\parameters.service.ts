import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TemplateParameter } from './entities/template-parameter.entity';
import { SceneTemplate } from '../templates/entities/scene-template.entity';
import { CreateTemplateParameterDto, UpdateTemplateParameterDto } from './dto/create-parameter.dto';
import { LoggerService } from '../../common/services/logger.service';

@Injectable()
export class ParametersService {
  constructor(
    @InjectRepository(TemplateParameter)
    private readonly parameterRepository: Repository<TemplateParameter>,
    @InjectRepository(SceneTemplate)
    private readonly templateRepository: Repository<SceneTemplate>,
    private readonly logger: LoggerService,
  ) {}

  /**
   * 创建模板参数
   */
  async create(
    templateId: string,
    createParameterDto: CreateTemplateParameterDto,
    userId: string,
  ): Promise<TemplateParameter> {
    // 验证模板是否存在且用户有权限
    const template = await this.templateRepository.findOne({
      where: { id: templateId },
      relations: ['creator'],
    });

    if (!template) {
      throw new NotFoundException('模板不存在');
    }

    if (template.creator.id !== userId) {
      throw new ForbiddenException('无权限修改此模板');
    }

    // 检查参数键名是否重复
    const existingParameter = await this.parameterRepository.findOne({
      where: { templateId, key: createParameterDto.key },
    });

    if (existingParameter) {
      throw new ForbiddenException('参数键名已存在');
    }

    const parameter = this.parameterRepository.create({
      ...createParameterDto,
      template,
      templateId,
    });

    const savedParameter = await this.parameterRepository.save(parameter);
    this.logger.log(`模板参数创建成功: ${savedParameter.id}`, 'ParametersService');
    
    return savedParameter;
  }

  /**
   * 获取模板的所有参数
   */
  async findByTemplate(templateId: string): Promise<TemplateParameter[]> {
    return await this.parameterRepository.find({
      where: { templateId },
      order: { group: 'ASC', sortOrder: 'ASC', name: 'ASC' },
    });
  }

  /**
   * 根据ID获取参数
   */
  async findOne(id: string): Promise<TemplateParameter> {
    const parameter = await this.parameterRepository.findOne({
      where: { id },
      relations: ['template', 'template.creator'],
    });

    if (!parameter) {
      throw new NotFoundException('参数不存在');
    }

    return parameter;
  }

  /**
   * 更新参数
   */
  async update(
    id: string,
    updateParameterDto: UpdateTemplateParameterDto,
    userId: string,
  ): Promise<TemplateParameter> {
    const parameter = await this.findOne(id);

    // 权限检查
    if (parameter.template.creator.id !== userId) {
      throw new ForbiddenException('无权限修改此参数');
    }

    // 如果更新键名，检查是否重复
    if (updateParameterDto.key && updateParameterDto.key !== parameter.key) {
      const existingParameter = await this.parameterRepository.findOne({
        where: { templateId: parameter.templateId, key: updateParameterDto.key },
      });

      if (existingParameter) {
        throw new ForbiddenException('参数键名已存在');
      }
    }

    Object.assign(parameter, updateParameterDto);
    const updatedParameter = await this.parameterRepository.save(parameter);

    this.logger.log(`模板参数更新成功: ${updatedParameter.id}`, 'ParametersService');
    return updatedParameter;
  }

  /**
   * 删除参数
   */
  async remove(id: string, userId: string): Promise<void> {
    const parameter = await this.findOne(id);

    // 权限检查
    if (parameter.template.creator.id !== userId) {
      throw new ForbiddenException('无权限删除此参数');
    }

    await this.parameterRepository.remove(parameter);
    this.logger.log(`模板参数删除成功: ${id}`, 'ParametersService');
  }

  /**
   * 批量更新参数排序
   */
  async updateOrder(
    templateId: string,
    parameterOrders: { id: string; sortOrder: number }[],
    userId: string,
  ): Promise<void> {
    // 验证模板权限
    const template = await this.templateRepository.findOne({
      where: { id: templateId },
      relations: ['creator'],
    });

    if (!template) {
      throw new NotFoundException('模板不存在');
    }

    if (template.creator.id !== userId) {
      throw new ForbiddenException('无权限修改此模板');
    }

    // 批量更新排序
    for (const order of parameterOrders) {
      await this.parameterRepository.update(
        { id: order.id, templateId },
        { sortOrder: order.sortOrder },
      );
    }

    this.logger.log(`模板参数排序更新成功: ${templateId}`, 'ParametersService');
  }

  /**
   * 复制参数到另一个模板
   */
  async copyToTemplate(
    sourceTemplateId: string,
    targetTemplateId: string,
    userId: string,
  ): Promise<TemplateParameter[]> {
    // 验证目标模板权限
    const targetTemplate = await this.templateRepository.findOne({
      where: { id: targetTemplateId },
      relations: ['creator'],
    });

    if (!targetTemplate) {
      throw new NotFoundException('目标模板不存在');
    }

    if (targetTemplate.creator.id !== userId) {
      throw new ForbiddenException('无权限修改目标模板');
    }

    // 获取源模板参数
    const sourceParameters = await this.findByTemplate(sourceTemplateId);

    // 复制参数
    const copiedParameters: TemplateParameter[] = [];
    for (const sourceParam of sourceParameters) {
      const newParameter = this.parameterRepository.create({
        ...sourceParam,
        id: undefined,
        template: targetTemplate,
        templateId: targetTemplateId,
      });

      const savedParameter = await this.parameterRepository.save(newParameter);
      copiedParameters.push(savedParameter);
    }

    this.logger.log(
      `参数复制成功: ${sourceTemplateId} -> ${targetTemplateId}`,
      'ParametersService',
    );

    return copiedParameters;
  }

  /**
   * 验证参数值
   */
  async validateParameterValues(
    templateId: string,
    values: Record<string, any>,
  ): Promise<{ isValid: boolean; errors: string[] }> {
    const parameters = await this.findByTemplate(templateId);
    const errors: string[] = [];

    for (const parameter of parameters) {
      const value = values[parameter.key];
      const validation = parameter.validateValue(value);
      
      if (!validation.isValid) {
        errors.push(...validation.errors);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 获取参数的默认值
   */
  async getDefaultValues(templateId: string): Promise<Record<string, any>> {
    const parameters = await this.findByTemplate(templateId);
    const defaultValues: Record<string, any> = {};

    for (const parameter of parameters) {
      if (parameter.defaultValue !== null && parameter.defaultValue !== undefined) {
        defaultValues[parameter.key] = parameter.defaultValue;
      }
    }

    return defaultValues;
  }

  /**
   * 按分组获取参数
   */
  async findByTemplateGrouped(templateId: string): Promise<Record<string, TemplateParameter[]>> {
    const parameters = await this.findByTemplate(templateId);
    const grouped: Record<string, TemplateParameter[]> = {};

    for (const parameter of parameters) {
      const group = parameter.group || '默认';
      if (!grouped[group]) {
        grouped[group] = [];
      }
      grouped[group].push(parameter);
    }

    return grouped;
  }
}
