/**
 * 动画库页面
 */
import React, { useState, useEffect } from 'react';
import { Layout, Button, Modal, Dropdown, message, Spin, Empty, Table, Space, Input } from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  MoreOutlined,
  ExportOutlined,
  ImportOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import type { AppDispatch } from '../store';
import { RootState } from '../store';
import AnimationEditor from '../components/animation/AnimationEditor';
import { fetchAnimations, createAnimation, updateAnimation, deleteAnimation } from '../store/animations/animationsSlice';
import './AnimationLibraryPage.less';

const { Header, Content } = Layout;
const { Search } = Input;

const AnimationLibraryPage: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { animations, loading } = useSelector((state: RootState) => state.animations);
  const isAuthenticated = useSelector((state: RootState) => state.auth?.isAuthenticated || false);

  const [searchValue, setSearchValue] = useState<string>('');
  const [editorVisible, setEditorVisible] = useState<boolean>(false);
  const [currentAnimationId, setCurrentAnimationId] = useState<string | null>(null);
  const [previewAnimationId, setPreviewAnimationId] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);

  // 加载动画
  useEffect(() => {
    // 只在用户已认证时才加载动画
    if (isAuthenticated) {
      dispatch(fetchAnimations());
    }
  }, [dispatch, isAuthenticated]);
  
  // 过滤动画
  const filteredAnimations = animations.filter(animation => 
    animation.name.toLowerCase().includes(searchValue.toLowerCase())
  );
  
  // 打开编辑器
  const openEditor = (animationId?: string) => {
    setCurrentAnimationId(animationId || null);
    setEditorVisible(true);
  };
  
  // 关闭编辑器
  const closeEditor = () => {
    setEditorVisible(false);
    setCurrentAnimationId(null);
  };
  
  // 保存动画
  const handleSaveAnimation = (animationData: any) => {
    if (animationData.id) {
      dispatch(updateAnimation(animationData));
      message.success(t('editor.animation.updateSuccess'));
    } else {
      dispatch(createAnimation(animationData));
      message.success(t('editor.animation.createSuccess'));
    }
    closeEditor();
  };
  
  // 删除动画
  const handleDeleteAnimation = (animationId: string) => {
    Modal.confirm({
      title: t('editor.animation.confirmDelete'),
      content: t('editor.animation.confirmDeleteContent'),
      okText: t('editor.delete'),
      okType: 'danger',
      cancelText: t('editor.cancel'),
      onOk: () => {
        dispatch(deleteAnimation(animationId));
        message.success(t('editor.animation.deleteSuccess'));
      }});
  };
  
  // 复制动画
  const handleDuplicateAnimation = (animation: any) => {
    const newAnimation = {
      ...animation,
      id: undefined,
      name: `${animation.name} (${t('editor.copy')})`};
    dispatch(createAnimation(newAnimation));
    message.success(t('editor.animation.duplicateSuccess'));
  };
  
  // 预览动画
  const handlePreviewAnimation = (animationId: string) => {
    setPreviewAnimationId(animationId);
    setIsPlaying(true);
  };
  
  // 关闭预览
  const closePreview = () => {
    setPreviewAnimationId(null);
    setIsPlaying(false);
  };
  
  // 表格列定义
  const columns = [
    {
      title: t('editor.animation.name'),
      dataIndex: 'name',
      key: 'name',
      sorter: (a: any, b: any) => a.name.localeCompare(b.name)},
    {
      title: t('editor.animation.duration'),
      dataIndex: 'duration',
      key: 'duration',
      render: (duration: number) => `${duration.toFixed(2)}s`,
      sorter: (a: any, b: any) => a.duration - b.duration},
    {
      title: t('editor.animation.tracks'),
      dataIndex: 'tracks',
      key: 'tracks',
      render: (tracks: any[]) => tracks.length},
    {
      title: t('editor.animation.createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleDateString(),
      sorter: (a: any, b: any) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()},
    {
      title: t('editor.actions'),
      key: 'actions',
      render: (_: any, record: any) => (
        <Space size="small">
          <Button
            type="text"
            icon={<PlayCircleOutlined />}
            onClick={() => handlePreviewAnimation(record.id)}
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => openEditor(record.id)}
          />
          <Dropdown
            menu={{
              items: [
                {
                  key: 'duplicate',
                  icon: <CopyOutlined />,
                  label: t('editor.animation.duplicate'),
                  onClick: () => handleDuplicateAnimation(record)
                },
                {
                  key: 'export',
                  icon: <ExportOutlined />,
                  label: t('editor.animation.export'),
                  onClick: () => message.info(`导出动画: ${record.name}`)
                },
                {
                  key: 'delete',
                  icon: <DeleteOutlined />,
                  label: t('editor.animation.delete'),
                  danger: true,
                  onClick: () => handleDeleteAnimation(record.id)
                }
              ]
            }}
            trigger={['click']}
          >
            <Button type="text" icon={<MoreOutlined />} />
          </Dropdown>
        </Space>
      )},
  ];
  
  return (
    <Layout className="animation-library-page">
      <Header className="page-header">
        <div className="header-title">
          <h1>{t('editor.animation.library')}</h1>
        </div>
        <div className="header-actions">
          <Search
            placeholder={t('editor.animation.search') || '搜索动画'}
            value={searchValue}
            onChange={e => setSearchValue(e.target.value)}
            style={{ width: 250 }}
            prefix={<SearchOutlined />}
          />
          <Button type="primary" icon={<PlusOutlined />} onClick={() => openEditor()}>
            {t('editor.animation.create')}
          </Button>
          <Button icon={<ImportOutlined />}>
            {t('editor.animation.import')}
          </Button>
        </div>
      </Header>
      
      <Content className="page-content">
        {loading ? (
          <div className="loading-container">
            <Spin size="large" />
          </div>
        ) : filteredAnimations.length === 0 ? (
          <Empty
            description={
              searchValue
                ? t('editor.animation.noSearchResults')
                : t('editor.animation.noAnimations')
            }
          >
            <Button type="primary" icon={<PlusOutlined />} onClick={() => openEditor()}>
              {t('editor.animation.create')}
            </Button>
          </Empty>
        ) : (
          <Table
            dataSource={filteredAnimations}
            columns={columns}
            rowKey="id"
            pagination={{ pageSize: 10 }}
          />
        )}
      </Content>
      
      {/* 动画编辑器模态框 */}
      <Modal
        title={null}
        open={editorVisible}
        onCancel={closeEditor}
        footer={null}
        width="80%"
        style={{ top: 20 }}
        styles={{ body: { padding: 0, height: 'calc(90vh - 40px)' } }}
      >
        <AnimationEditor
          animationId={currentAnimationId || undefined}
          onSave={handleSaveAnimation}
          onCancel={closeEditor}
        />
      </Modal>
      
      {/* 动画预览模态框 */}
      <Modal
        title={t('editor.animation.preview')}
        open={!!previewAnimationId}
        onCancel={closePreview}
        footer={[
          <Button key="close" onClick={closePreview}>
            {t('editor.close')}
          </Button>,
        ]}
        width={600}
      >
        <div className="animation-preview">
          <div className="preview-canvas-container">
            <canvas className="preview-canvas" />
          </div>
          <div className="preview-controls">
            <Button
              icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={() => setIsPlaying(!isPlaying)}
            >
              {isPlaying ? t('editor.pause') : t('editor.play')}
            </Button>
          </div>
        </div>
      </Modal>
    </Layout>
  );
};

export default AnimationLibraryPage;
