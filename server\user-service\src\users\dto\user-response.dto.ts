/**
 * 用户响应DTO
 */
import { ApiProperty } from '@nestjs/swagger';
import { UserRole } from '../entities/user.entity';

export class UserResponseDto {
  @ApiProperty({
    description: '用户ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: '用户名',
    example: 'johndoe',
  })
  username: string;

  @ApiProperty({
    description: '电子邮箱',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: '显示名称',
    example: '<PERSON>',
    required: false,
  })
  displayName?: string;

  @ApiProperty({
    description: '是否已验证',
    example: true,
  })
  isVerified: boolean;

  @ApiProperty({
    description: '是否访客',
    example: false,
  })
  isGuest: boolean;

  @ApiProperty({
    description: '用户角色',
    enum: UserRole,
    example: UserRole.USER,
  })
  role: UserRole;

  @ApiProperty({
    description: '邀请码',
    example: 'INV123456',
    required: false,
  })
  inviteCode?: string;

  @ApiProperty({
    description: '最后登录时间',
    example: '2023-01-01T00:00:00Z',
    required: false,
  })
  lastLoginAt?: Date;

  @ApiProperty({
    description: '创建时间',
    example: '2023-01-01T00:00:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: '更新时间',
    example: '2023-01-01T00:00:00Z',
  })
  updatedAt: Date;
}
