import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { Redis } from 'ioredis';

export interface LockOptions {
  ttl?: number; // 锁的生存时间（毫秒）
  retryDelay?: number; // 重试延迟（毫秒）
  retryCount?: number; // 重试次数
  identifier?: string; // 锁标识符
}

export interface LockInfo {
  key: string;
  identifier: string;
  acquiredAt: Date;
  ttl: number;
  isLocked: boolean;
}

@Injectable()
export class DistributedLockService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(DistributedLockService.name);
  private redisClient: Redis;
  private readonly locks = new Map<string, LockInfo>();
  private readonly defaultOptions: Required<LockOptions> = {
    ttl: 30000, // 30秒
    retryDelay: 100, // 100毫秒
    retryCount: 10, // 重试10次
    identifier: this.generateIdentifier(),
  };

  constructor() {}

  async onModuleInit() {
    await this.initializeRedis();
    this.startLockMonitoring();
    this.logger.log('分布式锁服务初始化完成');
  }

  async onModuleDestroy() {
    // 释放所有锁
    await this.releaseAllLocks();
    
    if (this.redisClient) {
      await this.redisClient.disconnect();
    }
    this.logger.log('分布式锁服务已关闭');
  }

  /**
   * 获取锁
   */
  async acquireLock(
    key: string,
    options: LockOptions = {},
  ): Promise<string | null> {
    const opts = { ...this.defaultOptions, ...options };
    const lockKey = this.getLockKey(key);
    const identifier = opts.identifier || this.generateIdentifier();

    for (let i = 0; i < opts.retryCount; i++) {
      try {
        // 使用SET命令的NX和PX选项原子性地设置锁
        const result = await this.redisClient.set(
          lockKey,
          identifier,
          'PX',
          opts.ttl,
          'NX'
        );

        if (result === 'OK') {
          const lockInfo: LockInfo = {
            key: lockKey,
            identifier,
            acquiredAt: new Date(),
            ttl: opts.ttl,
            isLocked: true,
          };

          this.locks.set(lockKey, lockInfo);
          this.logger.debug(`获取锁成功: ${key}`, { identifier, ttl: opts.ttl });
          return identifier;
        }

        // 如果获取失败，等待后重试
        if (i < opts.retryCount - 1) {
          await this.sleep(opts.retryDelay);
        }

      } catch (error) {
        this.logger.error(`获取锁失败: ${key}`, error);
        throw error;
      }
    }

    this.logger.warn(`获取锁超时: ${key}`, { retryCount: opts.retryCount });
    return null;
  }

  /**
   * 释放锁
   */
  async releaseLock(key: string, identifier: string): Promise<boolean> {
    const lockKey = this.getLockKey(key);

    try {
      // 使用Lua脚本确保原子性释放锁
      const luaScript = `
        if redis.call("GET", KEYS[1]) == ARGV[1] then
          return redis.call("DEL", KEYS[1])
        else
          return 0
        end
      `;

      const result = await this.redisClient.eval(luaScript, 1, lockKey, identifier) as number;

      if (result === 1) {
        this.locks.delete(lockKey);
        this.logger.debug(`释放锁成功: ${key}`, { identifier });
        return true;
      } else {
        this.logger.warn(`释放锁失败，锁不存在或标识符不匹配: ${key}`, { identifier });
        return false;
      }

    } catch (error) {
      this.logger.error(`释放锁失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 续期锁
   */
  async renewLock(
    key: string,
    identifier: string,
    ttl: number = this.defaultOptions.ttl,
  ): Promise<boolean> {
    const lockKey = this.getLockKey(key);

    try {
      // 使用Lua脚本确保原子性续期
      const luaScript = `
        if redis.call("GET", KEYS[1]) == ARGV[1] then
          return redis.call("PEXPIRE", KEYS[1], ARGV[2])
        else
          return 0
        end
      `;

      const result = await this.redisClient.eval(luaScript, 1, lockKey, identifier, ttl.toString()) as number;

      if (result === 1) {
        const lockInfo = this.locks.get(lockKey);
        if (lockInfo) {
          lockInfo.ttl = ttl;
        }
        this.logger.debug(`续期锁成功: ${key}`, { identifier, ttl });
        return true;
      } else {
        this.logger.warn(`续期锁失败，锁不存在或标识符不匹配: ${key}`, { identifier });
        return false;
      }

    } catch (error) {
      this.logger.error(`续期锁失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 检查锁是否存在
   */
  async isLocked(key: string): Promise<boolean> {
    const lockKey = this.getLockKey(key);
    
    try {
      const result = await this.redisClient.exists(lockKey);
      return result === 1;
    } catch (error) {
      this.logger.error(`检查锁状态失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 获取锁信息
   */
  async getLockInfo(key: string): Promise<LockInfo | null> {
    const lockKey = this.getLockKey(key);
    
    try {
      const [value, ttl] = await Promise.all([
        this.redisClient.get(lockKey),
        this.redisClient.pttl(lockKey),
      ]);

      if (value && ttl > 0) {
        return {
          key: lockKey,
          identifier: value,
          acquiredAt: new Date(Date.now() - ttl),
          ttl,
          isLocked: true,
        };
      }

      return null;
    } catch (error) {
      this.logger.error(`获取锁信息失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 使用锁执行操作
   */
  async withLock<T>(
    key: string,
    operation: () => Promise<T>,
    options: LockOptions = {},
  ): Promise<T> {
    const identifier = await this.acquireLock(key, options);
    
    if (!identifier) {
      throw new Error(`无法获取锁: ${key}`);
    }

    try {
      return await operation();
    } finally {
      await this.releaseLock(key, identifier);
    }
  }

  /**
   * 批量获取锁
   */
  async acquireMultipleLocks(
    keys: string[],
    options: LockOptions = {},
  ): Promise<Map<string, string | null>> {
    const results = new Map<string, string | null>();
    const acquiredLocks: Array<{ key: string; identifier: string }> = [];

    try {
      for (const key of keys) {
        const identifier = await this.acquireLock(key, options);
        results.set(key, identifier);
        
        if (identifier) {
          acquiredLocks.push({ key, identifier });
        } else {
          // 如果任何一个锁获取失败，释放已获取的锁
          for (const { key: acquiredKey, identifier: acquiredId } of acquiredLocks) {
            await this.releaseLock(acquiredKey, acquiredId);
          }
          throw new Error(`无法获取所有锁，失败的锁: ${key}`);
        }
      }

      return results;
    } catch (error) {
      this.logger.error('批量获取锁失败', error);
      throw error;
    }
  }

  /**
   * 批量释放锁
   */
  async releaseMultipleLocks(locks: Map<string, string>): Promise<void> {
    const promises = Array.from(locks.entries()).map(([key, identifier]) =>
      this.releaseLock(key, identifier)
    );

    await Promise.all(promises);
  }

  /**
   * 获取所有锁状态
   */
  getAllLocks(): LockInfo[] {
    return Array.from(this.locks.values());
  }

  /**
   * 强制释放锁（管理员操作）
   */
  async forceReleaseLock(key: string): Promise<boolean> {
    const lockKey = this.getLockKey(key);
    
    try {
      const result = await this.redisClient.del(lockKey);
      this.locks.delete(lockKey);
      
      if (result === 1) {
        this.logger.warn(`强制释放锁: ${key}`);
        return true;
      }
      
      return false;
    } catch (error) {
      this.logger.error(`强制释放锁失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 初始化Redis连接
   */
  private async initializeRedis(): Promise<void> {
    const redisHost = process.env.REDIS_HOST || 'localhost';
    const redisPort = parseInt(process.env.REDIS_PORT || '6379');
    const redisPassword = process.env.REDIS_PASSWORD || '';

    this.redisClient = new Redis({
      host: redisHost,
      port: redisPort,
      password: redisPassword || undefined,
    });

    this.logger.log('Redis连接已建立');
  }

  /**
   * 开始锁监控
   */
  private startLockMonitoring(): void {
    setInterval(() => {
      this.cleanupExpiredLocks();
    }, 10000); // 每10秒清理一次过期锁
  }

  /**
   * 清理过期锁
   */
  private async cleanupExpiredLocks(): Promise<void> {
    const now = Date.now();
    const expiredLocks: string[] = [];

    for (const [key, lockInfo] of this.locks) {
      const expireTime = lockInfo.acquiredAt.getTime() + lockInfo.ttl;
      if (now > expireTime) {
        expiredLocks.push(key);
      }
    }

    for (const key of expiredLocks) {
      this.locks.delete(key);
    }

    if (expiredLocks.length > 0) {
      this.logger.debug(`清理了 ${expiredLocks.length} 个过期锁`);
    }
  }

  /**
   * 释放所有锁
   */
  private async releaseAllLocks(): Promise<void> {
    const promises = Array.from(this.locks.entries()).map(([key, lockInfo]) =>
      this.releaseLock(key.replace('lock:', ''), lockInfo.identifier)
    );

    await Promise.allSettled(promises);
    this.locks.clear();
  }

  /**
   * 获取锁键名
   */
  private getLockKey(key: string): string {
    return `lock:${key}`;
  }

  /**
   * 生成唯一标识符
   */
  private generateIdentifier(): string {
    const serviceName = process.env.SERVICE_NAME || 'unknown';
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    return `${serviceName}:${timestamp}:${random}`;
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
