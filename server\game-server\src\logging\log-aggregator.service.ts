import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import * as fs from 'fs';
import * as path from 'path';
import * as zlib from 'zlib';
import { EnhancedLoggerService, LogEntry, LogLevel } from './enhanced-logger.service';

/**
 * 日志聚合配置
 */
interface LogAggregatorConfig {
  enabled: boolean;
  aggregationInterval: string;
  archiveDir: string;
  maxArchiveFiles: number;
  maxArchiveAge: number;
  compressArchives: boolean;
}

/**
 * 日志聚合服务
 * 负责收集、聚合和归档日志
 */
@Injectable()
export class LogAggregatorService implements OnModuleInit {
  private readonly logger = new Logger(LogAggregatorService.name);
  private readonly config: LogAggregatorConfig;
  private aggregatedLogs: Map<string, LogEntry[]> = new Map();

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly enhancedLogger: EnhancedLoggerService,
  ) {
    this.config = {
      enabled: this.configService.get<boolean>('LOG_AGGREGATION_ENABLED', true),
      aggregationInterval: this.configService.get<string>('LOG_AGGREGATION_INTERVAL', CronExpression.EVERY_DAY_AT_MIDNIGHT),
      archiveDir: this.configService.get<string>('LOG_ARCHIVE_DIR', 'logs/archives'),
      maxArchiveFiles: this.configService.get<number>('MAX_ARCHIVE_FILES', 30),
      maxArchiveAge: this.configService.get<number>('MAX_ARCHIVE_AGE', 30 * 24 * 60 * 60 * 1000), // 30天
      compressArchives: this.configService.get<boolean>('COMPRESS_ARCHIVES', true),
    };
    
    // 创建归档目录
    if (this.config.enabled && !fs.existsSync(this.config.archiveDir)) {
      fs.mkdirSync(this.config.archiveDir, { recursive: true });
    }
  }

  /**
   * 模块初始化
   */
  onModuleInit() {
    if (this.config.enabled) {
      // 监听日志事件
      this.eventEmitter.on('logging.log', this.handleLogEvent.bind(this));
      
      this.enhancedLogger.info('日志聚合服务已启动', 'LogAggregatorService');
    }
  }

  /**
   * 处理日志事件
   */
  private handleLogEvent(logEntry: LogEntry): void {
    // 按日期和级别聚合日志
    const date = new Date(logEntry.timestamp).toISOString().split('T')[0];
    const key = `${date}-${logEntry.level}`;
    
    if (!this.aggregatedLogs.has(key)) {
      this.aggregatedLogs.set(key, []);
    }
    
    this.aggregatedLogs.get(key)!.push(logEntry);
  }

  /**
   * 定时聚合和归档日志
   */
  @Cron('0 0 * * * *') // 每小时执行一次
  async aggregateAndArchiveLogs() {
    if (!this.config.enabled) {
      return;
    }
    
    try {
      const now = Date.now();
      const yesterday = new Date(now - 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      
      // 聚合昨天的日志
      for (const [key, logs] of this.aggregatedLogs.entries()) {
        const [date, level] = key.split('-');
        
        // 只处理昨天及以前的日志
        if (date >= yesterday) {
          continue;
        }
        
        // 归档日志
        await this.archiveLogs(date, level as LogLevel, logs);
        
        // 从内存中移除已归档的日志
        this.aggregatedLogs.delete(key);
      }
      
      // 清理过期的归档文件
      await this.cleanupArchives();
      
      this.enhancedLogger.info(`日志聚合和归档完成，处理了 ${yesterday} 及以前的日志`, 'LogAggregatorService');
    } catch (error) {
      this.enhancedLogger.error(`日志聚合和归档失败: ${error.message}`, error.stack, 'LogAggregatorService');
    }
  }

  /**
   * 归档日志
   */
  private async archiveLogs(date: string, level: LogLevel, logs: LogEntry[]): Promise<void> {
    if (logs.length === 0) {
      return;
    }
    
    const fileName = `${date}-${level}.json`;
    const filePath = path.join(this.config.archiveDir, fileName);
    
    try {
      // 将日志写入文件
      const content = JSON.stringify(logs, null, 2);
      
      if (this.config.compressArchives) {
        // 压缩日志
        const compressedContent = zlib.gzipSync(content);
        fs.writeFileSync(`${filePath}.gz`, compressedContent);
      } else {
        // 不压缩
        fs.writeFileSync(filePath, content);
      }
      
      this.enhancedLogger.info(`已归档 ${logs.length} 条 ${level} 级别的日志到 ${fileName}${this.config.compressArchives ? '.gz' : ''}`, 'LogAggregatorService');
    } catch (error) {
      this.enhancedLogger.error(`归档日志失败: ${error.message}`, error.stack, 'LogAggregatorService');
      throw error;
    }
  }

  /**
   * 清理过期的归档文件
   */
  private async cleanupArchives(): Promise<void> {
    try {
      const files = fs.readdirSync(this.config.archiveDir);
      const now = Date.now();
      const maxAge = this.config.maxArchiveAge;
      
      // 按修改时间排序
      const fileStats = files.map(file => {
        const filePath = path.join(this.config.archiveDir, file);
        const stats = fs.statSync(filePath);
        return {
          file,
          filePath,
          mtime: stats.mtime.getTime(),
        };
      }).sort((a, b) => a.mtime - b.mtime);
      
      // 删除过期文件
      let deletedCount = 0;
      for (const { file, filePath, mtime } of fileStats) {
        // 检查文件是否过期
        if (now - mtime > maxAge) {
          fs.unlinkSync(filePath);
          deletedCount++;
          this.enhancedLogger.debug(`已删除过期归档文件: ${file}`, 'LogAggregatorService');
        }
      }
      
      // 如果文件数量超过最大值，删除最旧的文件
      if (fileStats.length - deletedCount > this.config.maxArchiveFiles) {
        const excessCount = fileStats.length - deletedCount - this.config.maxArchiveFiles;
        for (let i = 0; i < excessCount; i++) {
          if (i < fileStats.length && !fs.existsSync(fileStats[i].filePath)) {
            continue;
          }
          fs.unlinkSync(fileStats[i].filePath);
          deletedCount++;
          this.enhancedLogger.debug(`已删除多余归档文件: ${fileStats[i].file}`, 'LogAggregatorService');
        }
      }
      
      if (deletedCount > 0) {
        this.enhancedLogger.info(`已清理 ${deletedCount} 个过期或多余的归档文件`, 'LogAggregatorService');
      }
    } catch (error) {
      this.enhancedLogger.error(`清理归档文件失败: ${error.message}`, error.stack, 'LogAggregatorService');
    }
  }

  /**
   * 获取归档日志
   */
  async getArchivedLogs(date: string, level: LogLevel): Promise<LogEntry[]> {
    const fileName = `${date}-${level}.json`;
    const filePath = path.join(this.config.archiveDir, fileName);
    const gzipFilePath = `${filePath}.gz`;
    
    try {
      let content: string;
      
      if (fs.existsSync(gzipFilePath)) {
        // 读取压缩文件
        const compressedContent = fs.readFileSync(gzipFilePath);
        content = zlib.gunzipSync(compressedContent).toString();
      } else if (fs.existsSync(filePath)) {
        // 读取未压缩文件
        content = fs.readFileSync(filePath, 'utf8');
      } else {
        return [];
      }
      
      return JSON.parse(content);
    } catch (error) {
      this.enhancedLogger.error(`获取归档日志失败: ${error.message}`, error.stack, 'LogAggregatorService');
      return [];
    }
  }

  /**
   * 获取可用的归档日期
   */
  getAvailableArchiveDates(): string[] {
    try {
      const files = fs.readdirSync(this.config.archiveDir);
      const dates = new Set<string>();
      
      for (const file of files) {
        const match = file.match(/^(\d{4}-\d{2}-\d{2})-/);
        if (match) {
          dates.add(match[1]);
        }
      }
      
      return Array.from(dates).sort();
    } catch (error) {
      this.enhancedLogger.error(`获取可用归档日期失败: ${error.message}`, error.stack, 'LogAggregatorService');
      return [];
    }
  }

  /**
   * 获取特定日期可用的日志级别
   */
  getAvailableLevelsForDate(date: string): LogLevel[] {
    try {
      const files = fs.readdirSync(this.config.archiveDir);
      const levels = new Set<LogLevel>();
      
      for (const file of files) {
        const match = file.match(new RegExp(`^${date}-([a-z]+)\\.json(\\.gz)?$`));
        if (match) {
          levels.add(match[1] as LogLevel);
        }
      }
      
      return Array.from(levels);
    } catch (error) {
      this.enhancedLogger.error(`获取可用日志级别失败: ${error.message}`, error.stack, 'LogAggregatorService');
      return [];
    }
  }

  /**
   * 搜索归档日志
   */
  async searchArchivedLogs(query: string, startDate: string, endDate: string, level?: LogLevel): Promise<LogEntry[]> {
    try {
      const dates = this.getDateRange(startDate, endDate);
      const results: LogEntry[] = [];
      
      for (const date of dates) {
        const levels = level ? [level] : this.getAvailableLevelsForDate(date);
        
        for (const lvl of levels) {
          const logs = await this.getArchivedLogs(date, lvl);
          
          // 过滤日志
          const filteredLogs = logs.filter(log => {
            return (
              log.message.toLowerCase().includes(query.toLowerCase()) ||
              (log.context && log.context.toLowerCase().includes(query.toLowerCase())) ||
              (log.stack && log.stack.toLowerCase().includes(query.toLowerCase()))
            );
          });
          
          results.push(...filteredLogs);
        }
      }
      
      // 按时间排序
      results.sort((a, b) => b.timestamp - a.timestamp);
      
      return results;
    } catch (error) {
      this.enhancedLogger.error(`搜索归档日志失败: ${error.message}`, error.stack, 'LogAggregatorService');
      return [];
    }
  }

  /**
   * 获取日期范围
   */
  private getDateRange(startDate: string, endDate: string): string[] {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const dates: string[] = [];
    
    for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
      dates.push(date.toISOString().split('T')[0]);
    }
    
    return dates;
  }
}
