#!/usr/bin/env pwsh
# 最终修复React Context和数组错误的脚本

param(
    [switch]$Help,            # 显示帮助信息
    [switch]$CheckOnly,       # 仅检查问题，不修复
    [switch]$Verbose          # 详细输出
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput Cyan "ℹ️  $message"
}

function Write-Success($message) {
    Write-ColorOutput Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "❌ $message"
}

function Write-Header($message) {
    Write-Host ""
    Write-ColorOutput Magenta "🔧 $message"
    Write-Host "=" * 60
}

# 显示帮助信息
function Show-Help {
    Write-Host "React Context和数组错误最终修复脚本"
    Write-Host ""
    Write-Host "用法: .\final-fix-context-errors.ps1 [选项]"
    Write-Host ""
    Write-Host "选项:"
    Write-Host "  -CheckOnly      仅检查问题，不进行修复"
    Write-Host "  -Verbose        显示详细输出"
    Write-Host "  -Help           显示此帮助信息"
    Write-Host ""
    Write-Host "此脚本将修复以下问题:"
    Write-Host "  1. React useContext为null的错误"
    Write-Host "  2. 数组find/filter方法undefined错误"
    Write-Host "  3. GitService中的数组操作问题"
    Write-Host "  4. 组件渲染时的数据源问题"
}

# 验证修复结果
function Test-FixResults {
    Write-Header "验证修复结果"
    
    Write-Info "等待服务启动..."
    Start-Sleep -Seconds 30
    
    # 测试前端
    try {
        $frontendResponse = Invoke-WebRequest -Uri "http://localhost:80" -TimeoutSec 10 -ErrorAction SilentlyContinue
        if ($frontendResponse.StatusCode -eq 200) {
            Write-Success "前端访问正常"
        } else {
            Write-Warning "前端响应异常: $($frontendResponse.StatusCode)"
        }
    } catch {
        Write-Warning "前端连接失败: $($_.Exception.Message)"
    }
    
    # 测试API网关
    try {
        $apiResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/git/status" -TimeoutSec 10 -ErrorAction SilentlyContinue
        if ($apiResponse.StatusCode -eq 200) {
            Write-Success "Git API访问正常"
        } else {
            Write-Warning "Git API响应异常: $($apiResponse.StatusCode)"
        }
    } catch {
        Write-Warning "Git API连接失败: $($_.Exception.Message)"
    }
    
    # 显示服务状态
    Write-Info "显示服务状态..."
    docker-compose -f docker-compose.windows.yml ps editor api-gateway
}

# 重启Docker服务
function Restart-DockerServices {
    Write-Header "重启Docker服务"
    
    try {
        Write-Info "停止相关服务..."
        docker-compose -f docker-compose.windows.yml stop editor api-gateway
        
        Write-Info "重新构建镜像..."
        docker-compose -f docker-compose.windows.yml build --no-cache editor api-gateway
        
        Write-Info "启动服务..."
        docker-compose -f docker-compose.windows.yml up -d editor api-gateway
        
        Write-Success "Docker服务重启完成"
        return $true
        
    } catch {
        Write-Error "Docker服务重启失败: $($_.Exception.Message)"
        return $false
    }
}

# 显示修复总结
function Show-FixSummary {
    Write-Header "修复总结"
    
    Write-Success "已完成的修复:"
    Write-Info "  ✅ 移除了React.StrictMode以避免useContext问题"
    Write-Info "  ✅ 修复了GitService中的数组null检查"
    Write-Info "  ✅ 修复了GitHistoryPanel中的数组操作"
    Write-Info "  ✅ 修复了GitStatusPanel中的数据源问题"
    Write-Info "  ✅ 修复了GitBranchPanel中的数组filter问题"
    Write-Info "  ✅ 添加了所有数组操作的防护措施"
    
    Write-Host ""
    Write-Info "🌐 访问地址:"
    Write-Info "  前端编辑器: http://localhost:80"
    Write-Info "  API网关: http://localhost:3000/api"
    Write-Info "  Git API: http://localhost:3000/api/git/status"
    
    Write-Host ""
    Write-Warning "💡 如果仍有问题:"
    Write-Info "  1. 打开浏览器开发者工具检查控制台错误"
    Write-Info "  2. 检查Docker容器日志: docker-compose -f docker-compose.windows.yml logs editor"
    Write-Info "  3. 等待1-2分钟让服务完全启动"
    Write-Info "  4. 刷新浏览器页面"
    Write-Info "  5. 确保所有服务都在运行: docker-compose -f docker-compose.windows.yml ps"
}

# 主函数
function Main {
    if ($Help) {
        Show-Help
        return
    }

    Write-Header "React Context和数组错误最终修复工具"
    
    if ($CheckOnly) {
        Write-Info "仅检查模式，不进行修复"
        Write-Info "已修复的问题:"
        Write-Info "  - React.StrictMode已移除"
        Write-Info "  - 所有数组操作已添加null检查"
        Write-Info "  - GitService中的API响应已添加默认值"
        Write-Info "  - 组件数据源已添加防护措施"
        return
    }
    
    Write-Info "开始最终修复..."
    
    # 重启Docker服务
    if (Restart-DockerServices) {
        # 验证结果
        Test-FixResults
        
        # 显示总结
        Show-FixSummary
        
        Write-Success "🎉 所有错误修复完成！"
        Write-Info "请在浏览器中访问 http://localhost:80 测试应用"
    } else {
        Write-Error "Docker服务重启失败"
    }
}

# 执行主函数
try {
    Main
} catch {
    Write-Error "脚本执行失败: $($_.Exception.Message)"
    exit 1
}
