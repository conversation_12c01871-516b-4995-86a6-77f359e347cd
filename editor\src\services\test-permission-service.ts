/**
 * 权限服务测试文件
 * 用于验证修复后的权限服务是否正常工作
 */
import { permissionService, Permission } from './PermissionService';
import { CollaborationRole } from './PermissionService';

// 测试权限服务
function testPermissionService() {
  console.log('开始测试权限服务...');

  try {
    // 初始化权限服务
    permissionService.initialize();
    console.log('✅ 权限服务初始化成功');

    // 测试设置用户角色
    permissionService.setUserRole('user123', CollaborationRole.ADMIN, 'operator123');
    console.log('✅ 用户角色设置成功');

    // 测试获取用户角色
    const userRole = permissionService.getUserRole('user123');
    console.log('✅ 获取用户角色:', userRole);

    // 测试设置角色权限
    permissionService.setRolePermissions(
      CollaborationRole.ADMIN,
      [Permission.EDIT_SCENE, Permission.CREATE_ENTITY, Permission.DELETE_ENTITY],
      'operator123'
    );
    console.log('✅ 角色权限设置成功');

    // 测试检查权限
    const hasEditPermission = permissionService.hasPermission('user123', Permission.EDIT_SCENE);
    console.log('✅ 编辑场景权限检查:', hasEditPermission);

    // 测试添加自定义权限
    permissionService.addUserCustomPermission('user123', Permission.MANAGE_USERS, 'operator123');
    console.log('✅ 自定义权限添加成功');

    // 测试拒绝权限
    permissionService.denyUserPermission('user123', Permission.DELETE_ENTITY, 'operator123');
    console.log('✅ 权限拒绝设置成功');

    // 测试权限检查（应该被拒绝）
    const hasDeletePermission = permissionService.hasPermission('user123', Permission.DELETE_ENTITY);
    console.log('✅ 删除实体权限检查（应该为false）:', hasDeletePermission);

    // 测试获取用户所有权限
    const allPermissions = permissionService.getUserAllPermissions('user123');
    console.log('✅ 用户所有权限:', allPermissions);

    // 测试获取用户自定义权限
    const customPermissions = permissionService.getUserCustomPermissions('user123');
    console.log('✅ 用户自定义权限:', customPermissions);

    // 测试获取用户被拒绝的权限
    const deniedPermissions = permissionService.getUserDeniedPermissions('user123');
    console.log('✅ 用户被拒绝的权限:', deniedPermissions);

    // 测试检查多个权限
    const hasAnyPermission = permissionService.hasAnyPermission('user123', [
      Permission.EDIT_SCENE,
      Permission.DELETE_ENTITY
    ]);
    console.log('✅ 是否有任何权限:', hasAnyPermission);

    const hasAllPermissions = permissionService.hasAllPermissions('user123', [
      Permission.EDIT_SCENE,
      Permission.CREATE_ENTITY
    ]);
    console.log('✅ 是否有所有权限:', hasAllPermissions);

    // 测试移除被拒绝的权限
    permissionService.removeUserDeniedPermission('user123', Permission.DELETE_ENTITY, 'operator123');
    console.log('✅ 移除被拒绝的权限成功');

    // 再次检查删除权限（现在应该有权限）
    const hasDeletePermissionAfter = permissionService.hasPermission('user123', Permission.DELETE_ENTITY);
    console.log('✅ 删除实体权限检查（恢复后）:', hasDeletePermissionAfter);

    // 测试移除自定义权限
    permissionService.removeUserCustomPermission('user123', Permission.MANAGE_USERS, 'operator123');
    console.log('✅ 移除自定义权限成功');

    // 测试获取角色权限
    const rolePermissions = permissionService.getRolePermissions(CollaborationRole.ADMIN);
    console.log('✅ 管理员角色权限:', rolePermissions);

    // 测试获取所有用户角色
    const allUserRoles = permissionService.getAllUserRoles();
    console.log('✅ 所有用户角色数量:', allUserRoles.size);

    // 测试清除用户权限
    permissionService.clearUserPermissions('user123', 'operator123');
    console.log('✅ 清除用户权限成功');

    // 验证权限已被清除
    const hasPermissionAfterClear = permissionService.hasPermission('user123', Permission.EDIT_SCENE);
    console.log('✅ 清除后权限检查（应该为false）:', hasPermissionAfterClear);

    console.log('🎉 权限服务测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 导出测试函数
export { testPermissionService };

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  testPermissionService();
}
