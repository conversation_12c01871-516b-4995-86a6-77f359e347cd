/**
 * 可抛掷组件
 * 用于处理物体的抛掷行为
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { Vector3, Quaternion, Euler } from 'three';
import { EventEmitter, EventCallback } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';
import { PhysicsBodyComponent    } from '../../physics/components/PhysicsBodyComponent';
import { Hand } from './GrabbableComponent';

/**
 * 抛掷类型
 */
export enum ThrowType {
  /** 物理抛掷 - 使用物理引擎计算 */
  PHYSICS = 'physics',
  /** 速度抛掷 - 直接设置速度 */
  VELOCITY = 'velocity',
  /** 弹道抛掷 - 计算弹道轨迹 */
  BALLISTIC = 'ballistic'
}

/**
 * 可抛掷组件配置
 */
export interface ThrowableComponentConfig {
  /** 是否可抛掷 */
  throwable?: boolean;
  /** 抛掷类型 */
  throwType?: ThrowType;
  /** 抛掷力量倍数 */
  throwForceMultiplier?: number;
  /** 抛掷角速度倍数 */
  throwAngularForceMultiplier?: number;
  /** 抛掷速度平滑因子 */
  throwVelocitySmoothingFactor?: number;
  /** 抛掷历史记录长度 */
  throwHistoryLength?: number;
  /** 抛掷时是否保持旋转 */
  preserveRotationOnThrow?: boolean;
  /** 抛掷声音 */
  throwSound?: string;
  /** 抛掷回调 */
  onThrow?: (entity: Entity, velocity: Vector3, angularVelocity: Vector3) => void;
}

/**
 * 可抛掷组件
 */
export class ThrowableComponent extends Component {
  /** 组件类型 */
  public static readonly TYPE: string = 'ThrowableComponent';

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 是否可抛掷 */
  private _throwable: boolean;

  /** 抛掷类型 */
  private _throwType: ThrowType;

  /** 抛掷力量倍数 */
  private _throwForceMultiplier: number;

  /** 抛掷角速度倍数 */
  private _throwAngularForceMultiplier: number;

  /** 抛掷速度平滑因子 - 预留功能 */
  // @ts-ignore: 预留功能，暂未使用
  private _throwVelocitySmoothingFactor: number;

  /** 抛掷历史记录长度 */
  private _throwHistoryLength: number;

  /** 抛掷时是否保持旋转 - 预留功能 */
  // @ts-ignore: 预留功能，暂未使用
  private _preserveRotationOnThrow: boolean;

  /** 抛掷声音 */
  private _throwSound?: string;

  /** 抛掷回调 */
  private _onThrow?: (entity: Entity, velocity: Vector3, angularVelocity: Vector3) => void;

  /** 位置历史记录 */
  private _positionHistory: Vector3[] = [];

  /** 旋转历史记录 */
  private _rotationHistory: Quaternion[] = [];

  /** 时间戳历史记录 */
  private _timeHistory: number[] = [];

  /** 是否正在被抓取 */
  private _isGrabbed: boolean = false;

  /** 抓取者 - 预留功能 */
  // @ts-ignore: 预留功能，暂未使用
  private _grabber?: Entity;

  /** 抓取手 - 预留功能 */
  // @ts-ignore: 预留功能，暂未使用
  private _hand?: Hand;

  /** 上次更新时间 - 预留功能 */
  // @ts-ignore: 预留功能，暂未使用
  private _lastUpdateTime: number = 0;

  /**
   * 构造函数
   * @param entity 实体
   * @param config 配置
   */
  constructor(entity: Entity, config: ThrowableComponentConfig = {}) {
    super(ThrowableComponent.TYPE);
    this.entity = entity;

    // 设置配置
    this._throwable = config.throwable !== undefined ? config.throwable : true;
    this._throwType = config.throwType || ThrowType.PHYSICS;
    this._throwForceMultiplier = config.throwForceMultiplier || 1.0;
    this._throwAngularForceMultiplier = config.throwAngularForceMultiplier || 1.0;
    this._throwVelocitySmoothingFactor = config.throwVelocitySmoothingFactor || 0.5;
    this._throwHistoryLength = config.throwHistoryLength || 5;
    this._preserveRotationOnThrow = config.preserveRotationOnThrow !== undefined ? config.preserveRotationOnThrow : true;
    this._throwSound = config.throwSound;
    this._onThrow = config.onThrow;
  }

  /**
   * 获取是否可抛掷
   */
  get throwable(): boolean {
    return this._throwable;
  }

  /**
   * 设置是否可抛掷
   */
  set throwable(value: boolean) {
    this._throwable = value;
  }

  /**
   * 获取抛掷类型
   */
  get throwType(): ThrowType {
    return this._throwType;
  }

  /**
   * 设置抛掷类型
   */
  set throwType(value: ThrowType) {
    this._throwType = value;
  }

  /**
   * 获取抛掷力量倍数
   */
  get throwForceMultiplier(): number {
    return this._throwForceMultiplier;
  }

  /**
   * 设置抛掷力量倍数
   */
  set throwForceMultiplier(value: number) {
    this._throwForceMultiplier = value;
  }

  /**
   * 获取抛掷角速度倍数
   */
  get throwAngularForceMultiplier(): number {
    return this._throwAngularForceMultiplier;
  }

  /**
   * 设置抛掷角速度倍数
   */
  set throwAngularForceMultiplier(value: number) {
    this._throwAngularForceMultiplier = value;
  }

  /**
   * 开始抓取
   * @param grabber 抓取者
   * @param hand 抓取手
   */
  startGrab(grabber: Entity, hand: Hand): void {
    this._isGrabbed = true;
    this._grabber = grabber;
    this._hand = hand;

    // 清空历史记录
    this.clearHistory();
  }

  /**
   * 结束抓取
   */
  endGrab(): void {
    // 如果不可抛掷或未被抓取，则直接返回
    if (!this._throwable || !this._isGrabbed) {
      this._isGrabbed = false;
      this._grabber = undefined;
      this._hand = undefined;
      return;
    }

    // 计算抛掷速度和角速度
    const { velocity, angularVelocity } = this.calculateThrowVelocity();

    // 应用抛掷
    this.applyThrow(velocity, angularVelocity);

    // 触发抛掷事件
    this.eventEmitter.emit('throw', this.entity, velocity, angularVelocity);

    // 调用回调
    if (this._onThrow) {
      this._onThrow(this.entity, velocity, angularVelocity);
    }

    // 重置状态
    this._isGrabbed = false;
    this._grabber = undefined;
    this._hand = undefined;
  }

  /**
   * 更新组件
   * @param deltaTime 时间增量（秒）
   */
  update(_deltaTime: number): void {
    // 如果未被抓取，则不需要更新
    if (!this._isGrabbed) {
      return;
    }

    const now = Date.now();

    // 记录位置和旋转历史
    const transform = this.entity.getComponent('Transform') as any as any as any as any;
    if (transform) {
      // 添加到历史记录
      this._positionHistory.push(new Vector3(
        transform.position.x,
        transform.position.y,
        transform.position.z
      ));
      this._rotationHistory.push(new Quaternion(
        transform.rotation.x,
        transform.rotation.y,
        transform.rotation.z,
        transform.rotation.w
      ));
      this._timeHistory.push(now);

      // 限制历史记录长度
      if (this._positionHistory.length > this._throwHistoryLength) {
        this._positionHistory.shift();
        this._rotationHistory.shift();
        this._timeHistory.shift();
      }
    }

    this._lastUpdateTime = now;
  }

  /**
   * 清空历史记录
   */
  private clearHistory(): void {
    this._positionHistory = [];
    this._rotationHistory = [];
    this._timeHistory = [];
    this._lastUpdateTime = Date.now();
  }

  /**
   * 计算抛掷速度和角速度
   * @returns 速度和角速度
   */
  private calculateThrowVelocity(): { velocity: Vector3, angularVelocity: Vector3 } {
    // 默认速度和角速度
    const velocity = new Vector3();
    const angularVelocity = new Vector3();

    // 如果历史记录不足，则返回默认值
    if (this._positionHistory.length < 2 || this._timeHistory.length < 2) {
      return { velocity, angularVelocity };
    }

    // 计算线性速度
    const positions = this._positionHistory;
    const times = this._timeHistory;
    const rotations = this._rotationHistory;

    // 使用最近的几个点计算平均速度
    const velocities: Vector3[] = [];
    const weights: number[] = [];
    // const totalTime = times[times.length - 1] - times[0]; // 预留功能

    for (let i = 1; i < positions.length; i++) {
      const dt = (times[i] - times[i - 1]) / 1000; // 转换为秒
      if (dt > 0) {
        const v = new Vector3().subVectors(positions[i], positions[i - 1]).divideScalar(dt);
        velocities.push(v);

        // 越近的速度权重越大
        const weight = (i / positions.length) * (i / positions.length);
        weights.push(weight);
      }
    }

    // 计算加权平均速度
    let totalWeight = 0;
    for (let i = 0; i < velocities.length; i++) {
      velocity.add(velocities[i].multiplyScalar(weights[i]));
      totalWeight += weights[i];
    }

    if (totalWeight > 0) {
      velocity.divideScalar(totalWeight);
    }

    // 应用力量倍数
    velocity.multiplyScalar(this._throwForceMultiplier);

    // 计算角速度（如果有足够的旋转历史）
    if (rotations.length >= 2) {
      // 使用最近的两个旋转计算角速度
      const lastRotation = rotations[rotations.length - 1];
      const prevRotation = rotations[rotations.length - 2];
      const dt = (times[times.length - 1] - times[times.length - 2]) / 1000; // 转换为秒

      if (dt > 0) {
        // 计算四元数差值
        const deltaRotation = new Quaternion().multiplyQuaternions(
          lastRotation,
          new Quaternion().copy(prevRotation).invert()
        );

        // 转换为欧拉角
        const euler = new Euler().setFromQuaternion(deltaRotation);

        // 计算角速度
        angularVelocity.set(
          euler.x / dt,
          euler.y / dt,
          euler.z / dt
        );

        // 应用角速度倍数
        angularVelocity.multiplyScalar(this._throwAngularForceMultiplier);
      }
    }

    return { velocity, angularVelocity };
  }

  /**
   * 应用抛掷
   * @param velocity 速度
   * @param angularVelocity 角速度
   */
  private applyThrow(velocity: Vector3, angularVelocity: Vector3): void {
    // 根据抛掷类型应用不同的抛掷方式
    switch (this._throwType) {
      case ThrowType.PHYSICS:
        this.applyPhysicsThrow(velocity, angularVelocity);
        break;
      case ThrowType.VELOCITY:
        this.applyVelocityThrow(velocity, angularVelocity);
        break;
      case ThrowType.BALLISTIC:
        this.applyBallisticThrow(velocity, angularVelocity);
        break;
    }

    // 播放抛掷声音
    if (this._throwSound) {
      // 这里应该调用音频系统播放声音
      Debug.log('ThrowableComponent', `播放抛掷声音: ${this._throwSound}`);
    }
  }

  /**
   * 应用物理抛掷
   * @param velocity 速度
   * @param angularVelocity 角速度
   */
  private applyPhysicsThrow(velocity: Vector3, angularVelocity: Vector3): void {
    // 获取物理体组件
    const physicsBody = this.entity.getComponent<PhysicsBodyComponent>(PhysicsBodyComponent.type);
    if (!physicsBody) {
      Debug.warn('ThrowableComponent', `实体 ${this.entity.id} 没有物理体组件，无法应用物理抛掷`);
      return;
    }

    // 设置线性速度
    physicsBody.setLinearVelocity(velocity);

    // 设置角速度
    physicsBody.setAngularVelocity(angularVelocity);

    Debug.log('ThrowableComponent', `应用物理抛掷: 速度=${velocity.toArray()}, 角速度=${angularVelocity.toArray()}`);
  }

  /**
   * 应用速度抛掷
   * @param velocity 速度
   * @param angularVelocity 角速度
   */
  private applyVelocityThrow(velocity: Vector3, angularVelocity: Vector3): void {
    // 这种方式不使用物理引擎，而是直接设置速度
    // 需要在外部系统中处理移动和旋转

    // 存储速度和角速度
    (this.entity as any).throwVelocity = velocity.clone();
    (this.entity as any).throwAngularVelocity = angularVelocity.clone();
    (this.entity as any).throwTime = Date.now();

    Debug.log('ThrowableComponent', `应用速度抛掷: 速度=${velocity.toArray()}, 角速度=${angularVelocity.toArray()}`);
  }

  /**
   * 应用弹道抛掷
   * @param velocity 速度
   * @param angularVelocity 角速度
   */
  private applyBallisticThrow(velocity: Vector3, angularVelocity: Vector3): void {
    // 获取物理体组件
    const physicsBody = this.entity.getComponent<PhysicsBodyComponent>(PhysicsBodyComponent.type);
    if (!physicsBody) {
      Debug.warn('ThrowableComponent', `实体 ${this.entity.id} 没有物理体组件，无法应用弹道抛掷`);
      return;
    }

    // 设置线性速度
    physicsBody.setLinearVelocity(velocity);

    // 设置角速度
    physicsBody.setAngularVelocity(angularVelocity);

    // 注意：重力是在物理世界级别控制的，不是在单个物理体上

    Debug.log('ThrowableComponent', `应用弹道抛掷: 速度=${velocity.toArray()}, 角速度=${angularVelocity.toArray()}`);
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param listener 监听器函数
   */
  on(event: string, listener: EventCallback): this {
    this.eventEmitter.on(event, listener);
    return this;
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param listener 监听器函数
   */
  off(event: string, listener?: EventCallback): this {
    this.eventEmitter.off(event, listener);
    return this;
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    // 清空事件监听器
    this.eventEmitter.removeAllListeners();

    // 清空历史记录
    this.clearHistory();
  }
}
