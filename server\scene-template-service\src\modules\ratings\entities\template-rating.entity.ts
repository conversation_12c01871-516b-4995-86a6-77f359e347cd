import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  Index,
  Unique,
} from 'typeorm';
import { SceneTemplate } from '../../templates/entities/scene-template.entity';
import { User } from '../../auth/entities/user.entity';

@Entity('template_ratings')
@Unique(['template', 'user'])
@Index(['template', 'rating'])
@Index(['user', 'createdAt'])
export class TemplateRating {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'decimal', precision: 2, scale: 1 })
  @Index()
  rating: number; // 1.0 - 5.0

  @Column({ type: 'text', nullable: true })
  comment: string;

  @Column({ type: 'json', nullable: true })
  tags: string[]; // 评价标签，如：['易用', '美观', '实用']

  @Column({ name: 'is_verified', default: false })
  isVerified: boolean; // 是否为认证用户评价

  @Column({ name: 'is_helpful', default: false })
  isHelpful: boolean; // 是否为有用评价

  @Column({ name: 'helpful_count', default: 0 })
  helpfulCount: number; // 有用投票数

  @Column({ name: 'unhelpful_count', default: 0 })
  unhelpfulCount: number; // 无用投票数

  // 关联关系
  @ManyToOne(() => SceneTemplate, template => template.ratings, { onDelete: 'CASCADE' })
  template: SceneTemplate;

  @Column({ name: 'template_id' })
  @Index()
  templateId: string;

  @ManyToOne(() => User)
  user: User;

  @Column({ name: 'user_id' })
  @Index()
  userId: string;

  // 时间戳
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 计算属性
  get helpfulnessRatio(): number {
    const total = this.helpfulCount + this.unhelpfulCount;
    return total > 0 ? this.helpfulCount / total : 0;
  }

  get isPositive(): boolean {
    return this.rating >= 3.0;
  }

  get ratingStars(): string {
    const fullStars = Math.floor(this.rating);
    const hasHalfStar = this.rating % 1 >= 0.5;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
    
    return '★'.repeat(fullStars) + 
           (hasHalfStar ? '☆' : '') + 
           '☆'.repeat(emptyStars);
  }
}
