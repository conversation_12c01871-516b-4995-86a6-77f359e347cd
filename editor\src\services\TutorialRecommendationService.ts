/**
 * 教程推荐系统服务
 * 负责整合和推荐各类教程，包括交互式教程、视频教程和示例项目
 */
import { EventEmitter } from '../utils/EventEmitter';
import { TutorialService, Tutorial } from './TutorialService';
import { VideoTutorialService, VideoTutorial } from './VideoTutorialService';
import { ExampleProjectService, ExampleProject } from './ExampleProjectService';

/**
 * 教程类型枚举
 */
export enum TutorialType {
  INTERACTIVE = 'interactive',
  VIDEO = 'video',
  EXAMPLE = 'example'}

/**
 * 教程难度级别枚举
 */
export enum TutorialDifficulty {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced',
  EXPERT = 'expert'}

/**
 * 教程项接口
 */
export interface TutorialItem {
  id: string;
  title: string;
  description: string;
  type: TutorialType;
  category: string;
  difficulty: TutorialDifficulty;
  duration: number; // 预计完成时间（分钟）
  thumbnailUrl?: string;
  tags?: string[];
  prerequisites?: string[];
  seriesId?: string; // 所属系列ID
  completed?: boolean; // 是否已完成
  progress?: number; // 完成进度（0-100）
  popularity?: number; // 受欢迎程度（0-100）
  rating?: number; // 评分（0-5）
  lastViewedAt?: number; // 最后查看时间
}

/**
 * 教程系列接口
 */
export interface TutorialSeries {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: TutorialDifficulty;
  thumbnailUrl?: string;
  tags?: string[];
  tutorials: string[]; // 教程ID列表
  progress?: number; // 完成进度（0-100）
  popularity?: number; // 受欢迎程度（0-100）
  featured?: boolean; // 是否为精选系列
}

/**
 * 教程推荐系统服务类
 */
export class TutorialRecommendationService {
  private static instance: TutorialRecommendationService;
  private events = new EventEmitter();
  private tutorialItems: Map<string, TutorialItem> = new Map();
  private tutorialSeries: Map<string, TutorialSeries> = new Map();
  private userPreferences: Map<string, number> = new Map(); // 用户对类别的偏好度
  private userHistory: string[] = []; // 用户最近查看的教程ID
  private userCompletedItems: Set<string> = new Set(); // 用户已完成的教程ID

  private constructor() {
    this.initialize();
  }

  /**
   * 获取教程推荐系统服务实例
   */
  public static getInstance(): TutorialRecommendationService {
    if (!TutorialRecommendationService.instance) {
      TutorialRecommendationService.instance = new TutorialRecommendationService();
    }
    return TutorialRecommendationService.instance;
  }

  /**
   * 初始化服务
   */
  private initialize(): void {
    this.loadTutorialItems();
    this.loadTutorialSeries();
    this.loadUserData();
    this.setupEventListeners();
  }

  /**
   * 加载所有教程项
   */
  private loadTutorialItems(): void {
    // 加载交互式教程
    const interactiveTutorials = TutorialService.getInstance().getTutorials();
    interactiveTutorials.forEach(tutorial => {
      this.tutorialItems.set(this.getItemKey(TutorialType.INTERACTIVE, tutorial.id), this.convertInteractiveTutorial(tutorial));
    });

    // 加载视频教程
    const videoTutorials = VideoTutorialService.getInstance().getTutorials();
    videoTutorials.forEach(tutorial => {
      this.tutorialItems.set(this.getItemKey(TutorialType.VIDEO, tutorial.id), this.convertVideoTutorial(tutorial));
    });

    // 加载示例项目
    const exampleProjects = ExampleProjectService.getInstance().getExampleProjects();
    exampleProjects.forEach(example => {
      this.tutorialItems.set(this.getItemKey(TutorialType.EXAMPLE, example.id), this.convertExampleProject(example));
    });
  }

  /**
   * 加载教程系列
   */
  private loadTutorialSeries(): void {
    // 从配置加载预定义的教程系列
    import('../data/tutorialSeries').then(module => {
      const series = module.tutorialSeries;
      series.forEach((seriesData: TutorialSeries) => {
        this.tutorialSeries.set(seriesData.id, seriesData);
        
        // 更新教程项的系列ID
        seriesData.tutorials.forEach(tutorialId => {
          const item = this.tutorialItems.get(tutorialId);
          if (item) {
            item.seriesId = seriesData.id;
            this.tutorialItems.set(tutorialId, item);
          }
        });
      });
    }).catch(error => {
      console.error('Failed to load tutorial series:', error);
    });
  }

  /**
   * 加载用户数据
   */
  private loadUserData(): void {
    try {
      // 加载用户偏好
      const preferencesJson = localStorage.getItem('tutorialPreferences');
      if (preferencesJson) {
        this.userPreferences = new Map(Object.entries(JSON.parse(preferencesJson)));
      }

      // 加载用户历史
      const historyJson = localStorage.getItem('tutorialHistory');
      if (historyJson) {
        this.userHistory = JSON.parse(historyJson);
      }

      // 加载已完成的教程
      const completedJson = localStorage.getItem('completedTutorials');
      if (completedJson) {
        this.userCompletedItems = new Set(JSON.parse(completedJson));
      }

      // 更新教程项的完成状态
      this.updateCompletionStatus();
    } catch (error) {
      console.error('Failed to load user data:', error);
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听交互式教程完成事件
    TutorialService.getInstance().on('tutorialCompleted', (tutorial: Tutorial) => {
      const itemId = this.getItemKey(TutorialType.INTERACTIVE, tutorial.id);
      this.markItemAsCompleted(itemId);
      this.updateUserPreferences(tutorial.category, 10); // 增加对该类别的偏好度
    });

    // 监听视频教程观看事件
    VideoTutorialService.getInstance().on('tutorialWatched', (tutorialId: string) => {
      const itemId = this.getItemKey(TutorialType.VIDEO, tutorialId);
      this.markItemAsCompleted(itemId);
      const tutorial = VideoTutorialService.getInstance().getTutorialById(tutorialId);
      if (tutorial) {
        this.updateUserPreferences(tutorial.category, 8); // 增加对该类别的偏好度
      }
    });

    // 监听示例项目查看事件
    ExampleProjectService.getInstance().on('exampleViewed', (exampleId: string) => {
      const itemId = this.getItemKey(TutorialType.EXAMPLE, exampleId);
      this.addToHistory(itemId);
      const example = ExampleProjectService.getInstance().getExampleProjectById(exampleId);
      if (example) {
        this.updateUserPreferences(example.category, 5); // 增加对该类别的偏好度
      }
    });
  }

  /**
   * 获取教程项的唯一键
   */
  private getItemKey(type: TutorialType, id: string): string {
    return `${type}:${id}`;
  }

  /**
   * 转换交互式教程为统一格式
   */
  private convertInteractiveTutorial(tutorial: Tutorial): TutorialItem {
    return {
      id: this.getItemKey(TutorialType.INTERACTIVE, tutorial.id),
      title: tutorial.title,
      description: tutorial.description,
      type: TutorialType.INTERACTIVE,
      category: tutorial.category,
      difficulty: tutorial.difficulty as TutorialDifficulty,
      duration: tutorial.duration,
      tags: tutorial.tags,
      prerequisites: tutorial.prerequisites?.map(id => this.getItemKey(TutorialType.INTERACTIVE, id)),
      completed: TutorialService.getInstance().isTutorialCompleted(tutorial.id),
      progress: TutorialService.getInstance().isTutorialCompleted(tutorial.id) ? 100 : 0};
  }

  /**
   * 转换视频教程为统一格式
   */
  private convertVideoTutorial(tutorial: VideoTutorial): TutorialItem {
    return {
      id: this.getItemKey(TutorialType.VIDEO, tutorial.id),
      title: tutorial.title,
      description: tutorial.description,
      type: TutorialType.VIDEO,
      category: tutorial.category,
      difficulty: tutorial.difficulty as TutorialDifficulty,
      duration: tutorial.duration,
      thumbnailUrl: tutorial.thumbnailUrl,
      tags: tutorial.tags,
      prerequisites: tutorial.prerequisites?.map(id => this.getItemKey(TutorialType.VIDEO, id)),
      completed: VideoTutorialService.getInstance().isTutorialWatched(tutorial.id),
      progress: VideoTutorialService.getInstance().getTutorialProgress(tutorial.id)};
  }

  /**
   * 转换示例项目为统一格式
   */
  private convertExampleProject(example: ExampleProject): TutorialItem {
    return {
      id: this.getItemKey(TutorialType.EXAMPLE, example.id),
      title: example.title,
      description: example.description,
      type: TutorialType.EXAMPLE,
      category: example.category,
      difficulty: example.difficulty as TutorialDifficulty,
      duration: example.estimatedTime || 30, // 默认30分钟
      thumbnailUrl: example.thumbnailUrl,
      tags: example.tags,
      completed: this.userCompletedItems.has(this.getItemKey(TutorialType.EXAMPLE, example.id)),
      progress: 0, // 示例项目没有进度
      popularity: example.popularity,
      rating: example.rating};
  }

  /**
   * 更新教程项的完成状态
   */
  private updateCompletionStatus(): void {
    this.tutorialItems.forEach((item, key) => {
      if (item.type === TutorialType.INTERACTIVE) {
        const tutorialId = key.split(':')[1];
        item.completed = TutorialService.getInstance().isTutorialCompleted(tutorialId);
        item.progress = item.completed ? 100 : 0;
      } else if (item.type === TutorialType.VIDEO) {
        const tutorialId = key.split(':')[1];
        item.completed = VideoTutorialService.getInstance().isTutorialWatched(tutorialId);
        item.progress = VideoTutorialService.getInstance().getTutorialProgress(tutorialId);
      } else if (item.type === TutorialType.EXAMPLE) {
        item.completed = this.userCompletedItems.has(key);
      }
    });
  }

  /**
   * 标记教程项为已完成
   */
  private markItemAsCompleted(itemId: string): void {
    this.userCompletedItems.add(itemId);
    const item = this.tutorialItems.get(itemId);
    if (item) {
      item.completed = true;
      item.progress = 100;
      this.tutorialItems.set(itemId, item);
    }
    this.saveUserData();
    this.events.emit('itemCompleted', itemId);
    
    // 检查系列是否完成
    if (item && item.seriesId) {
      this.checkSeriesCompletion(item.seriesId);
    }
  }

  /**
   * 检查系列是否完成
   */
  private checkSeriesCompletion(seriesId: string): void {
    const series = this.tutorialSeries.get(seriesId);
    if (!series) return;
    
    let completedCount = 0;
    series.tutorials.forEach(tutorialId => {
      if (this.tutorialItems.get(tutorialId)?.completed) {
        completedCount++;
      }
    });
    
    const progress = Math.round((completedCount / series.tutorials.length) * 100);
    series.progress = progress;
    this.tutorialSeries.set(seriesId, series);
    
    if (progress === 100) {
      this.events.emit('seriesCompleted', seriesId);
    }
  }

  /**
   * 添加到历史记录
   */
  private addToHistory(itemId: string): void {
    // 移除已存在的相同ID
    this.userHistory = this.userHistory.filter(id => id !== itemId);
    // 添加到历史记录开头
    this.userHistory.unshift(itemId);
    // 限制历史记录长度
    if (this.userHistory.length > 20) {
      this.userHistory = this.userHistory.slice(0, 20);
    }
    this.saveUserData();
  }

  /**
   * 更新用户偏好
   */
  private updateUserPreferences(category: string, value: number): void {
    const currentValue = this.userPreferences.get(category) || 0;
    this.userPreferences.set(category, currentValue + value);
    this.saveUserData();
  }

  /**
   * 保存用户数据
   */
  private saveUserData(): void {
    try {
      // 保存用户偏好
      localStorage.setItem('tutorialPreferences', JSON.stringify(Object.fromEntries(this.userPreferences)));
      
      // 保存用户历史
      localStorage.setItem('tutorialHistory', JSON.stringify(this.userHistory));
      
      // 保存已完成的教程
      localStorage.setItem('completedTutorials', JSON.stringify(Array.from(this.userCompletedItems)));
    } catch (error) {
      console.error('Failed to save user data:', error);
    }
  }

  /**
   * 获取所有教程项
   */
  public getAllTutorialItems(): TutorialItem[] {
    return Array.from(this.tutorialItems.values());
  }

  /**
   * 获取所有教程系列
   */
  public getAllTutorialSeries(): TutorialSeries[] {
    return Array.from(this.tutorialSeries.values());
  }

  /**
   * 根据ID获取教程项
   */
  public getTutorialItemById(id: string): TutorialItem | undefined {
    return this.tutorialItems.get(id);
  }

  /**
   * 根据ID获取教程系列
   */
  public getTutorialSeriesById(id: string): TutorialSeries | undefined {
    return this.tutorialSeries.get(id);
  }

  /**
   * 获取系列中的所有教程项
   */
  public getTutorialsInSeries(seriesId: string): TutorialItem[] {
    const series = this.tutorialSeries.get(seriesId);
    if (!series) return [];
    
    return series.tutorials
      .map(id => this.tutorialItems.get(id))
      .filter(item => item !== undefined) as TutorialItem[];
  }

  /**
   * 获取推荐教程项
   */
  public getRecommendedTutorialItems(count: number = 10): TutorialItem[] {
    // 更新完成状态
    this.updateCompletionStatus();
    
    // 获取所有未完成的教程项
    const uncompletedItems = [...this.tutorialItems.values()]
      .filter(item => !item.completed);
    
    // 如果没有未完成的教程，返回空数组
    if (uncompletedItems.length === 0) {
      return [];
    }
    
    // 计算每个教程项的推荐分数
    const scoredItems = uncompletedItems.map(item => {
      let score = 0;
      
      // 基于难度的分数
      switch (item.difficulty) {
        case TutorialDifficulty.BEGINNER:
          score += 10;
          break;
        case TutorialDifficulty.INTERMEDIATE:
          score += 5;
          break;
        case TutorialDifficulty.ADVANCED:
          score += 2;
          break;
        case TutorialDifficulty.EXPERT:
          score += 1;
          break;
      }
      
      // 基于用户偏好的分数
      const preferenceScore = this.userPreferences.get(item.category) || 0;
      score += preferenceScore;
      
      // 基于前置条件的分数
      if (item.prerequisites && item.prerequisites.length > 0) {
        const completedPrereqs = item.prerequisites.filter(prereq => 
          this.tutorialItems.get(prereq)?.completed
        ).length;
        
        const prereqScore = (completedPrereqs / item.prerequisites.length) * 20;
        score += prereqScore;
      } else {
        // 没有前置条件的教程得分更高
        score += 15;
      }
      
      // 基于系列的分数
      if (item.seriesId) {
        const series = this.tutorialSeries.get(item.seriesId);
        if (series && series.progress && series.progress > 0) {
          // 已经开始的系列得分更高
          score += 10;
        }
      }
      
      // 基于受欢迎程度的分数
      if (item.popularity) {
        score += item.popularity / 10;
      }
      
      // 基于评分的分数
      if (item.rating) {
        score += item.rating * 2;
      }
      
      return { item, score };
    });
    
    // 按分数降序排序
    scoredItems.sort((a, b) => b.score - a.score);
    
    // 返回前N个教程项
    return scoredItems.slice(0, count).map(scored => scored.item);
  }

  /**
   * 获取推荐教程系列
   */
  public getRecommendedTutorialSeries(count: number = 5): TutorialSeries[] {
    // 计算每个系列的推荐分数
    const scoredSeries = [...this.tutorialSeries.values()].map(series => {
      let score = 0;
      
      // 基于进度的分数
      if (series.progress) {
        if (series.progress < 100) {
          // 已开始但未完成的系列得分最高
          score += 20 * (series.progress / 100);
        } else {
          // 已完成的系列得分较低
          score -= 10;
        }
      }
      
      // 基于难度的分数
      switch (series.difficulty) {
        case TutorialDifficulty.BEGINNER:
          score += 10;
          break;
        case TutorialDifficulty.INTERMEDIATE:
          score += 5;
          break;
        case TutorialDifficulty.ADVANCED:
          score += 2;
          break;
        case TutorialDifficulty.EXPERT:
          score += 1;
          break;
      }
      
      // 基于用户偏好的分数
      const preferenceScore = this.userPreferences.get(series.category) || 0;
      score += preferenceScore;
      
      // 基于受欢迎程度的分数
      if (series.popularity) {
        score += series.popularity / 10;
      }
      
      // 精选系列得分更高
      if (series.featured) {
        score += 15;
      }
      
      return { series, score };
    });
    
    // 按分数降序排序
    scoredSeries.sort((a, b) => b.score - a.score);
    
    // 返回前N个系列
    return scoredSeries.slice(0, count).map(scored => scored.series);
  }

  /**
   * 获取用户最近查看的教程项
   */
  public getRecentlyViewedItems(count: number = 10): TutorialItem[] {
    return this.userHistory
      .slice(0, count)
      .map(id => this.tutorialItems.get(id))
      .filter(item => item !== undefined) as TutorialItem[];
  }

  /**
   * 获取特定难度级别的教程项
   */
  public getTutorialItemsByDifficulty(difficulty: TutorialDifficulty): TutorialItem[] {
    return [...this.tutorialItems.values()]
      .filter(item => item.difficulty === difficulty);
  }

  /**
   * 获取特定类别的教程项
   */
  public getTutorialItemsByCategory(category: string): TutorialItem[] {
    return [...this.tutorialItems.values()]
      .filter(item => item.category === category);
  }

  /**
   * 获取特定类型的教程项
   */
  public getTutorialItemsByType(type: TutorialType): TutorialItem[] {
    return [...this.tutorialItems.values()]
      .filter(item => item.type === type);
  }

  /**
   * 搜索教程项
   */
  public searchTutorialItems(query: string): TutorialItem[] {
    const lowerQuery = query.toLowerCase();
    return [...this.tutorialItems.values()]
      .filter(item =>
        item.title.toLowerCase().includes(lowerQuery) ||
        item.description.toLowerCase().includes(lowerQuery) ||
        item.tags?.some(tag => tag.toLowerCase().includes(lowerQuery))
      );
  }

  /**
   * 添加事件监听器
   */
  public on(event: string, listener: (...args: any[]) => void): void {
    this.events.on(event, listener);
  }

  /**
   * 移除事件监听器
   */
  public off(event: string, listener: (...args: any[]) => void): void {
    this.events.off(event, listener);
  }
}

// 导出单例实例
export const tutorialRecommendationService = TutorialRecommendationService.getInstance();
