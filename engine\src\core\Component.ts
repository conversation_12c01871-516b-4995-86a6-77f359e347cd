/**
 * 组件基类
 * 实体的功能模块
 */
import { type Entity  } from './Entity';
import { EventEmitter } from '../utils/EventEmitter';

export abstract class Component extends EventEmitter {
  /** 组件类型 */
  private type: string;

  /** 实体引用 */
  protected entity: Entity | null = null;

  /** 是否启用 */
  protected enabled: boolean = true;

  /**
   * 创建组件实例
   * @param type 组件类型
   */
  constructor(type: string) {
    super();
    this.type = type;
  }

  /**
   * 获取组件类型
   * @returns 组件类型
   */
  public getType(): string {
    return this.type;
  }

  /**
   * 设置实体引用
   * @param entity 实体实例
   */
  public setEntity(entity: Entity): void {
    this.entity = entity;
    this.onAttach();
  }

  /**
   * 获取实体引用
   * @returns 实体实例
   */
  public getEntity(): Entity | null {
    return this.entity;
  }

  /**
   * 设置启用状态
   * @param enabled 是否启用
   */
  public setEnabled(enabled: boolean): void {
    if (this.enabled === enabled) {
      return;
    }

    this.enabled = enabled;

    if (enabled) {
      this.onEnable();
    } else {
      this.onDisable();
    }

    // 发出启用状态变更事件
    this.emit('enabledChanged', enabled);
  }

  /**
   * 是否启用
   * @returns 是否启用
   */
  public isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * 当组件附加到实体时调用
   */
  protected onAttach(): void {
    // 子类可以重写此方法
  }

  /**
   * 当组件启用时调用
   */
  protected onEnable(): void {
    // 子类可以重写此方法
  }

  /**
   * 当组件禁用时调用
   */
  protected onDisable(): void {
    // 子类可以重写此方法
  }

  /**
   * 更新组件
   * @param _deltaTime 帧间隔时间（秒）
   */
  public update(_deltaTime: number): void {
    // 子类可以重写此方法
  }

  /**
   * 固定时间步长更新
   * @param _fixedDeltaTime 固定帧间隔时间（秒）
   */
  public fixedUpdate(_fixedDeltaTime: number): void {
    // 子类可以重写此方法
  }

  /**
   * 销毁组件
   */
  public dispose(): void {
    // 清除实体引用
    this.entity = null;

    // 移除所有事件监听器
    this.removeAllListeners();
  }
}
