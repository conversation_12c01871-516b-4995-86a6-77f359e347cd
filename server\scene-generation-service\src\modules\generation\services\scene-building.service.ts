import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from '../../../common/services/logger.service';
import { StorageService } from '../../../common/services/storage.service';
import { GeneratedLayout, LayoutElement } from './layout-generation.service';
import { Asset, AssetSelectionResult } from './asset-selection.service';

export interface SceneObject {
  id: string;
  name: string;
  type: 'mesh' | 'light' | 'camera' | 'group' | 'helper';
  assetId?: string;
  transform: {
    position: { x: number; y: number; z: number };
    rotation: { x: number; y: number; z: number };
    scale: { x: number; y: number; z: number };
  };
  properties: {
    visible: boolean;
    castShadow: boolean;
    receiveShadow: boolean;
    material?: {
      type: string;
      properties: Record<string, any>;
    };
    geometry?: {
      type: string;
      parameters: Record<string, any>;
    };
  };
  children: SceneObject[];
  userData: Record<string, any>;
}

export interface SceneEnvironment {
  background: {
    type: 'color' | 'texture' | 'skybox' | 'hdri';
    value: string | { r: number; g: number; b: number };
  };
  fog: {
    enabled: boolean;
    type: 'linear' | 'exponential';
    color: { r: number; g: number; b: number };
    near?: number;
    far?: number;
    density?: number;
  };
  lighting: {
    ambient: {
      color: { r: number; g: number; b: number };
      intensity: number;
    };
    directional: Array<{
      color: { r: number; g: number; b: number };
      intensity: number;
      position: { x: number; y: number; z: number };
      target: { x: number; y: number; z: number };
      castShadow: boolean;
    }>;
    point: Array<{
      color: { r: number; g: number; b: number };
      intensity: number;
      position: { x: number; y: number; z: number };
      distance: number;
      decay: number;
      castShadow: boolean;
    }>;
  };
  physics: {
    enabled: boolean;
    gravity: { x: number; y: number; z: number };
    world: {
      broadphase: string;
      solver: string;
    };
  };
}

export interface SceneMetadata {
  id: string;
  name: string;
  description: string;
  version: string;
  createdAt: string;
  updatedAt: string;
  author: string;
  tags: string[];
  category: string;
  style: string;
  complexity: 'simple' | 'medium' | 'complex';
  performance: {
    polygonCount: number;
    textureCount: number;
    lightCount: number;
    materialCount: number;
    estimatedMemoryUsage: number;
    estimatedLoadTime: number;
  };
  bounds: {
    min: { x: number; y: number; z: number };
    max: { x: number; y: number; z: number };
    center: { x: number; y: number; z: number };
    size: { x: number; y: number; z: number };
  };
  optimization: {
    level: 'none' | 'basic' | 'aggressive';
    techniques: string[];
    compressionRatio: number;
  };
}

export interface BuiltScene {
  metadata: SceneMetadata;
  environment: SceneEnvironment;
  objects: SceneObject[];
  cameras: Array<{
    id: string;
    name: string;
    type: 'perspective' | 'orthographic';
    position: { x: number; y: number; z: number };
    target: { x: number; y: number; z: number };
    fov?: number;
    aspect?: number;
    near: number;
    far: number;
    isDefault: boolean;
  }>;
  animations: Array<{
    id: string;
    name: string;
    duration: number;
    tracks: Array<{
      target: string;
      property: string;
      keyframes: Array<{
        time: number;
        value: any;
        interpolation: 'linear' | 'step' | 'cubic';
      }>;
    }>;
  }>;
  materials: Array<{
    id: string;
    name: string;
    type: string;
    properties: Record<string, any>;
    textures: Array<{
      type: string;
      url: string;
      properties: Record<string, any>;
    }>;
  }>;
  validation: {
    isValid: boolean;
    warnings: string[];
    errors: string[];
    performance: {
      score: number;
      bottlenecks: string[];
      recommendations: string[];
    };
  };
  export: {
    formats: string[];
    files: Array<{
      format: string;
      url: string;
      size: number;
    }>;
  };
}

@Injectable()
export class SceneBuildingService {
  private readonly maxPolygonCount = 100000;
  private readonly maxTextureSize = 2048;
  private readonly maxLightCount = 8;

  constructor(
    private readonly configService: ConfigService,
    private readonly logger: LoggerService,
    private readonly storageService: StorageService,
  ) {}

  async buildScene(
    layout: GeneratedLayout,
    assetSelection: AssetSelectionResult,
    generationConfig?: any
  ): Promise<BuiltScene> {
    try {
      this.logger.log(`开始构建场景: ${layout.name}`);

      // 创建场景元数据
      const metadata = this.createSceneMetadata(layout, assetSelection, generationConfig);

      // 创建场景环境
      const environment = this.createSceneEnvironment(layout, generationConfig);

      // 构建场景对象
      const objects = await this.buildSceneObjects(layout, assetSelection);

      // 创建相机
      const cameras = this.createSceneCameras(layout, objects);

      // 创建动画
      const animations = this.createSceneAnimations(objects, generationConfig);

      // 创建材质库
      const materials = this.createMaterialLibrary(assetSelection);

      // 验证场景
      const validation = this.validateScene(objects, environment, metadata);

      // 准备导出
      const exportInfo = await this.prepareSceneExport(metadata, objects, materials);

      const builtScene: BuiltScene = {
        metadata,
        environment,
        objects,
        cameras,
        animations,
        materials,
        validation,
        export: exportInfo
      };

      this.logger.log(`场景构建完成，包含 ${objects.length} 个对象`);
      return builtScene;

    } catch (error) {
      this.logger.error('场景构建失败:', error);
      throw new Error(`场景构建失败: ${error.message}`);
    }
  }

  /**
   * 创建场景元数据
   */
  private createSceneMetadata(
    layout: GeneratedLayout,
    assetSelection: AssetSelectionResult,
    config?: any
  ): SceneMetadata {
    const now = new Date().toISOString();

    return {
      id: `scene_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: layout.name,
      description: layout.description,
      version: '1.0.0',
      createdAt: now,
      updatedAt: now,
      author: 'Scene Generation Service',
      tags: [layout.metadata.style, layout.metadata.sceneType],
      category: layout.metadata.sceneType,
      style: layout.metadata.style,
      complexity: layout.metadata.complexity as 'simple' | 'medium' | 'complex',
      performance: {
        polygonCount: assetSelection.statistics.totalPolygons,
        textureCount: this.estimateTextureCount(assetSelection),
        lightCount: this.estimateLightCount(layout),
        materialCount: this.estimateMaterialCount(assetSelection),
        estimatedMemoryUsage: assetSelection.statistics.totalFileSize,
        estimatedLoadTime: assetSelection.statistics.estimatedLoadTime
      },
      bounds: {
        min: layout.spatial.bounds.min,
        max: layout.spatial.bounds.max,
        center: layout.spatial.center,
        size: {
          x: layout.spatial.bounds.max.x - layout.spatial.bounds.min.x,
          y: layout.spatial.bounds.max.y - layout.spatial.bounds.min.y,
          z: layout.spatial.bounds.max.z - layout.spatial.bounds.min.z
        }
      },
      optimization: {
        level: config?.optimization?.level || 'basic',
        techniques: ['LOD', 'Occlusion Culling', 'Texture Compression'],
        compressionRatio: 0.7
      }
    };
  }

  /**
   * 创建场景环境
   */
  private createSceneEnvironment(layout: GeneratedLayout, config?: any): SceneEnvironment {
    return {
      background: {
        type: 'color',
        value: { r: 0.9, g: 0.9, b: 0.9 }
      },
      fog: {
        enabled: false,
        type: 'linear',
        color: { r: 0.8, g: 0.8, b: 0.8 },
        near: 10,
        far: 100
      },
      lighting: {
        ambient: {
          color: { r: 0.4, g: 0.4, b: 0.4 },
          intensity: 0.3
        },
        directional: [
          {
            color: { r: 1, g: 1, b: 1 },
            intensity: 1.0,
            position: { x: 10, y: 10, z: 5 },
            target: { x: 0, y: 0, z: 0 },
            castShadow: true
          }
        ],
        point: []
      },
      physics: {
        enabled: config?.physics?.enabled ?? true,
        gravity: { x: 0, y: -9.81, z: 0 },
        world: {
          broadphase: 'NaiveBroadphase',
          solver: 'GSSolver'
        }
      }
    };
  }

  /**
   * 构建场景对象
   */
  private async buildSceneObjects(
    layout: GeneratedLayout,
    assetSelection: AssetSelectionResult
  ): Promise<SceneObject[]> {
    const objects: SceneObject[] = [];

    try {
      // 创建地面
      const ground = this.createGroundObject(layout);
      objects.push(ground);

      // 为每个布局元素创建场景对象
      for (const element of layout.elements) {
        const sceneObject = await this.createSceneObjectFromElement(element, assetSelection);
        if (sceneObject) {
          objects.push(sceneObject);
        }
      }

      // 添加环境对象（如墙壁、天花板等）
      const environmentObjects = this.createEnvironmentObjects(layout);
      objects.push(...environmentObjects);

      this.logger.log(`创建了 ${objects.length} 个场景对象`);
      return objects;

    } catch (error) {
      this.logger.error('构建场景对象失败:', error);
      throw error;
    }
  }

  /**
   * 创建地面对象
   */
  private createGroundObject(layout: GeneratedLayout): SceneObject {
    const size = layout.spatial.bounds.max;

    return {
      id: 'ground',
      name: '地面',
      type: 'mesh',
      transform: {
        position: { x: size.x / 2, y: 0, z: size.z / 2 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 }
      },
      properties: {
        visible: true,
        castShadow: false,
        receiveShadow: true,
        geometry: {
          type: 'PlaneGeometry',
          parameters: {
            width: size.x,
            height: size.z
          }
        },
        material: {
          type: 'MeshLambertMaterial',
          properties: {
            color: { r: 0.8, g: 0.8, b: 0.8 },
            transparent: false
          }
        }
      },
      children: [],
      userData: {
        category: 'environment',
        type: 'ground'
      }
    };
  }

  /**
   * 从布局元素创建场景对象
   */
  private async createSceneObjectFromElement(
    element: LayoutElement,
    assetSelection: AssetSelectionResult
  ): Promise<SceneObject | null> {
    try {
      // 查找对应的资产
      const asset = this.findAssetForElement(element, assetSelection);

      if (!asset) {
        this.logger.warn(`未找到元素 ${element.name} 对应的资产`);
        return this.createPlaceholderObject(element);
      }

      return {
        id: element.id,
        name: element.name,
        type: 'mesh',
        assetId: asset.id,
        transform: {
          position: element.position,
          rotation: element.rotation,
          scale: element.scale
        },
        properties: {
          visible: true,
          castShadow: true,
          receiveShadow: true,
          material: {
            type: 'MeshStandardMaterial',
            properties: this.createMaterialProperties(asset)
          }
        },
        children: [],
        userData: {
          category: asset.category,
          assetId: asset.id,
          originalElement: element
        }
      };

    } catch (error) {
      this.logger.error(`创建场景对象失败: ${element.name}`, error);
      return this.createPlaceholderObject(element);
    }
  }

  /**
   * 查找元素对应的资产
   */
  private findAssetForElement(element: LayoutElement, assetSelection: AssetSelectionResult): Asset | null {
    // 根据元素名称和类型查找最匹配的资产
    return assetSelection.selectedAssets.find(asset => {
      const nameMatch = asset.name.toLowerCase().includes(element.name.toLowerCase()) ||
                       element.name.toLowerCase().includes(asset.name.toLowerCase());

      const tagMatch = asset.tags.some(tag =>
        tag.toLowerCase().includes(element.name.toLowerCase())
      );

      return nameMatch || tagMatch;
    }) || assetSelection.selectedAssets[0] || null;
  }

  /**
   * 创建占位符对象
   */
  private createPlaceholderObject(element: LayoutElement): SceneObject {
    return {
      id: element.id,
      name: element.name,
      type: 'mesh',
      transform: {
        position: element.position,
        rotation: element.rotation,
        scale: element.scale
      },
      properties: {
        visible: true,
        castShadow: true,
        receiveShadow: true,
        geometry: {
          type: 'BoxGeometry',
          parameters: {
            width: 1,
            height: 1,
            depth: 1
          }
        },
        material: {
          type: 'MeshBasicMaterial',
          properties: {
            color: { r: 0.7, g: 0.7, b: 0.7 },
            wireframe: true
          }
        }
      },
      children: [],
      userData: {
        category: 'placeholder',
        originalElement: element
      }
    };
  }

  /**
   * 创建材质属性
   */
  private createMaterialProperties(asset: Asset): Record<string, any> {
    const properties: Record<string, any> = {
      transparent: false,
      opacity: 1.0,
      roughness: 0.5,
      metalness: 0.0
    };

    // 根据资产材质设置属性
    if (asset.materials.includes('金属')) {
      properties.metalness = 0.8;
      properties.roughness = 0.2;
    }

    if (asset.materials.includes('玻璃')) {
      properties.transparent = true;
      properties.opacity = 0.8;
      properties.roughness = 0.0;
    }

    // 设置颜色
    if (asset.colors.length > 0) {
      const color = this.parseColor(asset.colors[0]);
      if (color) {
        properties.color = color;
      }
    }

    return properties;
  }

  /**
   * 解析颜色
   */
  private parseColor(colorName: string): { r: number; g: number; b: number } | null {
    const colorMap = {
      '红色': { r: 1, g: 0, b: 0 },
      '绿色': { r: 0, g: 1, b: 0 },
      '蓝色': { r: 0, g: 0, b: 1 },
      '白色': { r: 1, g: 1, b: 1 },
      '黑色': { r: 0, g: 0, b: 0 },
      '灰色': { r: 0.5, g: 0.5, b: 0.5 },
      '黄色': { r: 1, g: 1, b: 0 },
      '紫色': { r: 0.5, g: 0, b: 0.5 },
      '橙色': { r: 1, g: 0.5, b: 0 },
      '粉色': { r: 1, g: 0.7, b: 0.7 }
    };

    return colorMap[colorName] || { r: 0.8, g: 0.8, b: 0.8 };
  }

  /**
   * 创建环境对象
   */
  private createEnvironmentObjects(layout: GeneratedLayout): SceneObject[] {
    const objects: SceneObject[] = [];
    const bounds = layout.spatial.bounds;

    // 创建墙壁
    const walls = this.createWalls(bounds);
    objects.push(...walls);

    // 创建天花板
    const ceiling = this.createCeiling(bounds);
    objects.push(ceiling);

    return objects;
  }

  /**
   * 创建墙壁
   */
  private createWalls(bounds: { min: { x: number; y: number; z: number }; max: { x: number; y: number; z: number } }): SceneObject[] {
    const walls: SceneObject[] = [];
    const wallHeight = bounds.max.y;
    const wallThickness = 0.1;

    // 前墙
    walls.push({
      id: 'wall_front',
      name: '前墙',
      type: 'mesh',
      transform: {
        position: { x: bounds.max.x / 2, y: wallHeight / 2, z: bounds.min.z - wallThickness / 2 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 }
      },
      properties: {
        visible: true,
        castShadow: false,
        receiveShadow: true,
        geometry: {
          type: 'BoxGeometry',
          parameters: {
            width: bounds.max.x,
            height: wallHeight,
            depth: wallThickness
          }
        },
        material: {
          type: 'MeshLambertMaterial',
          properties: {
            color: { r: 0.95, g: 0.95, b: 0.95 }
          }
        }
      },
      children: [],
      userData: { category: 'environment', type: 'wall' }
    });

    // 可以添加其他墙壁...

    return walls;
  }

  /**
   * 创建天花板
   */
  private createCeiling(bounds: { min: { x: number; y: number; z: number }; max: { x: number; y: number; z: number } }): SceneObject {
    return {
      id: 'ceiling',
      name: '天花板',
      type: 'mesh',
      transform: {
        position: { x: bounds.max.x / 2, y: bounds.max.y, z: bounds.max.z / 2 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 }
      },
      properties: {
        visible: true,
        castShadow: false,
        receiveShadow: false,
        geometry: {
          type: 'PlaneGeometry',
          parameters: {
            width: bounds.max.x,
            height: bounds.max.z
          }
        },
        material: {
          type: 'MeshLambertMaterial',
          properties: {
            color: { r: 1, g: 1, b: 1 }
          }
        }
      },
      children: [],
      userData: { category: 'environment', type: 'ceiling' }
    };
  }

  /**
   * 创建场景相机
   */
  private createSceneCameras(layout: GeneratedLayout, objects: SceneObject[]): BuiltScene['cameras'] {
    const cameras: BuiltScene['cameras'] = [];
    const center = layout.spatial.center;
    const bounds = layout.spatial.bounds;

    // 主相机
    cameras.push({
      id: 'main_camera',
      name: '主相机',
      type: 'perspective',
      position: {
        x: center.x + bounds.max.x * 0.3,
        y: center.y + 2,
        z: center.z + bounds.max.z * 0.3
      },
      target: center,
      fov: 75,
      aspect: 16 / 9,
      near: 0.1,
      far: 1000,
      isDefault: true
    });

    // 俯视相机
    cameras.push({
      id: 'top_camera',
      name: '俯视相机',
      type: 'orthographic',
      position: {
        x: center.x,
        y: bounds.max.y + 10,
        z: center.z
      },
      target: center,
      near: 0.1,
      far: 1000,
      isDefault: false
    });

    return cameras;
  }

  /**
   * 创建场景动画
   */
  private createSceneAnimations(objects: SceneObject[], config?: any): BuiltScene['animations'] {
    // 暂时返回空数组，可以根据需要添加动画
    return [];
  }

  /**
   * 创建材质库
   */
  private createMaterialLibrary(assetSelection: AssetSelectionResult): BuiltScene['materials'] {
    const materials: BuiltScene['materials'] = [];

    // 为每个资产创建材质
    assetSelection.selectedAssets.forEach(asset => {
      asset.materials.forEach((materialName, index) => {
        materials.push({
          id: `${asset.id}_material_${index}`,
          name: `${asset.name}_${materialName}`,
          type: 'MeshStandardMaterial',
          properties: this.createMaterialProperties(asset),
          textures: []
        });
      });
    });

    return materials;
  }

  /**
   * 验证场景
   */
  private validateScene(
    objects: SceneObject[],
    environment: SceneEnvironment,
    metadata: SceneMetadata
  ): BuiltScene['validation'] {
    const warnings: string[] = [];
    const errors: string[] = [];
    const bottlenecks: string[] = [];
    const recommendations: string[] = [];

    // 检查多边形数量
    if (metadata.performance.polygonCount > this.maxPolygonCount) {
      warnings.push(`多边形数量 (${metadata.performance.polygonCount}) 超过推荐值 (${this.maxPolygonCount})`);
      bottlenecks.push('高多边形数量');
      recommendations.push('考虑使用LOD或简化模型');
    }

    // 检查光源数量
    const lightCount = environment.lighting.directional.length + environment.lighting.point.length;
    if (lightCount > this.maxLightCount) {
      warnings.push(`光源数量 (${lightCount}) 超过推荐值 (${this.maxLightCount})`);
      bottlenecks.push('过多光源');
      recommendations.push('减少光源数量或使用烘焙光照');
    }

    // 检查对象数量
    if (objects.length > 100) {
      warnings.push(`对象数量 (${objects.length}) 较多，可能影响性能`);
      recommendations.push('考虑合并静态对象');
    }

    // 计算性能评分
    let score = 100;
    score -= Math.max(0, (metadata.performance.polygonCount - this.maxPolygonCount) / 1000);
    score -= Math.max(0, (lightCount - this.maxLightCount) * 5);
    score -= Math.max(0, (objects.length - 50) * 0.5);
    score = Math.max(0, Math.min(100, score));

    return {
      isValid: errors.length === 0,
      warnings,
      errors,
      performance: {
        score,
        bottlenecks,
        recommendations
      }
    };
  }

  /**
   * 准备场景导出
   */
  private async prepareSceneExport(
    metadata: SceneMetadata,
    objects: SceneObject[],
    materials: BuiltScene['materials']
  ): Promise<BuiltScene['export']> {
    const formats = ['gltf', 'glb', 'obj', 'fbx'];
    const files: BuiltScene['export']['files'] = [];

    // 这里应该实际生成导出文件
    // 暂时返回模拟数据
    formats.forEach(format => {
      files.push({
        format,
        url: `/exports/${metadata.id}.${format}`,
        size: Math.floor(metadata.performance.estimatedMemoryUsage * 0.8)
      });
    });

    return {
      formats,
      files
    };
  }

  // 辅助方法
  private estimateTextureCount(assetSelection: AssetSelectionResult): number {
    return assetSelection.selectedAssets.reduce((count, asset) =>
      count + asset.fileInfo.textureCount, 0
    );
  }

  private estimateLightCount(layout: GeneratedLayout): number {
    return layout.elements.filter(element => element.type === 'light').length + 2; // +2 for ambient and directional
  }

  private estimateMaterialCount(assetSelection: AssetSelectionResult): number {
    return assetSelection.selectedAssets.reduce((count, asset) =>
      count + asset.materials.length, 0
    );
  }
}
