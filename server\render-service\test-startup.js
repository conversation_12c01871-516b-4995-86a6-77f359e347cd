// 简单的启动测试脚本
const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('./dist/app.module');

async function testStartup() {
  try {
    console.log('正在测试渲染服务启动...');
    
    // 设置基本环境变量
    process.env.NODE_ENV = 'test';
    process.env.PORT = '3004';
    process.env.RENDER_SERVICE_HOST = 'localhost';
    process.env.RENDER_SERVICE_PORT = '3004';
    process.env.RENDER_SERVICE_HTTP_PORT = '4004';
    process.env.DB_HOST = 'localhost';
    process.env.DB_PORT = '3306';
    process.env.DB_USERNAME = 'root';
    process.env.DB_PASSWORD = 'test';
    process.env.DB_DATABASE = 'test';
    process.env.REDIS_HOST = 'localhost';
    process.env.REDIS_PORT = '6379';
    process.env.JWT_SECRET = 'test-secret';
    process.env.USER_SERVICE_HOST = 'localhost';
    process.env.USER_SERVICE_PORT = '3001';
    process.env.PROJECT_SERVICE_HOST = 'localhost';
    process.env.PROJECT_SERVICE_PORT = '3002';
    process.env.ASSET_SERVICE_HOST = 'localhost';
    process.env.ASSET_SERVICE_PORT = '3003';
    
    const app = await NestFactory.create(AppModule, {
      logger: ['error', 'warn'],
    });
    
    console.log('✅ 渲染服务模块创建成功');
    
    // 测试基本配置
    const configService = app.get('ConfigService');
    console.log('✅ 配置服务获取成功');
    
    await app.close();
    console.log('✅ 渲染服务测试完成，所有基本功能正常');
    
  } catch (error) {
    console.error('❌ 渲染服务启动测试失败:', error.message);
    console.error('详细错误:', error.stack);
    process.exit(1);
  }
}

testStartup();
