-- 创建各个微服务的数据库
-- 数字人系统MySQL数据库初始化脚本

-- 首先删除旧的ir_engine数据库（如果存在）
DROP DATABASE IF EXISTS `ir_engine`;
DROP DATABASE IF EXISTS `ir_engine_registry`;
DROP DATABASE IF EXISTS `ir_engine_users`;
DROP DATABASE IF EXISTS `ir_engine_projects`;
DROP DATABASE IF EXISTS `ir_engine_assets`;
DROP DATABASE IF EXISTS `ir_engine_render`;
DROP DATABASE IF EXISTS `ir_engine_knowledge`;
DROP DATABASE IF EXISTS `ir_engine_ai`;
DROP DATABASE IF EXISTS `ir_engine_asset_library`;
DROP DATABASE IF EXISTS `ir_engine_binding`;
DROP DATABASE IF EXISTS `ir_engine_scene_generation`;
DROP DATABASE IF EXISTS `ir_engine_scene_templates`;
DROP DATABASE IF EXISTS `ir_engine_monitoring`;

-- 创建服务注册中心数据库（主数据库）
CREATE DATABASE IF NOT EXISTS `dl_engine_registry` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户服务数据库
CREATE DATABASE IF NOT EXISTS `dl_engine_users` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建项目服务数据库
CREATE DATABASE IF NOT EXISTS `dl_engine_projects` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建资产服务数据库
CREATE DATABASE IF NOT EXISTS `dl_engine_assets` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建渲染服务数据库
CREATE DATABASE IF NOT EXISTS `dl_engine_render` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建知识库服务数据库
CREATE DATABASE IF NOT EXISTS `dl_engine_knowledge` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建AI模型服务数据库
CREATE DATABASE IF NOT EXISTS `dl_engine_ai` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建资源库服务数据库
CREATE DATABASE IF NOT EXISTS `dl_engine_asset_library` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建数字人知识库绑定服务数据库
CREATE DATABASE IF NOT EXISTS `dl_engine_binding` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建场景生成服务数据库
CREATE DATABASE IF NOT EXISTS `dl_engine_scene_generation` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建场景模板服务数据库
CREATE DATABASE IF NOT EXISTS `dl_engine_scene_templates` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建监控服务数据库
CREATE DATABASE IF NOT EXISTS `dl_engine_monitoring` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建测试数据库
CREATE DATABASE IF NOT EXISTS `dl_engine_test` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 修改root用户认证方式以兼容旧版客户端
ALTER USER 'root'@'%' IDENTIFIED WITH mysql_native_password BY 'DLEngine2024!@#';
ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'DLEngine2024!@#';

-- 创建用户并授权（使用兼容的认证方式）
CREATE USER IF NOT EXISTS 'dlengine'@'%' IDENTIFIED WITH mysql_native_password BY 'dlengine123';

-- 授权所有数据库权限
GRANT ALL PRIVILEGES ON `dl_engine_registry`.* TO 'dlengine'@'%';
GRANT ALL PRIVILEGES ON `dl_engine_users`.* TO 'dlengine'@'%';
GRANT ALL PRIVILEGES ON `dl_engine_projects`.* TO 'dlengine'@'%';
GRANT ALL PRIVILEGES ON `dl_engine_assets`.* TO 'dlengine'@'%';
GRANT ALL PRIVILEGES ON `dl_engine_render`.* TO 'dlengine'@'%';
GRANT ALL PRIVILEGES ON `dl_engine_knowledge`.* TO 'dlengine'@'%';
GRANT ALL PRIVILEGES ON `dl_engine_ai`.* TO 'dlengine'@'%';
GRANT ALL PRIVILEGES ON `dl_engine_asset_library`.* TO 'dlengine'@'%';
GRANT ALL PRIVILEGES ON `dl_engine_binding`.* TO 'dlengine'@'%';
GRANT ALL PRIVILEGES ON `dl_engine_scene_generation`.* TO 'dlengine'@'%';
GRANT ALL PRIVILEGES ON `dl_engine_scene_templates`.* TO 'dlengine'@'%';
GRANT ALL PRIVILEGES ON `dl_engine_monitoring`.* TO 'dlengine'@'%';
GRANT ALL PRIVILEGES ON `dl_engine_test`.* TO 'dlengine'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 显示创建的数据库
SHOW DATABASES;
