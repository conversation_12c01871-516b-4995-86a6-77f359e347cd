import { System } from '../../core/System';
import { type World } from '../../core/World';
import { DigitalHumanComponent, ClothingSlotType } from '../components/DigitalHumanComponent';
import * as THREE from 'three';
/**
 * 服装类别
 */
export declare enum ClothingCategory {
    CASUAL = "casual",
    FORMAL = "formal",
    SPORTS = "sports",
    TRADITIONAL = "traditional",
    FANTASY = "fantasy",
    UNIFORM = "uniform",
    SEASONAL = "seasonal"
}
/**
 * 服装材质类型
 */
export declare enum ClothingMaterialType {
    COTTON = "cotton",
    SILK = "silk",
    LEATHER = "leather",
    DENIM = "denim",
    WOOL = "wool",
    SYNTHETIC = "synthetic",
    METAL = "metal",
    PLASTIC = "plastic"
}
/**
 * 服装物理属性
 */
export interface ClothingPhysics {
    /** 是否启用物理模拟 */
    enabled: boolean;
    /** 质量 */
    mass: number;
    /** 刚度 */
    stiffness: number;
    /** 阻尼 */
    damping: number;
    /** 弹性 */
    elasticity: number;
    /** 摩擦力 */
    friction: number;
    /** 风阻 */
    airResistance: number;
    /** 重力影响 */
    gravityScale: number;
}
/**
 * 服装适配参数
 */
export interface ClothingFittingParams {
    /** 缩放 */
    scale: THREE.Vector3;
    /** 偏移 */
    offset: THREE.Vector3;
    /** 旋转 */
    rotation: THREE.Euler;
    /** 骨骼权重调整 */
    boneWeights?: Map<string, number>;
    /** 变形目标权重 */
    morphTargetWeights?: Map<string, number>;
}
/**
 * 服装项
 */
export interface ClothingItem {
    /** 服装ID */
    id: string;
    /** 服装名称 */
    name: string;
    /** 服装类别 */
    category: ClothingCategory;
    /** 插槽类型 */
    slotType: ClothingSlotType;
    /** 几何体URL */
    geometryUrl: string;
    /** 纹理URLs */
    textureUrls: Map<string, string>;
    /** 材质类型 */
    materialType: ClothingMaterialType;
    /** 材质属性 */
    materialProperties: Record<string, any>;
    /** 物理属性 */
    physics: ClothingPhysics;
    /** 适配参数 */
    fittingParams: ClothingFittingParams;
    /** 兼容的身体类型 */
    compatibleBodyTypes: string[];
    /** 价格 */
    price?: number;
    /** 标签 */
    tags: string[];
    /** 创建者 */
    creator?: string;
    /** 版本 */
    version: string;
}
/**
 * 适配后的服装
 */
export interface FittedClothing {
    /** 原始服装项 */
    item: ClothingItem;
    /** 适配后的几何体 */
    geometry: THREE.BufferGeometry;
    /** 适配后的材质 */
    material: THREE.Material;
    /** 适配后的网格 */
    mesh: THREE.Mesh;
    /** 适配质量评分 */
    fittingScore: number;
    /** 适配参数 */
    appliedParams: ClothingFittingParams;
}
/**
 * 服装组合
 */
export interface ClothingOutfit {
    /** 组合ID */
    id: string;
    /** 组合名称 */
    name: string;
    /** 服装项映射 */
    items: Map<ClothingSlotType, string>;
    /** 组合标签 */
    tags: string[];
    /** 创建时间 */
    createdAt: Date;
    /** 更新时间 */
    updatedAt: Date;
}
/**
 * 换装系统配置
 */
export interface ClothingSystemConfig {
    /** 是否启用物理模拟 */
    enablePhysics: boolean;
    /** 是否启用自动适配 */
    enableAutoFitting: boolean;
    /** 是否启用碰撞检测 */
    enableCollisionDetection: boolean;
    /** 适配质量阈值 */
    fittingQualityThreshold: number;
    /** 最大服装数量 */
    maxClothingItems: number;
    /** 是否启用调试 */
    debug: boolean;
}
/**
 * 高级换装系统
 * 支持多类型服装、自动适配、物理模拟和材质系统
 */
export declare class ClothingSystem extends System {
    /** 系统类型 */
    static readonly TYPE = "ClothingSystem";
    /** 系统优先级 */
    static readonly PRIORITY = 50;
    /** 事件发射器 */
    private eventEmitter;
    /** 系统配置 */
    private config;
    /** 服装分类管理 */
    private clothingCategories;
    /** 服装库 */
    private clothingLibrary;
    /** 服装组合库 */
    private outfitLibrary;
    /** 当前装备的服装 */
    private equippedClothing;
    /** 物理世界 */
    private physicsWorld?;
    /** 材质缓存 */
    private materialCache;
    /** 几何体缓存 */
    private geometryCache;
    /**
     * 构造函数
     * @param world 世界实例
     * @param config 系统配置
     */
    constructor(world: World, config?: Partial<ClothingSystemConfig>);
    /**
     * 获取系统类型
     * @returns 系统类型
     */
    getType(): string;
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 更新系统
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void;
    /**
     * 销毁系统
     */
    dispose(): void;
    /**
     * 初始化默认服装类别
     */
    private initializeDefaultCategories;
    /**
     * 初始化物理世界
     */
    private initializePhysicsWorld;
    /**
     * 组件添加事件处理
     * @param entity 实体
     * @param component 组件
     */
    private onComponentAdded;
    /**
     * 组件移除事件处理
     * @param entity 实体
     * @param component 组件
     */
    private onComponentRemoved;
    /**
     * 更新物理模拟
     * @param deltaTime 时间增量
     */
    private updatePhysicsSimulation;
    /**
     * 更新装备的服装
     * @param deltaTime 时间增量
     */
    private updateEquippedClothing;
    /**
     * 更新服装动画
     * @param fittedClothing 适配的服装
     * @param deltaTime 时间增量
     */
    private updateClothingAnimation;
    /**
     * 添加服装项到库中
     * @param clothingItem 服装项
     */
    addClothingItem(clothingItem: ClothingItem): void;
    /**
     * 移除服装项
     * @param itemId 服装项ID
     */
    removeClothingItem(itemId: string): boolean;
    /**
     * 获取服装项
     * @param itemId 服装项ID
     * @returns 服装项
     */
    getClothingItem(itemId: string): ClothingItem | undefined;
    /**
     * 按类别获取服装项
     * @param category 服装类别
     * @returns 服装项列表
     */
    getClothingItemsByCategory(category: ClothingCategory): ClothingItem[];
    /**
     * 搜索服装项
     * @param query 搜索条件
     * @returns 匹配的服装项
     */
    searchClothingItems(query: {
        category?: ClothingCategory;
        slotType?: ClothingSlotType;
        materialType?: ClothingMaterialType;
        tags?: string[];
        priceRange?: [number, number];
        name?: string;
    }): ClothingItem[];
    /**
     * 为数字人装备服装
     * @param entityId 实体ID
     * @param itemId 服装项ID
     * @returns 是否成功装备
     */
    equipClothing(entityId: string, itemId: string): Promise<boolean>;
    /**
     * 卸下服装
     * @param entityId 实体ID
     * @param slotType 插槽类型
     * @returns 是否成功卸下
     */
    unequipClothing(entityId: string, slotType: ClothingSlotType): boolean;
    /**
     * 将服装适配到数字人身体
     * @param clothingItem 服装项
     * @param digitalHuman 数字人组件
     * @returns 适配后的服装
     */
    fitClothingToBody(clothingItem: ClothingItem, digitalHuman: DigitalHumanComponent): Promise<FittedClothing>;
    /**
     * 计算适配参数
     * @param clothingItem 服装项
     * @param digitalHuman 数字人组件
     * @returns 适配参数
     */
    private calculateFittingParams;
    /**
     * 应用适配变换
     * @param geometry 原始几何体
     * @param params 适配参数
     * @returns 变换后的几何体
     */
    private applyFittingTransform;
    /**
     * 计算适配质量评分
     * @param clothingItem 服装项
     * @param digitalHuman 数字人组件
     * @param params 适配参数
     * @returns 质量评分 (0-1)
     */
    private calculateFittingScore;
    /**
     * 获取身体类型
     * @param digitalHuman 数字人组件
     * @returns 身体类型
     */
    private getBodyType;
    /**
     * 检查材质兼容性
     * @param clothingItem 服装项
     * @param digitalHuman 数字人组件
     * @returns 兼容性评分 (0-1)
     */
    private checkMaterialCompatibility;
    /**
     * 加载几何体
     * @param url 几何体URL
     * @returns 几何体
     */
    private loadGeometry;
    /**
     * 创建材质
     * @param clothingItem 服装项
     * @returns 材质
     */
    private createMaterial;
    /**
     * 根据类型创建材质
     * @param clothingItem 服装项
     * @returns 材质
     */
    private createMaterialByType;
    /**
     * 加载纹理
     * @param material 材质
     * @param textureUrls 纹理URL映射
     */
    private loadTextures;
    /**
     * 将纹理应用到材质
     * @param material 材质
     * @param type 纹理类型
     * @param texture 纹理
     */
    private applyTextureToMaterial;
    /**
     * 模拟服装物理效果
     * @param fittedClothing 适配的服装
     * @param animation 动画数据
     */
    simulateClothPhysics(fittedClothing: FittedClothing, animation?: any): void;
    /**
     * 应用重力效果
     * @param mesh 网格
     * @param physics 物理属性
     */
    private applyGravityEffect;
    /**
     * 应用风效果
     * @param mesh 网格
     * @param physics 物理属性
     */
    private applyWindEffect;
    /**
     * 应用动画影响
     * @param mesh 网格
     * @param physics 物理属性
     * @param animation 动画数据
     */
    private applyAnimationInfluence;
    /**
     * 计算动画强度
     * @param animation 动画数据
     * @returns 动画强度 (0-1)
     */
    private calculateAnimationIntensity;
    /**
     * 应用动态物理属性
     * @param mesh 网格
     * @param damping 阻尼
     * @param stiffness 刚度
     */
    private applyDynamicPhysics;
    /**
     * 创建服装组合
     * @param name 组合名称
     * @param items 服装项映射
     * @param tags 标签
     * @returns 服装组合
     */
    createOutfit(name: string, items: Map<ClothingSlotType, string>, tags?: string[]): ClothingOutfit;
    /**
     * 获取服装组合
     * @param outfitId 组合ID
     * @returns 服装组合
     */
    getOutfit(outfitId: string): ClothingOutfit | undefined;
    /**
     * 获取所有服装组合
     * @returns 服装组合列表
     */
    getAllOutfits(): ClothingOutfit[];
    /**
     * 应用服装组合到数字人
     * @param entityId 实体ID
     * @param outfitId 组合ID
     * @returns 是否成功应用
     */
    applyOutfit(entityId: string, outfitId: string): Promise<boolean>;
    /**
     * 删除服装组合
     * @param outfitId 组合ID
     * @returns 是否成功删除
     */
    deleteOutfit(outfitId: string): boolean;
    /**
     * 生成服装组合ID
     * @returns 组合ID
     */
    private generateOutfitId;
    /**
     * 获取系统统计信息
     * @returns 统计信息
     */
    getStats(): {
        clothingItems: number;
        outfits: number;
        equippedEntities: number;
        cacheSize: number;
    };
    /**
     * 清理缓存
     */
    clearCache(): void;
}
