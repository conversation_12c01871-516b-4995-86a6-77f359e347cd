import { Controller, Get, Post, Query, Body, Param } from '@nestjs/common';
import { LoggingService } from './logging.service';
import { LogAnalysisService } from './log-analysis.service';
import { LogLevel } from './entities/log.entity';

/**
 * 日志控制器
 * 提供日志查询和分析的API接口
 */
@Controller('logs')
export class LoggingController {
  constructor(
    private readonly loggingService: LoggingService,
    private readonly logAnalysisService: LogAnalysisService,
  ) {}

  /**
   * 查询日志
   */
  @Get()
  async getLogs(
    @Query('level') level?: LogLevel,
    @Query('serviceId') serviceId?: string,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('keyword') keyword?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return await this.loggingService.queryLogs({
      levels: level ? [level] : undefined,
      serviceId,
      startTime: from ? new Date(from) : undefined,
      endTime: to ? new Date(to) : undefined,
      query: keyword,
      limit: limit || 100,
      offset: page ? (page - 1) * (limit || 100) : 0,
    });
  }

  /**
   * 获取日志统计信息
   */
  @Get('stats')
  async getLogStats(@Query('from') from?: string, @Query('to') to?: string, @Query('serviceId') serviceId?: string) {
    return await this.logAnalysisService.getLogStats({
      from: from ? new Date(from) : undefined,
      to: to ? new Date(to) : undefined,
      serviceId,
    });
  }

  /**
   * 获取错误日志趋势
   */
  @Get('trends/errors')
  async getErrorTrends(@Query('from') from?: string, @Query('to') to?: string, @Query('interval') interval?: string) {
    return await this.logAnalysisService.getErrorTrends({
      from: from ? new Date(from) : undefined,
      to: to ? new Date(to) : undefined,
      interval: interval || 'hour',
    });
  }

  /**
   * 搜索日志
   */
  @Post('search')
  async searchLogs(@Body() searchQuery: any) {
    return await this.loggingService.searchLogs(searchQuery);
  }

  /**
   * 获取特定服务的日志
   */
  @Get('service/:serviceName')
  async getServiceLogs(
    @Param('serviceName') serviceName: string,
    @Query('level') level?: LogLevel,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return await this.loggingService.getServiceLogs(serviceName, {
      level,
      from: from ? new Date(from) : undefined,
      to: to ? new Date(to) : undefined,
      page: page || 1,
      limit: limit || 100,
    });
  }

  /**
   * 获取日志级别分布
   */
  @Get('distribution/levels')
  async getLogLevelDistribution(@Query('from') from?: string, @Query('to') to?: string) {
    return await this.logAnalysisService.getLogLevelDistribution({
      from: from ? new Date(from) : undefined,
      to: to ? new Date(to) : undefined,
    });
  }
}
