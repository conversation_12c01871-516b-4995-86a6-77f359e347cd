/**
 * 风场系统
 * 用于管理风区域和风场
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';
import { PhysicalWindSystem, WindZone, WindZoneType, WindFieldType } from './PhysicalWindSystem';

/**
 * 风场系统配置
 */
export interface WindFieldSystemConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
  /** 是否使用风场网格 */
  useWindFieldGrid?: boolean;
  /** 风场网格分辨率 */
  windFieldGridResolution?: number;
  /** 是否使用风场粒子 */
  useWindFieldParticles?: boolean;
  /** 风场粒子数量 */
  windFieldParticlesCount?: number;
}

/**
 * 风场系统事件类型
 */
export enum WindFieldSystemEventType {
  /** 风场更新 */
  WIND_FIELD_UPDATED = 'wind_field_updated',
  /** 风区域添加 */
  WIND_ZONE_ADDED = 'wind_zone_added',
  /** 风区域移除 */
  WIND_ZONE_REMOVED = 'wind_zone_removed',
  /** 风区域更新 */
  WIND_ZONE_UPDATED = 'wind_zone_updated'
}

/**
 * 风场系统
 */
export class WindFieldSystem extends System {
  /** 系统类型 */
  public static readonly TYPE: string = 'WindFieldSystem';

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: WindFieldSystemConfig = {
    enabled: true,
    autoUpdate: true,
    updateFrequency: 1,
    useDebugVisualization: false,
    useWindFieldGrid: false,
    windFieldGridResolution: 10,
    useWindFieldParticles: false,
    windFieldParticlesCount: 1000
  };

  /** 配置 */
  private config: WindFieldSystemConfig;

  /** 是否自动更新 */
  private autoUpdate: boolean;

  /** 更新频率 */
  private updateFrequency: number;

  /** 帧计数器 */
  private frameCount: number = 0;

  /** 是否使用调试可视化 */
  private useDebugVisualization: boolean;

  /** 是否使用风场网格 */
  private useWindFieldGrid: boolean;

  /** 风场网格分辨率 */
  private windFieldGridResolution: number;

  /** 是否使用风场粒子 */
  private useWindFieldParticles: boolean;

  /** 风场粒子数量 */
  private windFieldParticlesCount: number;

  /** 物理风效果系统 */
  private physicalWindSystem: PhysicalWindSystem | null = null;

  /** 风区域列表 */
  private windZones: Map<string, WindZone> = new Map();

  /** 风场网格 */
  private windFieldGrid: THREE.Vector3[][][] | null = null;

  /** 风场粒子系统 */
  private windFieldParticles: THREE.Points | null = null;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 性能监控器 */
  private performanceMonitor: PerformanceMonitor = PerformanceMonitor.getInstance();

  /** 当前时间 */
  private time: number = 0;

  /** 调试网格列表 */
  private debugMeshes: THREE.Object3D[] = [];

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config?: WindFieldSystemConfig) {
    super(0); // 传递优先级参数

    // 合并配置
    this.config = { ...WindFieldSystem.DEFAULT_CONFIG, ...config };

    // 设置属性
    this.autoUpdate = this.config.autoUpdate !== undefined ? this.config.autoUpdate : true;
    this.updateFrequency = this.config.updateFrequency || 1;
    this.useDebugVisualization = this.config.useDebugVisualization !== undefined ? this.config.useDebugVisualization : false;
    this.useWindFieldGrid = this.config.useWindFieldGrid !== undefined ? this.config.useWindFieldGrid : false;
    this.windFieldGridResolution = this.config.windFieldGridResolution || 10;
    this.useWindFieldParticles = this.config.useWindFieldParticles !== undefined ? this.config.useWindFieldParticles : false;
    this.windFieldParticlesCount = this.config.windFieldParticlesCount || 1000;

    // 设置启用状态
    this.setEnabled(this.config.enabled !== undefined ? this.config.enabled : true);
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return WindFieldSystem.TYPE;
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    super.initialize();

    // 查找物理风效果系统
    this.physicalWindSystem = this.world?.getSystem(PhysicalWindSystem) as PhysicalWindSystem;
    if (!this.physicalWindSystem) {
      Debug.warn('WindFieldSystem', '找不到PhysicalWindSystem，将创建新的实例');
      this.physicalWindSystem = new PhysicalWindSystem({
        enabled: true,
        autoUpdate: true,
        updateFrequency: this.updateFrequency,
        useWindZones: true,
        useDebugVisualization: this.useDebugVisualization
      });
      this.world?.addSystem(this.physicalWindSystem);
    }

    // 初始化风场网格
    if (this.useWindFieldGrid) {
      this.initWindFieldGrid();
    }

    // 初始化风场粒子
    if (this.useWindFieldParticles) {
      this.initWindFieldParticles();
    }

    // 初始化调试可视化
    if (this.useDebugVisualization) {
      this.initDebugVisualization();
    }

    Debug.log('WindFieldSystem', '风场系统初始化完成');
  }

  /**
   * 销毁系统
   */
  public destroy(): void {
    // 清除调试网格
    this.clearDebugMeshes();

    // 清除风场粒子
    if (this.windFieldParticles) {
      const scene = this.world?.getActiveScene();
      if (scene) {
        scene.getThreeScene().remove(this.windFieldParticles);
      }
      this.windFieldParticles = null;
    }

    // 清除风场网格
    this.windFieldGrid = null;

    // 清除风区域
    this.windZones.clear();

    // 调用父类的dispose方法
    (this as any).dispose();
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.isEnabled() || !this.autoUpdate) {
      return;
    }

    // 更新时间
    this.time += deltaTime;

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.updateFrequency !== 0) {
      return;
    }

    // 如果启用了性能监控，开始计时
    if (this.useDebugVisualization) {
      this.performanceMonitor.beginMeasure('windFieldUpdate');
    }

    // 更新风场网格
    if (this.useWindFieldGrid) {
      this.updateWindFieldGrid();
    }

    // 更新风场粒子
    if (this.useWindFieldParticles) {
      this.updateWindFieldParticles(deltaTime);
    }

    // 如果启用了调试可视化，更新调试可视化
    if (this.useDebugVisualization) {
      this.updateDebugVisualization();
      this.performanceMonitor.endMeasure('windFieldUpdate');
    }
  }

  /**
   * 添加风区域
   * @param id 风区域ID
   * @param zone 风区域
   */
  public addWindZone(id: string, zone: WindZone): void {
    // 添加到风区域列表
    this.windZones.set(id, zone);

    // 添加到物理风效果系统
    if (this.physicalWindSystem) {
      this.physicalWindSystem.addWindZone(id, zone);
    }

    // 发出风区域添加事件
    this.eventEmitter.emit(WindFieldSystemEventType.WIND_ZONE_ADDED, id, zone);
  }

  /**
   * 移除风区域
   * @param id 风区域ID
   */
  public removeWindZone(id: string): void {
    // 获取风区域
    const zone = this.windZones.get(id);
    if (!zone) {
      return;
    }

    // 从风区域列表中移除
    this.windZones.delete(id);

    // 从物理风效果系统中移除
    if (this.physicalWindSystem) {
      this.physicalWindSystem.removeWindZone(id);
    }

    // 发出风区域移除事件
    this.eventEmitter.emit(WindFieldSystemEventType.WIND_ZONE_REMOVED, id, zone);
  }

  /**
   * 更新风区域
   * @param id 风区域ID
   * @param zone 风区域
   */
  public updateWindZone(id: string, zone: Partial<WindZone>): void {
    // 获取风区域
    const existingZone = this.windZones.get(id);
    if (!existingZone) {
      return;
    }

    // 更新风区域属性
    Object.assign(existingZone, zone);

    // 更新物理风效果系统中的风区域
    if (this.physicalWindSystem) {
      this.physicalWindSystem.updateWindZone(id, zone);
    }

    // 发出风区域更新事件
    this.eventEmitter.emit(WindFieldSystemEventType.WIND_ZONE_UPDATED, id, existingZone);
  }

  /**
   * 创建球形风区域
   * @param id 风区域ID
   * @param position 位置
   * @param radius 半径
   * @param strength 强度
   * @param direction 方向
   * @param fieldType 风场类型
   * @returns 风区域
   */
  public createSphereWindZone(
    id: string,
    position: THREE.Vector3,
    radius: number,
    strength: number,
    direction: THREE.Vector3,
    fieldType: WindFieldType = WindFieldType.UNIFORM
  ): WindZone {
    // 创建风区域
    const zone: WindZone = {
      type: WindZoneType.SPHERE,
      position: position.clone(),
      size: new THREE.Vector3(radius, radius, radius),
      rotation: new THREE.Euler(),
      fieldType,
      strength,
      direction: direction.clone(),
      frequency: 0.2,
      turbulence: 0.1,
      falloffDistance: radius * 0.2,
      enabled: true
    };

    // 添加风区域
    this.addWindZone(id, zone);

    return zone;
  }

  /**
   * 创建盒形风区域
   * @param id 风区域ID
   * @param position 位置
   * @param size 大小
   * @param rotation 旋转
   * @param strength 强度
   * @param direction 方向
   * @param fieldType 风场类型
   * @returns 风区域
   */
  public createBoxWindZone(
    id: string,
    position: THREE.Vector3,
    size: THREE.Vector3,
    rotation: THREE.Euler,
    strength: number,
    direction: THREE.Vector3,
    fieldType: WindFieldType = WindFieldType.UNIFORM
  ): WindZone {
    // 创建风区域
    const zone: WindZone = {
      type: WindZoneType.BOX,
      position: position.clone(),
      size: size.clone(),
      rotation: rotation.clone(),
      fieldType,
      strength,
      direction: direction.clone(),
      frequency: 0.2,
      turbulence: 0.1,
      falloffDistance: Math.min(size.x, size.y, size.z) * 0.2,
      enabled: true
    };

    // 添加风区域
    this.addWindZone(id, zone);

    return zone;
  }

  /**
   * 创建圆柱形风区域
   * @param id 风区域ID
   * @param position 位置
   * @param radius 半径
   * @param height 高度
   * @param rotation 旋转
   * @param strength 强度
   * @param direction 方向
   * @param fieldType 风场类型
   * @returns 风区域
   */
  public createCylinderWindZone(
    id: string,
    position: THREE.Vector3,
    radius: number,
    height: number,
    rotation: THREE.Euler,
    strength: number,
    direction: THREE.Vector3,
    fieldType: WindFieldType = WindFieldType.UNIFORM
  ): WindZone {
    // 创建风区域
    const zone: WindZone = {
      type: WindZoneType.CYLINDER,
      position: position.clone(),
      size: new THREE.Vector3(radius * 2, height, radius * 2),
      rotation: rotation.clone(),
      fieldType,
      strength,
      direction: direction.clone(),
      frequency: 0.2,
      turbulence: 0.1,
      falloffDistance: Math.min(radius, height / 2) * 0.2,
      enabled: true
    };

    // 添加风区域
    this.addWindZone(id, zone);

    return zone;
  }

  /**
   * 初始化风场网格
   */
  private initWindFieldGrid(): void {
    // 创建风场网格
    const gridResolution = this.windFieldGridResolution;

    // 创建三维数组
    this.windFieldGrid = new Array(gridResolution);
    for (let i = 0; i < gridResolution; i++) {
      this.windFieldGrid[i] = new Array(gridResolution);
      for (let j = 0; j < gridResolution; j++) {
        this.windFieldGrid[i][j] = new Array(gridResolution);
        for (let k = 0; k < gridResolution; k++) {
          this.windFieldGrid[i][j][k] = new THREE.Vector3();
        }
      }
    }

    Debug.log('WindFieldSystem', `风场网格初始化完成，分辨率: ${gridResolution}x${gridResolution}x${gridResolution}`);
  }

  /**
   * 更新风场网格
   */
  private updateWindFieldGrid(): void {
    if (!this.windFieldGrid) {
      return;
    }

    // 获取网格参数
    const gridResolution = this.windFieldGridResolution;
    const gridSize = 100;
    const cellSize = gridSize / gridResolution;
    const halfGridSize = gridSize / 2;

    // 更新风场网格
    for (let i = 0; i < gridResolution; i++) {
      for (let j = 0; j < gridResolution; j++) {
        for (let k = 0; k < gridResolution; k++) {
          // 计算网格点位置
          const x = i * cellSize - halfGridSize;
          const y = j * cellSize - halfGridSize;
          const z = k * cellSize - halfGridSize;
          const position = new THREE.Vector3(x, y, z);

          // 计算风力
          const windForce = this.calculateWindForce(position);

          // 更新网格点
          this.windFieldGrid[i][j][k].copy(windForce);
        }
      }
    }
  }

  /**
   * 初始化风场粒子
   */
  private initWindFieldParticles(): void {
    // 获取活跃场景
    const scene = this.world.getActiveScene();
    if (!scene) {
      return;
    }

    // 创建粒子几何体
    const geometry = new THREE.BufferGeometry();
    const positions = new Float32Array(this.windFieldParticlesCount * 3);
    const colors = new Float32Array(this.windFieldParticlesCount * 3);
    const sizes = new Float32Array(this.windFieldParticlesCount);

    // 设置随机位置
    const gridSize = 100;
    const halfGridSize = gridSize / 2;
    for (let i = 0; i < this.windFieldParticlesCount; i++) {
      positions[i * 3] = Math.random() * gridSize - halfGridSize;
      positions[i * 3 + 1] = Math.random() * gridSize - halfGridSize;
      positions[i * 3 + 2] = Math.random() * gridSize - halfGridSize;

      colors[i * 3] = 0.5;
      colors[i * 3 + 1] = 0.5;
      colors[i * 3 + 2] = 1.0;

      sizes[i] = Math.random() * 2 + 1;
    }

    // 设置几何体属性
    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
    geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

    // 创建粒子材质
    const material = new THREE.PointsMaterial({
      size: 1,
      vertexColors: true,
      transparent: true,
      opacity: 0.5,
      sizeAttenuation: true
    });

    // 创建粒子系统
    this.windFieldParticles = new THREE.Points(geometry, material);
    this.windFieldParticles.name = 'wind_field_particles';

    // 添加到场景
    scene.getThreeScene().add(this.windFieldParticles);

    Debug.log('WindFieldSystem', `风场粒子初始化完成，粒子数量: ${this.windFieldParticlesCount}`);
  }

  /**
   * 更新风场粒子
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateWindFieldParticles(deltaTime: number): void {
    if (!this.windFieldParticles) {
      return;
    }

    // 获取粒子几何体
    const geometry = this.windFieldParticles.geometry;
    const positions = geometry.getAttribute('position');
    const colors = geometry.getAttribute('color');

    // 更新粒子位置
    const gridSize = 100;
    const halfGridSize = gridSize / 2;
    const particleSpeed = 0.5;

    for (let i = 0; i < this.windFieldParticlesCount; i++) {
      // 获取当前位置
      const x = positions.getX(i);
      const y = positions.getY(i);
      const z = positions.getZ(i);
      const position = new THREE.Vector3(x, y, z);

      // 计算风力
      const windForce = this.calculateWindForce(position);

      // 更新位置
      position.add(windForce.multiplyScalar(deltaTime * particleSpeed));

      // 检查边界
      if (position.x < -halfGridSize) position.x = halfGridSize;
      if (position.x > halfGridSize) position.x = -halfGridSize;
      if (position.y < -halfGridSize) position.y = halfGridSize;
      if (position.y > halfGridSize) position.y = -halfGridSize;
      if (position.z < -halfGridSize) position.z = halfGridSize;
      if (position.z > halfGridSize) position.z = -halfGridSize;

      // 更新位置
      positions.setXYZ(i, position.x, position.y, position.z);

      // 更新颜色（基于风力强度）
      const windStrength = windForce.length();
      const r = 0.5 + windStrength * 0.5;
      const g = 0.5 + windStrength * 0.2;
      const b = 1.0;
      colors.setXYZ(i, r, g, b);
    }

    // 标记属性需要更新
    positions.needsUpdate = true;
    colors.needsUpdate = true;
  }

  /**
   * 计算风力
   * @param position 位置
   * @returns 风力向量
   */
  private calculateWindForce(position: THREE.Vector3): THREE.Vector3 {
    // 如果有物理风效果系统，使用它计算风力
    if (this.physicalWindSystem) {
      // 这里假设物理风效果系统有一个公共方法来计算风力
      // 实际上需要在PhysicalWindSystem中添加这个方法
      // return this.physicalWindSystem.calculateWindForceAt(position);

      // 临时解决方案：创建一个简单的风力
      const windForce = new THREE.Vector3(0.1, 0, 0);

      // 添加一些随机性
      const time = this.time;
      const x = Math.sin(position.x * 0.1 + time * 0.2) * 0.05;
      const y = Math.sin(position.y * 0.1 + time * 0.3) * 0.05;
      const z = Math.sin(position.z * 0.1 + time * 0.4) * 0.05;

      windForce.add(new THREE.Vector3(x, y, z));

      return windForce;
    }

    // 如果没有物理风效果系统，返回零向量
    return new THREE.Vector3();
  }

  /**
   * 初始化调试可视化
   */
  private initDebugVisualization(): void {
    // 清除现有调试网格
    this.clearDebugMeshes();

    // 如果使用风场网格，创建风场网格可视化
    if (this.useWindFieldGrid) {
      this.createWindFieldGridVisualization();
    }
  }

  /**
   * 更新调试可视化
   */
  private updateDebugVisualization(): void {
    // 如果使用风场网格，更新风场网格可视化
    if (this.useWindFieldGrid) {
      this.updateWindFieldGridVisualization();
    }
  }

  /**
   * 清除调试网格
   */
  private clearDebugMeshes(): void {
    // 获取活跃场景
    const scene = this.world.getActiveScene();
    if (!scene) {
      return;
    }

    // 移除所有调试网格
    for (const mesh of this.debugMeshes) {
      scene.getThreeScene().remove(mesh);
    }

    // 清空调试网格列表
    this.debugMeshes = [];
  }

  /**
   * 创建风场网格可视化
   */
  private createWindFieldGridVisualization(): void {
    // 获取活跃场景
    const scene = this.world.getActiveScene();
    if (!scene || !this.windFieldGrid) {
      return;
    }

    // 创建箭头辅助对象
    const gridResolution = this.windFieldGridResolution;
    const gridSize = 100;
    const cellSize = gridSize / gridResolution;
    const halfGridSize = gridSize / 2;
    const arrowScale = 5;

    // 每隔几个网格点创建一个箭头
    const step = Math.max(1, Math.floor(gridResolution / 5));

    for (let i = 0; i < gridResolution; i += step) {
      for (let j = 0; j < gridResolution; j += step) {
        for (let k = 0; k < gridResolution; k += step) {
          // 计算网格点位置
          const x = i * cellSize - halfGridSize;
          const y = j * cellSize - halfGridSize;
          const z = k * cellSize - halfGridSize;
          const position = new THREE.Vector3(x, y, z);

          // 计算风力
          const windForce = this.calculateWindForce(position);
          const windDirection = windForce.clone().normalize();
          const windStrength = windForce.length();

          // 创建箭头辅助对象
          const arrowHelper = new THREE.ArrowHelper(
            windDirection,
            position,
            windStrength * arrowScale,
            0x00ffff
          );
          arrowHelper.name = `wind_field_arrow_${i}_${j}_${k}`;

          // 添加到场景
          scene.getThreeScene().add(arrowHelper);

          // 添加到调试网格列表
          this.debugMeshes.push(arrowHelper);
        }
      }
    }
  }

  /**
   * 更新风场网格可视化
   */
  private updateWindFieldGridVisualization(): void {
    // 获取活跃场景
    const scene = this.world.getActiveScene();
    if (!scene || !this.windFieldGrid) {
      return;
    }

    // 更新箭头辅助对象
    const gridResolution = this.windFieldGridResolution;
    const gridSize = 100;
    const cellSize = gridSize / gridResolution;
    const halfGridSize = gridSize / 2;
    const arrowScale = 5;
    const step = Math.max(1, Math.floor(gridResolution / 5));

    let arrowIndex = 0;
    for (let i = 0; i < gridResolution; i += step) {
      for (let j = 0; j < gridResolution; j += step) {
        for (let k = 0; k < gridResolution; k += step) {
          // 获取箭头辅助对象
          if (arrowIndex >= this.debugMeshes.length) {
            continue;
          }
          const arrowHelper = this.debugMeshes[arrowIndex++] as THREE.ArrowHelper;

          // 计算网格点位置
          const x = i * cellSize - halfGridSize;
          const y = j * cellSize - halfGridSize;
          const z = k * cellSize - halfGridSize;
          const position = new THREE.Vector3(x, y, z);

          // 计算风力
          const windForce = this.calculateWindForce(position);
          const windDirection = windForce.clone().normalize();
          const windStrength = windForce.length();

          // 更新箭头辅助对象
          arrowHelper.position.copy(position);
          arrowHelper.setDirection(windDirection);
          arrowHelper.setLength(windStrength * arrowScale);
        }
      }
    }
  }
}