/**
 * 数字人路径编辑器
 * 基于现有RiverGenerator的路径功能，为数字人导航创建专用的路径编辑系统
 */

import * as THREE from 'three';
import { v4 as uuidv4 } from 'uuid';

/**
 * 路径点类型
 */
export type PathPointType = 'normal' | 'stop' | 'waypoint' | 'interaction';

/**
 * 路径点接口
 */
export interface IPathPoint {
  id: string;
  position: THREE.Vector3;
  type: PathPointType;
  waitTime?: number;
  actions?: string[];
  knowledgePointId?: string;
  metadata?: Record<string, any>;
}

/**
 * 停留点接口
 */
export interface IStopPoint extends IPathPoint {
  type: 'stop';
  waitTime: number;
  triggerActions: string[];
  knowledgePointId?: string;
  interactionRadius?: number;
}

/**
 * 路径配置接口
 */
export interface PathEditorConfig {
  pathType: 'digital_human_navigation';
  allowCurves: boolean;
  showDirection: boolean;
  enableStops: boolean;
  smoothness?: number;
  segments?: number;
  debug?: boolean;
}

/**
 * 路径点类
 */
export class PathPoint implements IPathPoint {
  public marker: THREE.Object3D | null = null;
  public isSelected: boolean = false;

  constructor(
    public id: string,
    public position: THREE.Vector3,
    public type: PathPointType = 'normal',
    public waitTime: number = 0,
    public actions: string[] = [],
    public knowledgePointId?: string,
    public metadata?: Record<string, any>
  ) {}

  /**
   * 创建路径点的可视化标记
   */
  public createMarker(): THREE.Object3D {
    const group = new THREE.Group();
    
    // 根据类型选择不同的几何体和颜色
    let geometry: THREE.BufferGeometry;
    let color: number;
    
    switch (this.type) {
      case 'stop':
        geometry = new THREE.BoxGeometry(0.3, 0.3, 0.3);
        color = 0xff0000; // 红色
        break;
      case 'waypoint':
        geometry = new THREE.ConeGeometry(0.15, 0.4, 6);
        color = 0x00ff00; // 绿色
        break;
      case 'interaction':
        geometry = new THREE.OctahedronGeometry(0.2);
        color = 0x0000ff; // 蓝色
        break;
      default:
        geometry = new THREE.SphereGeometry(0.1);
        color = 0xffff00; // 黄色
        break;
    }
    
    const material = new THREE.MeshBasicMaterial({ 
      color,
      transparent: true,
      opacity: 0.8
    });
    
    const mesh = new THREE.Mesh(geometry, material);
    group.add(mesh);
    
    // 添加文字标签（如果有等待时间或动作）
    if (this.waitTime > 0 || this.actions.length > 0) {
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d')!;
      canvas.width = 128;
      canvas.height = 32;
      
      context.fillStyle = '#ffffff';
      context.fillRect(0, 0, canvas.width, canvas.height);
      context.fillStyle = '#000000';
      context.font = '12px Arial';
      context.textAlign = 'center';
      
      let text = '';
      if (this.waitTime > 0) {
        text = `${this.waitTime}s`;
      }
      if (this.actions.length > 0) {
        text += ` [${this.actions.length}]`;
      }
      
      context.fillText(text, canvas.width / 2, canvas.height / 2 + 4);
      
      const texture = new THREE.CanvasTexture(canvas);
      const labelMaterial = new THREE.SpriteMaterial({ map: texture });
      const label = new THREE.Sprite(labelMaterial);
      label.position.set(0, 0.3, 0);
      label.scale.set(0.5, 0.125, 1);
      
      group.add(label);
    }
    
    // 存储路径点引用
    group.userData = { pathPoint: this };
    
    this.marker = group;
    return group;
  }

  /**
   * 设置选中状态
   */
  public setSelected(selected: boolean): void {
    this.isSelected = selected;
    if (this.marker) {
      const mesh = this.marker.children[0] as THREE.Mesh;
      const material = mesh.material as THREE.MeshBasicMaterial;
      
      if (selected) {
        material.color.setHex(0xffffff);
        material.opacity = 1.0;
      } else {
        // 恢复原始颜色
        switch (this.type) {
          case 'stop':
            material.color.setHex(0xff0000);
            break;
          case 'waypoint':
            material.color.setHex(0x00ff00);
            break;
          case 'interaction':
            material.color.setHex(0x0000ff);
            break;
          default:
            material.color.setHex(0xffff00);
            break;
        }
        material.opacity = 0.8;
      }
    }
  }

  /**
   * 更新位置
   */
  public updatePosition(position: THREE.Vector3): void {
    this.position.copy(position);
    if (this.marker) {
      this.marker.position.copy(position);
    }
  }

  /**
   * 销毁标记
   */
  public dispose(): void {
    if (this.marker) {
      this.marker.parent?.remove(this.marker);
      
      // 清理几何体和材质
      this.marker.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          (child.geometry as any).dispose();
          if (Array.isArray(child.material)) {
            child.material.forEach(mat => (mat as any).dispose());
          } else {
            (child.material as any).dispose();
          }
        }
        if (child instanceof THREE.Sprite) {
          (child.material as any).dispose();
        }
      });
      
      this.marker = null;
    }
  }
}

/**
 * 停留点类
 */
export class StopPoint extends PathPoint implements IStopPoint {
  public type: 'stop' = 'stop';
  
  constructor(
    id: string,
    position: THREE.Vector3,
    public waitTime: number = 3000, // 默认停留3秒
    public triggerActions: string[] = [],
    public knowledgePointId?: string,
    public interactionRadius: number = 1.0
  ) {
    super(id, position, 'stop', waitTime, triggerActions, knowledgePointId);
  }

  /**
   * 检查是否在交互范围内
   */
  public isInRange(position: THREE.Vector3): boolean {
    return this.position.distanceTo(position) <= this.interactionRadius;
  }
}

/**
 * 数字人路径编辑器
 */
export class DigitalHumanPathEditor {
  private path: THREE.CurvePath<THREE.Vector3>;
  private pathPoints: PathPoint[] = [];
  private stopPoints: StopPoint[] = [];
  private pathLine: THREE.Line | null = null;
  private directionArrows: THREE.Object3D[] = [];
  private selectedPoint: PathPoint | null = null;
  
  // 事件回调
  public onPathChanged?: () => void;
  public onPointAdded?: (point: PathPoint) => void;
  public onPointRemoved?: (pointId: string) => void;
  public onPointSelected?: (point: PathPoint | null) => void;

  constructor(
    private scene: THREE.Scene,
    private config: PathEditorConfig
  ) {
    this.path = new THREE.CurvePath<THREE.Vector3>();
  }

  /**
   * 添加路径点
   */
  public addPathPoint(
    position: THREE.Vector3, 
    type: PathPointType = 'normal',
    options: Partial<IPathPoint> = {}
  ): string {
    const id = uuidv4();
    
    let pathPoint: PathPoint;
    
    if (type === 'stop') {
      pathPoint = new StopPoint(
        id,
        position.clone(),
        options.waitTime || 3000,
        (options as IStopPoint).triggerActions || [],
        options.knowledgePointId,
        (options as IStopPoint).interactionRadius || 1.0
      );
      this.stopPoints.push(pathPoint as StopPoint);
    } else {
      pathPoint = new PathPoint(
        id,
        position.clone(),
        type,
        options.waitTime || 0,
        options.actions || [],
        options.knowledgePointId,
        options.metadata
      );
    }
    
    this.pathPoints.push(pathPoint);
    
    // 创建可视化标记
    const marker = pathPoint.createMarker();
    marker.position.copy(position);
    this.scene.add(marker);
    
    // 更新路径
    this.updatePath();
    this.updateVisualPath();
    
    // 触发事件
    if (this.onPointAdded) {
      this.onPointAdded(pathPoint);
    }
    
    if (this.onPathChanged) {
      this.onPathChanged();
    }
    
    return id;
  }

  /**
   * 移除路径点
   */
  public removePathPoint(id: string): boolean {
    const pointIndex = this.pathPoints.findIndex(p => p.id === id);
    if (pointIndex === -1) return false;
    
    const point = this.pathPoints[pointIndex];
    
    // 清理可视化标记
    (point as any).dispose();
    
    // 从数组中移除
    this.pathPoints.splice(pointIndex, 1);
    
    // 如果是停留点，也从停留点数组中移除
    if (point.type === 'stop') {
      const stopIndex = this.stopPoints.findIndex(sp => sp.id === id);
      if (stopIndex !== -1) {
        this.stopPoints.splice(stopIndex, 1);
      }
    }
    
    // 如果是当前选中的点，清除选择
    if (this.selectedPoint === point) {
      this.selectedPoint = null;
      if (this.onPointSelected) {
        this.onPointSelected(null);
      }
    }
    
    // 更新路径
    this.updatePath();
    this.updateVisualPath();
    
    // 触发事件
    if (this.onPointRemoved) {
      this.onPointRemoved(id);
    }
    
    if (this.onPathChanged) {
      this.onPathChanged();
    }
    
    return true;
  }

  /**
   * 更新路径
   */
  private updatePath(): void {
    this.path = new THREE.CurvePath<THREE.Vector3>();

    if (this.pathPoints.length < 2) return;

    // 按顺序排列路径点（这里简化为添加顺序）
    const points = this.pathPoints.map(p => p.position);

    if (this.config.allowCurves) {
      // 创建平滑曲线
      const curve = new THREE.CatmullRomCurve3(points);
      curve.closed = false;
      this.path.add(curve);
    } else {
      // 创建直线段
      for (let i = 0; i < points.length - 1; i++) {
        const line = new THREE.LineCurve3(points[i], points[i + 1]);
        this.path.add(line);
      }
    }
  }

  /**
   * 更新可视化路径
   */
  private updateVisualPath(): void {
    // 清除现有的路径线
    if (this.pathLine) {
      this.scene.remove(this.pathLine);
      (this.pathLine.geometry as any).dispose();
      (this.pathLine.material as THREE.Material).dispose();
      this.pathLine = null;
    }

    // 清除方向箭头
    this.clearDirectionArrows();

    if (this.pathPoints.length < 2) return;

    // 创建路径线
    const points = this.path.getPoints(this.config.segments || 50);
    const geometry = new THREE.BufferGeometry().setFromPoints(points);
    const material = new THREE.LineBasicMaterial({
      color: 0x00ffff,
      linewidth: 2
    });

    this.pathLine = new THREE.Line(geometry, material);
    this.scene.add(this.pathLine);

    // 创建方向箭头
    if (this.config.showDirection) {
      this.createDirectionArrows();
    }
  }

  /**
   * 创建方向箭头
   */
  private createDirectionArrows(): void {
    if (this.pathPoints.length < 2) return;

    const arrowCount = Math.min(10, this.pathPoints.length - 1);

    for (let i = 0; i < arrowCount; i++) {
      const t = (i + 1) / (arrowCount + 1);
      const position = this.path.getPoint(t);
      const direction = this.path.getTangent(t).normalize();

      // 创建箭头几何体
      const arrowGeometry = new THREE.ConeGeometry(0.05, 0.2, 6);
      const arrowMaterial = new THREE.MeshBasicMaterial({ color: 0xff8800 });
      const arrow = new THREE.Mesh(arrowGeometry, arrowMaterial);

      // 设置箭头位置和方向
      arrow.position.copy(position);
      arrow.lookAt(position.clone().add(direction));
      arrow.rotateX(Math.PI / 2);

      this.scene.add(arrow);
      this.directionArrows.push(arrow);
    }
  }

  /**
   * 清除方向箭头
   */
  private clearDirectionArrows(): void {
    this.directionArrows.forEach(arrow => {
      this.scene.remove(arrow);
      ((arrow as THREE.Mesh).geometry as any).dispose();
      ((arrow as THREE.Mesh).material as THREE.Material).dispose();
    });
    this.directionArrows = [];
  }

  /**
   * 获取路径上的位置
   */
  public getPositionAt(t: number): THREE.Vector3 {
    if (this.path.curves.length === 0) {
      return new THREE.Vector3();
    }
    return this.path.getPoint(Math.max(0, Math.min(1, t)));
  }

  /**
   * 获取路径上的方向
   */
  public getDirectionAt(t: number): THREE.Vector3 {
    if (this.path.curves.length === 0) {
      return new THREE.Vector3(0, 0, 1);
    }
    return this.path.getTangent(Math.max(0, Math.min(1, t))).normalize();
  }

  /**
   * 获取路径长度
   */
  public getLength(): number {
    return this.path.getLength();
  }

  /**
   * 获取所有路径点
   */
  public getPathPoints(): PathPoint[] {
    return [...this.pathPoints];
  }

  /**
   * 获取所有停留点
   */
  public getStopPoints(): StopPoint[] {
    return [...this.stopPoints];
  }

  /**
   * 获取路径点
   */
  public getPathPoint(id: string): PathPoint | undefined {
    return this.pathPoints.find(p => p.id === id);
  }

  /**
   * 更新路径点位置
   */
  public updatePathPointPosition(id: string, position: THREE.Vector3): boolean {
    const point = this.pathPoints.find(p => p.id === id);
    if (!point) return false;

    point.updatePosition(position);

    // 更新路径
    this.updatePath();
    this.updateVisualPath();

    if (this.onPathChanged) {
      this.onPathChanged();
    }

    return true;
  }

  /**
   * 选择路径点
   */
  public selectPathPoint(point: PathPoint | null): void {
    // 取消之前的选择
    if (this.selectedPoint) {
      this.selectedPoint.setSelected(false);
    }

    // 设置新的选择
    this.selectedPoint = point;
    if (point) {
      point.setSelected(true);
    }

    // 触发事件
    if (this.onPointSelected) {
      this.onPointSelected(point);
    }
  }

  /**
   * 获取选中的路径点
   */
  public getSelectedPoint(): PathPoint | null {
    return this.selectedPoint;
  }

  /**
   * 插入路径点
   */
  public insertPathPoint(index: number, position: THREE.Vector3, type: PathPointType = 'normal'): string {
    const id = uuidv4();
    const pathPoint = new PathPoint(id, position.clone(), type);

    // 插入到指定位置
    this.pathPoints.splice(index, 0, pathPoint);

    if (type === 'stop') {
      this.stopPoints.push(pathPoint as StopPoint);
    }

    // 创建可视化标记
    const marker = pathPoint.createMarker();
    marker.position.copy(position);
    this.scene.add(marker);

    // 更新路径
    this.updatePath();
    this.updateVisualPath();

    // 触发事件
    if (this.onPointAdded) {
      this.onPointAdded(pathPoint);
    }

    if (this.onPathChanged) {
      this.onPathChanged();
    }

    return id;
  }

  /**
   * 清除所有路径点
   */
  public clearPath(): void {
    // 清理所有路径点
    this.pathPoints.forEach(point => (point as any).dispose());
    this.pathPoints = [];
    this.stopPoints = [];
    this.selectedPoint = null;

    // 清理可视化元素
    if (this.pathLine) {
      this.scene.remove(this.pathLine);
      (this.pathLine.geometry as any).dispose();
      (this.pathLine.material as THREE.Material).dispose();
      this.pathLine = null;
    }

    this.clearDirectionArrows();

    // 重置路径
    this.path = new THREE.CurvePath<THREE.Vector3>();

    if (this.onPathChanged) {
      this.onPathChanged();
    }
  }

  /**
   * 导出路径数据
   */
  public exportPath(): any {
    return {
      version: '1.0',
      timestamp: new Date().toISOString(),
      config: this.config,
      pathPoints: this.pathPoints.map(point => ({
        id: point.id,
        position: {
          x: point.position.x,
          y: point.position.y,
          z: point.position.z
        },
        type: point.type,
        waitTime: point.waitTime,
        actions: point.actions,
        knowledgePointId: point.knowledgePointId,
        metadata: point.metadata
      }))
    };
  }

  /**
   * 导入路径数据
   */
  public importPath(data: any): boolean {
    try {
      if (!data.pathPoints || !Array.isArray(data.pathPoints)) {
        return false;
      }

      // 清除现有路径
      this.clearPath();

      // 导入路径点
      for (const pointData of data.pathPoints) {
        const position = new THREE.Vector3(
          pointData.position.x,
          pointData.position.y,
          pointData.position.z
        );

        this.addPathPoint(position, pointData.type, {
          waitTime: pointData.waitTime,
          actions: pointData.actions,
          knowledgePointId: pointData.knowledgePointId,
          metadata: pointData.metadata
        });
      }

      return true;
    } catch (error) {
      console.error('导入路径数据失败:', error);
      return false;
    }
  }

  /**
   * 销毁编辑器
   */
  public dispose(): void {
    this.clearPath();
    this.scene = null as any;
  }
}
