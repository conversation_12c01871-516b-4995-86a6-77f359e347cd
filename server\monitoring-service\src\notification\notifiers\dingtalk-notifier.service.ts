import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { NotificationChannelEntity } from '../entities/notification-channel.entity';
import { NotificationHistoryEntity } from '../entities/notification-history.entity';
import { AlertEntity, AlertSeverity } from '../../alert/entities/alert.entity';

/**
 * 钉钉通知服务
 * 负责向钉钉发送通知消息
 */
@Injectable()
export class DingTalkNotifierService {
  private readonly logger = new Logger(DingTalkNotifierService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 发送钉钉通知
   */
  async send(
    channel: NotificationChannelEntity,
    alert: AlertEntity,
    notification: NotificationHistoryEntity,
  ): Promise<void> {
    try {
      // 获取钉钉配置
      const config = channel.config;

      if (!config.webhookUrl) {
        throw new Error('未配置钉钉 Webhook URL');
      }

      const timestamp = Date.now();
      const sign = config.secret ? this.generateSign(timestamp, config.secret) : undefined;

      const payload = {
        msgtype: 'markdown',
        markdown: {
          title: notification.subject,
          text: this.formatAlertMarkdown(alert, notification),
        },
        at: {
          atMobiles: config.atMobiles || [],
          atUserIds: config.atUserIds || [],
          isAtAll: config.isAtAll || false,
        },
      };

      const url = sign ? `${config.webhookUrl}&timestamp=${timestamp}&sign=${sign}` : config.webhookUrl;

      const response = await firstValueFrom(this.httpService.post(url, payload, { timeout: 10000 }));

      if (response.data.errcode !== 0) {
        throw new Error(`钉钉API返回错误: ${response.data.errmsg}`);
      }

      this.logger.debug(`钉钉通知已发送: ${notification.subject}`);
    } catch (error) {
      this.logger.error(`发送钉钉通知失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 发送钉钉通知（兼容旧接口）
   */
  async sendNotification(data: {
    title: string;
    message: string;
    type: 'info' | 'warning' | 'error' | 'success';
    webhookUrl: string;
    secret?: string;
    atMobiles?: string[];
    atUserIds?: string[];
    isAtAll?: boolean;
    metadata?: any;
  }): Promise<boolean> {
    try {
      const timestamp = Date.now();
      const sign = data.secret ? this.generateSign(timestamp, data.secret) : undefined;

      const payload = {
        msgtype: 'markdown',
        markdown: {
          title: data.title,
          text: this.formatMarkdownMessage(data),
        },
        at: {
          atMobiles: data.atMobiles || [],
          atUserIds: data.atUserIds || [],
          isAtAll: data.isAtAll || false,
        },
      };

      const url = sign ? `${data.webhookUrl}&timestamp=${timestamp}&sign=${sign}` : data.webhookUrl;

      const response = await firstValueFrom(this.httpService.post(url, payload));

      if (response.data.errcode === 0) {
        this.logger.log(`钉钉通知发送成功: ${data.title}`);
        return true;
      } else {
        this.logger.error(`钉钉通知发送失败: ${response.data.errmsg}`);
        return false;
      }
    } catch (error) {
      this.logger.error(`发送钉钉通知时出错: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 测试钉钉连接
   */
  async testConnection(webhookUrl: string, secret?: string): Promise<boolean> {
    try {
      const timestamp = Date.now();
      const sign = secret ? this.generateSign(timestamp, secret) : undefined;

      const testPayload = {
        msgtype: 'text',
        text: {
          content: '这是一条测试消息，用于验证钉钉通知配置是否正确。',
        },
      };

      const url = sign ? `${webhookUrl}&timestamp=${timestamp}&sign=${sign}` : webhookUrl;

      const response = await firstValueFrom(this.httpService.post(url, testPayload));

      return response.data.errcode === 0;
    } catch (error) {
      this.logger.error(`测试钉钉连接失败: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 生成签名
   */
  private generateSign(timestamp: number, secret: string): string {
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const crypto = require('crypto');
    const stringToSign = `${timestamp}\n${secret}`;
    const sign = crypto.createHmac('sha256', secret).update(stringToSign).digest('base64');

    return encodeURIComponent(sign);
  }

  /**
   * 格式化Markdown消息
   */
  private formatMarkdownMessage(data: { title: string; message: string; type: string; metadata?: any }): string {
    const emoji = this.getEmojiByType(data.type);
    let markdown = `## ${emoji} ${data.title}\n\n`;
    markdown += `${data.message}\n\n`;

    if (data.metadata) {
      markdown += '**详细信息:**\n\n';
      for (const [key, value] of Object.entries(data.metadata)) {
        markdown += `- **${key}:** ${typeof value === 'object' ? JSON.stringify(value) : value}\n`;
      }
    }

    markdown += `\n---\n*发送时间: ${new Date().toLocaleString('zh-CN')}*`;

    return markdown;
  }

  /**
   * 根据类型获取表情符号
   */
  private getEmojiByType(type: string): string {
    const emojis = {
      info: 'ℹ️',
      warning: '⚠️',
      error: '❌',
      success: '✅',
    };

    return emojis[type] || emojis.info;
  }

  /**
   * 获取严重性对应的表情符号
   */
  private getSeverityEmoji(severity: AlertSeverity): string {
    switch (severity) {
      case AlertSeverity.INFO:
        return 'ℹ️';
      case AlertSeverity.WARNING:
        return '⚠️';
      case AlertSeverity.ERROR:
        return '❌';
      case AlertSeverity.CRITICAL:
        return '🚨';
      default:
        return '❓';
    }
  }

  /**
   * 格式化告警Markdown消息
   */
  private formatAlertMarkdown(alert: AlertEntity, _notification: NotificationHistoryEntity): string {
    const emoji = this.getSeverityEmoji(alert.severity);
    let markdown = `## ${emoji} ${alert.name}\n\n`;
    markdown += `${alert.description}\n\n`;

    markdown += '**告警详情:**\n\n';
    markdown += `- **严重性:** ${this.getSeverityText(alert.severity)}\n`;
    markdown += `- **状态:** ${alert.status}\n`;
    markdown += `- **开始时间:** ${alert.startTime.toLocaleString('zh-CN')}\n`;

    if (alert.serviceType) {
      markdown += `- **服务类型:** ${alert.serviceType}\n`;
    }

    if (alert.hostname) {
      markdown += `- **主机:** ${alert.hostname}\n`;
    }

    if (alert.labels && Object.keys(alert.labels).length > 0) {
      markdown += '\n**标签:**\n\n';
      for (const [key, value] of Object.entries(alert.labels)) {
        markdown += `- **${key}:** ${value}\n`;
      }
    }

    if (alert.value && Object.keys(alert.value).length > 0) {
      markdown += '\n**值:**\n\n';
      for (const [key, value] of Object.entries(alert.value)) {
        markdown += `- **${key}:** ${value}\n`;
      }
    }

    markdown += `\n---\n*发送时间: ${new Date().toLocaleString('zh-CN')}*`;

    return markdown;
  }

  /**
   * 获取严重性文本
   */
  private getSeverityText(severity: AlertSeverity): string {
    switch (severity) {
      case AlertSeverity.INFO:
        return '信息';
      case AlertSeverity.WARNING:
        return '警告';
      case AlertSeverity.ERROR:
        return '错误';
      case AlertSeverity.CRITICAL:
        return '严重';
      default:
        return severity;
    }
  }
}
