import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { LogStorageService } from './log-storage.service';
import { LogAnalysisService } from './log-analysis.service';

/**
 * 日志聚合服务
 * 负责定期聚合和分析日志数据
 */
@Injectable()
export class LogAggregatorService {
  private readonly logger = new Logger(LogAggregatorService.name);

  constructor(
    private readonly logStorageService: LogStorageService,
    private readonly logAnalysisService: LogAnalysisService,
  ) {}

  /**
   * 每小时聚合日志统计
   */
  @Cron(CronExpression.EVERY_HOUR)
  async aggregateHourlyStats(): Promise<void> {
    try {
      this.logger.log('开始每小时日志聚合...');

      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

      // 获取过去一小时的日志统计
      const stats = await this.logStorageService.getLogStats({
        from: oneHourAgo,
        to: now,
      });

      // 分析错误趋势
      const _errorTrends = await this.logAnalysisService.getErrorTrends({
        from: oneHourAgo,
        to: now,
        interval: 'hour',
      });

      this.logger.log(`每小时聚合完成: ${JSON.stringify(stats)}`);
    } catch (error) {
      this.logger.error(`每小时日志聚合失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 每天聚合日志统计
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async aggregateDailyStats(): Promise<void> {
    try {
      this.logger.log('开始每日日志聚合...');

      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      // 获取过去一天的日志统计
      const stats = await this.logStorageService.getLogStats({
        from: oneDayAgo,
        to: now,
      });

      this.logger.log(`每日聚合完成: ${JSON.stringify(stats)}`);
    } catch (error) {
      this.logger.error(`每日日志聚合失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 清理过期日志
   */
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async cleanupExpiredLogs(): Promise<void> {
    try {
      this.logger.log('开始清理过期日志...');

      // 删除30天前的日志
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const deletedCount = await this.logStorageService.deleteExpiredLogs(thirtyDaysAgo);

      this.logger.log(`清理完成，删除了 ${deletedCount} 条过期日志`);
    } catch (error) {
      this.logger.error(`清理过期日志失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 手动触发聚合
   */
  async triggerAggregation(type: 'hourly' | 'daily'): Promise<void> {
    try {
      if (type === 'hourly') {
        await this.aggregateHourlyStats();
      } else if (type === 'daily') {
        await this.aggregateDailyStats();
      }

      this.logger.log(`手动触发 ${type} 聚合完成`);
    } catch (error) {
      this.logger.error(`手动触发聚合失败: ${error.message}`, error.stack);
      throw error;
    }
  }
}
