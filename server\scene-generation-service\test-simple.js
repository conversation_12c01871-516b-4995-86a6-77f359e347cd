// 简单的模块测试脚本
console.log('正在测试场景生成服务模块...');

try {
  // 测试应用模块
  const { AppModule } = require('./dist/app.module');
  console.log('✅ 应用模块加载成功');

  // 测试公共服务
  const { LoggerService } = require('./dist/common/services/logger.service');
  const { CacheService } = require('./dist/common/services/cache.service');
  const { StorageService } = require('./dist/common/services/storage.service');
  const { HttpClientService } = require('./dist/common/services/http-client.service');
  console.log('✅ 公共服务模块加载成功');

  // 测试Redis模块
  const { RedisModule } = require('./dist/common/modules/redis.module');
  console.log('✅ Redis模块加载成功');

  // 测试健康检查
  const { HealthController } = require('./dist/common/controllers/health.controller');
  console.log('✅ 健康检查控制器加载成功');

  // 测试生成模块
  const { GenerationModule } = require('./dist/modules/generation/generation.module');
  const { GenerationService } = require('./dist/modules/generation/generation.service');
  const { GenerationController } = require('./dist/modules/generation/generation.controller');
  console.log('✅ 生成模块加载成功');

  // 测试任务模块
  const { TasksModule } = require('./dist/modules/tasks/tasks.module');
  console.log('✅ 任务模块加载成功');

  // 测试场景模块
  const { ScenesModule } = require('./dist/modules/scenes/scenes.module');
  console.log('✅ 场景模块加载成功');

  // 测试模板模块
  const { TemplatesModule } = require('./dist/modules/templates/templates.module');
  console.log('✅ 模板模块加载成功');

  // 测试资产模块
  const { AssetsModule } = require('./dist/modules/assets/assets.module');
  console.log('✅ 资产模块加载成功');

  // 测试AI模型模块
  const { AiModelsModule } = require('./dist/modules/ai-models/ai-models.module');
  console.log('✅ AI模型模块加载成功');

  // 测试WebSocket模块
  const { WebsocketModule } = require('./dist/modules/websocket/websocket.module');
  const { WebsocketGateway } = require('./dist/modules/websocket/websocket.gateway');
  console.log('✅ WebSocket模块加载成功');

  // 测试认证模块
  const { AuthModule } = require('./dist/modules/auth/auth.module');
  console.log('✅ 认证模块加载成功');

  console.log('\n🎉 所有核心模块加载成功！');
  console.log('\n修复总结:');
  console.log('1. ✅ 修复了helmet导入错误');
  console.log('2. ✅ 修复了端口配置不一致问题');
  console.log('3. ✅ 修复了CORS配置');
  console.log('4. ✅ 修复了package.json中缺少的mysql2依赖');
  console.log('5. ✅ 修复了Docker Compose配置中的构建上下文');
  console.log('6. ✅ 修复了Redis模块的错误处理');
  console.log('7. ✅ 添加了缓存降级机制');
  console.log('8. ✅ 添加了完整的环境变量配置');
  console.log('9. ✅ 所有TypeScript编译错误已修复');

} catch (error) {
  console.error('❌ 模块测试失败:', error.message);
  console.error('详细错误:', error.stack);
  process.exit(1);
}
