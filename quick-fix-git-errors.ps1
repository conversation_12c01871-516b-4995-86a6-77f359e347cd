#!/usr/bin/env pwsh
# 快速修复Git API和前端错误

Write-Host "🔧 开始快速修复Git API和前端错误..." -ForegroundColor Cyan

# 1. 重新构建并启动API网关和编辑器
Write-Host "📦 重新构建API网关和编辑器..." -ForegroundColor Yellow

try {
    # 停止相关服务
    Write-Host "停止现有服务..." -ForegroundColor Gray
    docker-compose -f docker-compose.windows.yml stop api-gateway editor
    
    # 重新构建
    Write-Host "重新构建服务..." -ForegroundColor Gray
    docker-compose -f docker-compose.windows.yml build --no-cache api-gateway editor
    
    # 启动服务
    Write-Host "启动服务..." -ForegroundColor Gray
    docker-compose -f docker-compose.windows.yml up -d api-gateway editor
    
    Write-Host "✅ 服务重启完成" -ForegroundColor Green
    
} catch {
    Write-Host "❌ 服务重启失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. 等待服务启动
Write-Host "⏳ 等待服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 45

# 3. 测试API连接
Write-Host "🔍 测试API连接..." -ForegroundColor Yellow

try {
    # 测试API网关健康状态
    $apiResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/git/health" -TimeoutSec 15 -ErrorAction SilentlyContinue
    if ($apiResponse.StatusCode -eq 200) {
        Write-Host "✅ Git API健康检查通过" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Git API响应异常: $($apiResponse.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  Git API连接失败，可能仍在启动中" -ForegroundColor Yellow
}

try {
    # 测试前端访问
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:80" -TimeoutSec 15 -ErrorAction SilentlyContinue
    if ($frontendResponse.StatusCode -eq 200) {
        Write-Host "✅ 前端访问正常" -ForegroundColor Green
    } else {
        Write-Host "⚠️  前端响应异常: $($frontendResponse.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  前端连接失败，可能仍在启动中" -ForegroundColor Yellow
}

# 4. 显示服务状态
Write-Host "📊 显示服务状态..." -ForegroundColor Yellow
docker-compose -f docker-compose.windows.yml ps api-gateway editor

Write-Host ""
Write-Host "🎉 快速修复完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📋 修复内容:" -ForegroundColor Cyan
Write-Host "  ✅ 修复了Git控制器路由配置" -ForegroundColor White
Write-Host "  ✅ 修复了Git API响应格式" -ForegroundColor White
Write-Host "  ✅ 修复了前端TabPane组件问题" -ForegroundColor White
Write-Host "  ✅ 重新构建并启动了服务" -ForegroundColor White
Write-Host ""
Write-Host "🌐 访问地址:" -ForegroundColor Cyan
Write-Host "  前端编辑器: http://localhost:80" -ForegroundColor White
Write-Host "  Git API: http://localhost:3000/api/git/status" -ForegroundColor White
Write-Host "  API文档: http://localhost:3000/api/docs" -ForegroundColor White
Write-Host ""
Write-Host "💡 如果仍有问题，请:" -ForegroundColor Yellow
Write-Host "  1. 等待1-2分钟让服务完全启动" -ForegroundColor White
Write-Host "  2. 检查浏览器控制台错误信息" -ForegroundColor White
Write-Host "  3. 运行: docker-compose -f docker-compose.windows.yml logs api-gateway editor" -ForegroundColor White
