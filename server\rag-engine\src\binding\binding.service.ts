import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

export interface DigitalHumanBinding {
  id: string;
  digitalHumanId: string;
  knowledgeBaseId: string;
  bindingType: string;
  priority: number;
  isActive: boolean;
  bindingConfig: any;
  knowledgeBase: {
    id: string;
    name: string;
    description: string;
    category: string;
    language: string;
    documentCount: number;
    totalChunks: number;
  };
  config?: {
    isActive?: boolean;
  };
}

@Injectable()
export class BindingService {
  private readonly bindingServiceUrl: string;

  constructor(private readonly configService: ConfigService) {
    this.bindingServiceUrl = this.configService.get('BINDING_SERVICE_URL', 'http://localhost:3003');
  }

  /**
   * 获取数字人的知识库绑定
   */
  async getDigitalHumanKnowledgeBases(digitalHumanId: string): Promise<DigitalHumanBinding[]> {
    try {
      const response = await axios.get(
        `${this.bindingServiceUrl}/api/bindings/digital-human/${digitalHumanId}/knowledge-bases`,
      );
      return response.data;
    } catch (error) {
      console.error('Failed to get digital human knowledge bases:', error);
      return [];
    }
  }

  /**
   * 获取知识库的数字人绑定
   */
  async getKnowledgeBaseDigitalHumans(knowledgeBaseId: string): Promise<DigitalHumanBinding[]> {
    try {
      const response = await axios.get(
        `${this.bindingServiceUrl}/api/bindings/knowledge-base/${knowledgeBaseId}/digital-humans`,
      );
      return response.data;
    } catch (error) {
      console.error('Failed to get knowledge base digital humans:', error);
      return [];
    }
  }

  /**
   * 检查数字人是否有权限访问知识库
   */
  async hasAccess(digitalHumanId: string, knowledgeBaseId: string): Promise<boolean> {
    try {
      const response = await axios.get(
        `${this.bindingServiceUrl}/api/bindings/check-access/${digitalHumanId}/${knowledgeBaseId}`,
      );
      return response.data.hasAccess;
    } catch (error) {
      console.error('Failed to check access:', error);
      return false;
    }
  }
}
