import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { createClient } from 'redis';

@Global()
@Module({
  imports: [ConfigModule],
  providers: [
    {
      provide: 'REDIS_CLIENT',
      useFactory: async (configService: ConfigService) => {
        try {
          const client = createClient({
            socket: {
              host: configService.get('REDIS_HOST', 'localhost'),
              port: configService.get('REDIS_PORT', 6379),
              connectTimeout: 5000,
            },
            password: configService.get('REDIS_PASSWORD'),
          });

          client.on('error', (err) => {
            console.warn('Redis Client Error (缓存服务不可用):', err.message);
          });

          client.on('connect', () => {
            console.log('Redis Client Connected');
          });

          try {
            await client.connect();
            return client;
          } catch (error) {
            console.warn('Redis连接失败，将使用内存缓存:', error.message);
            return null;
          }
        } catch (error) {
          console.warn('Redis初始化失败，将使用内存缓存:', error.message);
          return null;
        }
      },
      inject: [ConfigService],
    },
  ],
  exports: ['REDIS_CLIENT'],
})
export class RedisModule {}
