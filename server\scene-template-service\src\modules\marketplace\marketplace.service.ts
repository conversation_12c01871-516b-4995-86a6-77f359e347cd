import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SceneTemplate, TemplateStatus } from '../templates/entities/scene-template.entity';
import { TemplateRating } from '../ratings/entities/template-rating.entity';
import { TemplateShare, ShareType } from '../sharing/entities/template-share.entity';
import { CacheService } from '../../common/services/cache.service';
import { LoggerService } from '../../common/services/logger.service';

export interface MarketplaceSearchQuery {
  search?: string;
  category?: string;
  type?: string;
  complexity?: string;
  license?: string;
  minRating?: number;
  sortBy?: 'popular' | 'newest' | 'rating' | 'downloads';
  page?: number;
  limit?: number;
}

export interface MarketplaceStats {
  totalTemplates: number;
  totalDownloads: number;
  averageRating: number;
  topCategories: { name: string; count: number }[];
  recentTemplates: SceneTemplate[];
}

@Injectable()
export class MarketplaceService {
  constructor(
    @InjectRepository(SceneTemplate)
    private readonly templateRepository: Repository<SceneTemplate>,
    @InjectRepository(TemplateRating)
    private readonly ratingRepository: Repository<TemplateRating>,
    @InjectRepository(TemplateShare)
    private readonly shareRepository: Repository<TemplateShare>,
    private readonly cacheService: CacheService,
    private readonly logger: LoggerService,
  ) {}

  /**
   * 搜索市场模板
   */
  async searchTemplates(query: MarketplaceSearchQuery): Promise<{
    templates: SceneTemplate[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const cacheKey = `marketplace:search:${JSON.stringify(query)}`;
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const queryBuilder = this.templateRepository
      .createQueryBuilder('template')
      .leftJoinAndSelect('template.category', 'category')
      .leftJoinAndSelect('template.creator', 'creator')
      .where('template.status = :status', { status: TemplateStatus.PUBLISHED })
      .andWhere('template.isPublic = :isPublic', { isPublic: true })
      .andWhere('template.deletedAt IS NULL');

    // 搜索条件
    if (query.search) {
      queryBuilder.andWhere(
        '(template.name ILIKE :search OR template.description ILIKE :search)',
        { search: `%${query.search}%` },
      );
    }

    if (query.category) {
      queryBuilder.andWhere('category.slug = :category', { category: query.category });
    }

    if (query.type) {
      queryBuilder.andWhere('template.type = :type', { type: query.type });
    }

    if (query.complexity) {
      queryBuilder.andWhere('template.complexity = :complexity', { complexity: query.complexity });
    }

    if (query.license) {
      queryBuilder.andWhere('template.license = :license', { license: query.license });
    }

    if (query.minRating) {
      queryBuilder.andWhere('template.rating >= :minRating', { minRating: query.minRating });
    }

    // 排序
    switch (query.sortBy) {
      case 'popular':
        queryBuilder.orderBy('template.downloadCount', 'DESC');
        break;
      case 'newest':
        queryBuilder.orderBy('template.publishedAt', 'DESC');
        break;
      case 'rating':
        queryBuilder.orderBy('template.rating', 'DESC');
        break;
      case 'downloads':
        queryBuilder.orderBy('template.downloadCount', 'DESC');
        break;
      default:
        queryBuilder.orderBy('template.publishedAt', 'DESC');
    }

    // 分页
    const page = query.page || 1;
    const limit = query.limit || 20;
    const skip = (page - 1) * limit;

    const total = await queryBuilder.getCount();
    const templates = await queryBuilder.skip(skip).take(limit).getMany();

    const result = {
      templates,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };

    // 缓存结果
    await this.cacheService.set(cacheKey, result, 300); // 5分钟缓存

    return result;
  }

  /**
   * 获取热门模板
   */
  async getPopularTemplates(limit: number = 10): Promise<SceneTemplate[]> {
    const cacheKey = `marketplace:popular:${limit}`;
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const templates = await this.templateRepository.find({
      where: {
        status: TemplateStatus.PUBLISHED,
        isPublic: true,
        deletedAt: null,
      },
      relations: ['category', 'creator'],
      order: {
        downloadCount: 'DESC',
        rating: 'DESC',
      },
      take: limit,
    });

    await this.cacheService.set(cacheKey, templates, 1800); // 30分钟缓存
    return templates;
  }

  /**
   * 获取精选模板
   */
  async getFeaturedTemplates(limit: number = 10): Promise<SceneTemplate[]> {
    const cacheKey = `marketplace:featured:${limit}`;
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const templates = await this.templateRepository.find({
      where: {
        status: TemplateStatus.PUBLISHED,
        isPublic: true,
        isFeatured: true,
        deletedAt: null,
      },
      relations: ['category', 'creator'],
      order: {
        rating: 'DESC',
        downloadCount: 'DESC',
      },
      take: limit,
    });

    await this.cacheService.set(cacheKey, templates, 3600); // 1小时缓存
    return templates;
  }

  /**
   * 获取今日模板
   */
  async getTemplateOfDay(): Promise<SceneTemplate | null> {
    const cacheKey = 'marketplace:template-of-day';
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const template = await this.templateRepository.findOne({
      where: {
        status: TemplateStatus.PUBLISHED,
        isPublic: true,
        isTemplateOfDay: true,
        deletedAt: null,
      },
      relations: ['category', 'creator'],
    });

    if (template) {
      await this.cacheService.set(cacheKey, template, 86400); // 24小时缓存
    }

    return template;
  }

  /**
   * 获取市场统计
   */
  async getMarketplaceStats(): Promise<MarketplaceStats> {
    const cacheKey = 'marketplace:stats';
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    // 总模板数
    const totalTemplates = await this.templateRepository.count({
      where: {
        status: TemplateStatus.PUBLISHED,
        isPublic: true,
        deletedAt: null,
      },
    });

    // 总下载数
    const downloadResult = await this.templateRepository
      .createQueryBuilder('template')
      .select('SUM(template.downloadCount)', 'total')
      .where('template.status = :status', { status: TemplateStatus.PUBLISHED })
      .andWhere('template.isPublic = :isPublic', { isPublic: true })
      .andWhere('template.deletedAt IS NULL')
      .getRawOne();

    const totalDownloads = parseInt(downloadResult.total) || 0;

    // 平均评分
    const ratingResult = await this.templateRepository
      .createQueryBuilder('template')
      .select('AVG(template.rating)', 'average')
      .where('template.status = :status', { status: TemplateStatus.PUBLISHED })
      .andWhere('template.isPublic = :isPublic', { isPublic: true })
      .andWhere('template.deletedAt IS NULL')
      .andWhere('template.ratingCount > 0')
      .getRawOne();

    const averageRating = parseFloat(ratingResult.average) || 0;

    // 热门分类
    const categoryResult = await this.templateRepository
      .createQueryBuilder('template')
      .leftJoin('template.category', 'category')
      .select('category.name', 'name')
      .addSelect('COUNT(template.id)', 'count')
      .where('template.status = :status', { status: TemplateStatus.PUBLISHED })
      .andWhere('template.isPublic = :isPublic', { isPublic: true })
      .andWhere('template.deletedAt IS NULL')
      .groupBy('category.name')
      .orderBy('count', 'DESC')
      .limit(5)
      .getRawMany();

    const topCategories = categoryResult.map(item => ({
      name: item.name,
      count: parseInt(item.count),
    }));

    // 最新模板
    const recentTemplates = await this.templateRepository.find({
      where: {
        status: TemplateStatus.PUBLISHED,
        isPublic: true,
        deletedAt: null,
      },
      relations: ['category', 'creator'],
      order: { publishedAt: 'DESC' },
      take: 5,
    });

    const stats: MarketplaceStats = {
      totalTemplates,
      totalDownloads,
      averageRating: Math.round(averageRating * 10) / 10,
      topCategories,
      recentTemplates,
    };

    await this.cacheService.set(cacheKey, stats, 3600); // 1小时缓存
    return stats;
  }

  /**
   * 获取相关模板
   */
  async getRelatedTemplates(templateId: string, limit: number = 5): Promise<SceneTemplate[]> {
    const template = await this.templateRepository.findOne({
      where: { id: templateId },
      relations: ['category'],
    });

    if (!template) {
      return [];
    }

    const cacheKey = `marketplace:related:${templateId}:${limit}`;
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    // 查找同分类的其他模板
    const relatedTemplates = await this.templateRepository.find({
      where: {
        category: { id: template.category.id },
        status: TemplateStatus.PUBLISHED,
        isPublic: true,
        deletedAt: null,
      },
      relations: ['category', 'creator'],
      order: {
        rating: 'DESC',
        downloadCount: 'DESC',
      },
      take: limit + 1, // 多取一个，排除自己
    });

    // 排除当前模板
    const filtered = relatedTemplates.filter(t => t.id !== templateId).slice(0, limit);

    await this.cacheService.set(cacheKey, filtered, 1800); // 30分钟缓存
    return filtered;
  }

  /**
   * 获取用户推荐模板
   */
  async getRecommendedTemplates(userId: string, limit: number = 10): Promise<SceneTemplate[]> {
    // 这里可以实现基于用户行为的推荐算法
    // 目前简单返回热门模板
    return this.getPopularTemplates(limit);
  }
}
