/**
 * 知识场景编辑器
 * 扩展现有场景编辑器，支持知识点标注和管理
 */
import * as THREE from 'three';
import { type Entity } from '../../core/Entity';
import { Component } from '../../core/Component';
import { System } from '../../core/System';
import { type World } from '../../core/World';
/**
 * 知识点数据接口
 */
export interface KnowledgeData {
    id: string;
    title: string;
    content: string;
    type: 'text' | 'image' | 'video' | 'audio' | 'document';
    tags: string[];
    category: string;
    priority: number;
    relatedTopics: string[];
    metadata?: Record<string, any>;
}
/**
 * 知识点实体
 */
export declare class KnowledgePoint {
    id: string;
    position: THREE.Vector3;
    knowledge: KnowledgeData;
    marker: THREE.Object3D | null;
    isVisible: boolean;
    isSelected: boolean;
    constructor(id: string, position: THREE.Vector3, knowledge: KnowledgeData);
    /**
     * 创建知识点的3D标记
     */
    createMarker(): THREE.Object3D;
    /**
     * 根据类别获取颜色
     */
    private getCategoryColor;
    /**
     * 更新标记可见性
     */
    setVisible(visible: boolean): void;
    /**
     * 设置选中状态
     */
    setSelected(selected: boolean): void;
    /**
     * 更新位置
     */
    updatePosition(position: THREE.Vector3): void;
    /**
     * 销毁标记
     */
    dispose(): void;
}
/**
 * 知识点组件
 */
export declare class KnowledgePointComponent extends Component {
    knowledgePoint: KnowledgePoint;
    static readonly TYPE = "KnowledgePoint";
    constructor(entity: Entity, knowledgePoint: KnowledgePoint);
}
/**
 * 知识场景编辑器
 */
export declare class KnowledgeSceneEditor extends System {
    static readonly TYPE = "KnowledgeSceneEditor";
    private knowledgePoints;
    private scene;
    private selectedKnowledgePoint;
    private raycaster;
    private mouse;
    private camera;
    onKnowledgePointAdded?: (knowledgePoint: KnowledgePoint) => void;
    onKnowledgePointSelected?: (knowledgePoint: KnowledgePoint | null) => void;
    onKnowledgePointUpdated?: (knowledgePoint: KnowledgePoint) => void;
    onKnowledgePointRemoved?: (knowledgePointId: string) => void;
    constructor(world: World, scene: THREE.Scene, camera: THREE.Camera);
    /**
     * 设置事件监听器
     */
    private setupEventListeners;
    /**
     * 添加知识点
     */
    addKnowledgePoint(position: THREE.Vector3, knowledge: KnowledgeData): string;
    /**
     * 创建知识点标记
     */
    private createKnowledgeMarker;
    /**
     * 移除知识点
     */
    removeKnowledgePoint(id: string): boolean;
    /**
     * 获取知识点
     */
    getKnowledgePoint(id: string): KnowledgePoint | undefined;
    /**
     * 获取所有知识点
     */
    getAllKnowledgePoints(): KnowledgePoint[];
    /**
     * 更新知识点
     */
    updateKnowledgePoint(id: string, updates: Partial<KnowledgeData>): boolean;
    /**
     * 鼠标点击事件处理
     */
    private onMouseClick;
    /**
     * 鼠标移动事件处理
     */
    private onMouseMove;
    /**
     * 选择知识点
     */
    selectKnowledgePoint(knowledgePoint: KnowledgePoint | null): void;
    /**
     * 获取当前选中的知识点
     */
    getSelectedKnowledgePoint(): KnowledgePoint | null;
    /**
     * 按类别过滤知识点
     */
    getKnowledgePointsByCategory(category: string): KnowledgePoint[];
    /**
     * 按标签搜索知识点
     */
    searchKnowledgePointsByTag(tag: string): KnowledgePoint[];
    /**
     * 搜索知识点
     */
    searchKnowledgePoints(query: string): KnowledgePoint[];
    /**
     * 设置知识点可见性
     */
    setKnowledgePointVisibility(id: string, visible: boolean): boolean;
    /**
     * 设置类别可见性
     */
    setCategoryVisibility(category: string, visible: boolean): void;
    /**
     * 导出知识点数据
     */
    exportKnowledgePoints(): any;
    /**
     * 导入知识点数据
     */
    importKnowledgePoints(data: any): boolean;
    /**
     * 清除所有知识点
     */
    clearAllKnowledgePoints(): void;
    /**
     * 系统更新
     */
    update(deltaTime: number): void;
    /**
     * 销毁系统
     */
    dispose(): void;
}
