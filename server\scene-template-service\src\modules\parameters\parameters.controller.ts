import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { ParametersService } from './parameters.service';
import { CreateTemplateParameterDto, UpdateTemplateParameterDto } from './dto/create-parameter.dto';
import { TemplateParameter } from './entities/template-parameter.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('parameters')
@Controller('templates/:templateId/parameters')
export class ParametersController {
  constructor(private readonly parametersService: ParametersService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建模板参数' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '参数创建成功',
    type: TemplateParameter,
  })
  @ApiParam({ name: 'templateId', description: '模板ID' })
  async create(
    @Param('templateId', ParseUUIDPipe) templateId: string,
    @Body() createParameterDto: CreateTemplateParameterDto,
    @Request() req: any,
  ): Promise<TemplateParameter> {
    return await this.parametersService.create(templateId, createParameterDto, req.user.id);
  }

  @Get()
  @ApiOperation({ summary: '获取模板参数列表' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '参数列表获取成功',
    type: [TemplateParameter],
  })
  @ApiParam({ name: 'templateId', description: '模板ID' })
  async findByTemplate(
    @Param('templateId', ParseUUIDPipe) templateId: string,
  ): Promise<TemplateParameter[]> {
    return await this.parametersService.findByTemplate(templateId);
  }

  @Get('grouped')
  @ApiOperation({ summary: '按分组获取模板参数' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '分组参数获取成功',
  })
  @ApiParam({ name: 'templateId', description: '模板ID' })
  async findByTemplateGrouped(
    @Param('templateId', ParseUUIDPipe) templateId: string,
  ): Promise<Record<string, TemplateParameter[]>> {
    return await this.parametersService.findByTemplateGrouped(templateId);
  }

  @Get('defaults')
  @ApiOperation({ summary: '获取参数默认值' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '默认值获取成功',
  })
  @ApiParam({ name: 'templateId', description: '模板ID' })
  async getDefaultValues(
    @Param('templateId', ParseUUIDPipe) templateId: string,
  ): Promise<Record<string, any>> {
    return await this.parametersService.getDefaultValues(templateId);
  }

  @Post('validate')
  @ApiOperation({ summary: '验证参数值' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '参数验证结果',
  })
  @ApiParam({ name: 'templateId', description: '模板ID' })
  @ApiBody({
    description: '参数值',
    schema: {
      type: 'object',
      properties: {
        values: {
          type: 'object',
          description: '参数键值对',
          example: {
            deskColor: '#8B4513',
            lightIntensity: 1.2,
          },
        },
      },
    },
  })
  async validateValues(
    @Param('templateId', ParseUUIDPipe) templateId: string,
    @Body('values') values: Record<string, any>,
  ): Promise<{ isValid: boolean; errors: string[] }> {
    return await this.parametersService.validateParameterValues(templateId, values);
  }

  @Patch('order')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新参数排序' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '排序更新成功',
  })
  @ApiParam({ name: 'templateId', description: '模板ID' })
  @ApiBody({
    description: '参数排序',
    schema: {
      type: 'object',
      properties: {
        orders: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', description: '参数ID' },
              sortOrder: { type: 'number', description: '排序顺序' },
            },
          },
        },
      },
    },
  })
  async updateOrder(
    @Param('templateId', ParseUUIDPipe) templateId: string,
    @Body('orders') orders: { id: string; sortOrder: number }[],
    @Request() req: any,
  ): Promise<void> {
    await this.parametersService.updateOrder(templateId, orders, req.user.id);
  }

  @Post('copy/:targetTemplateId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '复制参数到另一个模板' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '参数复制成功',
    type: [TemplateParameter],
  })
  @ApiParam({ name: 'templateId', description: '源模板ID' })
  @ApiParam({ name: 'targetTemplateId', description: '目标模板ID' })
  async copyToTemplate(
    @Param('templateId', ParseUUIDPipe) templateId: string,
    @Param('targetTemplateId', ParseUUIDPipe) targetTemplateId: string,
    @Request() req: any,
  ): Promise<TemplateParameter[]> {
    return await this.parametersService.copyToTemplate(templateId, targetTemplateId, req.user.id);
  }
}

@ApiTags('parameters')
@Controller('parameters')
export class ParameterController {
  constructor(private readonly parametersService: ParametersService) {}

  @Get(':id')
  @ApiOperation({ summary: '获取参数详情' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '参数详情获取成功',
    type: TemplateParameter,
  })
  @ApiParam({ name: 'id', description: '参数ID' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<TemplateParameter> {
    return await this.parametersService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新参数' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '参数更新成功',
    type: TemplateParameter,
  })
  @ApiParam({ name: 'id', description: '参数ID' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateParameterDto: UpdateTemplateParameterDto,
    @Request() req: any,
  ): Promise<TemplateParameter> {
    return await this.parametersService.update(id, updateParameterDto, req.user.id);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除参数' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: '参数删除成功',
  })
  @ApiParam({ name: 'id', description: '参数ID' })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<void> {
    await this.parametersService.remove(id, req.user.id);
  }
}
