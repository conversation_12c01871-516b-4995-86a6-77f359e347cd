import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  Index,
} from 'typeorm';
import { Scene } from '../../scenes/entities/scene.entity';
import { User } from '../../auth/entities/user.entity';

export enum TaskType {
  TEXT_TO_SCENE = 'text_to_scene',
  VOICE_TO_SCENE = 'voice_to_scene',
  IMAGE_TO_SCENE = 'image_to_scene',
  TEMPLATE_BASED = 'template_based',
  HYBRID = 'hybrid',
}

export enum TaskStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  ANALYZING_INPUT = 'analyzing_input',
  GENERATING_LAYOUT = 'generating_layout',
  SELECTING_ASSETS = 'selecting_assets',
  BUILDING_SCENE = 'building_scene',
  OPTIMIZING = 'optimizing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export enum TaskPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent',
}

@Entity('generation_tasks')
@Index(['status', 'createdAt'])
@Index(['userId', 'createdAt'])
@Index(['type', 'status'])
export class GenerationTask {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  @Index()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: TaskType,
    default: TaskType.TEXT_TO_SCENE,
  })
  @Index()
  type: TaskType;

  @Column({
    type: 'enum',
    enum: TaskStatus,
    default: TaskStatus.PENDING,
  })
  @Index()
  status: TaskStatus;

  @Column({
    type: 'enum',
    enum: TaskPriority,
    default: TaskPriority.NORMAL,
  })
  priority: TaskPriority;

  // 输入数据
  @Column({ type: 'text', nullable: true })
  textInput: string; // 文本输入

  @Column({ name: 'voice_file_url', nullable: true })
  voiceFileUrl: string; // 语音文件URL

  @Column({ name: 'image_file_url', nullable: true })
  imageFileUrl: string; // 图片文件URL

  @Column({ type: 'json', nullable: true })
  inputMetadata: Record<string, any>; // 输入元数据

  // 生成配置
  @Column({ type: 'json', nullable: true })
  generationConfig: Record<string, any>; // 生成配置参数

  @Column({ name: 'template_id', nullable: true })
  templateId: string; // 使用的模板ID

  @Column({ name: 'style_preferences', type: 'json', nullable: true })
  stylePreferences: Record<string, any>; // 风格偏好

  @Column({ name: 'scene_constraints', type: 'json', nullable: true })
  sceneConstraints: Record<string, any>; // 场景约束

  // 处理结果
  @Column({ type: 'json', nullable: true })
  analysisResult: Record<string, any>; // 输入分析结果

  @Column({ type: 'json', nullable: true })
  layoutData: Record<string, any>; // 布局数据

  @Column({ type: 'json', nullable: true })
  selectedAssets: Record<string, any>[]; // 选中的资源

  @Column({ type: 'json', nullable: true })
  sceneData: Record<string, any>; // 最终场景数据

  // 进度和状态
  @Column({ type: 'float', default: 0 })
  progress: number; // 进度百分比 (0-100)

  @Column({ name: 'current_step', nullable: true })
  currentStep: string; // 当前处理步骤

  @Column({ name: 'total_steps', default: 0 })
  totalSteps: number; // 总步骤数

  @Column({ name: 'estimated_duration', nullable: true })
  estimatedDuration: number; // 预估耗时（秒）

  @Column({ name: 'actual_duration', nullable: true })
  actualDuration: number; // 实际耗时（秒）

  // 错误信息
  @Column({ name: 'error_message', type: 'text', nullable: true })
  errorMessage: string;

  @Column({ name: 'error_code', nullable: true })
  errorCode: string;

  @Column({ name: 'error_details', type: 'json', nullable: true })
  errorDetails: Record<string, any>;

  // 质量评估
  @Column({ name: 'quality_score', type: 'float', nullable: true })
  qualityScore: number; // 质量分数 (0-1)

  @Column({ name: 'complexity_score', type: 'float', nullable: true })
  complexityScore: number; // 复杂度分数 (0-1)

  @Column({ name: 'performance_metrics', type: 'json', nullable: true })
  performanceMetrics: Record<string, any>; // 性能指标

  // 资源使用
  @Column({ name: 'memory_usage_mb', type: 'float', nullable: true })
  memoryUsageMb: number;

  @Column({ name: 'gpu_usage_percent', type: 'float', nullable: true })
  gpuUsagePercent: number;

  @Column({ name: 'processing_time_ms', type: 'float', nullable: true })
  processingTimeMs: number;

  // 输出信息
  @Column({ name: 'output_format', default: 'gltf' })
  outputFormat: string; // 输出格式

  @Column({ name: 'output_quality', default: 'medium' })
  outputQuality: string; // 输出质量

  @Column({ name: 'output_file_url', nullable: true })
  outputFileUrl: string; // 输出文件URL

  @Column({ name: 'preview_image_url', nullable: true })
  previewImageUrl: string; // 预览图URL

  @Column({ name: 'output_size_mb', type: 'float', nullable: true })
  outputSizeMb: number; // 输出文件大小

  // 版本控制
  @Column({ default: 1 })
  version: number;

  @Column({ name: 'parent_task_id', nullable: true })
  parentTaskId: string; // 父任务ID（用于任务链）

  // 关联关系
  @ManyToOne(() => User, user => user.generationTasks)
  user: User;

  @Column({ name: 'user_id' })
  @Index()
  userId: string;

  @OneToMany(() => Scene, scene => scene.generationTask)
  scenes: Scene[];

  // 时间戳
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'started_at', nullable: true })
  startedAt: Date;

  @Column({ name: 'completed_at', nullable: true })
  completedAt: Date;

  @Column({ name: 'cancelled_at', nullable: true })
  cancelledAt: Date;

  // 计算属性
  get isCompleted(): boolean {
    return this.status === TaskStatus.COMPLETED;
  }

  get isFailed(): boolean {
    return this.status === TaskStatus.FAILED;
  }

  get isProcessing(): boolean {
    return [
      TaskStatus.PROCESSING,
      TaskStatus.ANALYZING_INPUT,
      TaskStatus.GENERATING_LAYOUT,
      TaskStatus.SELECTING_ASSETS,
      TaskStatus.BUILDING_SCENE,
      TaskStatus.OPTIMIZING,
    ].includes(this.status);
  }

  get canCancel(): boolean {
    return [TaskStatus.PENDING, ...Object.values(TaskStatus).filter(s => s.includes('ing'))].includes(this.status);
  }

  get progressPercentage(): string {
    return `${Math.round(this.progress)}%`;
  }

  get estimatedTimeRemaining(): number | null {
    if (!this.estimatedDuration || this.progress === 0) {
      return null;
    }
    const elapsed = this.actualDuration || 0;
    const totalEstimated = this.estimatedDuration;
    const progressRatio = this.progress / 100;
    return Math.max(0, totalEstimated - elapsed);
  }

  get hasOutput(): boolean {
    return !!this.outputFileUrl;
  }

  get inputType(): string {
    if (this.textInput) return 'text';
    if (this.voiceFileUrl) return 'voice';
    if (this.imageFileUrl) return 'image';
    return 'unknown';
  }

  get statusDisplayName(): string {
    const statusNames = {
      [TaskStatus.PENDING]: '等待中',
      [TaskStatus.PROCESSING]: '处理中',
      [TaskStatus.ANALYZING_INPUT]: '分析输入',
      [TaskStatus.GENERATING_LAYOUT]: '生成布局',
      [TaskStatus.SELECTING_ASSETS]: '选择资源',
      [TaskStatus.BUILDING_SCENE]: '构建场景',
      [TaskStatus.OPTIMIZING]: '优化中',
      [TaskStatus.COMPLETED]: '已完成',
      [TaskStatus.FAILED]: '失败',
      [TaskStatus.CANCELLED]: '已取消',
    };
    return statusNames[this.status] || this.status;
  }

  get priorityDisplayName(): string {
    const priorityNames = {
      [TaskPriority.LOW]: '低',
      [TaskPriority.NORMAL]: '普通',
      [TaskPriority.HIGH]: '高',
      [TaskPriority.URGENT]: '紧急',
    };
    return priorityNames[this.priority] || this.priority;
  }

  // 更新进度
  updateProgress(progress: number, currentStep?: string): void {
    this.progress = Math.max(0, Math.min(100, progress));
    if (currentStep) {
      this.currentStep = currentStep;
    }
    this.updatedAt = new Date();
  }

  // 标记为开始
  markAsStarted(): void {
    this.status = TaskStatus.PROCESSING;
    this.startedAt = new Date();
    this.updatedAt = new Date();
  }

  // 标记为完成
  markAsCompleted(outputFileUrl?: string): void {
    this.status = TaskStatus.COMPLETED;
    this.progress = 100;
    this.completedAt = new Date();
    this.updatedAt = new Date();
    if (outputFileUrl) {
      this.outputFileUrl = outputFileUrl;
    }
  }

  // 标记为失败
  markAsFailed(errorMessage: string, errorCode?: string, errorDetails?: Record<string, any>): void {
    this.status = TaskStatus.FAILED;
    this.errorMessage = errorMessage;
    this.errorCode = errorCode;
    this.errorDetails = errorDetails;
    this.updatedAt = new Date();
  }

  // 标记为取消
  markAsCancelled(): void {
    this.status = TaskStatus.CANCELLED;
    this.cancelledAt = new Date();
    this.updatedAt = new Date();
  }
}
