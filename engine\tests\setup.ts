/**
 * 测试设置文件
 * 用于设置测试环境和全局模拟
 */
import { vi } from 'vitest';
import * as THREE from 'three';

// 模拟requestAnimationFrame和cancelAnimationFrame
global.requestAnimationFrame = vi.fn((callback) => {
  return setTimeout(callback, 16);
});

global.cancelAnimationFrame = vi.fn((id) => {
  clearTimeout(id);
});

// 模拟性能API
global.performance = {
  now: vi.fn(() => Date.now()),
  timeOrigin: Date.now(),
} as any;

// 模拟WebGL上下文
HTMLCanvasElement.prototype.getContext = vi.fn((contextType) => {
  if (contextType === 'webgl' || contextType === 'webgl2') {
    return {
      canvas: null,
      drawingBufferWidth: 800,
      drawingBufferHeight: 600,
      getExtension: vi.fn(() => null),
      getParameter: vi.fn(() => null),
      getShaderPrecisionFormat: vi.fn(() => ({
        precision: 1,
        rangeMin: 1,
        rangeMax: 1,
      })),
      getContextAttributes: vi.fn(() => ({
        alpha: true,
        antialias: true,
        depth: true,
        failIfMajorPerformanceCaveat: false,
        powerPreference: 'default',
        premultipliedAlpha: true,
        preserveDrawingBuffer: false,
        stencil: true,
        desynchronized: false,
      })),
      viewport: vi.fn(),
      clearColor: vi.fn(),
      clearDepth: vi.fn(),
      clearStencil: vi.fn(),
      clear: vi.fn(),
      enable: vi.fn(),
      disable: vi.fn(),
      depthFunc: vi.fn(),
      depthMask: vi.fn(),
      blendFunc: vi.fn(),
      blendEquation: vi.fn(),
      blendFuncSeparate: vi.fn(),
      blendEquationSeparate: vi.fn(),
      cullFace: vi.fn(),
      frontFace: vi.fn(),
      scissor: vi.fn(),
      useProgram: vi.fn(),
      activeTexture: vi.fn(),
      bindTexture: vi.fn(),
      bindRenderbuffer: vi.fn(),
      bindFramebuffer: vi.fn(),
      bindBuffer: vi.fn(),
      bindVertexArray: vi.fn(),
      drawArrays: vi.fn(),
      drawElements: vi.fn(),
      createTexture: vi.fn(() => ({})),
      createRenderbuffer: vi.fn(() => ({})),
      createFramebuffer: vi.fn(() => ({})),
      createBuffer: vi.fn(() => ({})),
      createVertexArray: vi.fn(() => ({})),
      createProgram: vi.fn(() => ({})),
      createShader: vi.fn(() => ({})),
      shaderSource: vi.fn(),
      compileShader: vi.fn(),
      getShaderParameter: vi.fn(() => true),
      getShaderInfoLog: vi.fn(() => ''),
      attachShader: vi.fn(),
      linkProgram: vi.fn(),
      getProgramParameter: vi.fn(() => true),
      getProgramInfoLog: vi.fn(() => ''),
      deleteShader: vi.fn(),
      deleteProgram: vi.fn(),
      deleteTexture: vi.fn(),
      deleteRenderbuffer: vi.fn(),
      deleteFramebuffer: vi.fn(),
      deleteBuffer: vi.fn(),
      deleteVertexArray: vi.fn(),
      texImage2D: vi.fn(),
      texParameteri: vi.fn(),
      generateMipmap: vi.fn(),
      pixelStorei: vi.fn(),
      readPixels: vi.fn(),
      renderbufferStorage: vi.fn(),
      framebufferTexture2D: vi.fn(),
      framebufferRenderbuffer: vi.fn(),
      checkFramebufferStatus: vi.fn(() => 0x8CD5), // FRAMEBUFFER_COMPLETE
      bufferData: vi.fn(),
      bufferSubData: vi.fn(),
      getUniformLocation: vi.fn(() => ({})),
      getAttribLocation: vi.fn(() => 0),
      uniform1i: vi.fn(),
      uniform1f: vi.fn(),
      uniform2f: vi.fn(),
      uniform3f: vi.fn(),
      uniform4f: vi.fn(),
      uniform1iv: vi.fn(),
      uniform1fv: vi.fn(),
      uniform2fv: vi.fn(),
      uniform3fv: vi.fn(),
      uniform4fv: vi.fn(),
      uniformMatrix2fv: vi.fn(),
      uniformMatrix3fv: vi.fn(),
      uniformMatrix4fv: vi.fn(),
      vertexAttribPointer: vi.fn(),
      enableVertexAttribArray: vi.fn(),
      disableVertexAttribArray: vi.fn(),
      vertexAttribDivisor: vi.fn(),
      drawArraysInstanced: vi.fn(),
      drawElementsInstanced: vi.fn(),
    };
  }
  return null;
});

// 模拟WebRTC API
global.RTCPeerConnection = vi.fn(() => ({
  createDataChannel: vi.fn(() => ({
    onopen: null,
    onclose: null,
    onmessage: null,
    send: vi.fn(),
    close: vi.fn(),
  })),
  createOffer: vi.fn(() => Promise.resolve({})),
  createAnswer: vi.fn(() => Promise.resolve({})),
  setLocalDescription: vi.fn(() => Promise.resolve()),
  setRemoteDescription: vi.fn(() => Promise.resolve()),
  addIceCandidate: vi.fn(() => Promise.resolve()),
  onicecandidate: null,
  oniceconnectionstatechange: null,
  ondatachannel: null,
  close: vi.fn(),
}));

global.RTCSessionDescription = vi.fn();
global.RTCIceCandidate = vi.fn();

// 模拟WebSocket API
global.WebSocket = vi.fn(() => ({
  onopen: null,
  onclose: null,
  onmessage: null,
  onerror: null,
  send: vi.fn(),
  close: vi.fn(),
  readyState: 0,
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
}));

// 模拟AudioContext API
global.AudioContext = vi.fn(() => ({
  createAnalyser: vi.fn(() => ({
    connect: vi.fn(),
    disconnect: vi.fn(),
    getByteFrequencyData: vi.fn(),
    getByteTimeDomainData: vi.fn(),
    fftSize: 2048,
    frequencyBinCount: 1024,
  })),
  createGain: vi.fn(() => ({
    connect: vi.fn(),
    disconnect: vi.fn(),
    gain: {
      value: 1,
      setValueAtTime: vi.fn(),
    },
  })),
  createMediaStreamSource: vi.fn(() => ({
    connect: vi.fn(),
    disconnect: vi.fn(),
  })),
  destination: {
    connect: vi.fn(),
    disconnect: vi.fn(),
  },
  close: vi.fn(() => Promise.resolve()),
  resume: vi.fn(() => Promise.resolve()),
  suspend: vi.fn(() => Promise.resolve()),
  state: 'running',
}));

// 模拟THREE.js对象
vi.mock('three', async () => {
  const actual = await vi.importActual('three');
  return {
    ...actual,
    WebGLRenderer: vi.fn(() => ({
      setSize: vi.fn(),
      setPixelRatio: vi.fn(),
      setClearColor: vi.fn(),
      clear: vi.fn(),
      render: vi.fn(),
      dispose: vi.fn(),
      domElement: document.createElement('canvas'),
      shadowMap: {
        enabled: false,
        type: 0,
      },
      info: {
        render: {
          triangles: 0,
          calls: 0,
        },
      },
    })),
  };
});
