/**
 * 场景生成服务主服务
 */
import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { v4 as uuidv4 } from 'uuid';
import { firstValueFrom, timeout, catchError, of } from 'rxjs';

@Injectable()
export class AppService implements OnModuleInit {
  private readonly logger = new Logger(AppService.name);
  private readonly instanceId = uuidv4();
  
  constructor(
    private readonly configService: ConfigService,
    @Inject('SERVICE_REGISTRY') private readonly serviceRegistry: ClientProxy,
  ) {}
  
  async onModuleInit() {
    await this.registerService();
    this.startHeartbeat();
  }
  
  getInfo() {
    return {
      name: '场景生成服务',
      version: '1.0.0',
      description: 'DL（Digital Learning）引擎场景生成服务',
      environment: this.configService.get<string>('NODE_ENV', 'development'),
      instanceId: this.instanceId,
    };
  }
  
  private async registerService() {
    try {
      const host = this.configService.get<string>('SCENE_GENERATION_SERVICE_HOST', 'localhost');
      const port = this.configService.get<number>('SCENE_GENERATION_SERVICE_PORT', 3004);
      const httpPort = this.configService.get<number>('SCENE_GENERATION_SERVICE_HTTP_PORT', 3004);
      
      await firstValueFrom(
        this.serviceRegistry.send({ cmd: 'register' }, {
          name: 'scene-generation-service',
          description: 'DL（Digital Learning）引擎场景生成服务',
          instanceId: this.instanceId,
          host,
          port,
          httpPort,
          metadata: {
            version: '1.0.0',
            environment: this.configService.get<string>('NODE_ENV', 'development'),
          },
        }),
      );
      
      this.logger.log('场景生成服务已注册到服务注册中心');
    } catch (error) {
      this.logger.error('注册服务失败', error);
    }
  }
  
  private startHeartbeat() {
    setInterval(async () => {
      try {
        await firstValueFrom(
          this.serviceRegistry.send({ cmd: 'heartbeat' }, {
            name: 'scene-generation-service',
            instanceId: this.instanceId,
            status: {
              uptime: process.uptime(),
              memory: process.memoryUsage(),
            },
          }).pipe(
            timeout(5000),
            catchError(error => {
              this.logger.error('心跳发送失败', error);
              return of(null); // 返回默认值而不是抛出错误
            }),
          ),
        );
        this.logger.debug('发送心跳成功');
      } catch (error) {
        this.logger.error('发送心跳失败', error);
      }
    }, 30000); // 每30秒发送一次心跳
  }
}
