/**
 * 认证服务
 */
import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom, timeout, catchError, throwError } from 'rxjs';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly jwtService: JwtService,
    @Inject('USER_SERVICE') private readonly userService: ClientProxy,
  ) {}

  /**
   * 验证用户
   */
  async validateUser(email: string, password: string): Promise<any> {
    try {
      const user = await firstValueFrom(
        this.userService.send({ cmd: 'validateUser' }, { usernameOrEmail: email, password }).pipe(
          timeout(10000),
          catchError(error => {
            this.logger.error('用户服务调用失败', error);
            return throwError(() => error);
          })
        )
      );
      return user;
    } catch (error) {
      this.logger.error('用户验证失败', error);
      throw new UnauthorizedException('邮箱或密码错误');
    }
  }

  /**
   * 登录
   */
  async login(user: any) {
    const payload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
    };

    return {
      access_token: this.jwtService.sign(payload),
      user,
    };
  }

  /**
   * 注册
   */
  async register(username: string, email: string, password: string, displayName?: string) {
    try {
      const user = await firstValueFrom(
        this.userService.send({ cmd: 'register' }, { username, email, password, displayName }).pipe(
          timeout(10000),
          catchError(error => {
            this.logger.error('用户服务调用失败', error);
            return throwError(() => error);
          })
        )
      );

      // 注册成功后，生成JWT令牌
      return this.login(user);
    } catch (error) {
      this.logger.error('用户注册失败', error);
      throw error;
    }
  }

  /**
   * 验证JWT
   */
  async validateJwt(payload: any): Promise<any> {
    try {
      const user = await firstValueFrom(
        this.userService.send({ cmd: 'findUserById' }, payload.sub).pipe(
          timeout(10000),
          catchError(error => {
            this.logger.error('用户服务调用失败', error);
            return throwError(() => error);
          })
        )
      );
      return user;
    } catch (error) {
      this.logger.error('JWT验证失败', error);
      throw new UnauthorizedException('无效的认证令牌');
    }
  }
}
