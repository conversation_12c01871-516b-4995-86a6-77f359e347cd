/**
 * 照片到3D转换管道
 * 将2D照片转换为3D数字人模型的完整管道
 */
import * as THREE from 'three';
import { EventEmitter } from '../../utils/EventEmitter';
import { Entity } from '../../core/Entity';
import { type World } from '../../core/World';
import { MinIOStorageService } from '../../storage/MinIOStorageService';
/**
 * 转换管道配置
 */
export interface PhotoTo3DConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 输出质量 */
    quality?: 'low' | 'medium' | 'high' | 'ultra';
    /** 是否生成纹理 */
    generateTextures?: boolean;
    /** 是否生成法线贴图 */
    generateNormalMaps?: boolean;
    /** 是否优化网格 */
    optimizeMesh?: boolean;
    /** 最大处理时间（秒） */
    maxProcessingTime?: number;
}
/**
 * 人脸特征点
 */
export interface FacialLandmarks {
    /** 特征点坐标 */
    points: THREE.Vector2[];
    /** 置信度 */
    confidence: number;
    /** 人脸边界框 */
    boundingBox: {
        x: number;
        y: number;
        width: number;
        height: number;
    };
}
/**
 * 人脸分析结果
 */
export interface FaceAnalysisResult {
    /** 人脸特征点 */
    landmarks: FacialLandmarks;
    /** 人脸朝向 */
    pose: {
        yaw: number;
        pitch: number;
        roll: number;
    };
    /** 人脸属性 */
    attributes: {
        age?: number;
        gender?: 'male' | 'female';
        emotion?: string;
        glasses?: boolean;
        beard?: boolean;
    };
    /** 人脸质量评分 */
    qualityScore: number;
}
/**
 * 3D网格生成结果
 */
export interface MeshGenerationResult {
    /** 生成的网格 */
    mesh: THREE.Mesh;
    /** 顶点数量 */
    vertexCount: number;
    /** 面数量 */
    faceCount: number;
    /** 纹理坐标 */
    uvCoordinates: Float32Array;
    /** 法线数据 */
    normals: Float32Array;
}
/**
 * 纹理生成结果
 */
export interface TextureGenerationResult {
    /** 漫反射纹理 */
    diffuseTexture: THREE.Texture;
    /** 法线纹理 */
    normalTexture?: THREE.Texture;
    /** 粗糙度纹理 */
    roughnessTexture?: THREE.Texture;
    /** 金属度纹理 */
    metallicTexture?: THREE.Texture;
    /** 纹理分辨率 */
    resolution: [number, number];
}
/**
 * 转换进度信息
 */
export interface ConversionProgress {
    /** 当前阶段 */
    stage: string;
    /** 进度百分比 */
    progress: number;
    /** 状态消息 */
    message: string;
    /** 预估剩余时间（秒） */
    estimatedTimeRemaining?: number;
}
/**
 * 转换结果
 */
export interface ConversionResult {
    /** 是否成功 */
    success: boolean;
    /** 生成的数字人实体 */
    digitalHuman?: Entity;
    /** 生成的资产ID列表 */
    assetIds?: string[];
    /** 处理时间（秒） */
    processingTime?: number;
    /** 错误信息 */
    error?: string;
    /** 警告信息 */
    warnings?: string[];
}
/**
 * 照片到3D转换管道
 */
export declare class PhotoTo3DPipeline extends EventEmitter {
    /** 配置 */
    private config;
    /** 世界实例 */
    private world;
    /** 存储服务 */
    private storageService;
    /** 当前处理任务 */
    private activeConversions;
    /**
     * 构造函数
     * @param world 世界实例
     * @param storageService 存储服务
     * @param config 配置
     */
    constructor(world: World, storageService: MinIOStorageService, config?: PhotoTo3DConfig);
    /**
     * 从照片生成3D数字人
     * @param photoFile 照片文件
     * @param options 生成选项
     * @returns 转换结果
     */
    generateDigitalHumanFromPhoto(photoFile: File, options?: any): Promise<ConversionResult>;
    /**
     * 预处理照片
     * @param photoFile 照片文件
     * @returns 预处理后的图像数据
     */
    private preprocessPhoto;
    /**
     * 获取目标图像尺寸
     * @returns 目标尺寸
     */
    private getTargetImageSize;
    /**
     * 增强图像
     * @param ctx Canvas上下文
     * @param width 图像宽度
     * @param height 图像高度
     */
    private enhanceImage;
    /**
     * 分析人脸
     * @param imageData 图像数据
     * @returns 人脸分析结果
     */
    private analyzeFace;
    /**
     * 生成模拟特征点
     * @returns 特征点数组
     */
    private generateMockLandmarks;
    /**
     * 更新进度
     * @param conversionId 转换ID
     * @param stage 阶段
     * @param progress 进度
     * @param message 消息
     */
    private updateProgress;
    /**
     * 估算剩余时间
     * @param currentProgress 当前进度
     * @returns 估算剩余时间（秒）
     */
    private estimateRemainingTime;
    /**
     * 生成3D网格
     * @param faceAnalysis 人脸分析结果
     * @returns 网格生成结果
     */
    private generate3DMesh;
    /**
     * 创建人脸几何体
     * @param landmarks 人脸特征点
     * @returns 几何体
     */
    private createFaceGeometry;
    /**
     * 从特征点生成顶点
     * @param landmarks 特征点
     * @returns 顶点数组
     */
    private generateVerticesFromLandmarks;
    /**
     * 估算特征点深度
     * @param point 特征点
     * @param landmarks 所有特征点
     * @returns 深度值
     */
    private estimateDepthFromLandmark;
    /**
     * 生成额外顶点
     * @param landmarks 特征点
     * @returns 额外顶点数组
     */
    private generateAdditionalVertices;
    /**
     * 生成面索引
     * @param vertexCount 顶点数量
     * @returns 面索引数组
     */
    private generateFaceIndices;
    /**
     * 生成UV坐标
     * @param vertexCount 顶点数量
     * @returns UV坐标数组
     */
    private generateUVCoordinates;
    /**
     * 计算法线
     * @param vertices 顶点数组
     * @param faces 面索引数组
     * @returns 法线数组
     */
    private calculateNormals;
    /**
     * 生成纹理
     * @param imageData 原始图像数据
     * @param meshResult 网格结果
     * @returns 纹理生成结果
     */
    private generateTextures;
    /**
     * 从图像数据创建纹理
     * @param imageData 图像数据
     * @returns Three.js纹理
     */
    private createTextureFromImageData;
    /**
     * 生成法线贴图
     * @param imageData 原始图像数据
     * @returns 法线纹理
     */
    private generateNormalMap;
    /**
     * 获取像素灰度值
     * @param imageData 图像数据
     * @param x X坐标
     * @param y Y坐标
     * @returns 灰度值
     */
    private getGrayscale;
    /**
     * 生成粗糙度贴图
     * @param imageData 原始图像数据
     * @returns 粗糙度纹理
     */
    private generateRoughnessMap;
    /**
     * 生成金属度贴图
     * @param imageData 原始图像数据
     * @returns 金属度纹理
     */
    private generateMetallicMap;
    /**
     * 从RGB获取灰度值
     * @param r 红色分量
     * @param g 绿色分量
     * @param b 蓝色分量
     * @returns 灰度值
     */
    private getGrayscaleFromRGB;
    /**
     * 组装数字人
     * @param meshResult 网格结果
     * @param textureResult 纹理结果
     * @param faceAnalysis 人脸分析结果
     * @returns 数字人实体
     */
    private assembleDigitalHuman;
    /**
     * 优化数字人
     * @param digitalHuman 数字人实体
     */
    private optimizeDigitalHuman;
    /**
     * 生成优化的索引
     * @param geometry 几何体
     * @returns 索引数组
     */
    private generateOptimizedIndices;
    /**
     * 压缩纹理
     * @param material 材质
     */
    private compressTextures;
    /**
     * 保存资产
     * @param digitalHuman 数字人实体
     * @param conversionId 转换ID
     * @returns 资产ID列表
     */
    private saveAssets;
    /**
     * 保存模型资产
     * @param digitalHuman 数字人实体
     * @param conversionId 转换ID
     * @returns 模型资产ID
     */
    private saveModelAsset;
    /**
     * 保存纹理资产
     * @param digitalHuman 数字人实体
     * @param conversionId 转换ID
     * @returns 纹理资产ID列表
     */
    private saveTextureAssets;
    /**
     * 保存元数据资产
     * @param digitalHuman 数字人实体
     * @param conversionId 转换ID
     * @returns 元数据资产ID
     */
    private saveMetadataAsset;
    /**
     * 导出为GLTF格式
     * @param mesh 网格
     * @returns GLTF数据
     */
    private exportToGLTF;
    /**
     * Canvas转Blob
     * @param canvas Canvas元素
     * @returns Blob Promise
     */
    private canvasToBlob;
    /**
     * 销毁管道
     */
    dispose(): void;
}
