/**
 * 教程验证服务
 * 负责验证用户是否完成了教程步骤
 */
import { EventEmitter } from '../utils/EventEmitter';
import { store } from '../store';
import { achievementService } from './AchievementService';

/**
 * 验证条件类型
 */
export enum ValidationConditionType {
  PROPERTY_EQUALS = 'property_equals',
  PROPERTY_RANGE = 'property_range',
  PROPERTY_EXISTS = 'property_exists',
  OBJECT_SELECTED = 'object_selected',
  COMPONENT_ADDED = 'component_added',
  ACTION_PERFORMED = 'action_performed',
  PANEL_OPENED = 'panel_opened',
  TOOL_USED = 'tool_used',
  CUSTOM = 'custom'
}

/**
 * 验证条件接口
 */
export interface ValidationCondition {
  type: ValidationConditionType;
  target?: string;
  property?: string;
  value?: any;
  min?: number;
  max?: number;
  customValidator?: (state: any) => boolean;
}

/**
 * 教程任务接口
 */
export interface TutorialTask {
  id: string;
  title: string;
  description: string;
  conditions: ValidationCondition[];
  completed: boolean;
  optional?: boolean;
  hint?: string;
}

/**
 * 教程步骤接口
 */
export interface TutorialStep {
  id: string;
  title: string;
  description: string;
  tasks: TutorialTask[];
  completed: boolean;
  highlightSelector?: string;
  highlightElement?: string;
  highlightMessage?: string;
  nextButtonEnabled?: boolean;
}

/**
 * 教程接口
 */
export interface Tutorial {
  id: string;
  title: string;
  description: string;
  steps: TutorialStep[];
  currentStepIndex: number;
  completed: boolean;
  category: string;
  difficulty: string;
  estimatedTime: string;
  prerequisites?: string[];
  reward?: {
    type: string;
    value: any;
  };
}

/**
 * 教程验证服务类
 */
export class TutorialValidationService {
  private static instance: TutorialValidationService;
  private events = new EventEmitter();
  private activeTutorial: Tutorial | null = null;
  private stateChangeListeners: (() => void)[] = [];

  /**
   * 私有构造函数
   */
  private constructor() {
    // 添加Redux状态变化监听器
    const unsubscribe = store.subscribe(() => {
      this.checkConditions();
    });
    this.stateChangeListeners.push(unsubscribe);
  }

  /**
   * 获取教程验证服务实例
   */
  public static getInstance(): TutorialValidationService {
    if (!TutorialValidationService.instance) {
      TutorialValidationService.instance = new TutorialValidationService();
    }
    return TutorialValidationService.instance;
  }

  /**
   * 设置当前活动教程
   */
  public setActiveTutorial(tutorial: Tutorial): void {
    this.activeTutorial = tutorial;
    this.events.emit('tutorialStarted', tutorial);
    this.checkConditions();
  }

  /**
   * 获取当前活动教程
   */
  public getActiveTutorial(): Tutorial | null {
    return this.activeTutorial;
  }

  /**
   * 清除当前活动教程
   */
  public clearActiveTutorial(): void {
    if (this.activeTutorial) {
      this.events.emit('tutorialCancelled', this.activeTutorial);
      this.activeTutorial = null;
    }
  }

  /**
   * 检查条件是否满足
   */
  private checkConditions(): void {
    if (!this.activeTutorial) return;

    const state = store.getState();
    const currentStep = this.activeTutorial.steps[this.activeTutorial.currentStepIndex];
    
    if (!currentStep) return;

    // 检查每个任务的条件
    let allTasksCompleted = true;
    let tasksChanged = false;

    for (const task of currentStep.tasks) {
      if (task.completed) continue;

      const allConditionsMet = task.conditions.every((condition: any) => {
        return this.checkCondition(condition, state);
      });

      if (allConditionsMet) {
        task.completed = true;
        tasksChanged = true;
        this.events.emit('taskCompleted', { tutorialId: this.activeTutorial.id, taskId: task.id });
      }

      if (!task.completed && !task.optional) {
        allTasksCompleted = false;
      }
    }

    // 如果所有任务都完成了，标记步骤为完成
    if (allTasksCompleted && !currentStep.completed) {
      currentStep.completed = true;
      this.events.emit('stepCompleted', { 
        tutorialId: this.activeTutorial.id, 
        stepId: currentStep.id,
        stepIndex: this.activeTutorial.currentStepIndex
      });

      // 启用下一步按钮
      currentStep.nextButtonEnabled = true;
    }

    // 如果任务状态发生变化，触发更新事件
    if (tasksChanged) {
      this.events.emit('tasksUpdated', { 
        tutorialId: this.activeTutorial.id, 
        stepId: currentStep.id,
        tasks: currentStep.tasks
      });
    }
  }

  /**
   * 检查单个条件
   */
  private checkCondition(condition: ValidationCondition, state: any): boolean {
    switch (condition.type) {
      case ValidationConditionType.PROPERTY_EQUALS:
        if (!condition.target || !condition.property) return false;
        return this.getNestedProperty(state, condition.target, condition.property) === condition.value;

      case ValidationConditionType.PROPERTY_RANGE:
        if (!condition.target || !condition.property || condition.min === undefined || condition.max === undefined) return false;
        const value = this.getNestedProperty(state, condition.target, condition.property);
        return value >= condition.min && value <= condition.max;

      case ValidationConditionType.PROPERTY_EXISTS:
        if (!condition.target || !condition.property) return false;
        return this.getNestedProperty(state, condition.target, condition.property) !== undefined;

      case ValidationConditionType.OBJECT_SELECTED:
        if (!condition.value) return false;
        return state.editor.selectedObject?.id === condition.value;

      case ValidationConditionType.COMPONENT_ADDED:
        if (!condition.target || !condition.value) return false;
        // 这里需要根据实际情况检查组件是否添加
        return false;

      case ValidationConditionType.ACTION_PERFORMED:
        // 这种类型的条件通常由外部调用 markActionPerformed 方法来满足
        return false;

      case ValidationConditionType.PANEL_OPENED:
        if (!condition.value) return false;
        return state.ui.openPanels.includes(condition.value);

      case ValidationConditionType.TOOL_USED:
        if (!condition.value) return false;
        return state.editor.activeTool === condition.value;

      case ValidationConditionType.CUSTOM:
        if (!condition.customValidator) return false;
        return condition.customValidator(state);

      default:
        return false;
    }
  }

  /**
   * 获取嵌套属性值
   */
  private getNestedProperty(obj: any, target: string, property: string): any {
    if (!obj || !target || !property) return undefined;
    
    const targetObj = target.split('.').reduce((o, i) => o?.[i], obj);
    if (!targetObj) return undefined;
    
    return property.split('.').reduce((o, i) => o?.[i], targetObj);
  }

  /**
   * 标记动作已执行
   */
  public markActionPerformed(tutorialId: string, taskId: string, actionId: string): boolean {
    if (!this.activeTutorial || this.activeTutorial.id !== tutorialId) return false;

    const currentStep = this.activeTutorial.steps[this.activeTutorial.currentStepIndex];
    if (!currentStep) return false;

    const task = currentStep.tasks.find(t => t.id === taskId);
    if (!task) return false;

    const condition = task.conditions.find(c => 
      c.type === ValidationConditionType.ACTION_PERFORMED && c.value === actionId
    );

    if (!condition) return false;

    // 将条件类型更改为自定义，并设置一个始终返回 true 的验证器
    condition.type = ValidationConditionType.CUSTOM;
    condition.customValidator = () => true;

    // 检查条件
    this.checkConditions();
    return true;
  }

  /**
   * 前进到下一步
   */
  public nextStep(): boolean {
    if (!this.activeTutorial) return false;

    const currentStep = this.activeTutorial.steps[this.activeTutorial.currentStepIndex];
    if (!currentStep || !currentStep.completed) return false;

    // 如果已经是最后一步，标记教程为完成
    if (this.activeTutorial.currentStepIndex >= this.activeTutorial.steps.length - 1) {
      this.completeTutorial();
      return true;
    }

    // 前进到下一步
    this.activeTutorial.currentStepIndex++;
    
    // 触发步骤变更事件
    this.events.emit('stepChanged', { 
      tutorialId: this.activeTutorial.id, 
      stepIndex: this.activeTutorial.currentStepIndex,
      step: this.activeTutorial.steps[this.activeTutorial.currentStepIndex]
    });

    // 检查新步骤的条件
    this.checkConditions();
    
    return true;
  }

  /**
   * 返回上一步
   */
  public previousStep(): boolean {
    if (!this.activeTutorial || this.activeTutorial.currentStepIndex <= 0) return false;

    // 返回上一步
    this.activeTutorial.currentStepIndex--;
    
    // 触发步骤变更事件
    this.events.emit('stepChanged', { 
      tutorialId: this.activeTutorial.id, 
      stepIndex: this.activeTutorial.currentStepIndex,
      step: this.activeTutorial.steps[this.activeTutorial.currentStepIndex]
    });
    
    return true;
  }

  /**
   * 完成教程
   */
  private completeTutorial(): void {
    if (!this.activeTutorial) return;

    this.activeTutorial.completed = true;
    
    // 触发教程完成事件
    this.events.emit('tutorialCompleted', this.activeTutorial);
    
    // 解锁相关成就
    achievementService.handleTutorialCompleted(this.activeTutorial.id);
    
    // 清除当前活动教程
    this.activeTutorial = null;
  }

  /**
   * 添加事件监听器
   */
  public on(event: string, listener: (...args: any[]) => void): void {
    this.events.on(event, listener);
  }

  /**
   * 移除事件监听器
   */
  public off(event: string, listener: (...args: any[]) => void): void {
    this.events.off(event, listener);
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    // 移除所有状态变化监听器
    this.stateChangeListeners.forEach(unsubscribe => unsubscribe());
    this.stateChangeListeners = [];
    
    // 移除所有事件监听器
    this.events.removeAllListeners();
  }
}

// 导出单例实例
export const tutorialValidationService = TutorialValidationService.getInstance();
