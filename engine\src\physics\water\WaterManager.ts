/**
 * 水体管理器
 * 统一管理场景中的所有水体
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { WaterBodyComponent } from './WaterBodyComponent';
import { WaterGenerator, WaterGenerationConfig } from './WaterGenerator';
import { WaterPresetType } from './WaterPresets';
import { WaterPhysicsSystem } from './WaterPhysicsSystem';

/**
 * 水体信息
 */
export interface WaterInfo {
  id: string;
  name: string;
  type: WaterPresetType;
  entity: Entity;
  waterBody: WaterBodyComponent;
  createdAt: Date;
  lastUpdated: Date;
}

/**
 * 水体统计信息
 */
export interface WaterStats {
  totalWaterBodies: number;
  typeDistribution: Record<WaterPresetType, number>;
  totalVolume: number;
  averageDensity: number;
  activeWaterBodies: number;
}

/**
 * 水体管理器类
 */
export class WaterManager {
  private static instance: WaterManager;
  private waterBodies: Map<string, WaterInfo> = new Map();
  private physicsSystem: WaterPhysicsSystem;
  private scene: THREE.Scene;
  private updateCallbacks: Array<(waterInfo: WaterInfo) => void> = [];

  /**
   * 获取单例实例
   */
  public static getInstance(): WaterManager {
    if (!WaterManager.instance) {
      WaterManager.instance = new WaterManager();
    }
    return WaterManager.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {
    // 注意：这里暂时传入 null，在 initialize 方法中会设置正确的 world
    this.physicsSystem = new WaterPhysicsSystem(null as any);
  }

  /**
   * 初始化管理器
   * @param scene Three.js 场景
   */
  public initialize(scene: THREE.Scene): void {
    this.scene = scene;
    this.physicsSystem.initialize();
  }

  /**
   * 添加水体
   * @param config 生成配置
   * @returns 水体信息
   */
  public addWater(config: WaterGenerationConfig): WaterInfo {
    const { entity, waterBody } = WaterGenerator.generateWater(config);
    
    const waterInfo: WaterInfo = {
      id: entity.id,
      name: entity.name,
      type: config.preset,
      entity,
      waterBody,
      createdAt: new Date(),
      lastUpdated: new Date()
    };

    this.waterBodies.set(entity.id, waterInfo);
    
    // 添加到场景
    if (this.scene && waterBody.getWaterMesh()) {
      this.scene.add(waterBody.getWaterMesh());
    }

    // 添加到物理系统
    this.physicsSystem.addWaterBody(entity, waterBody);

    // 触发回调
    this.updateCallbacks.forEach(callback => callback(waterInfo));

    console.log(`✅ 添加水体: ${waterInfo.name} (${waterInfo.id})`);
    return waterInfo;
  }

  /**
   * 移除水体
   * @param id 水体ID
   * @returns 是否成功移除
   */
  public removeWater(id: string): boolean {
    const waterInfo = this.waterBodies.get(id);
    if (!waterInfo) {
      console.warn(`⚠️ 未找到水体: ${id}`);
      return false;
    }

    // 从场景移除
    if (this.scene && waterInfo.waterBody.getWaterMesh()) {
      this.scene.remove(waterInfo.waterBody.getWaterMesh());
    }

    // 从物理系统移除
    this.physicsSystem.removeWaterBody(waterInfo.entity);

    // 从管理器移除
    this.waterBodies.delete(id);

    console.log(`✅ 移除水体: ${waterInfo.name} (${id})`);
    return true;
  }

  /**
   * 获取水体信息
   * @param id 水体ID
   * @returns 水体信息
   */
  public getWater(id: string): WaterInfo | undefined {
    return this.waterBodies.get(id);
  }

  /**
   * 获取所有水体
   * @returns 所有水体信息
   */
  public getAllWaters(): WaterInfo[] {
    return Array.from(this.waterBodies.values());
  }

  /**
   * 根据类型获取水体
   * @param type 水体类型
   * @returns 指定类型的水体
   */
  public getWatersByType(type: WaterPresetType): WaterInfo[] {
    return this.getAllWaters().filter(water => water.type === type);
  }

  /**
   * 更新水体
   * @param id 水体ID
   * @param updates 更新内容
   * @returns 是否成功更新
   */
  public updateWater(id: string, updates: Partial<WaterGenerationConfig>): boolean {
    const waterInfo = this.waterBodies.get(id);
    if (!waterInfo) {
      console.warn(`⚠️ 未找到水体: ${id}`);
      return false;
    }

    // 更新水体属性
    if (updates.size) {
      waterInfo.waterBody.setSize(updates.size);
    }

    if (updates.physicsParams) {
      if (updates.physicsParams.density !== undefined) {
        waterInfo.waterBody.setDensity(updates.physicsParams.density);
      }
      if (updates.physicsParams.viscosity !== undefined) {
        waterInfo.waterBody.setViscosity(updates.physicsParams.viscosity);
      }
    }

    if (updates.waveParams) {
      // 转换 waveParams 格式以匹配 WaterWaveParams 接口
      const waveParams = {
        amplitude: updates.waveParams.amplitude || 0.1,
        frequency: updates.waveParams.frequency || 0.5,
        speed: updates.waveParams.speed || 0.3,
        direction: updates.waveParams.direction ?
          { x: updates.waveParams.direction.x, z: updates.waveParams.direction.y } :
          { x: 1, z: 1 }
      };
      waterInfo.waterBody.setWaveParams(waveParams);
    }

    if (updates.flowParams) {
      if (updates.flowParams.direction) {
        waterInfo.waterBody.setFlowDirection(updates.flowParams.direction);
      }
      if (updates.flowParams.speed !== undefined) {
        waterInfo.waterBody.setFlowSpeed(updates.flowParams.speed);
      }
    }

    // 更新位置、旋转、缩放
    if (updates.position) {
      waterInfo.entity.transform.setPosition(updates.position);
    }
    if (updates.rotation) {
      waterInfo.entity.transform.setRotation(updates.rotation);
    }
    if (updates.scale) {
      waterInfo.entity.transform.setScale(updates.scale);
    }

    waterInfo.lastUpdated = new Date();

    console.log(`✅ 更新水体: ${waterInfo.name} (${id})`);
    return true;
  }

  /**
   * 清空所有水体
   */
  public clearAllWaters(): void {
    const waterIds = Array.from(this.waterBodies.keys());
    waterIds.forEach(id => this.removeWater(id));
    console.log('✅ 清空所有水体');
  }

  /**
   * 获取水体统计信息
   * @returns 统计信息
   */
  public getStats(): WaterStats {
    const waters = this.getAllWaters();
    const typeDistribution: Record<WaterPresetType, number> = {} as any;
    
    // 初始化类型分布
    Object.values(WaterPresetType).forEach(type => {
      typeDistribution[type] = 0;
    });

    let totalVolume = 0;
    let totalDensity = 0;
    let activeCount = 0;

    waters.forEach(water => {
      typeDistribution[water.type]++;
      
      const size = water.waterBody.getSize();
      const volume = size.width * size.height * size.depth;
      totalVolume += volume;
      totalDensity += water.waterBody.getDensity();
      
      if (water.waterBody.isEnabled()) {
        activeCount++;
      }
    });

    return {
      totalWaterBodies: waters.length,
      typeDistribution,
      totalVolume,
      averageDensity: waters.length > 0 ? totalDensity / waters.length : 0,
      activeWaterBodies: activeCount
    };
  }

  /**
   * 更新所有水体
   * @param deltaTime 时间增量
   */
  public update(deltaTime: number): void {
    // 更新物理系统
    this.physicsSystem.update(deltaTime);

    // 更新每个水体
    this.waterBodies.forEach(waterInfo => {
      if (waterInfo.waterBody.isEnabled()) {
        waterInfo.waterBody.update(deltaTime);
      }
    });
  }

  /**
   * 添加更新回调
   * @param callback 回调函数
   */
  public addUpdateCallback(callback: (waterInfo: WaterInfo) => void): void {
    this.updateCallbacks.push(callback);
  }

  /**
   * 移除更新回调
   * @param callback 回调函数
   */
  public removeUpdateCallback(callback: (waterInfo: WaterInfo) => void): void {
    const index = this.updateCallbacks.indexOf(callback);
    if (index > -1) {
      this.updateCallbacks.splice(index, 1);
    }
  }

  /**
   * 导出水体配置
   * @returns 配置数据
   */
  public exportConfig(): any {
    const waters = this.getAllWaters();
    return {
      version: '1.0',
      timestamp: new Date().toISOString(),
      waters: waters.map(water => ({
        id: water.id,
        name: water.name,
        type: water.type,
        position: water.entity.transform.getPosition().toArray(),
        rotation: water.entity.transform.getRotation().toArray(),
        scale: water.entity.transform.getScale().toArray(),
        size: water.waterBody.getSize(),
        density: water.waterBody.getDensity(),
        viscosity: water.waterBody.getViscosity(),
        waveParams: water.waterBody.getWaveParams(),
        flowDirection: [water.waterBody.getFlowDirection().x, water.waterBody.getFlowDirection().y, water.waterBody.getFlowDirection().z],
        flowSpeed: water.waterBody.getFlowSpeed()
      }))
    };
  }

  /**
   * 导入水体配置
   * @param config 配置数据
   */
  public importConfig(config: any): void {
    if (!config.waters || !Array.isArray(config.waters)) {
      console.error('❌ 无效的配置数据');
      return;
    }

    // 清空现有水体
    this.clearAllWaters();

    // 导入水体
    config.waters.forEach((waterConfig: any) => {
      try {
        const generationConfig: WaterGenerationConfig = {
          preset: waterConfig.type,
          entityId: waterConfig.id,
          entityName: waterConfig.name,
          position: new THREE.Vector3().fromArray(waterConfig.position),
          rotation: new THREE.Euler().fromArray(waterConfig.rotation),
          scale: new THREE.Vector3().fromArray(waterConfig.scale),
          size: waterConfig.size,
          physicsParams: {
            density: waterConfig.density,
            viscosity: waterConfig.viscosity
          },
          waveParams: waterConfig.waveParams,
          flowParams: {
            direction: new THREE.Vector3().fromArray(waterConfig.flowDirection),
            speed: waterConfig.flowSpeed
          }
        };

        this.addWater(generationConfig);
      } catch (error) {
        console.error(`❌ 导入水体失败: ${waterConfig.name}`, error);
      }
    });

    console.log(`✅ 导入 ${config.waters.length} 个水体配置`);
  }
}
