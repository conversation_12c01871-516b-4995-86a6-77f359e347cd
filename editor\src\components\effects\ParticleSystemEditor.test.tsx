/**
 * 粒子系统编辑器测试
 */
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ParticleSystemEditor from './ParticleSystemEditor';

// Mock antd components that might have issues in test environment
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  ColorPicker: ({ children, ...props }: any) => (
    <div data-testid="color-picker" {...props}>
      {children}
    </div>
  ),
  Upload: ({ children, ...props }: any) => (
    <div data-testid="upload" {...props}>
      {children}
    </div>
  )
}));

describe('ParticleSystemEditor', () => {
  const mockOnChange = jest.fn();

  beforeEach(() => {
    mockOnChange.mockClear();
  });

  it('应该渲染基本的编辑器界面', () => {
    render(<ParticleSystemEditor onChange={mockOnChange} />);
    
    // 检查标题
    expect(screen.getByText('粒子系统编辑器')).toBeInTheDocument();
    
    // 检查预览区域
    expect(screen.getByText('预览')).toBeInTheDocument();
    expect(screen.getByText('播放')).toBeInTheDocument();
    expect(screen.getByText('重置')).toBeInTheDocument();
    
    // 检查预设按钮
    expect(screen.getByText('火焰')).toBeInTheDocument();
    expect(screen.getByText('烟雾')).toBeInTheDocument();
    expect(screen.getByText('雪花')).toBeInTheDocument();
    expect(screen.getByText('爆炸')).toBeInTheDocument();
  });

  it('应该渲染所有标签页', () => {
    render(<ParticleSystemEditor onChange={mockOnChange} />);
    
    // 检查标签页
    expect(screen.getByText('发射器')).toBeInTheDocument();
    expect(screen.getByText('外观')).toBeInTheDocument();
    expect(screen.getByText('运动')).toBeInTheDocument();
    expect(screen.getByText('形状')).toBeInTheDocument();
    expect(screen.getByText('高级')).toBeInTheDocument();
  });

  it('应该显示基本表单字段', () => {
    render(<ParticleSystemEditor onChange={mockOnChange} />);
    
    // 检查基本字段
    expect(screen.getByLabelText('启用')).toBeInTheDocument();
    expect(screen.getByLabelText('名称')).toBeInTheDocument();
    expect(screen.getByDisplayValue('新粒子系统')).toBeInTheDocument();
  });

  it('应该在发射器标签页显示正确的字段', () => {
    render(<ParticleSystemEditor onChange={mockOnChange} />);
    
    // 检查发射器字段
    expect(screen.getByLabelText('最大粒子数')).toBeInTheDocument();
    expect(screen.getByLabelText('发射率')).toBeInTheDocument();
    expect(screen.getByLabelText('持续时间')).toBeInTheDocument();
    expect(screen.getByLabelText('循环')).toBeInTheDocument();
    expect(screen.getByLabelText('预热')).toBeInTheDocument();
    expect(screen.getByLabelText('模拟空间')).toBeInTheDocument();
    expect(screen.getByLabelText('生命周期')).toBeInTheDocument();
  });

  it('应该处理预设选择', async () => {
    render(<ParticleSystemEditor onChange={mockOnChange} />);

    // 等待预设按钮渲染
    const fireButton = await screen.findByText('火焰');
    fireEvent.click(fireButton);

    // 等待onChange被调用
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalled();
    });

    // 检查是否调用了onChange并且包含火焰预设的数据
    const lastCall = mockOnChange.mock.calls[mockOnChange.mock.calls.length - 1];
    expect(lastCall[0]).toMatchObject({
      name: '火焰',
      maxParticles: 1000,
      emissionRate: 100
    });
  });

  it('应该处理表单值变化', async () => {
    render(<ParticleSystemEditor onChange={mockOnChange} />);
    
    // 修改名称字段
    const nameInput = screen.getByLabelText('名称');
    fireEvent.change(nameInput, { target: { value: '测试粒子系统' } });
    
    // 等待onChange被调用
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalled();
    });
    
    // 检查是否调用了onChange并且包含新的名称
    const lastCall = mockOnChange.mock.calls[mockOnChange.mock.calls.length - 1];
    expect(lastCall[0]).toMatchObject({
      name: '测试粒子系统'
    });
  });

  it('应该处理播放/暂停按钮', () => {
    render(<ParticleSystemEditor onChange={mockOnChange} />);
    
    // 初始状态应该是播放
    const playButton = screen.getByText('暂停');
    expect(playButton).toBeInTheDocument();
    
    // 点击暂停
    fireEvent.click(playButton);
    
    // 应该变成播放按钮
    expect(screen.getByText('播放')).toBeInTheDocument();
  });

  it('应该切换标签页', async () => {
    render(<ParticleSystemEditor onChange={mockOnChange} />);

    // 点击外观标签页
    const appearanceTab = screen.getByText('外观');
    fireEvent.click(appearanceTab);

    // 等待标签页内容渲染
    await waitFor(() => {
      // 检查外观相关字段是否显示
      expect(screen.getByText('起始大小')).toBeInTheDocument();
      expect(screen.getByText('结束大小')).toBeInTheDocument();
      expect(screen.getByText('起始颜色')).toBeInTheDocument();
      expect(screen.getByText('结束颜色')).toBeInTheDocument();
    });
  });

  it('应该根据形状类型显示相应的控件', () => {
    render(<ParticleSystemEditor onChange={mockOnChange} />);
    
    // 切换到形状标签页
    const shapeTab = screen.getByText('形状');
    fireEvent.click(shapeTab);
    
    // 检查发射器形状选择器
    expect(screen.getByLabelText('发射器形状')).toBeInTheDocument();
  });

  it('应该支持禁用预览', () => {
    render(<ParticleSystemEditor onChange={mockOnChange} showPreview={false} />);
    
    // 预览区域不应该显示
    expect(screen.queryByText('预览')).not.toBeInTheDocument();
    expect(screen.queryByText('播放')).not.toBeInTheDocument();
  });

  it('应该使用传入的数据初始化表单', () => {
    const testData = {
      name: '测试粒子系统',
      maxParticles: 500,
      emissionRate: 50,
      enabled: false
    };
    
    render(<ParticleSystemEditor data={testData} onChange={mockOnChange} />);
    
    // 检查表单是否使用了传入的数据
    expect(screen.getByDisplayValue('测试粒子系统')).toBeInTheDocument();
    expect(screen.getByDisplayValue('500')).toBeInTheDocument();
    expect(screen.getByDisplayValue('50')).toBeInTheDocument();
  });

  it('应该验证必填字段', async () => {
    render(<ParticleSystemEditor onChange={mockOnChange} />);
    
    // 清空名称字段
    const nameInput = screen.getByLabelText('名称');
    fireEvent.change(nameInput, { target: { value: '' } });
    
    // 触发表单验证
    fireEvent.blur(nameInput);
    
    // 应该显示错误信息
    await waitFor(() => {
      expect(screen.getByText('请输入名称')).toBeInTheDocument();
    });
  });
});
