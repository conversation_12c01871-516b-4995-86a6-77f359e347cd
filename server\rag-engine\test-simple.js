// 简单的模块测试脚本
console.log('正在测试RAG引擎模块...');

try {
  // 测试配置模块
  const ragConfig = require('./dist/config/rag.config');
  console.log('✅ RAG配置模块加载成功');

  // 测试嵌入服务
  const { EmbeddingService } = require('./dist/embedding/embedding.service');
  console.log('✅ 嵌入服务模块加载成功');

  // 测试向量搜索服务
  const { VectorSearchService } = require('./dist/vector-search/vector-search.service');
  console.log('✅ 向量搜索服务模块加载成功');

  // 测试绑定服务
  const { BindingService } = require('./dist/binding/binding.service');
  console.log('✅ 绑定服务模块加载成功');

  // 测试LLM服务
  const { LLMService } = require('./dist/llm/llm.service');
  console.log('✅ LLM服务模块加载成功');

  // 测试RAG服务
  const { RAGService } = require('./dist/rag/rag.service');
  console.log('✅ RAG服务模块加载成功');

  // 测试RAG控制器
  const { RAGController } = require('./dist/rag/rag.controller');
  console.log('✅ RAG控制器模块加载成功');

  console.log('\n🎉 所有核心模块加载成功！');
  console.log('\n修复总结:');
  console.log('1. ✅ 修复了配置路径错误');
  console.log('2. ✅ 修复了fetch调用，改用axios');
  console.log('3. ✅ 修复了流式响应实现');
  console.log('4. ✅ 修复了绑定服务接口');
  console.log('5. ✅ 修复了提示词语法错误');
  console.log('6. ✅ 添加了缓存降级机制');
  console.log('7. ✅ 所有TypeScript编译错误已修复');

} catch (error) {
  console.error('❌ 模块测试失败:', error.message);
  console.error('详细错误:', error.stack);
  process.exit(1);
}
