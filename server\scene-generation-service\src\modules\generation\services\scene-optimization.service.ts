import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from '../../../common/services/logger.service';
import { BuiltScene, SceneObject } from './scene-building.service';

export interface OptimizationConfig {
  level: 'none' | 'basic' | 'aggressive';
  targetPlatform: 'web' | 'mobile' | 'desktop' | 'vr';
  targetFrameRate: number;
  maxPolygonCount?: number;
  maxTextureSize?: number;
  maxFileSize?: number;
  enableLOD: boolean;
  enableOcclusion: boolean;
  enableInstancing: boolean;
  enableCompression: boolean;
  qualityPreset: 'low' | 'medium' | 'high' | 'ultra';
}

export interface OptimizationResult {
  originalMetrics: SceneMetrics;
  optimizedMetrics: SceneMetrics;
  appliedOptimizations: AppliedOptimization[];
  performanceGain: {
    polygonReduction: number;
    textureSizeReduction: number;
    fileSizeReduction: number;
    estimatedFpsGain: number;
    memoryReduction: number;
  };
  recommendations: string[];
  warnings: string[];
}

export interface SceneMetrics {
  polygonCount: number;
  vertexCount: number;
  textureCount: number;
  textureMemory: number;
  materialCount: number;
  lightCount: number;
  objectCount: number;
  fileSize: number;
  estimatedMemoryUsage: number;
  estimatedLoadTime: number;
  estimatedFps: number;
}

export interface AppliedOptimization {
  type: string;
  description: string;
  impact: 'low' | 'medium' | 'high';
  metrics: {
    before: Partial<SceneMetrics>;
    after: Partial<SceneMetrics>;
  };
  objects?: string[];
}

export interface OptimizedScene extends BuiltScene {
  optimization: {
    config: OptimizationConfig;
    result: OptimizationResult;
    version: string;
    timestamp: string;
  };
}

@Injectable()
export class SceneOptimizationService {
  private readonly platformLimits = new Map<string, any>();
  private readonly qualityPresets = new Map<string, any>();

  constructor(
    private readonly configService: ConfigService,
    private readonly logger: LoggerService,
  ) {
    this.initializePlatformLimits();
    this.initializeQualityPresets();
  }

  async optimizeScene(
    scene: BuiltScene,
    config?: OptimizationConfig
  ): Promise<OptimizedScene> {
    try {
      this.logger.log(`开始优化场景: ${scene.metadata.name}`);

      // 设置默认配置
      const optimizationConfig = this.setDefaultConfig(config);

      // 计算原始指标
      const originalMetrics = this.calculateSceneMetrics(scene);

      // 创建场景副本进行优化
      const optimizedScene = this.cloneScene(scene);

      // 应用优化
      const appliedOptimizations: AppliedOptimization[] = [];

      // 1. 几何优化
      if (optimizationConfig.level !== 'none') {
        const geometryOpt = await this.optimizeGeometry(optimizedScene, optimizationConfig);
        appliedOptimizations.push(...geometryOpt);
      }

      // 2. 纹理优化
      if (optimizationConfig.level !== 'none') {
        const textureOpt = await this.optimizeTextures(optimizedScene, optimizationConfig);
        appliedOptimizations.push(...textureOpt);
      }

      // 3. 材质优化
      if (optimizationConfig.level === 'aggressive') {
        const materialOpt = await this.optimizeMaterials(optimizedScene, optimizationConfig);
        appliedOptimizations.push(...materialOpt);
      }

      // 4. 光照优化
      if (optimizationConfig.level !== 'none') {
        const lightingOpt = await this.optimizeLighting(optimizedScene, optimizationConfig);
        appliedOptimizations.push(...lightingOpt);
      }

      // 5. LOD优化
      if (optimizationConfig.enableLOD) {
        const lodOpt = await this.generateLOD(optimizedScene, optimizationConfig);
        appliedOptimizations.push(...lodOpt);
      }

      // 6. 实例化优化
      if (optimizationConfig.enableInstancing) {
        const instancingOpt = await this.optimizeInstancing(optimizedScene, optimizationConfig);
        appliedOptimizations.push(...instancingOpt);
      }

      // 7. 遮挡剔除优化
      if (optimizationConfig.enableOcclusion) {
        const occlusionOpt = await this.optimizeOcclusion(optimizedScene, optimizationConfig);
        appliedOptimizations.push(...occlusionOpt);
      }

      // 计算优化后指标
      const optimizedMetrics = this.calculateSceneMetrics(optimizedScene);

      // 计算性能提升
      const performanceGain = this.calculatePerformanceGain(originalMetrics, optimizedMetrics);

      // 生成建议和警告
      const recommendations = this.generateRecommendations(optimizedMetrics, optimizationConfig);
      const warnings = this.generateWarnings(optimizedMetrics, optimizationConfig);

      // 创建优化结果
      const optimizationResult: OptimizationResult = {
        originalMetrics,
        optimizedMetrics,
        appliedOptimizations,
        performanceGain,
        recommendations,
        warnings
      };

      // 更新场景元数据
      this.updateSceneMetadata(optimizedScene, optimizationResult);

      const result: OptimizedScene = {
        ...optimizedScene,
        optimization: {
          config: optimizationConfig,
          result: optimizationResult,
          version: '1.0.0',
          timestamp: new Date().toISOString()
        }
      };

      this.logger.log(`场景优化完成，多边形减少: ${performanceGain.polygonReduction.toFixed(1)}%`);
      return result;

    } catch (error) {
      this.logger.error('场景优化失败:', error);
      throw new Error(`场景优化失败: ${error.message}`);
    }
  }

  /**
   * 初始化平台限制
   */
  private initializePlatformLimits(): void {
    this.platformLimits.set('web', {
      maxPolygons: 50000,
      maxTextureSize: 1024,
      maxLights: 4,
      maxFileSize: 10 * 1024 * 1024 // 10MB
    });

    this.platformLimits.set('mobile', {
      maxPolygons: 20000,
      maxTextureSize: 512,
      maxLights: 2,
      maxFileSize: 5 * 1024 * 1024 // 5MB
    });

    this.platformLimits.set('desktop', {
      maxPolygons: 200000,
      maxTextureSize: 2048,
      maxLights: 8,
      maxFileSize: 50 * 1024 * 1024 // 50MB
    });

    this.platformLimits.set('vr', {
      maxPolygons: 30000,
      maxTextureSize: 1024,
      maxLights: 3,
      maxFileSize: 15 * 1024 * 1024 // 15MB
    });
  }

  /**
   * 初始化质量预设
   */
  private initializeQualityPresets(): void {
    this.qualityPresets.set('low', {
      polygonReduction: 0.7,
      textureReduction: 0.5,
      shadowQuality: 'low',
      enablePostProcessing: false
    });

    this.qualityPresets.set('medium', {
      polygonReduction: 0.5,
      textureReduction: 0.7,
      shadowQuality: 'medium',
      enablePostProcessing: true
    });

    this.qualityPresets.set('high', {
      polygonReduction: 0.3,
      textureReduction: 0.8,
      shadowQuality: 'high',
      enablePostProcessing: true
    });

    this.qualityPresets.set('ultra', {
      polygonReduction: 0.1,
      textureReduction: 0.9,
      shadowQuality: 'ultra',
      enablePostProcessing: true
    });
  }

  /**
   * 设置默认配置
   */
  private setDefaultConfig(config?: OptimizationConfig): OptimizationConfig {
    const platformLimits = this.platformLimits.get(config?.targetPlatform || 'web');

    return {
      level: config?.level || 'basic',
      targetPlatform: config?.targetPlatform || 'web',
      targetFrameRate: config?.targetFrameRate || 60,
      maxPolygonCount: config?.maxPolygonCount || platformLimits?.maxPolygons,
      maxTextureSize: config?.maxTextureSize || platformLimits?.maxTextureSize,
      maxFileSize: config?.maxFileSize || platformLimits?.maxFileSize,
      enableLOD: config?.enableLOD ?? true,
      enableOcclusion: config?.enableOcclusion ?? true,
      enableInstancing: config?.enableInstancing ?? true,
      enableCompression: config?.enableCompression ?? true,
      qualityPreset: config?.qualityPreset || 'medium'
    };
  }

  /**
   * 计算场景指标
   */
  private calculateSceneMetrics(scene: BuiltScene): SceneMetrics {
    let polygonCount = 0;
    let vertexCount = 0;
    let textureMemory = 0;

    // 计算对象指标
    scene.objects.forEach(obj => {
      if (obj.properties.geometry) {
        // 估算多边形数量
        polygonCount += this.estimatePolygonCount(obj);
        vertexCount += this.estimateVertexCount(obj);
      }
    });

    // 计算纹理内存
    scene.materials.forEach(material => {
      material.textures.forEach(texture => {
        textureMemory += this.estimateTextureMemory(texture);
      });
    });

    const estimatedMemoryUsage = scene.metadata.performance.estimatedMemoryUsage;
    const estimatedLoadTime = scene.metadata.performance.estimatedLoadTime;
    const estimatedFps = this.estimateFPS(polygonCount, scene.environment.lighting);

    return {
      polygonCount,
      vertexCount,
      textureCount: scene.metadata.performance.textureCount,
      textureMemory,
      materialCount: scene.metadata.performance.materialCount,
      lightCount: scene.metadata.performance.lightCount,
      objectCount: scene.objects.length,
      fileSize: estimatedMemoryUsage,
      estimatedMemoryUsage,
      estimatedLoadTime,
      estimatedFps
    };
  }

  /**
   * 估算多边形数量
   */
  private estimatePolygonCount(obj: SceneObject): number {
    if (!obj.properties.geometry) return 0;

    const geometry = obj.properties.geometry;
    switch (geometry.type) {
      case 'BoxGeometry':
        return 12; // 立方体有12个三角形
      case 'PlaneGeometry':
        return 2; // 平面有2个三角形
      case 'SphereGeometry':
        const segments = geometry.parameters?.segments || 32;
        return segments * segments * 2;
      default:
        return 100; // 默认估算
    }
  }

  /**
   * 估算顶点数量
   */
  private estimateVertexCount(obj: SceneObject): number {
    return this.estimatePolygonCount(obj) * 3; // 每个三角形3个顶点
  }

  /**
   * 估算纹理内存
   */
  private estimateTextureMemory(texture: any): number {
    // 假设1024x1024 RGBA纹理
    return 1024 * 1024 * 4; // 4MB
  }

  /**
   * 估算FPS
   */
  private estimateFPS(polygonCount: number, lighting: any): number {
    let baseFps = 60;

    // 根据多边形数量调整
    if (polygonCount > 100000) baseFps -= 30;
    else if (polygonCount > 50000) baseFps -= 15;
    else if (polygonCount > 20000) baseFps -= 5;

    // 根据光源数量调整
    const lightCount = lighting.directional.length + lighting.point.length;
    baseFps -= lightCount * 2;

    return Math.max(15, baseFps);
  }

  /**
   * 克隆场景
   */
  private cloneScene(scene: BuiltScene): BuiltScene {
    // 深度克隆场景对象
    return JSON.parse(JSON.stringify(scene));
  }

  /**
   * 几何优化
   */
  private async optimizeGeometry(scene: BuiltScene, config: OptimizationConfig): Promise<AppliedOptimization[]> {
    const optimizations: AppliedOptimization[] = [];
    const qualityPreset = this.qualityPresets.get(config.qualityPreset);

    if (!qualityPreset) return optimizations;

    let totalPolygonsBefore = 0;
    let totalPolygonsAfter = 0;

    scene.objects.forEach(obj => {
      if (obj.properties.geometry) {
        const beforePolygons = this.estimatePolygonCount(obj);
        totalPolygonsBefore += beforePolygons;

        // 应用多边形减少
        const reductionFactor = qualityPreset.polygonReduction;
        const afterPolygons = Math.floor(beforePolygons * reductionFactor);
        totalPolygonsAfter += afterPolygons;

        // 更新几何体参数（模拟优化）
        if (obj.properties.geometry.parameters) {
          Object.keys(obj.properties.geometry.parameters).forEach(key => {
            if (typeof obj.properties.geometry.parameters[key] === 'number') {
              obj.properties.geometry.parameters[key] *= reductionFactor;
            }
          });
        }
      }
    });

    if (totalPolygonsBefore > totalPolygonsAfter) {
      optimizations.push({
        type: 'geometry_simplification',
        description: `几何体简化，减少了 ${((totalPolygonsBefore - totalPolygonsAfter) / totalPolygonsBefore * 100).toFixed(1)}% 的多边形`,
        impact: 'high',
        metrics: {
          before: { polygonCount: totalPolygonsBefore },
          after: { polygonCount: totalPolygonsAfter }
        }
      });
    }

    return optimizations;
  }

  /**
   * 纹理优化
   */
  private async optimizeTextures(scene: BuiltScene, config: OptimizationConfig): Promise<AppliedOptimization[]> {
    const optimizations: AppliedOptimization[] = [];
    let textureMemoryBefore = 0;
    let textureMemoryAfter = 0;

    scene.materials.forEach(material => {
      material.textures.forEach(texture => {
        const beforeMemory = this.estimateTextureMemory(texture);
        textureMemoryBefore += beforeMemory;

        // 应用纹理压缩和尺寸优化
        const maxSize = config.maxTextureSize || 1024;
        const compressionRatio = config.enableCompression ? 0.5 : 1.0;

        const afterMemory = beforeMemory * compressionRatio;
        textureMemoryAfter += afterMemory;

        // 更新纹理属性
        texture.properties = {
          ...texture.properties,
          maxSize,
          compressed: config.enableCompression
        };
      });
    });

    if (textureMemoryBefore > textureMemoryAfter) {
      optimizations.push({
        type: 'texture_optimization',
        description: `纹理优化，减少了 ${((textureMemoryBefore - textureMemoryAfter) / textureMemoryBefore * 100).toFixed(1)}% 的纹理内存`,
        impact: 'medium',
        metrics: {
          before: { textureMemory: textureMemoryBefore },
          after: { textureMemory: textureMemoryAfter }
        }
      });
    }

    return optimizations;
  }

  /**
   * 材质优化
   */
  private async optimizeMaterials(scene: BuiltScene, config: OptimizationConfig): Promise<AppliedOptimization[]> {
    const optimizations: AppliedOptimization[] = [];
    const materialsBefore = scene.materials.length;

    // 合并相似材质
    const uniqueMaterials = new Map<string, any>();
    scene.materials.forEach(material => {
      const key = this.getMaterialKey(material);
      if (!uniqueMaterials.has(key)) {
        uniqueMaterials.set(key, material);
      }
    });

    scene.materials = Array.from(uniqueMaterials.values());
    const materialsAfter = scene.materials.length;

    if (materialsBefore > materialsAfter) {
      optimizations.push({
        type: 'material_merging',
        description: `材质合并，从 ${materialsBefore} 个减少到 ${materialsAfter} 个`,
        impact: 'low',
        metrics: {
          before: { materialCount: materialsBefore },
          after: { materialCount: materialsAfter }
        }
      });
    }

    return optimizations;
  }

  /**
   * 光照优化
   */
  private async optimizeLighting(scene: BuiltScene, config: OptimizationConfig): Promise<AppliedOptimization[]> {
    const optimizations: AppliedOptimization[] = [];
    const lightsBefore = scene.environment.lighting.directional.length + scene.environment.lighting.point.length;

    const platformLimits = this.platformLimits.get(config.targetPlatform);
    const maxLights = platformLimits?.maxLights || 4;

    if (lightsBefore > maxLights) {
      // 保留最重要的光源
      scene.environment.lighting.directional = scene.environment.lighting.directional.slice(0, Math.min(2, maxLights));
      scene.environment.lighting.point = scene.environment.lighting.point.slice(0, Math.max(0, maxLights - 2));

      const lightsAfter = scene.environment.lighting.directional.length + scene.environment.lighting.point.length;

      optimizations.push({
        type: 'lighting_optimization',
        description: `光源优化，从 ${lightsBefore} 个减少到 ${lightsAfter} 个`,
        impact: 'medium',
        metrics: {
          before: { lightCount: lightsBefore },
          after: { lightCount: lightsAfter }
        }
      });
    }

    return optimizations;
  }

  /**
   * 生成LOD
   */
  private async generateLOD(scene: BuiltScene, config: OptimizationConfig): Promise<AppliedOptimization[]> {
    const optimizations: AppliedOptimization[] = [];
    let lodObjectsCount = 0;

    scene.objects.forEach(obj => {
      if (obj.type === 'mesh' && this.estimatePolygonCount(obj) > 1000) {
        // 为高多边形对象添加LOD信息
        obj.userData.lod = {
          levels: [
            { distance: 0, polygonReduction: 1.0 },
            { distance: 10, polygonReduction: 0.5 },
            { distance: 50, polygonReduction: 0.2 },
            { distance: 100, polygonReduction: 0.1 }
          ]
        };
        lodObjectsCount++;
      }
    });

    if (lodObjectsCount > 0) {
      optimizations.push({
        type: 'lod_generation',
        description: `为 ${lodObjectsCount} 个对象生成了LOD`,
        impact: 'high',
        metrics: {
          before: { objectCount: scene.objects.length },
          after: { objectCount: scene.objects.length }
        }
      });
    }

    return optimizations;
  }

  /**
   * 实例化优化
   */
  private async optimizeInstancing(scene: BuiltScene, config: OptimizationConfig): Promise<AppliedOptimization[]> {
    const optimizations: AppliedOptimization[] = [];
    const instanceGroups = new Map<string, SceneObject[]>();

    // 查找可实例化的对象
    scene.objects.forEach(obj => {
      if (obj.assetId) {
        if (!instanceGroups.has(obj.assetId)) {
          instanceGroups.set(obj.assetId, []);
        }
        instanceGroups.get(obj.assetId)!.push(obj);
      }
    });

    let instancedObjects = 0;
    instanceGroups.forEach((objects, assetId) => {
      if (objects.length > 1) {
        // 标记为实例化对象
        objects.forEach(obj => {
          obj.userData.instanced = true;
          obj.userData.instanceGroup = assetId;
        });
        instancedObjects += objects.length;
      }
    });

    if (instancedObjects > 0) {
      optimizations.push({
        type: 'instancing',
        description: `${instancedObjects} 个对象启用了实例化渲染`,
        impact: 'medium',
        metrics: {
          before: { objectCount: scene.objects.length },
          after: { objectCount: scene.objects.length }
        }
      });
    }

    return optimizations;
  }

  /**
   * 遮挡剔除优化
   */
  private async optimizeOcclusion(scene: BuiltScene, config: OptimizationConfig): Promise<AppliedOptimization[]> {
    const optimizations: AppliedOptimization[] = [];

    // 为场景添加遮挡剔除信息
    scene.objects.forEach(obj => {
      obj.userData.occlusionCulling = {
        enabled: true,
        boundingBox: this.calculateBoundingBox(obj),
        visibility: 'auto'
      };
    });

    optimizations.push({
      type: 'occlusion_culling',
      description: '启用了遮挡剔除优化',
      impact: 'medium',
      metrics: {
        before: { objectCount: scene.objects.length },
        after: { objectCount: scene.objects.length }
      }
    });

    return optimizations;
  }

  /**
   * 计算性能提升
   */
  private calculatePerformanceGain(original: SceneMetrics, optimized: SceneMetrics): OptimizationResult['performanceGain'] {
    return {
      polygonReduction: ((original.polygonCount - optimized.polygonCount) / original.polygonCount) * 100,
      textureSizeReduction: ((original.textureMemory - optimized.textureMemory) / original.textureMemory) * 100,
      fileSizeReduction: ((original.fileSize - optimized.fileSize) / original.fileSize) * 100,
      estimatedFpsGain: optimized.estimatedFps - original.estimatedFps,
      memoryReduction: ((original.estimatedMemoryUsage - optimized.estimatedMemoryUsage) / original.estimatedMemoryUsage) * 100
    };
  }

  /**
   * 生成建议
   */
  private generateRecommendations(metrics: SceneMetrics, config: OptimizationConfig): string[] {
    const recommendations: string[] = [];

    if (metrics.polygonCount > (config.maxPolygonCount || 50000)) {
      recommendations.push('考虑进一步简化几何体或使用更激进的LOD策略');
    }

    if (metrics.lightCount > 4) {
      recommendations.push('考虑使用烘焙光照或减少实时光源数量');
    }

    if (metrics.textureMemory > 50 * 1024 * 1024) {
      recommendations.push('考虑使用更小的纹理尺寸或更高的压缩比');
    }

    if (metrics.estimatedFps < config.targetFrameRate) {
      recommendations.push('当前优化可能不足以达到目标帧率，建议降低质量设置');
    }

    return recommendations;
  }

  /**
   * 生成警告
   */
  private generateWarnings(metrics: SceneMetrics, config: OptimizationConfig): string[] {
    const warnings: string[] = [];

    if (config.level === 'aggressive') {
      warnings.push('激进优化可能会影响视觉质量');
    }

    if (metrics.polygonCount < 1000) {
      warnings.push('多边形数量过低可能影响视觉效果');
    }

    return warnings;
  }

  /**
   * 更新场景元数据
   */
  private updateSceneMetadata(scene: BuiltScene, result: OptimizationResult): void {
    scene.metadata.performance = {
      ...scene.metadata.performance,
      polygonCount: result.optimizedMetrics.polygonCount,
      textureCount: result.optimizedMetrics.textureCount,
      lightCount: result.optimizedMetrics.lightCount,
      materialCount: result.optimizedMetrics.materialCount,
      estimatedMemoryUsage: result.optimizedMetrics.estimatedMemoryUsage,
      estimatedLoadTime: result.optimizedMetrics.estimatedLoadTime
    };

    scene.metadata.optimization = {
      level: 'basic',
      techniques: result.appliedOptimizations.map(opt => opt.type),
      compressionRatio: result.performanceGain.fileSizeReduction / 100
    };
  }

  /**
   * 获取材质键值
   */
  private getMaterialKey(material: any): string {
    return `${material.type}_${JSON.stringify(material.properties)}`;
  }

  /**
   * 计算包围盒
   */
  private calculateBoundingBox(obj: SceneObject): any {
    return {
      min: { x: -1, y: -1, z: -1 },
      max: { x: 1, y: 1, z: 1 }
    };
  }
}
