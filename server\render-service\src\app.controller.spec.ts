import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';

describe('AppController', () => {
  let appController: AppController;
  let appService: AppService;

  beforeEach(async () => {
    const mockConfigService = {
      get: jest.fn((key: string, defaultValue?: any) => {
        const config = {
          NODE_ENV: 'test',
          RENDER_SERVICE_HOST: 'localhost',
          RENDER_SERVICE_PORT: 3004,
          RENDER_SERVICE_HTTP_PORT: 4004,
        };
        return config[key] || defaultValue;
      }),
    };

    const mockServiceRegistry = {
      send: jest.fn().mockReturnValue({ subscribe: jest.fn() }),
    };

    const app: TestingModule = await Test.createTestingModule({
      controllers: [AppController],
      providers: [
        AppService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: 'SERVICE_REGISTRY',
          useValue: mockServiceRegistry,
        },
      ],
    }).compile();

    appController = app.get<AppController>(AppController);
    appService = app.get<AppService>(AppService);
  });

  describe('root', () => {
    it('should return service info', () => {
      const result = appController.getInfo();
      expect(result).toHaveProperty('name');
      expect(result.name).toBe('渲染服务');
      expect(result).toHaveProperty('version');
      expect(result).toHaveProperty('description');
      expect(result).toHaveProperty('environment');
      expect(result).toHaveProperty('instanceId');
    });
  });
});
