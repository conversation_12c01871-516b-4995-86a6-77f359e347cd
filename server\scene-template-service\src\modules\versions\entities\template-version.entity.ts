import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  Index,
} from 'typeorm';
import { SceneTemplate } from '../../templates/entities/scene-template.entity';
import { User } from '../../auth/entities/user.entity';

export enum VersionStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  DEPRECATED = 'deprecated',
  DELETED = 'deleted',
}

@Entity('template_versions')
@Index(['templateId', 'version'])
@Index(['status', 'createdAt'])
export class TemplateVersion {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 20 })
  @Index()
  version: string; // 语义化版本号，如 1.0.0

  @Column({ type: 'text', nullable: true })
  changelog: string; // 版本更新日志

  @Column({
    type: 'enum',
    enum: VersionStatus,
    default: VersionStatus.DRAFT,
  })
  @Index()
  status: VersionStatus;

  // 模板数据快照
  @Column({ type: 'json' })
  sceneDataSnapshot: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  parametersSnapshot: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  metadataSnapshot: Record<string, any>;

  // 文件信息
  @Column({ name: 'file_path', nullable: true })
  filePath: string;

  @Column({ name: 'file_size', type: 'bigint', nullable: true })
  fileSize: number;

  @Column({ name: 'file_hash', length: 64, nullable: true })
  fileHash: string; // SHA-256 哈希值

  // 版本特定的统计
  @Column({ name: 'download_count', default: 0 })
  downloadCount: number;

  @Column({ name: 'is_current', default: false })
  isCurrent: boolean; // 是否为当前版本

  // 关联关系
  @ManyToOne(() => SceneTemplate, template => template.versions, { onDelete: 'CASCADE' })
  template: SceneTemplate;

  @Column({ name: 'template_id' })
  @Index()
  templateId: string;

  @ManyToOne(() => User)
  creator: User;

  @Column({ name: 'creator_id' })
  creatorId: string;

  // 时间戳
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @Column({ name: 'published_at', nullable: true })
  publishedAt: Date;

  // 计算属性
  get isPublished(): boolean {
    return this.status === VersionStatus.PUBLISHED;
  }

  get fileSizeFormatted(): string {
    if (!this.fileSize) return '0 B';
    
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = Number(this.fileSize);
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  get versionParts(): { major: number; minor: number; patch: number } {
    const parts = this.version.split('.').map(Number);
    return {
      major: parts[0] || 0,
      minor: parts[1] || 0,
      patch: parts[2] || 0,
    };
  }
}
