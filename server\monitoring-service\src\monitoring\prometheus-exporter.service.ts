import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { register, collectDefaultMetrics, Counter, Histogram, Gauge } from 'prom-client';

/**
 * Prometheus导出服务
 * 负责收集和导出Prometheus格式的指标
 */
@Injectable()
export class PrometheusExporterService {
  private readonly logger = new Logger(PrometheusExporterService.name);

  // 自定义指标
  private readonly httpRequestsTotal: Counter<string>;
  private readonly httpRequestDuration: Histogram<string>;
  private readonly activeConnections: Gauge<string>;
  private readonly systemCpuUsage: Gauge<string>;
  private readonly systemMemoryUsage: Gauge<string>;

  constructor(private readonly configService: ConfigService) {
    // 启用默认指标收集
    collectDefaultMetrics({
      prefix: 'monitoring_service_',
      register,
    });

    // 初始化自定义指标
    this.httpRequestsTotal = new Counter({
      name: 'monitoring_http_requests_total',
      help: 'Total number of HTTP requests',
      labelNames: ['method', 'route', 'status_code'],
      registers: [register],
    });

    this.httpRequestDuration = new Histogram({
      name: 'monitoring_http_request_duration_seconds',
      help: 'Duration of HTTP requests in seconds',
      labelNames: ['method', 'route'],
      buckets: [0.1, 0.5, 1, 2, 5],
      registers: [register],
    });

    this.activeConnections = new Gauge({
      name: 'monitoring_active_connections',
      help: 'Number of active connections',
      registers: [register],
    });

    this.systemCpuUsage = new Gauge({
      name: 'monitoring_system_cpu_usage_percent',
      help: 'System CPU usage percentage',
      registers: [register],
    });

    this.systemMemoryUsage = new Gauge({
      name: 'monitoring_system_memory_usage_bytes',
      help: 'System memory usage in bytes',
      registers: [register],
    });

    this.logger.log('Prometheus导出服务已初始化');
  }

  /**
   * 记录HTTP请求
   */
  recordHttpRequest(method: string, route: string, statusCode: number, duration: number): void {
    this.httpRequestsTotal.inc({
      method,
      route,
      status_code: statusCode.toString(),
    });

    this.httpRequestDuration.observe(
      { method, route },
      duration / 1000, // 转换为秒
    );
  }

  /**
   * 设置活跃连接数
   */
  setActiveConnections(count: number): void {
    this.activeConnections.set(count);
  }

  /**
   * 设置系统CPU使用率
   */
  setSystemCpuUsage(percentage: number): void {
    this.systemCpuUsage.set(percentage);
  }

  /**
   * 设置系统内存使用量
   */
  setSystemMemoryUsage(bytes: number): void {
    this.systemMemoryUsage.set(bytes);
  }

  /**
   * 获取所有指标
   */
  async getMetrics(): Promise<string> {
    return await register.metrics();
  }

  /**
   * 清除所有指标
   */
  clearMetrics(): void {
    register.clear();
    this.logger.log('所有指标已清除');
  }
}
