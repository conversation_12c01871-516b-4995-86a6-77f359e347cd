/**
 * 数字人交互控制器
 * 集成语音、动作、表情控制，实现完整的数字人交互体验，支持实时响应和状态同步
 */
import * as THREE from 'three';
import { type Entity } from '../../core/Entity';
import { Component } from '../../core/Component';
import { SpeechRecognitionService } from '../speech/SpeechRecognitionService';
import { SpeechSynthesisService } from '../speech/SpeechSynthesisService';
import { DialogueManager, DialogueResponse } from '../dialogue/DialogueManager';
/**
 * 数字人状态枚举
 */
export declare enum DigitalHumanState {
    IDLE = "idle",
    LISTENING = "listening",
    THINKING = "thinking",
    SPEAKING = "speaking",
    MOVING = "moving",
    INTERACTING = "interacting",
    ERROR = "error"
}
/**
 * 表情类型枚举
 */
export declare enum FacialExpression {
    NEUTRAL = "neutral",
    HAPPY = "happy",
    SAD = "sad",
    SURPRISED = "surprised",
    ANGRY = "angry",
    CONFUSED = "confused",
    EXCITED = "excited",
    THINKING = "thinking"
}
/**
 * 动画状态接口
 */
export interface AnimationState {
    name: string;
    duration: number;
    loop: boolean;
    weight: number;
    fadeInTime: number;
    fadeOutTime: number;
}
/**
 * 数字人外观配置接口
 */
export interface DigitalHumanAppearance {
    model: string;
    textures: Record<string, string>;
    materials: Record<string, any>;
    scale: THREE.Vector3;
    animations: Record<string, AnimationState>;
}
/**
 * 交互配置接口
 */
export interface InteractionConfig {
    enableVoiceInput: boolean;
    enableVoiceOutput: boolean;
    enableGestures: boolean;
    enableFacialExpressions: boolean;
    enableLipSync: boolean;
    responseDelay: number;
    idleTimeout: number;
    maxConversationLength: number;
    autoNavigation: boolean;
}
/**
 * 数字人交互事件接口
 */
export interface InteractionEvent {
    type: string;
    data: any;
    timestamp: Date;
    source: 'voice' | 'gesture' | 'navigation' | 'system';
}
/**
 * 动画控制器
 */
export declare class AnimationController {
    private mixer;
    private actions;
    private currentAction;
    private model;
    constructor(model?: THREE.Object3D);
    /**
     * 设置模型
     */
    setModel(model: THREE.Object3D): void;
    /**
     * 加载动画
     */
    private loadAnimations;
    /**
     * 创建基础动画
     */
    private createBasicAnimations;
    /**
     * 创建待机动画
     */
    private createIdleAnimation;
    /**
     * 创建挥手动画
     */
    private createWaveAnimation;
    /**
     * 创建指向动画
     */
    private createPointAnimation;
    /**
     * 创建思考动画
     */
    private createThinkAnimation;
    /**
     * 播放动画
     */
    playAnimation(name: string, loop?: boolean, fadeTime?: number): boolean;
    /**
     * 停止动画
     */
    stopAnimation(fadeTime?: number): void;
    /**
     * 更新动画
     */
    update(deltaTime: number): void;
    /**
     * 获取当前动画
     */
    getCurrentAnimation(): string | null;
    /**
     * 检查动画是否存在
     */
    hasAnimation(name: string): boolean;
    /**
     * 获取所有动画名称
     */
    getAnimationNames(): string[];
}
/**
 * 表情控制器
 */
export declare class FacialExpressionController {
    private currentExpression;
    private expressionIntensity;
    private model;
    private morphTargets;
    constructor(model?: THREE.Object3D);
    /**
     * 设置模型
     */
    setModel(model: THREE.Object3D): void;
    /**
     * 初始化变形目标
     */
    private initializeMorphTargets;
    /**
     * 设置表情
     */
    setExpression(expression: FacialExpression, intensity?: number, duration?: number): void;
    /**
     * 应用表情
     */
    private applyExpression;
    /**
     * 应用变形目标
     */
    private applyMorphTargets;
    /**
     * 获取当前表情
     */
    getCurrentExpression(): FacialExpression;
    /**
     * 获取表情强度
     */
    getExpressionIntensity(): number;
    /**
     * 重置表情
     */
    resetExpression(duration?: number): void;
}
/**
 * 数字人交互控制器组件
 */
export declare class DigitalHumanInteractionController extends Component {
    static readonly TYPE = "DigitalHumanInteraction";
    private speechRecognition;
    private speechSynthesis;
    private dialogueManager;
    private navigationComponent;
    private animationController;
    private facialController;
    private state;
    private config;
    private sessionId;
    private lastInteractionTime;
    private idleTimer;
    onStateChanged?: (state: DigitalHumanState) => void;
    onInteractionEvent?: (event: InteractionEvent) => void;
    onResponse?: (response: DialogueResponse) => void;
    constructor(entity: Entity, speechRecognition: SpeechRecognitionService, speechSynthesis: SpeechSynthesisService, dialogueManager: DialogueManager, config: InteractionConfig, sessionId?: string);
    /**
     * 设置事件处理器
     */
    private setupEventHandlers;
    /**
     * 处理语音输入
     */
    private handleVoiceInput;
    /**
     * 处理语音命令
     */
    private handleVoiceCommand;
    /**
     * 执行响应
     */
    private executeResponse;
    /**
     * 根据情感设置表情
     */
    private setExpressionFromEmotion;
    /**
     * 执行动作
     */
    private executeActions;
    /**
     * 执行单个动作
     */
    private executeAction;
    /**
     * 播放手势动画
     */
    private playGesture;
    /**
     * 指向方向
     */
    private pointToDirection;
    /**
     * 移动到位置
     */
    private moveToPosition;
    /**
     * 看向目标
     */
    private lookAtTarget;
    /**
     * 转向方向
     */
    private turnToDirection;
    /**
     * 处理口型同步
     */
    private handleLipSync;
    /**
     * 处理停留点到达
     */
    private handleStopPointReached;
    /**
     * 开始导航
     */
    private startNavigation;
    /**
     * 停止导航
     */
    private stopNavigation;
    /**
     * 解释当前位置
     */
    private explainCurrentLocation;
    /**
     * 导航到下一个点
     */
    private navigateToNext;
    /**
     * 导航到上一个点
     */
    private navigateToPrevious;
    /**
     * 重复最后的响应
     */
    private repeatLastResponse;
    /**
     * 语音错误提示
     */
    private speakError;
    /**
     * 开始监听
     */
    startListening(): void;
    /**
     * 停止监听
     */
    stopListening(): void;
    /**
     * 设置状态
     */
    private setState;
    /**
     * 根据状态更新动画
     */
    private updateAnimationForState;
    /**
     * 开始空闲计时器
     */
    private startIdleTimer;
    /**
     * 处理空闲超时
     */
    private handleIdleTimeout;
    /**
     * 重置空闲计时器
     */
    private resetIdleTimer;
    /**
     * 更新最后交互时间
     */
    private updateLastInteractionTime;
    /**
     * 获取当前位置
     */
    private getCurrentLocation;
    /**
     * 获取用户画像
     */
    private getUserProfile;
    /**
     * 设置模型
     */
    setModel(model: THREE.Object3D): void;
    /**
     * 获取当前状态
     */
    getState(): DigitalHumanState;
    /**
     * 获取配置
     */
    getConfig(): InteractionConfig;
    /**
     * 更新配置
     */
    updateConfig(config: Partial<InteractionConfig>): void;
    /**
     * 组件更新
     */
    update(deltaTime: number): void;
    /**
     * 获取统计信息
     */
    getStatistics(): any;
    /**
     * 销毁组件
     */
    dispose(): void;
}
