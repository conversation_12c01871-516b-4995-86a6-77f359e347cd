/**
 * 用户服务测试
 */
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
// import { ConflictException, NotFoundException } from '@nestjs/common';
import { UsersService } from './users.service';
import { User, UserRole } from './entities/user.entity';
import { UserAvatar } from './entities/user-avatar.entity';
import { UserSetting } from './entities/user-setting.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';

// 模拟数据
const mockUsers = [
  {
    id: '1',
    username: 'testuser1',
    email: '<EMAIL>',
    password: 'hashedpassword1',
    displayName: 'Test User 1',
    isVerified: false,
    isGuest: false,
    role: UserRole.USER,
    inviteCode: null,
    lastLoginAt: null,
    avatar: null,
    settings: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '2',
    username: 'testuser2',
    email: '<EMAIL>',
    password: 'hashedpassword2',
    displayName: 'Test User 2',
    isVerified: false,
    isGuest: false,
    role: UserRole.USER,
    inviteCode: null,
    lastLoginAt: null,
    avatar: null,
    settings: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

// 模拟用户仓库
const mockUserRepository = {
  find: jest.fn().mockResolvedValue(mockUsers),
  findOne: jest.fn().mockImplementation(({ where }) => {
    if (where.id === '1') {
      return Promise.resolve(mockUsers[0]);
    }
    if (where.username === 'testuser1') {
      return Promise.resolve(mockUsers[0]);
    }
    if (where.email === '<EMAIL>') {
      return Promise.resolve(mockUsers[0]);
    }
    return Promise.resolve(null);
  }),
  create: jest.fn().mockImplementation(dto => ({ id: '3', ...dto })),
  save: jest.fn().mockImplementation(user => Promise.resolve({ id: '3', ...user })),
  update: jest.fn().mockResolvedValue({ affected: 1 }),
  delete: jest.fn().mockResolvedValue({ affected: 1 }),
  remove: jest.fn().mockResolvedValue(undefined),
};

// 模拟用户头像仓库
const mockUserAvatarRepository = {
  find: jest.fn().mockResolvedValue([]),
  findOne: jest.fn().mockResolvedValue(null),
  create: jest.fn().mockImplementation(dto => dto),
  save: jest.fn().mockImplementation(avatar => Promise.resolve({ id: '1', ...avatar })),
  update: jest.fn().mockResolvedValue({ affected: 1 }),
  delete: jest.fn().mockResolvedValue({ affected: 1 }),
};

// 模拟用户设置仓库
const mockUserSettingRepository = {
  find: jest.fn().mockResolvedValue([]),
  findOne: jest.fn().mockResolvedValue(null),
  create: jest.fn().mockImplementation(dto => dto),
  save: jest.fn().mockImplementation(setting => Promise.resolve({ id: '1', ...setting })),
  update: jest.fn().mockResolvedValue({ affected: 1 }),
  delete: jest.fn().mockResolvedValue({ affected: 1 }),
};

describe('UsersService', () => {
  let service: UsersService;
  let userRepository: Repository<User>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: getRepositoryToken(UserAvatar),
          useValue: mockUserAvatarRepository,
        },
        {
          provide: getRepositoryToken(UserSetting),
          useValue: mockUserSettingRepository,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
  });

  it('应该被定义', () => {
    expect(service).toBeDefined();
  });

  describe('findAll', () => {
    it('应该返回所有用户', async () => {
      const result = await service.findAll();
      expect(result).toEqual(mockUsers);
      expect(userRepository.find).toHaveBeenCalledWith({
        relations: ['avatar', 'settings'],
      });
    });
  });

  describe('findOne', () => {
    it('应该通过ID返回单个用户', async () => {
      const result = await service.findOne('1');
      expect(result).toEqual(mockUsers[0]);
      expect(userRepository.findOne).toHaveBeenCalledWith({
        where: { id: '1' },
        relations: ['avatar', 'settings'],
      });
    });

    it('当用户不存在时应该抛出异常', async () => {
      await expect(service.findOne('999')).rejects.toThrow('用户ID 999 不存在');
    });
  });

  describe('findByUsername', () => {
    it('应该通过用户名返回用户', async () => {
      const result = await service.findByUsername('testuser1');
      expect(result).toEqual(mockUsers[0]);
      expect(userRepository.findOne).toHaveBeenCalledWith({
        where: { username: 'testuser1' },
        relations: ['avatar', 'settings'],
      });
    });

    it('当用户名不存在时应该抛出异常', async () => {
      await expect(service.findByUsername('nonexistent')).rejects.toThrow(
        '用户名 nonexistent 不存在',
      );
    });
  });

  describe('create', () => {
    it('应该创建并返回新用户', async () => {
      const createUserDto: CreateUserDto = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
      };

      const result = await service.create(createUserDto);

      expect(result).toHaveProperty('id');
      expect(result.username).toEqual(createUserDto.username);
      expect(result.email).toEqual(createUserDto.email);
      expect(userRepository.create).toHaveBeenCalled();
      expect(userRepository.save).toHaveBeenCalled();
    });

    it('当用户名已存在时应该抛出异常', async () => {
      const createUserDto: CreateUserDto = {
        username: 'testuser1', // 已存在的用户名
        email: '<EMAIL>',
        password: 'password123',
      };

      await expect(service.create(createUserDto)).rejects.toThrow('用户名已存在');
    });

    it('当邮箱已存在时应该抛出异常', async () => {
      const createUserDto: CreateUserDto = {
        username: 'newuser',
        email: '<EMAIL>', // 已存在的邮箱
        password: 'password123',
      };

      await expect(service.create(createUserDto)).rejects.toThrow('邮箱已存在');
    });
  });

  describe('update', () => {
    it('应该更新并返回用户', async () => {
      const updateUserDto: UpdateUserDto = {
        username: 'updateduser',
        email: '<EMAIL>',
      };

      jest.spyOn(service, 'findOne').mockResolvedValueOnce(mockUsers[0]);

      const result = await service.update('1', updateUserDto);

      expect(result).toBeDefined();
      expect(userRepository.save).toHaveBeenCalled();
    });
  });

  describe('remove', () => {
    it('应该删除用户', async () => {
      jest.spyOn(service, 'findOne').mockResolvedValueOnce(mockUsers[0]);

      await service.remove('1');

      expect(userRepository.remove).toHaveBeenCalledWith(mockUsers[0]);
    });
  });
});
