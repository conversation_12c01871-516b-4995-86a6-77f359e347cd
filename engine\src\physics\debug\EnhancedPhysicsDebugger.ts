/**
 * 增强型物理调试器
 * 提供更多物理调试功能和性能监控
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { PhysicsSystem } from '../PhysicsSystem';
import type { PhysicsBody } from '../PhysicsBody';
import { PhysicsCollider } from '../PhysicsCollider';
import { PhysicsConstraint } from '../constraints/PhysicsConstraint';
import { PhysicsDebugger, PhysicsDebuggerOptions } from './PhysicsDebugger';
import { SliderConstraint } from '../constraints/SliderConstraint';
import { FixedConstraint } from '../constraints/FixedConstraint';
import { WheelConstraint } from '../constraints/WheelConstraint';

/**
 * 增强型物理调试器选项
 */
export interface EnhancedPhysicsDebuggerOptions extends PhysicsDebuggerOptions {
  /** 是否显示速度向量 */
  showVelocities?: boolean;
  /** 是否显示力向量 */
  showForces?: boolean;
  /** 是否显示质心 */
  showCenterOfMass?: boolean;
  /** 是否显示睡眠状态 */
  showSleepState?: boolean;
  /** 是否显示物理性能统计 */
  showPerformanceStats?: boolean;
  /** 是否显示碰撞点法线 */
  showContactNormals?: boolean;
  /** 是否显示碰撞点力 */
  showContactForces?: boolean;
  /** 是否显示碰撞点摩擦力 */
  showFrictionForces?: boolean;
  /** 速度向量颜色 */
  velocityColor?: THREE.Color | number;
  /** 力向量颜色 */
  forceColor?: THREE.Color | number;
  /** 质心颜色 */
  centerOfMassColor?: THREE.Color | number;
  /** 睡眠状态颜色 */
  sleepStateColor?: THREE.Color | number;
  /** 碰撞法线颜色 */
  contactNormalColor?: THREE.Color | number;
  /** 碰撞力颜色 */
  contactForceColor?: THREE.Color | number;
  /** 摩擦力颜色 */
  frictionForceColor?: THREE.Color | number;
  /** 向量缩放因子 */
  vectorScale?: number;
  /** 碰撞力缩放因子 */
  contactForceScale?: number;
}

/**
 * 物理性能统计
 */
interface PhysicsPerformanceStats {
  /** 物理更新时间 (ms) */
  updateTime: number;
  /** 碰撞检测时间 (ms) */
  collisionTime: number;
  /** 约束求解时间 (ms) */
  constraintTime: number;
  /** 物理体数量 */
  bodyCount: number;
  /** 约束数量 */
  constraintCount: number;
  /** 碰撞对数量 */
  contactCount: number;
  /** 帧率 */
  fps: number;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 增强型物理调试器
 */
export class EnhancedPhysicsDebugger extends PhysicsDebugger {
  /** 是否显示速度向量 */
  private showVelocities: boolean;

  /** 是否显示力向量 */
  private showForces: boolean;

  /** 是否显示质心 */
  private showCenterOfMass: boolean;

  /** 是否显示睡眠状态 */
  private showSleepState: boolean;

  /** 是否显示物理性能统计 */
  private showPerformanceStats: boolean;

  /** 是否显示碰撞点法线 */
  private showContactNormals: boolean;

  /** 是否显示碰撞点力 */
  private showContactForces: boolean;

  /** 是否显示碰撞点摩擦力 */
  private showFrictionForces: boolean;

  /** 速度向量颜色 */
  private velocityColor: THREE.Color;

  /** 力向量颜色 */
  private forceColor: THREE.Color;

  /** 质心颜色 */
  private centerOfMassColor: THREE.Color;

  /** 睡眠状态颜色 */
  private sleepStateColor: THREE.Color;

  /** 碰撞法线颜色 */
  private contactNormalColor: THREE.Color;

  /** 碰撞力颜色 */
  private contactForceColor: THREE.Color;

  /** 摩擦力颜色（暂时未使用，保留以备将来扩展） */
  private frictionForceColor: THREE.Color;

  /** 向量缩放因子 */
  private vectorScale: number;

  /** 碰撞力缩放因子 */
  private contactForceScale: number;

  /** 速度向量映射 */
  private velocityArrows: Map<CANNON.Body, THREE.ArrowHelper> = new Map();

  /** 力向量映射 */
  private forceArrows: Map<CANNON.Body, THREE.ArrowHelper> = new Map();

  /** 碰撞法线向量 */
  private contactNormalArrows: THREE.ArrowHelper[] = [];

  /** 碰撞力向量 */
  private contactForceArrows: THREE.ArrowHelper[] = [];

  /** 摩擦力向量 */
  private frictionForceArrows: THREE.ArrowHelper[] = [];

  /** 质心网格映射 */
  private centerOfMassMeshes: Map<CANNON.Body, THREE.Mesh> = new Map();

  /** 睡眠状态指示器映射 */
  private sleepStateMeshes: Map<CANNON.Body, THREE.Sprite> = new Map();

  /** 性能统计数据 */
  private performanceStats: PhysicsPerformanceStats[] = [];

  /** 性能统计面板 */
  private statsPanel: HTMLElement | null = null;

  /** 性能监控开始时间 */
  private monitorStartTime: number = 0;

  /** 性能监控结束时间 */
  private monitorEndTime: number = 0;

  /** 上一帧时间 */
  private lastFrameTime: number = 0;

  /** 帧计数器 */
  private frameCount: number = 0;

  /** 帧率计算间隔 (ms) */
  private fpsInterval: number = 1000;

  /** 帧率计算时间戳 */
  private fpsTimestamp: number = 0;

  /** 当前帧率 */
  private currentFps: number = 0;

  /**
   * 创建增强型物理调试器
   * @param physicsSystem 物理系统
   * @param options 调试器选项
   */
  constructor(physicsSystem: PhysicsSystem, options: EnhancedPhysicsDebuggerOptions = {}) {
    super(physicsSystem, options);

    // 设置增强选项
    this.showVelocities = options.showVelocities !== undefined ? options.showVelocities : false;
    this.showForces = options.showForces !== undefined ? options.showForces : false;
    this.showCenterOfMass = options.showCenterOfMass !== undefined ? options.showCenterOfMass : false;
    this.showSleepState = options.showSleepState !== undefined ? options.showSleepState : false;
    this.showPerformanceStats = options.showPerformanceStats !== undefined ? options.showPerformanceStats : false;
    this.showContactNormals = options.showContactNormals !== undefined ? options.showContactNormals : false;
    this.showContactForces = options.showContactForces !== undefined ? options.showContactForces : false;
    this.showFrictionForces = options.showFrictionForces !== undefined ? options.showFrictionForces : false;

    // 设置颜色
    this.velocityColor = options.velocityColor instanceof THREE.Color
      ? options.velocityColor
      : new THREE.Color(options.velocityColor !== undefined ? options.velocityColor : 0x00ffff);

    this.forceColor = options.forceColor instanceof THREE.Color
      ? options.forceColor
      : new THREE.Color(options.forceColor !== undefined ? options.forceColor : 0xff00ff);

    this.centerOfMassColor = options.centerOfMassColor instanceof THREE.Color
      ? options.centerOfMassColor
      : new THREE.Color(options.centerOfMassColor !== undefined ? options.centerOfMassColor : 0xffff00);

    this.sleepStateColor = options.sleepStateColor instanceof THREE.Color
      ? options.sleepStateColor
      : new THREE.Color(options.sleepStateColor !== undefined ? options.sleepStateColor : 0x888888);

    this.contactNormalColor = options.contactNormalColor instanceof THREE.Color
      ? options.contactNormalColor
      : new THREE.Color(options.contactNormalColor !== undefined ? options.contactNormalColor : 0x00ff00);

    this.contactForceColor = options.contactForceColor instanceof THREE.Color
      ? options.contactForceColor
      : new THREE.Color(options.contactForceColor !== undefined ? options.contactForceColor : 0xff0000);

    this.frictionForceColor = options.frictionForceColor instanceof THREE.Color
      ? options.frictionForceColor
      : new THREE.Color(options.frictionForceColor !== undefined ? options.frictionForceColor : 0xffa500);

    // 设置向量缩放因子
    this.vectorScale = options.vectorScale !== undefined ? options.vectorScale : 0.1;
    this.contactForceScale = options.contactForceScale !== undefined ? options.contactForceScale : 0.01;

    // 初始化性能监控
    if (this.showPerformanceStats) {
      this.initPerformanceStats();
    }
  }

  /**
   * 初始化性能统计面板
   */
  private initPerformanceStats(): void {
    // 创建性能统计面板
    this.statsPanel = document.createElement('div');
    this.statsPanel.style.position = 'absolute';
    this.statsPanel.style.top = '10px';
    this.statsPanel.style.right = '10px';
    this.statsPanel.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    this.statsPanel.style.color = 'white';
    this.statsPanel.style.padding = '10px';
    this.statsPanel.style.borderRadius = '5px';
    this.statsPanel.style.fontFamily = 'monospace';
    this.statsPanel.style.fontSize = '12px';
    this.statsPanel.style.zIndex = '1000';

    document.body.appendChild(this.statsPanel);
  }

  /**
   * 更新性能统计
   */
  private updatePerformanceStats(): void {
    if (!this.showPerformanceStats || !this.statsPanel) return;

    const world = this.getPhysicsWorld();

    // 计算帧率
    const now = performance.now();
    this.frameCount++;

    if (now - this.fpsTimestamp >= this.fpsInterval) {
      this.currentFps = Math.round((this.frameCount * 1000) / (now - this.fpsTimestamp));
      this.fpsTimestamp = now;
      this.frameCount = 0;
    }

    // 收集性能数据
    const stats: PhysicsPerformanceStats = {
      updateTime: world.lastCallTime,
      collisionTime: (world as any).collisionMatrix?.stats?.broadphase || 0,
      constraintTime: (world as any).solver?.stats?.solve || 0,
      bodyCount: world.bodies.length,
      constraintCount: world.constraints.length,
      contactCount: world.contacts.length,
      fps: this.currentFps,
      timestamp: now
    };

    // 保存性能数据
    this.performanceStats.push(stats);
    if (this.performanceStats.length > 100) {
      this.performanceStats.shift();
    }

    // 更新性能统计面板
    this.statsPanel.innerHTML = `
      <div>物理性能统计</div>
      <div>FPS: ${stats.fps}</div>
      <div>物理更新时间: ${stats.updateTime.toFixed(2)} ms</div>
      <div>碰撞检测时间: ${stats.collisionTime.toFixed(2)} ms</div>
      <div>约束求解时间: ${stats.constraintTime.toFixed(2)} ms</div>
      <div>物理体数量: ${stats.bodyCount}</div>
      <div>约束数量: ${stats.constraintCount}</div>
      <div>碰撞对数量: ${stats.contactCount}</div>
    `;
  }

  /**
   * 获取物理世界
   * @returns 物理世界
   */
  private getPhysicsWorld(): CANNON.World {
    return this.getPhysicsSystem().getPhysicsWorld();
  }

  /**
   * 获取物理系统
   * @returns 物理系统
   */
  public getPhysicsSystem(): PhysicsSystem {
    return this.physicsSystem;
  }

  /**
   * 开始性能监控
   */
  public startPerformanceMonitor(): void {
    this.monitorStartTime = performance.now();
  }

  /**
   * 结束性能监控
   * @returns 监控时间 (ms)
   */
  public endPerformanceMonitor(): number {
    this.monitorEndTime = performance.now();
    return this.monitorEndTime - this.monitorStartTime;
  }

  /**
   * 获取性能统计数据
   * @returns 性能统计数据
   */
  public getPerformanceStats(): PhysicsPerformanceStats[] {
    return this.performanceStats;
  }

  /**
   * 清除性能统计数据
   */
  public clearPerformanceStats(): void {
    this.performanceStats = [];
  }

  /**
   * 设置是否显示速度向量
   * @param show 是否显示
   */
  public setShowVelocities(show: boolean): void {
    this.showVelocities = show;

    if (!show) {
      // 移除所有速度向量
      for (const arrow of this.velocityArrows.values()) {
        this.getScene().remove(arrow);
      }
      this.velocityArrows.clear();
    }
  }

  /**
   * 设置是否显示力向量
   * @param show 是否显示
   */
  public setShowForces(show: boolean): void {
    this.showForces = show;

    if (!show) {
      // 移除所有力向量
      for (const arrow of this.forceArrows.values()) {
        this.getScene().remove(arrow);
      }
      this.forceArrows.clear();
    }
  }

  /**
   * 设置是否显示质心
   * @param show 是否显示
   */
  public setShowCenterOfMass(show: boolean): void {
    this.showCenterOfMass = show;

    if (!show) {
      // 移除所有质心网格
      for (const mesh of this.centerOfMassMeshes.values()) {
        this.getScene().remove(mesh);
      }
      this.centerOfMassMeshes.clear();
    }
  }

  /**
   * 设置是否显示睡眠状态
   * @param show 是否显示
   */
  public setShowSleepState(show: boolean): void {
    this.showSleepState = show;

    if (!show) {
      // 移除所有睡眠状态指示器
      for (const mesh of this.sleepStateMeshes.values()) {
        this.getScene().remove(mesh);
      }
      this.sleepStateMeshes.clear();
    }
  }

  /**
   * 设置是否显示物理性能统计
   * @param show 是否显示
   */
  public setShowPerformanceStats(show: boolean): void {
    this.showPerformanceStats = show;

    if (show && !this.statsPanel) {
      this.initPerformanceStats();
    } else if (!show && this.statsPanel) {
      document.body.removeChild(this.statsPanel);
      this.statsPanel = null;
    }
  }

  /**
   * 设置是否显示碰撞法线
   * @param show 是否显示
   */
  public setShowContactNormals(show: boolean): void {
    this.showContactNormals = show;

    if (!show) {
      // 移除所有碰撞法线向量
      for (const arrow of this.contactNormalArrows) {
        this.getScene().remove(arrow);
      }
      this.contactNormalArrows = [];
    }
  }

  /**
   * 设置是否显示碰撞力
   * @param show 是否显示
   */
  public setShowContactForces(show: boolean): void {
    this.showContactForces = show;

    if (!show) {
      // 移除所有碰撞力向量
      for (const arrow of this.contactForceArrows) {
        this.getScene().remove(arrow);
      }
      this.contactForceArrows = [];
    }
  }

  /**
   * 设置是否显示摩擦力
   * @param show 是否显示
   */
  public setShowFrictionForces(show: boolean): void {
    this.showFrictionForces = show;

    if (!show) {
      // 移除所有摩擦力向量
      for (const arrow of this.frictionForceArrows) {
        this.getScene().remove(arrow);
      }
      this.frictionForceArrows = [];
    }
  }

  /**
   * 获取调试场景
   * @returns 调试场景
   */
  public getScene(): THREE.Scene {
    return this.scene;
  }

  /**
   * 初始化调试器
   */
  public initialize(): void {
    // 调用父类的初始化方法
    super.initialize();

    if (!this.initialized) return;

    // 获取物理世界
    const world = this.getPhysicsWorld();

    // 创建速度向量
    if (this.showVelocities) {
      for (let i = 0; i < world.bodies.length; i++) {
        const body = world.bodies[i];
        if (body.type === CANNON.Body.DYNAMIC) {
          const arrow = this.createVelocityArrow(body);
          this.velocityArrows.set(body, arrow);
          this.getScene().add(arrow);
        }
      }
    }

    // 创建力向量
    if (this.showForces) {
      for (let i = 0; i < world.bodies.length; i++) {
        const body = world.bodies[i];
        if (body.type === CANNON.Body.DYNAMIC) {
          const arrow = this.createForceArrow(body);
          this.forceArrows.set(body, arrow);
          this.getScene().add(arrow);
        }
      }
    }

    // 创建质心网格
    if (this.showCenterOfMass) {
      for (let i = 0; i < world.bodies.length; i++) {
        const body = world.bodies[i];
        if (body.type === CANNON.Body.DYNAMIC) {
          const mesh = this.createCenterOfMassMesh(body);
          this.centerOfMassMeshes.set(body, mesh);
          this.getScene().add(mesh);
        }
      }
    }

    // 创建睡眠状态指示器
    if (this.showSleepState) {
      for (let i = 0; i < world.bodies.length; i++) {
        const body = world.bodies[i];
        if (body.type === CANNON.Body.DYNAMIC) {
          const mesh = this.createSleepStateMesh(body);
          this.sleepStateMeshes.set(body, mesh);
          this.getScene().add(mesh);
        }
      }
    }
  }

  /**
   * 更新调试器
   */
  public update(): void {
    // 开始性能监控
    this.startPerformanceMonitor();

    // 调用父类的更新方法
    super.update();

    if (!this.initialized) return;

    // 获取物理世界
    const world = this.getPhysicsWorld();

    // 更新速度向量
    if (this.showVelocities) {
      for (let i = 0; i < world.bodies.length; i++) {
        const body = world.bodies[i];
        if (body.type === CANNON.Body.DYNAMIC) {
          // 如果物理体没有速度向量，创建一个
          if (!this.velocityArrows.has(body)) {
            const arrow = this.createVelocityArrow(body);
            this.velocityArrows.set(body, arrow);
            this.getScene().add(arrow);
          }

          // 更新速度向量
          const arrow = this.velocityArrows.get(body);
          this.updateVelocityArrow(body, arrow);
        }
      }

      // 移除不存在的速度向量
      for (const [body, arrow] of this.velocityArrows.entries()) {
        if (!world.bodies.includes(body)) {
          this.getScene().remove(arrow);
          this.velocityArrows.delete(body);
        }
      }
    }

    // 更新力向量
    if (this.showForces) {
      for (let i = 0; i < world.bodies.length; i++) {
        const body = world.bodies[i];
        if (body.type === CANNON.Body.DYNAMIC) {
          // 如果物理体没有力向量，创建一个
          if (!this.forceArrows.has(body)) {
            const arrow = this.createForceArrow(body);
            this.forceArrows.set(body, arrow);
            this.getScene().add(arrow);
          }

          // 更新力向量
          const arrow = this.forceArrows.get(body);
          this.updateForceArrow(body, arrow);
        }
      }

      // 移除不存在的力向量
      for (const [body, arrow] of this.forceArrows.entries()) {
        if (!world.bodies.includes(body)) {
          this.getScene().remove(arrow);
          this.forceArrows.delete(body);
        }
      }
    }

    // 更新质心网格
    if (this.showCenterOfMass) {
      for (let i = 0; i < world.bodies.length; i++) {
        const body = world.bodies[i];
        if (body.type === CANNON.Body.DYNAMIC) {
          // 如果物理体没有质心网格，创建一个
          if (!this.centerOfMassMeshes.has(body)) {
            const mesh = this.createCenterOfMassMesh(body);
            this.centerOfMassMeshes.set(body, mesh);
            this.getScene().add(mesh);
          }

          // 更新质心网格
          const mesh = this.centerOfMassMeshes.get(body);
          this.updateCenterOfMassMesh(body, mesh);
        }
      }

      // 移除不存在的质心网格
      for (const [body, mesh] of this.centerOfMassMeshes.entries()) {
        if (!world.bodies.includes(body)) {
          this.getScene().remove(mesh);
          this.centerOfMassMeshes.delete(body);
        }
      }
    }

    // 更新睡眠状态指示器
    if (this.showSleepState) {
      for (let i = 0; i < world.bodies.length; i++) {
        const body = world.bodies[i];
        if (body.type === CANNON.Body.DYNAMIC) {
          // 如果物理体没有睡眠状态指示器，创建一个
          if (!this.sleepStateMeshes.has(body)) {
            const mesh = this.createSleepStateMesh(body);
            this.sleepStateMeshes.set(body, mesh);
            this.getScene().add(mesh);
          }

          // 更新睡眠状态指示器
          const mesh = this.sleepStateMeshes.get(body);
          this.updateSleepStateMesh(body, mesh);
        }
      }

      // 移除不存在的睡眠状态指示器
      for (const [body, mesh] of this.sleepStateMeshes.entries()) {
        if (!world.bodies.includes(body)) {
          this.getScene().remove(mesh);
          this.sleepStateMeshes.delete(body);
        }
      }
    }

    // 更新碰撞可视化
    if (this.showContactNormals || this.showContactForces || this.showFrictionForces) {
      this.updateContactVisualization();
    }

    // 更新性能统计
    if (this.showPerformanceStats) {
      this.updatePerformanceStats();
    }

    // 结束性能监控
    this.endPerformanceMonitor();
  }

  /**
   * 更新碰撞可视化
   */
  private updateContactVisualization(): void {
    // 获取物理世界
    const world = this.getPhysicsWorld();

    // 清除现有的碰撞可视化
    for (const arrow of this.contactNormalArrows) {
      this.getScene().remove(arrow);
    }
    this.contactNormalArrows = [];

    for (const arrow of this.contactForceArrows) {
      this.getScene().remove(arrow);
    }
    this.contactForceArrows = [];

    for (const arrow of this.frictionForceArrows) {
      this.getScene().remove(arrow);
    }
    this.frictionForceArrows = [];

    // 遍历所有碰撞
    for (let i = 0; i < world.contacts.length; i++) {
      const contact = world.contacts[i];

      // 在 cannon-es 中，world.contacts 中的每个元素就是 ContactEquation
      // 获取碰撞点和法线
      const contactPoint = new THREE.Vector3(
        contact.ri.x + (contact.bi as any).getPosition().x,
        contact.ri.y + (contact.bi as any).getPosition().y,
        contact.ri.z + (contact.bi as any).getPosition().z
      );

      const normal = new THREE.Vector3(
        contact.ni.x,
        contact.ni.y,
        contact.ni.z
      );

      // 创建碰撞法线向量
      if (this.showContactNormals) {
        const normalArrow = new THREE.ArrowHelper(
          normal.clone().normalize(),
          contactPoint,
          0.2,
          this.contactNormalColor.getHex(),
          0.05,
          0.02
        );
        this.contactNormalArrows.push(normalArrow);
        this.getScene().add(normalArrow);
      }

      // 创建碰撞力向量
      if (this.showContactForces) {
        // 使用接触方程的冲量来计算力
        const impulse = (contact as any).impulse || 0;
        const force = normal.clone().multiplyScalar(Math.abs(impulse) * this.contactForceScale);
        if (force.length() > 0.01) {
          const forceArrow = new THREE.ArrowHelper(
            force.clone().normalize(),
            contactPoint,
            force.length(),
            this.contactForceColor.getHex(),
            0.05,
            0.02
          );
          this.contactForceArrows.push(forceArrow);
          this.getScene().add(forceArrow);
        }
      }
    }

    // 注意：cannon-es 中摩擦力的处理方式不同，这里简化处理
    // 如果需要显示摩擦力，可以通过其他方式获取摩擦方程
  }

  /**
   * 创建速度向量
   * @param body 物理体
   * @returns 速度向量
   */
  private createVelocityArrow(body: CANNON.Body): THREE.ArrowHelper {
    const origin = new THREE.Vector3(body.position.x, body.position.y, body.position.z);
    const direction = new THREE.Vector3(body.velocity.x, body.velocity.y, body.velocity.z).normalize();
    const length = Math.sqrt(
      body.velocity.x * body.velocity.x +
      body.velocity.y * body.velocity.y +
      body.velocity.z * body.velocity.z
    ) * this.vectorScale;

    return new THREE.ArrowHelper(
      direction,
      origin,
      length,
      this.velocityColor.getHex(),
      0.05,
      0.02
    );
  }

  /**
   * 更新速度向量
   * @param body 物理体
   * @param arrow 速度向量
   */
  private updateVelocityArrow(body: CANNON.Body, arrow: THREE.ArrowHelper | undefined): void {
    if (!arrow) return;

    const origin = new THREE.Vector3(body.position.x, body.position.y, body.position.z);
    const direction = new THREE.Vector3(body.velocity.x, body.velocity.y, body.velocity.z);
    const length = Math.sqrt(
      direction.x * direction.x +
      direction.y * direction.y +
      direction.z * direction.z
    ) * this.vectorScale;

    if (length > 0.001) {
      direction.normalize();
      arrow.position.copy(origin);
      arrow.setDirection(direction);
      arrow.setLength(length, length * 0.2, length * 0.1);
      arrow.visible = true;
    } else {
      arrow.visible = false;
    }
  }

  /**
   * 创建力向量
   * @param body 物理体
   * @returns 力向量
   */
  private createForceArrow(body: CANNON.Body): THREE.ArrowHelper {
    const origin = new THREE.Vector3(body.position.x, body.position.y, body.position.z);
    const direction = new THREE.Vector3(body.force.x, body.force.y, body.force.z);
    const length = Math.sqrt(
      direction.x * direction.x +
      direction.y * direction.y +
      direction.z * direction.z
    ) * this.vectorScale;

    if (length > 0.001) {
      direction.normalize();
    } else {
      direction.set(0, 1, 0);
    }

    return new THREE.ArrowHelper(
      direction,
      origin,
      length,
      this.forceColor.getHex(),
      0.05,
      0.02
    );
  }

  /**
   * 更新力向量
   * @param body 物理体
   * @param arrow 力向量
   */
  private updateForceArrow(body: CANNON.Body, arrow: THREE.ArrowHelper | undefined): void {
    if (!arrow) return;

    const origin = new THREE.Vector3(body.position.x, body.position.y, body.position.z);
    const direction = new THREE.Vector3(body.force.x, body.force.y, body.force.z);
    const length = Math.sqrt(
      direction.x * direction.x +
      direction.y * direction.y +
      direction.z * direction.z
    ) * this.vectorScale;

    if (length > 0.001) {
      direction.normalize();
      arrow.position.copy(origin);
      arrow.setDirection(direction);
      arrow.setLength(length, length * 0.2, length * 0.1);
      arrow.visible = true;
    } else {
      arrow.visible = false;
    }
  }

  /**
   * 创建质心网格
   * @param body 物理体
   * @returns 质心网格
   */
  private createCenterOfMassMesh(body: CANNON.Body): THREE.Mesh {
    const geometry = new THREE.SphereGeometry(0.05, 8, 8);
    const material = new THREE.MeshBasicMaterial({ color: this.centerOfMassColor });
    const mesh = new THREE.Mesh(geometry, material);

    mesh.position.set(body.position.x, body.position.y, body.position.z);
    return mesh;
  }

  /**
   * 更新质心网格
   * @param body 物理体
   * @param mesh 质心网格
   */
  private updateCenterOfMassMesh(body: CANNON.Body, mesh: THREE.Mesh | undefined): void {
    if (!mesh) return;
    mesh.position.set(body.position.x, body.position.y, body.position.z);
  }

  /**
   * 创建睡眠状态网格
   * @param body 物理体
   * @returns 睡眠状态网格
   */
  private createSleepStateMesh(body: CANNON.Body): THREE.Sprite {
    const canvas = document.createElement('canvas');
    canvas.width = 64;
    canvas.height = 64;
    const context = canvas.getContext('2d')!;

    context.fillStyle = body.sleepState === CANNON.Body.SLEEPING ? '#ff0000' : '#00ff00';
    context.fillRect(0, 0, 64, 64);
    context.fillStyle = '#ffffff';
    context.font = '12px Arial';
    context.textAlign = 'center';
    context.fillText(body.sleepState === CANNON.Body.SLEEPING ? 'S' : 'A', 32, 36);

    const texture = new THREE.CanvasTexture(canvas);
    const material = new THREE.SpriteMaterial({ map: texture });
    const sprite = new THREE.Sprite(material);

    sprite.position.set(body.position.x, body.position.y + 1, body.position.z);
    sprite.scale.set(0.5, 0.5, 0.5);

    return sprite;
  }

  /**
   * 更新睡眠状态网格
   * @param body 物理体
   * @param sprite 睡眠状态精灵
   */
  private updateSleepStateMesh(body: CANNON.Body, sprite: THREE.Sprite | undefined): void {
    if (!sprite) return;

    sprite.position.set(body.position.x, body.position.y + 1, body.position.z);

    // 更新睡眠状态颜色
    const canvas = document.createElement('canvas');
    canvas.width = 64;
    canvas.height = 64;
    const context = canvas.getContext('2d')!;

    context.fillStyle = body.sleepState === CANNON.Body.SLEEPING ? '#ff0000' : '#00ff00';
    context.fillRect(0, 0, 64, 64);
    context.fillStyle = '#ffffff';
    context.font = '12px Arial';
    context.textAlign = 'center';
    context.fillText(body.sleepState === CANNON.Body.SLEEPING ? 'S' : 'A', 32, 36);

    const texture = new THREE.CanvasTexture(canvas);
    (sprite.material as THREE.SpriteMaterial).map = texture;
    (sprite.material as THREE.SpriteMaterial).needsUpdate = true;
  }

  /**
   * 销毁调试器
   */
  public dispose(): void {
    // 移除性能统计面板
    if (this.statsPanel) {
      document.body.removeChild(this.statsPanel);
      this.statsPanel = null;
    }

    // 清理所有向量和网格
    this.velocityArrows.clear();
    this.forceArrows.clear();
    this.centerOfMassMeshes.clear();
    this.sleepStateMeshes.clear();
    this.contactNormalArrows = [];
    this.contactForceArrows = [];
    this.frictionForceArrows = [];

    // 调用父类的销毁方法
    super.dispose();
  }
}
