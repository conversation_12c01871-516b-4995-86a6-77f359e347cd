import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import * as compression from 'compression';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { AppModule } from './app.module';
import { LoggerService } from './common/services/logger.service';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // 获取配置服务
  const configService = app.get(ConfigService);
  const logger = app.get(LoggerService);
  
  // 全局中间件
  app.use(helmet());
  app.use(compression());
  
  // 限流配置
  app.use(
    rateLimit({
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 1000, // 限制每个IP 15分钟内最多1000个请求
      message: '请求过于频繁，请稍后再试',
    }),
  );
  
  // 全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );
  
  // CORS配置
  app.enableCors({
    origin: configService.get('CORS_ORIGIN', '*'),
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization'],
  });
  
  // Swagger文档配置
  const config = new DocumentBuilder()
    .setTitle('场景模板微服务 API')
    .setDescription('预定义场景模板系统、模板参数化和定制、模板分享和评价、模板版本管理服务')
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('templates', '场景模板管理')
    .addTag('categories', '模板分类')
    .addTag('parameters', '模板参数')
    .addTag('versions', '模板版本')
    .addTag('sharing', '模板分享')
    .addTag('ratings', '模板评价')
    .addTag('marketplace', '模板市场')
    .build();
    
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);
  
  // 启动服务
  const port = configService.get('SCENE_TEMPLATE_SERVICE_PORT', 8004);
  const host = configService.get('SCENE_TEMPLATE_SERVICE_HOST', '0.0.0.0');
  await app.listen(port, host);
  
  logger.log(`场景模板微服务启动成功，端口: ${port}`);
  logger.log(`Swagger文档地址: http://localhost:${port}/api/docs`);
}

bootstrap().catch((error) => {
  console.error('服务启动失败:', error);
  process.exit(1);
});
