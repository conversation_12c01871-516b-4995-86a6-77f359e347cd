import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  ManyToMany,
  JoinTable,
  Index,
} from 'typeorm';
import { TemplateCategory } from '../../categories/entities/template-category.entity';
import { TemplateParameter } from '../../parameters/entities/template-parameter.entity';
import { TemplateVersion } from '../../versions/entities/template-version.entity';
import { TemplateShare } from '../../sharing/entities/template-share.entity';
import { TemplateRating } from '../../ratings/entities/template-rating.entity';
import { User } from '../../auth/entities/user.entity';

export enum TemplateType {
  INDOOR = 'indoor',
  OUTDOOR = 'outdoor',
  OFFICE = 'office',
  RESIDENTIAL = 'residential',
  COMMERCIAL = 'commercial',
  INDUSTRIAL = 'industrial',
  EDUCATIONAL = 'educational',
  ENTERTAINMENT = 'entertainment',
  MEDICAL = 'medical',
  CUSTOM = 'custom',
}

export enum TemplateStatus {
  DRAFT = 'draft',
  PENDING_REVIEW = 'pending_review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
}

export enum TemplateComplexity {
  SIMPLE = 'simple',
  MEDIUM = 'medium',
  COMPLEX = 'complex',
  ADVANCED = 'advanced',
}

export enum TemplateLicense {
  FREE = 'free',
  PREMIUM = 'premium',
  COMMERCIAL = 'commercial',
  RESTRICTED = 'restricted',
}

@Entity('scene_templates')
@Index(['name', 'type'])
@Index(['status', 'createdAt'])
@Index(['category', 'type'])
@Index(['isPublic', 'status'])
export class SceneTemplate {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  @Index()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'text', nullable: true })
  instructions: string; // 使用说明

  @Column({
    type: 'enum',
    enum: TemplateType,
    default: TemplateType.CUSTOM,
  })
  @Index()
  type: TemplateType;

  @Column({
    type: 'enum',
    enum: TemplateStatus,
    default: TemplateStatus.DRAFT,
  })
  @Index()
  status: TemplateStatus;

  @Column({
    type: 'enum',
    enum: TemplateComplexity,
    default: TemplateComplexity.MEDIUM,
  })
  complexity: TemplateComplexity;

  @Column({
    type: 'enum',
    enum: TemplateLicense,
    default: TemplateLicense.FREE,
  })
  license: TemplateLicense;

  // 模板数据
  @Column({ type: 'json' })
  sceneData: Record<string, any>; // 场景配置数据

  @Column({ type: 'json', nullable: true })
  defaultParameters: Record<string, any>; // 默认参数值

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>; // 元数据

  // 预览信息
  @Column({ name: 'thumbnail_url', nullable: true })
  thumbnailUrl: string;

  @Column({ name: 'preview_images', type: 'json', nullable: true })
  previewImages: string[]; // 预览图片URL数组

  @Column({ name: 'demo_video_url', nullable: true })
  demoVideoUrl: string;

  // 可见性和分享
  @Column({ name: 'is_public', default: false })
  @Index()
  isPublic: boolean;

  @Column({ name: 'is_featured', default: false })
  isFeatured: boolean;

  @Column({ name: 'is_template_of_day', default: false })
  isTemplateOfDay: boolean;

  // 统计信息
  @Column({ name: 'download_count', default: 0 })
  downloadCount: number;

  @Column({ name: 'view_count', default: 0 })
  viewCount: number;

  @Column({ name: 'use_count', default: 0 })
  useCount: number; // 使用次数

  @Column({ name: 'like_count', default: 0 })
  likeCount: number;

  @Column({ name: 'share_count', default: 0 })
  shareCount: number;

  @Column({ type: 'decimal', precision: 3, scale: 2, default: 0 })
  rating: number;

  @Column({ name: 'rating_count', default: 0 })
  ratingCount: number;

  // 版本信息
  @Column({ name: 'current_version', default: '1.0.0' })
  currentVersion: string;

  @Column({ name: 'is_latest_version', default: true })
  isLatestVersion: boolean;

  // 标签
  @Column({ type: 'text', array: true, default: [] })
  tags: string[];

  // 关联关系
  @ManyToOne(() => TemplateCategory, category => category.templates, { eager: true })
  category: TemplateCategory;

  @OneToMany(() => TemplateParameter, parameter => parameter.template, { cascade: true })
  parameters: TemplateParameter[];

  @OneToMany(() => TemplateVersion, version => version.template)
  versions: TemplateVersion[];

  @OneToMany(() => TemplateShare, share => share.template)
  shares: TemplateShare[];

  @OneToMany(() => TemplateRating, rating => rating.template)
  ratings: TemplateRating[];

  @ManyToOne(() => User, user => user.templates)
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  reviewer: User;

  // 依赖模板（基于其他模板创建）
  @ManyToOne(() => SceneTemplate, { nullable: true })
  baseTemplate: SceneTemplate;

  @OneToMany(() => SceneTemplate, template => template.baseTemplate)
  derivedTemplates: SceneTemplate[];

  // 时间戳
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'published_at', nullable: true })
  publishedAt: Date;

  @Column({ name: 'last_used_at', nullable: true })
  lastUsedAt: Date;

  // 软删除
  @Column({ name: 'deleted_at', nullable: true })
  deletedAt: Date;

  // 计算属性
  get isPublished(): boolean {
    return this.status === TemplateStatus.PUBLISHED && this.publishedAt !== null;
  }

  get isPopular(): boolean {
    return this.downloadCount > 100 || this.rating > 4.0;
  }

  get complexityScore(): number {
    const scores = {
      [TemplateComplexity.SIMPLE]: 1,
      [TemplateComplexity.MEDIUM]: 2,
      [TemplateComplexity.COMPLEX]: 3,
      [TemplateComplexity.ADVANCED]: 4,
    };
    return scores[this.complexity] || 2;
  }

  get engagementScore(): number {
    // 计算参与度分数
    return (
      this.downloadCount * 0.3 +
      this.viewCount * 0.1 +
      this.useCount * 0.4 +
      this.likeCount * 0.2 +
      this.rating * this.ratingCount * 0.5
    );
  }

  get hasParameters(): boolean {
    return this.parameters && this.parameters.length > 0;
  }

  get isCustomizable(): boolean {
    return this.hasParameters;
  }
}
