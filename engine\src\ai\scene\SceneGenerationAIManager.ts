/**
 * 场景生成AI模型管理器
 * 整合场景理解、布局生成、资产匹配等AI模型，提供统一的场景生成接口
 */
import { AIModelManager, AIModelManagerConfig } from '../AIModelManager';
import { type World  } from '../../core/World';
import { SceneUnderstandingModel } from './SceneUnderstandingModel';
import { LayoutGenerationModel } from './LayoutGenerationModel';
import { AssetMatchingModel } from './AssetMatchingModel';
import {
  SceneGenerationOptions,
  SceneGenerationResult,
  SceneUnderstanding,
  SceneLayout,
  AssetMatchResult
} from './SceneGenerationTypes';

/**
 * 场景生成AI管理器配置
 */
export interface SceneGenerationAIManagerConfig extends AIModelManagerConfig {
  /** 场景理解模型配置 */
  sceneUnderstanding?: any;
  /** 布局生成模型配置 */
  layoutGeneration?: any;
  /** 资产匹配模型配置 */
  assetMatching?: any;
  /** 是否启用实时生成 */
  enableRealTimeGeneration?: boolean;
  /** 生成质量级别 */
  qualityLevel?: 'fast' | 'balanced' | 'high';
}

/**
 * 场景生成AI模型管理器
 */
export class SceneGenerationAIManager extends AIModelManager {
  private sceneUnderstandingModel: SceneUnderstandingModel;
  private layoutGenerationModel: LayoutGenerationModel;
  private assetMatchingModel: AssetMatchingModel;
  private sceneGenerationConfig: SceneGenerationAIManagerConfig;
  private initialized: boolean = false;

  constructor(world: World, config: SceneGenerationAIManagerConfig = {}) {
    super(world, config);
    
    this.sceneGenerationConfig = {
      enableRealTimeGeneration: true,
      qualityLevel: 'balanced',
      ...config
    };

    this.initializeSceneModels();
  }

  /**
   * 初始化场景生成相关模型
   */
  private async initializeSceneModels(): Promise<void> {
    try {
      // 场景理解模型
      this.sceneUnderstandingModel = new SceneUnderstandingModel(
        {
          modelType: 'transformer',
          language: 'zh-CN',
          domain: 'scene_description',
          ...this.sceneGenerationConfig.sceneUnderstanding
        },
        this.config
      );

      // 布局生成模型
      this.layoutGenerationModel = new LayoutGenerationModel(
        {
          algorithm: 'constraint_satisfaction',
          optimization: 'genetic_algorithm',
          ...this.sceneGenerationConfig.layoutGeneration
        },
        this.config
      );

      // 资产匹配模型
      this.assetMatchingModel = new AssetMatchingModel(
        {
          embeddingModel: 'sentence-transformers',
          similarityThreshold: 0.7,
          ...this.sceneGenerationConfig.assetMatching
        },
        this.config
      );

      // 初始化所有模型
      await Promise.all([
        this.sceneUnderstandingModel.initialize(),
        this.layoutGenerationModel.initialize(),
        this.assetMatchingModel.initialize()
      ]);

      this.initialized = true;

      if (this.config.debug) {
        console.log('场景生成AI模型管理器初始化成功');
      }
    } catch (error) {
      console.error('场景生成AI模型管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 生成场景
   */
  async generateScene(
    description: string,
    options: SceneGenerationOptions = {}
  ): Promise<SceneGenerationResult> {
    const startTime = Date.now();

    try {
      if (this.config.debug) {
        console.log(`开始生成场景: ${description}`);
      }

      // 1. 理解场景描述
      const understanding = await this.understandScene(description);
      
      if (this.config.debug) {
        console.log('场景理解完成:', understanding);
      }

      // 2. 生成布局
      const layout = await this.generateLayout(understanding);
      
      if (this.config.debug) {
        console.log('布局生成完成:', layout);
      }

      // 3. 匹配资产
      const assets = await this.matchAssets(understanding, layout);
      
      if (this.config.debug) {
        console.log('资产匹配完成:', assets);
      }

      // 4. 构建场景
      const scene = await this.buildScene(layout, assets, options);
      
      // 5. 计算总体置信度
      const confidence = this.calculateOverallConfidence(understanding, layout, assets);

      const generationTime = Date.now() - startTime;

      const result: SceneGenerationResult = {
        scene,
        understanding,
        layout,
        assets,
        confidence,
        generationTime
      };

      if (this.config.debug) {
        console.log(`场景生成完成，耗时: ${generationTime}ms`);
      }

      return result;
    } catch (error) {
      console.error('场景生成失败:', error);
      throw error;
    }
  }

  /**
   * 理解场景描述
   */
  private async understandScene(description: string): Promise<SceneUnderstanding> {
    return await this.sceneUnderstandingModel.understand(description);
  }

  /**
   * 生成布局
   */
  private async generateLayout(understanding: SceneUnderstanding): Promise<SceneLayout> {
    return await this.layoutGenerationModel.generateLayout(understanding);
  }

  /**
   * 匹配资产
   */
  private async matchAssets(
    understanding: SceneUnderstanding,
    layout: SceneLayout
  ): Promise<AssetMatchResult[]> {
    return await this.assetMatchingModel.matchAssets(understanding, layout);
  }

  /**
   * 构建场景
   */
  private async buildScene(
    layout: SceneLayout,
    assets: AssetMatchResult[],
    options: SceneGenerationOptions
  ): Promise<any> {
    // 这里应该调用实际的场景构建器
    // 目前返回一个简化的场景对象
    return {
      id: `scene_${Date.now()}`,
      name: 'Generated Scene',
      layout,
      assets,
      options,
      metadata: {
        generatedAt: new Date().toISOString(),
        generator: 'SceneGenerationAIManager'
      }
    };
  }

  /**
   * 计算总体置信度
   */
  private calculateOverallConfidence(
    understanding: SceneUnderstanding,
    layout: SceneLayout,
    assets: AssetMatchResult[]
  ): number {
    // 理解置信度权重: 30%
    const understandingWeight = 0.3;
    const understandingConfidence = understanding.confidence;

    // 布局质量权重: 40%
    const layoutWeight = 0.4;
    const layoutConfidence = layout.score || 0.5;

    // 资产匹配置信度权重: 30%
    const assetWeight = 0.3;
    const assetConfidence = assets.length > 0 
      ? assets.reduce((sum, asset) => sum + asset.confidence, 0) / assets.length
      : 0.3;

    const overallConfidence = 
      understandingConfidence * understandingWeight +
      layoutConfidence * layoutWeight +
      assetConfidence * assetWeight;

    return Math.min(overallConfidence, 1.0);
  }

  /**
   * 实时生成场景（流式）
   */
  async *generateSceneStream(
    description: string,
    options: SceneGenerationOptions = {}
  ): AsyncGenerator<Partial<SceneGenerationResult>, SceneGenerationResult, unknown> {
    if (!this.sceneGenerationConfig.enableRealTimeGeneration) {
      throw new Error('实时生成未启用');
    }

    try {
      // 1. 理解场景描述
      yield { understanding: undefined };
      const understanding = await this.understandScene(description);
      yield { understanding };

      // 2. 生成布局
      yield { understanding, layout: undefined };
      const layout = await this.generateLayout(understanding);
      yield { understanding, layout };

      // 3. 匹配资产
      yield { understanding, layout, assets: [] };
      const assets = await this.matchAssets(understanding, layout);
      yield { understanding, layout, assets };

      // 4. 构建场景
      const scene = await this.buildScene(layout, assets, options);
      const confidence = this.calculateOverallConfidence(understanding, layout, assets);

      const finalResult: SceneGenerationResult = {
        scene,
        understanding,
        layout,
        assets,
        confidence,
        generationTime: Date.now()
      };

      return finalResult;
    } catch (error) {
      console.error('流式场景生成失败:', error);
      throw error;
    }
  }

  /**
   * 优化现有场景
   */
  async optimizeScene(
    currentScene: any,
    feedback: string,
    options: SceneGenerationOptions = {}
  ): Promise<SceneGenerationResult> {
    try {
      // 分析反馈
      const feedbackUnderstanding = await this.understandScene(feedback);
      
      // 基于反馈调整场景
      // 这里应该实现场景优化逻辑
      
      // 目前返回原场景（简化实现）
      return {
        scene: currentScene,
        understanding: feedbackUnderstanding,
        layout: currentScene.layout,
        assets: currentScene.assets,
        confidence: 0.8
      };
    } catch (error) {
      console.error('场景优化失败:', error);
      throw error;
    }
  }

  /**
   * 获取场景生成建议
   */
  async getGenerationSuggestions(partialDescription: string): Promise<string[]> {
    try {
      // 基于部分描述生成建议
      const suggestions: string[] = [];
      
      // 简化的建议生成逻辑
      if (partialDescription.includes('办公')) {
        suggestions.push('添加办公桌和椅子');
        suggestions.push('设置现代风格');
        suggestions.push('添加电脑和文件柜');
      }
      
      if (partialDescription.includes('客厅')) {
        suggestions.push('添加沙发和茶几');
        suggestions.push('设置温馨的灯光');
        suggestions.push('添加电视和装饰品');
      }
      
      return suggestions;
    } catch (error) {
      console.error('获取生成建议失败:', error);
      return [];
    }
  }

  /**
   * 验证场景描述
   */
  async validateDescription(description: string): Promise<{
    isValid: boolean;
    issues: string[];
    suggestions: string[];
  }> {
    const issues: string[] = [];
    const suggestions: string[] = [];

    // 基本验证
    if (!description || description.trim().length === 0) {
      issues.push('场景描述不能为空');
      suggestions.push('请提供详细的场景描述');
    }

    if (description.length < 10) {
      issues.push('场景描述过于简短');
      suggestions.push('请提供更详细的描述，包括对象、布局和风格信息');
    }

    // 检查是否包含基本元素
    const hasObjects = /桌子|椅子|沙发|床|柜子|灯/.test(description);
    if (!hasObjects) {
      suggestions.push('建议添加具体的家具或对象描述');
    }

    const hasStyle = /现代|古典|简约|豪华|工业|田园/.test(description);
    if (!hasStyle) {
      suggestions.push('建议指定场景风格，如现代、古典等');
    }

    return {
      isValid: issues.length === 0,
      issues,
      suggestions
    };
  }

  /**
   * 获取支持的场景类型
   */
  getSupportedSceneTypes(): string[] {
    return [
      'office',      // 办公室
      'living_room', // 客厅
      'bedroom',     // 卧室
      'kitchen',     // 厨房
      'classroom',   // 教室
      'meeting_room',// 会议室
      'restaurant',  // 餐厅
      'library',     // 图书馆
      'gallery',     // 画廊
      'workshop'     // 工作室
    ];
  }

  /**
   * 获取支持的风格
   */
  getSupportedStyles(): string[] {
    return [
      'modern',      // 现代
      'classical',   // 古典
      'minimalist',  // 简约
      'industrial',  // 工业
      'scandinavian',// 北欧
      'traditional', // 传统
      'contemporary',// 当代
      'rustic',      // 乡村
      'luxury',      // 豪华
      'vintage'      // 复古
    ];
  }

  /**
   * 销毁管理器
   */
  async destroy(): Promise<void> {
    try {
      await Promise.all([
        this.sceneUnderstandingModel?.destroy(),
        this.layoutGenerationModel?.destroy(),
        this.assetMatchingModel?.destroy()
      ]);
      

      
      if (this.config.debug) {
        console.log('场景生成AI模型管理器已销毁');
      }
    } catch (error) {
      console.error('销毁场景生成AI模型管理器失败:', error);
    }
  }

  /**
   * 获取管理器状态
   */
  getStatus(): {
    initialized: boolean;
    models: {
      sceneUnderstanding: boolean;
      layoutGeneration: boolean;
      assetMatching: boolean;
    };
    config: SceneGenerationAIManagerConfig;
  } {
    return {
      initialized: this.initialized,
      models: {
        sceneUnderstanding: !!this.sceneUnderstandingModel,
        layoutGeneration: !!this.layoutGenerationModel,
        assetMatching: !!this.assetMatchingModel
      },
      config: this.sceneGenerationConfig
    };
  }
}
