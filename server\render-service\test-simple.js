// 简单的模块测试脚本
console.log('正在测试渲染服务模块...');

try {
  // 测试应用模块
  const { AppModule } = require('./dist/app.module');
  console.log('✅ 应用模块加载成功');

  // 测试渲染模块
  const { RenderModule } = require('./dist/render/render.module');
  console.log('✅ 渲染模块加载成功');

  // 测试渲染控制器
  const { RenderController } = require('./dist/render/render.controller');
  console.log('✅ 渲染控制器加载成功');

  // 测试渲染服务
  const { RenderService } = require('./dist/render/render.service');
  console.log('✅ 渲染服务加载成功');

  // 测试渲染处理器
  const { RenderProcessor } = require('./dist/render/render.processor');
  console.log('✅ 渲染处理器加载成功');

  // 测试实体
  const { RenderJob } = require('./dist/render/entities/render-job.entity');
  const { RenderResult } = require('./dist/render/entities/render-result.entity');
  console.log('✅ 实体类加载成功');

  // 测试DTO
  const { CreateRenderJobDto } = require('./dist/render/dto/create-render-job.dto');
  console.log('✅ DTO类加载成功');

  // 测试健康检查
  const { HealthModule } = require('./dist/health/health.module');
  const { HealthController } = require('./dist/health/health.controller');
  console.log('✅ 健康检查模块加载成功');

  // 测试认证守卫
  const { JwtAuthGuard } = require('./dist/auth/guards/jwt-auth.guard');
  console.log('✅ JWT认证守卫加载成功');

  console.log('\n🎉 所有核心模块加载成功！');
  console.log('\n修复总结:');
  console.log('1. ✅ 修复了Docker Compose配置中的端口映射');
  console.log('2. ✅ 修复了PowerShell脚本中的服务地址');
  console.log('3. ✅ 修复了CORS配置');
  console.log('4. ✅ 修复了环境变量配置');
  console.log('5. ✅ 修复了PowerShell脚本中的目录切换问题');
  console.log('6. ✅ 添加了完整的环境变量示例文件');
  console.log('7. ✅ 所有TypeScript编译错误已修复');

} catch (error) {
  console.error('❌ 模块测试失败:', error.message);
  console.error('详细错误:', error.stack);
  process.exit(1);
}
