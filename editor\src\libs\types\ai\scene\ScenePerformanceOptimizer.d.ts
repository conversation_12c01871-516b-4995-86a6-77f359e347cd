/**
 * 场景性能优化器
 * 优化场景生成和渲染性能，包括LOD、几何体合并、纹理优化等
 */
import * as THREE from 'three';
import { Scene } from '../../scene/Scene';
import { type Entity } from '../../core/Entity';
/**
 * 性能配置
 */
export interface PerformanceConfig {
    /** 目标FPS */
    targetFPS: number;
    /** 最大多边形数 */
    maxPolygons: number;
    /** 最大纹理尺寸 */
    maxTextureSize: number;
    /** 启用LOD */
    enableLOD: boolean;
    /** 启用几何体合并 */
    enableGeometryMerging: boolean;
    /** 启用纹理压缩 */
    enableTextureCompression: boolean;
    /** 启用实例化渲染 */
    enableInstancing: boolean;
}
/**
 * 性能统计
 */
export interface PerformanceStats {
    /** 当前FPS */
    currentFPS: number;
    /** 多边形数量 */
    polygonCount: number;
    /** 纹理内存使用 */
    textureMemory: number;
    /** 绘制调用次数 */
    drawCalls: number;
    /** 优化建议 */
    suggestions: string[];
}
/**
 * LOD管理器
 */
export declare class LODManager {
    private lodLevels;
    private distanceThresholds;
    /**
     * 创建LOD对象
     */
    createLOD(entity: Entity, meshes: THREE.Mesh[]): THREE.LOD;
    /**
     * 更新LOD
     */
    updateLOD(camera: THREE.Camera): void;
    /**
     * 移除LOD
     */
    removeLOD(entityId: string): void;
}
/**
 * 几何体合并器
 */
export declare class GeometryMerger {
    private mergedGeometries;
    /**
     * 合并几何体
     */
    mergeGeometries(entities: Entity[]): THREE.BufferGeometry[];
    /**
     * 合并几何体组
     */
    private mergeGeometryGroup;
    /**
     * 获取材质键
     */
    private getMaterialKey;
}
/**
 * 纹理优化器
 */
export declare class TextureOptimizer {
    private maxTextureSize;
    private compressionFormat;
    constructor(maxSize?: number);
    /**
     * 优化纹理
     */
    optimizeTexture(texture: THREE.Texture): Promise<THREE.Texture>;
    /**
     * 调整图像尺寸
     */
    private resizeImage;
    /**
     * 获取最近的2的幂
     */
    private nearestPowerOfTwo;
    /**
     * 压缩纹理
     */
    compressTexture(texture: THREE.Texture): THREE.CompressedTexture | THREE.Texture;
}
/**
 * 实例化渲染器
 */
export declare class InstancedRenderer {
    private instancedMeshes;
    /**
     * 创建实例化网格
     */
    createInstancedMesh(geometry: THREE.BufferGeometry, material: THREE.Material, count: number, transforms: THREE.Matrix4[]): THREE.InstancedMesh;
    /**
     * 更新实例
     */
    updateInstance(key: string, index: number, matrix: THREE.Matrix4): void;
    /**
     * 移除实例化网格
     */
    removeInstancedMesh(key: string): void;
}
/**
 * 场景性能优化器
 */
export declare class ScenePerformanceOptimizer {
    private config;
    private lodManager;
    private geometryMerger;
    private textureOptimizer;
    private instancedRenderer;
    private performanceMonitor;
    constructor(config?: Partial<PerformanceConfig>);
    /**
     * 优化场景
     */
    optimizeScene(scene: Scene): Promise<void>;
    /**
     * 优化几何体
     */
    private optimizeGeometry;
    /**
     * 检查是否可合并
     */
    private isMergeable;
    /**
     * 优化纹理
     */
    private optimizeTextures;
    /**
     * 收集材质中的纹理
     */
    private collectTextures;
    /**
     * 设置LOD
     */
    private setupLOD;
    /**
     * 检查是否应该使用LOD
     */
    private shouldUseLOD;
    /**
     * 生成LOD网格
     */
    private generateLODMeshes;
    /**
     * 设置实例化渲染
     */
    private setupInstancing;
    /**
     * 创建实例化组
     */
    private createInstancedGroup;
    /**
     * 获取材质键
     */
    private getMaterialKey;
    /**
     * 清理资源
     */
    private cleanupResources;
    /**
     * 更新LOD
     */
    updateLOD(camera: THREE.Camera): void;
    /**
     * 获取性能统计
     */
    getPerformanceStats(): PerformanceStats;
    /**
     * 设置性能配置
     */
    setConfig(config: Partial<PerformanceConfig>): void;
}
/**
 * 性能监控器
 */
export declare class PerformanceMonitor {
    private frameCount;
    private lastTime;
    private fps;
    /**
     * 更新性能统计
     */
    update(): void;
    /**
     * 获取统计信息
     */
    getStats(): PerformanceStats;
    /**
     * 生成优化建议
     */
    private generateSuggestions;
}
