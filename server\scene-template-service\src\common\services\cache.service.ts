import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createClient, RedisClientType } from 'redis';
import { LoggerService } from './logger.service';

@Injectable()
export class CacheService implements OnModuleInit, OnModuleDestroy {
  private client: RedisClientType;
  private isConnected = false;

  constructor(
    private configService: ConfigService,
    private logger: LoggerService,
  ) {}

  async onModuleInit(): Promise<void> {
    await this.connect();
  }

  async onModuleDestroy(): Promise<void> {
    await this.disconnect();
  }

  private async connect(): Promise<void> {
    try {
      this.client = createClient({
        url: this.configService.get('REDIS_URL', 'redis://localhost:6379'),
        password: this.configService.get('REDIS_PASSWORD'),
        database: this.configService.get('REDIS_DB', 1), // 使用数据库1，避免与资源库服务冲突
        socket: {
          connectTimeout: 5000, // 5秒连接超时
        },
      });

      this.client.on('error', (error) => {
        this.logger.error('Redis连接错误', error.message, 'CacheService');
        this.isConnected = false;
      });

      this.client.on('connect', () => {
        this.logger.log('Redis连接成功', 'CacheService');
        this.isConnected = true;
      });

      this.client.on('disconnect', () => {
        this.logger.warn('Redis连接断开', 'CacheService');
        this.isConnected = false;
      });

      this.client.on('reconnecting', () => {
        this.logger.log('Redis重新连接中...', 'CacheService');
      });

      // 设置连接超时
      const connectPromise = this.client.connect();
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Redis连接超时')), 10000);
      });

      await Promise.race([connectPromise, timeoutPromise]);
    } catch (error) {
      this.logger.error('Redis初始化失败', error.message, 'CacheService');
      this.isConnected = false;
      // 不抛出错误，允许服务在没有Redis的情况下继续运行
    }
  }

  private async disconnect(): Promise<void> {
    if (this.client && this.isConnected) {
      await this.client.disconnect();
      this.isConnected = false;
    }
  }

  async set(key: string, value: any, ttl?: number): Promise<void> {
    if (!this.isConnected) {
      this.logger.warn('Redis未连接，跳过缓存设置', 'CacheService');
      return;
    }

    try {
      const serializedValue = JSON.stringify(value);
      if (ttl) {
        await this.client.setEx(key, ttl, serializedValue);
      } else {
        await this.client.set(key, serializedValue);
      }
    } catch (error) {
      this.logger.error('缓存设置失败', error.message, 'CacheService');
    }
  }

  async get<T = any>(key: string): Promise<T | null> {
    if (!this.isConnected) {
      return null;
    }

    try {
      const value = await this.client.get(key);
      if (value) {
        return JSON.parse(value);
      }
      return null;
    } catch (error) {
      this.logger.error('缓存获取失败', error.message, 'CacheService');
      return null;
    }
  }

  async del(key: string): Promise<void> {
    if (!this.isConnected) {
      return;
    }

    try {
      await this.client.del(key);
    } catch (error) {
      this.logger.error('缓存删除失败', error.message, 'CacheService');
    }
  }

  getConnectionStatus(): boolean {
    return this.isConnected;
  }
}
