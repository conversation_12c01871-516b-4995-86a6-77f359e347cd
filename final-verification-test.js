#!/usr/bin/env node

/**
 * 最终验证测试脚本
 * 综合验证所有错误修复和配置一致性
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 运行最终验证测试...\n');

// 测试1: 验证VIEWER错误修复
function testViewerErrorFix() {
  console.log('📋 测试1: 验证VIEWER错误修复');
  
  const permissionServicePath = path.join(__dirname, 'editor/src/services/PermissionService.ts');
  
  if (!fs.existsSync(permissionServicePath)) {
    console.log('❌ PermissionService.ts文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(permissionServicePath, 'utf8');
  
  // 检查CollaborationRole枚举定义
  const hasCollaborationRole = content.includes('export enum CollaborationRole {') && 
                               content.includes('VIEWER = \'viewer\'');
  
  if (hasCollaborationRole) {
    console.log('✅ VIEWER错误已修复');
    return true;
  } else {
    console.log('❌ VIEWER错误未修复');
    return false;
  }
}

// 测试2: 验证ENTITY_CONFLICT错误修复
function testEntityConflictFix() {
  console.log('📋 测试2: 验证ENTITY_CONFLICT错误修复');
  
  const conflictResolutionPath = path.join(__dirname, 'editor/src/services/ConflictResolutionService.ts');
  
  if (!fs.existsSync(conflictResolutionPath)) {
    console.log('❌ ConflictResolutionService.ts文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(conflictResolutionPath, 'utf8');
  
  // 检查ConflictType枚举定义
  const hasConflictType = content.includes('export enum ConflictType {') && 
                         content.includes('ENTITY_CONFLICT = \'entity_conflict\'');
  
  // 检查循环导入修复
  const hasCircularImportFix = content.includes('// import { aiConflictResolver } from \'./AIConflictResolver\'; // 移除循环导入') &&
                              content.includes('import(\'./AIConflictResolver\')');
  
  if (hasConflictType && hasCircularImportFix) {
    console.log('✅ ENTITY_CONFLICT错误已修复');
    return true;
  } else {
    console.log('❌ ENTITY_CONFLICT错误未修复');
    if (!hasConflictType) console.log('   - ConflictType枚举定义有问题');
    if (!hasCircularImportFix) console.log('   - 循环导入未修复');
    return false;
  }
}

// 测试3: 验证DISCONNECTED错误修复
function testDisconnectedErrorFix() {
  console.log('📋 测试3: 验证DISCONNECTED错误修复');
  
  const webSocketManagerPath = path.join(__dirname, 'editor/src/services/WebSocketConnectionManager.ts');
  
  if (!fs.existsSync(webSocketManagerPath)) {
    console.log('❌ WebSocketConnectionManager.ts文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(webSocketManagerPath, 'utf8');
  
  // 检查ConnectionStatus枚举定义
  const hasConnectionStatus = content.includes('export enum ConnectionStatus {') && 
                             content.includes('DISCONNECTED = \'disconnected\'');
  
  // 检查延迟导入
  const hasDynamicImport = content.includes('import(\'../store/collaboration/connectionSlice\')');
  
  if (hasConnectionStatus && hasDynamicImport) {
    console.log('✅ DISCONNECTED错误已修复');
    return true;
  } else {
    console.log('❌ DISCONNECTED错误未修复');
    if (!hasConnectionStatus) console.log('   - ConnectionStatus枚举定义有问题');
    if (!hasDynamicImport) console.log('   - 延迟导入未实现');
    return false;
  }
}

// 测试4: 验证构建成功
function testBuildSuccess() {
  console.log('📋 测试4: 验证构建成功');
  
  const distPath = path.join(__dirname, 'editor/dist');
  
  if (!fs.existsSync(distPath)) {
    console.log('❌ 构建产物不存在');
    return false;
  }
  
  const files = fs.readdirSync(distPath);
  const hasIndexHtml = files.includes('index.html');
  const hasAssets = files.some(file => file.startsWith('assets'));
  
  if (hasIndexHtml && hasAssets) {
    console.log('✅ 构建成功');
    console.log(`   - 构建文件数量: ${files.length}`);
    return true;
  } else {
    console.log('❌ 构建产物不完整');
    if (!hasIndexHtml) console.log('   - 缺少index.html');
    if (!hasAssets) console.log('   - 缺少assets目录');
    return false;
  }
}

// 测试5: 验证配置文件一致性
function testConfigConsistency() {
  console.log('📋 测试5: 验证配置文件一致性');
  
  const requiredFiles = [
    '.env',
    'docker-compose.windows.yml',
    'start-windows.ps1',
    'editor/Dockerfile',
    'editor/nginx.conf'
  ];
  
  let allExist = true;
  for (const file of requiredFiles) {
    const filePath = path.join(__dirname, file);
    if (!fs.existsSync(filePath)) {
      console.log(`   ❌ 缺少文件: ${file}`);
      allExist = false;
    }
  }
  
  if (allExist) {
    console.log('✅ 配置文件一致性验证通过');
    return true;
  } else {
    console.log('❌ 配置文件一致性验证失败');
    return false;
  }
}

// 测试6: 验证TypeScript编译
function testTypeScriptCompilation() {
  console.log('📋 测试6: 验证TypeScript编译');
  
  const tsConfigPath = path.join(__dirname, 'editor/tsconfig.json');
  
  if (!fs.existsSync(tsConfigPath)) {
    console.log('❌ tsconfig.json文件不存在');
    return false;
  }
  
  // 检查关键TypeScript文件是否存在
  const keyFiles = [
    'editor/src/services/PermissionService.ts',
    'editor/src/services/ConflictResolutionService.ts',
    'editor/src/services/WebSocketConnectionManager.ts',
    'editor/src/services/AIConflictResolver.ts'
  ];
  
  let allExist = true;
  for (const file of keyFiles) {
    const filePath = path.join(__dirname, file);
    if (!fs.existsSync(filePath)) {
      console.log(`   ❌ 缺少关键文件: ${file}`);
      allExist = false;
    }
  }
  
  if (allExist) {
    console.log('✅ TypeScript文件完整');
    return true;
  } else {
    console.log('❌ TypeScript文件不完整');
    return false;
  }
}

// 运行所有测试
function runAllTests() {
  const tests = [
    { name: 'VIEWER错误修复', test: testViewerErrorFix },
    { name: 'ENTITY_CONFLICT错误修复', test: testEntityConflictFix },
    { name: 'DISCONNECTED错误修复', test: testDisconnectedErrorFix },
    { name: '构建成功验证', test: testBuildSuccess },
    { name: '配置文件一致性', test: testConfigConsistency },
    { name: 'TypeScript编译验证', test: testTypeScriptCompilation }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const { name, test } of tests) {
    try {
      if (test()) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${name}测试执行失败: ${error.message}`);
      failed++;
    }
    console.log('');
  }
  
  console.log('📊 最终测试结果总结:');
  console.log(`✅ 通过: ${passed}/${tests.length}`);
  console.log(`❌ 失败: ${failed}/${tests.length}`);
  
  if (failed === 0) {
    console.log('\n🎉 所有错误修复验证通过！项目已准备就绪！');
    console.log('\n🚀 下一步操作建议:');
    console.log('   1. 运行 .\\start-windows.ps1 启动所有服务');
    console.log('   2. 等待所有服务启动完成（约2-3分钟）');
    console.log('   3. 访问 http://localhost:80 测试编辑器');
    console.log('   4. 检查浏览器控制台确认无错误');
    console.log('   5. 测试编辑器的基本功能');
    console.log('\n📋 修复总结:');
    console.log('   ✅ 修复了VIEWER属性未定义错误');
    console.log('   ✅ 修复了ENTITY_CONFLICT属性未定义错误');
    console.log('   ✅ 修复了DISCONNECTED属性未定义错误');
    console.log('   ✅ 解决了循环导入问题');
    console.log('   ✅ 验证了配置文件一致性');
    console.log('   ✅ 确认了构建成功');
  } else {
    console.log('\n⚠️  部分测试失败，请检查上述错误信息');
    console.log('   建议重新检查相关文件的修复情况');
  }
}

// 执行测试
runAllTests();
