import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from '../../../common/services/logger.service';
import { HttpClientService } from '../../../common/services/http-client.service';
import { CacheService } from '../../../common/services/cache.service';
import { TextAnalysisResult } from './text-analysis.service';
import { StylePreferences } from './layout-generation.service';

export interface AssetCriteria {
  category: string;
  style: string;
  tags: string[];
  dimensions?: {
    minWidth?: number;
    maxWidth?: number;
    minHeight?: number;
    maxHeight?: number;
    minDepth?: number;
    maxDepth?: number;
  };
  materials?: string[];
  colors?: string[];
  quality: 'low' | 'medium' | 'high';
  budget?: 'low' | 'medium' | 'high';
  performance?: 'low' | 'medium' | 'high';
}

export interface Asset {
  id: string;
  name: string;
  category: string;
  subcategory: string;
  description: string;
  tags: string[];
  style: string;
  dimensions: {
    width: number;
    height: number;
    depth: number;
  };
  materials: string[];
  colors: string[];
  textures: string[];
  fileInfo: {
    format: string;
    size: number;
    polygonCount: number;
    textureCount: number;
    animationCount: number;
  };
  quality: {
    level: 'low' | 'medium' | 'high';
    detailLevel: number;
    optimized: boolean;
  };
  metadata: {
    author: string;
    license: string;
    version: string;
    createdAt: string;
    updatedAt: string;
  };
  urls: {
    preview: string;
    thumbnail: string;
    download: string;
    documentation?: string;
  };
  rating: {
    average: number;
    count: number;
  };
  usage: {
    downloadCount: number;
    popularity: number;
  };
}

export interface AssetRecommendation {
  asset: Asset;
  score: number;
  reasons: string[];
  alternatives: Asset[];
  placement: {
    suggested: boolean;
    position?: { x: number; y: number; z: number };
    rotation?: { x: number; y: number; z: number };
    scale?: { x: number; y: number; z: number };
  };
}

export interface AssetSelectionResult {
  selectedAssets: Asset[];
  recommendations: AssetRecommendation[];
  statistics: {
    totalAssets: number;
    categoryCounts: Record<string, number>;
    totalPolygons: number;
    totalFileSize: number;
    estimatedLoadTime: number;
  };
  alternatives: {
    budget: Asset[];
    performance: Asset[];
    style: Asset[];
  };
  validation: {
    isValid: boolean;
    warnings: string[];
    errors: string[];
  };
}

@Injectable()
export class AssetSelectionService {
  private readonly assetCategories = new Map<string, string[]>();
  private readonly styleMapping = new Map<string, string[]>();
  private readonly qualityThresholds = new Map<string, number>();

  constructor(
    private readonly configService: ConfigService,
    private readonly logger: LoggerService,
    private readonly httpClientService: HttpClientService,
    private readonly cacheService: CacheService,
  ) {
    this.initializeAssetCategories();
    this.initializeStyleMapping();
    this.initializeQualityThresholds();
  }

  async selectAssets(
    criteria: AssetCriteria[],
    analysisResult?: TextAnalysisResult,
    stylePreferences?: StylePreferences
  ): Promise<AssetSelectionResult> {
    try {
      this.logger.log(`开始选择资产，条件数量: ${criteria.length}`);

      // 验证输入参数
      this.validateCriteria(criteria);

      // 获取可用资产库
      const availableAssets = await this.getAvailableAssets();

      // 为每个条件选择资产
      const selectionResults = await Promise.all(
        criteria.map(criterion => this.selectAssetsForCriteria(criterion, availableAssets, analysisResult, stylePreferences))
      );

      // 合并结果
      const mergedResult = this.mergeSelectionResults(selectionResults);

      // 去重和优化
      const optimizedResult = await this.optimizeAssetSelection(mergedResult, stylePreferences);

      // 生成推荐
      const recommendations = await this.generateRecommendations(optimizedResult, criteria, stylePreferences);

      // 计算统计信息
      const statistics = this.calculateStatistics(optimizedResult);

      // 生成替代方案
      const alternatives = await this.generateAlternatives(optimizedResult, criteria);

      // 验证结果
      const validation = this.validateSelection(optimizedResult, criteria);

      const result: AssetSelectionResult = {
        selectedAssets: optimizedResult,
        recommendations,
        statistics,
        alternatives,
        validation
      };

      this.logger.log(`资产选择完成，选中 ${result.selectedAssets.length} 个资产`);
      return result;

    } catch (error) {
      this.logger.error('资产选择失败:', error);
      throw new Error(`资产选择失败: ${error.message}`);
    }
  }

  /**
   * 从文本分析结果选择资产
   */
  async selectAssetsFromAnalysis(
    analysisResult: TextAnalysisResult,
    stylePreferences?: StylePreferences
  ): Promise<AssetSelectionResult> {
    try {
      // 将文本分析结果转换为资产选择条件
      const criteria = this.convertAnalysisToAssetCriteria(analysisResult);

      return await this.selectAssets(criteria, analysisResult, stylePreferences);

    } catch (error) {
      this.logger.error('从文本分析选择资产失败:', error);
      throw error;
    }
  }

  /**
   * 初始化资产分类
   */
  private initializeAssetCategories(): void {
    this.assetCategories.set('furniture', [
      '沙发', '椅子', '桌子', '床', '衣柜', '书架', '电视柜', '茶几', '餐桌', '办公桌'
    ]);

    this.assetCategories.set('lighting', [
      '吊灯', '台灯', '落地灯', '壁灯', '射灯', '筒灯', '装饰灯', '氛围灯'
    ]);

    this.assetCategories.set('decoration', [
      '装饰画', '花瓶', '雕塑', '摆件', '相框', '镜子', '时钟', '书籍'
    ]);

    this.assetCategories.set('plants', [
      '盆栽', '花卉', '绿植', '树木', '草坪', '花园', '景观植物'
    ]);

    this.assetCategories.set('electronics', [
      '电视', '电脑', '音响', '空调', '冰箱', '洗衣机', '微波炉', '咖啡机'
    ]);

    this.assetCategories.set('textiles', [
      '窗帘', '地毯', '抱枕', '床单', '毛毯', '桌布', '沙发套'
    ]);
  }

  /**
   * 初始化风格映射
   */
  private initializeStyleMapping(): void {
    this.styleMapping.set('modern', [
      '简约', '现代', '极简', '工业', '北欧', '当代'
    ]);

    this.styleMapping.set('classic', [
      '古典', '传统', '欧式', '美式', '中式', '复古'
    ]);

    this.styleMapping.set('luxury', [
      '豪华', '奢华', '高端', '精致', '优雅', '贵族'
    ]);

    this.styleMapping.set('natural', [
      '自然', '田园', '乡村', '原木', '生态', '环保'
    ]);
  }

  /**
   * 初始化质量阈值
   */
  private initializeQualityThresholds(): void {
    this.qualityThresholds.set('polygon_count_low', 1000);
    this.qualityThresholds.set('polygon_count_medium', 5000);
    this.qualityThresholds.set('polygon_count_high', 20000);

    this.qualityThresholds.set('file_size_low', 1024 * 1024); // 1MB
    this.qualityThresholds.set('file_size_medium', 5 * 1024 * 1024); // 5MB
    this.qualityThresholds.set('file_size_high', 20 * 1024 * 1024); // 20MB
  }

  /**
   * 验证选择条件
   */
  private validateCriteria(criteria: AssetCriteria[]): void {
    if (!criteria || criteria.length === 0) {
      throw new Error('资产选择条件不能为空');
    }

    criteria.forEach((criterion, index) => {
      if (!criterion.category) {
        throw new Error(`第 ${index + 1} 个条件缺少分类信息`);
      }
      if (!criterion.style) {
        throw new Error(`第 ${index + 1} 个条件缺少风格信息`);
      }
    });
  }

  /**
   * 获取可用资产
   */
  private async getAvailableAssets(): Promise<Asset[]> {
    try {
      // 尝试从缓存获取
      const cacheKey = 'available_assets';
      const cachedAssets = await this.cacheService.get<Asset[]>(cacheKey);

      if (cachedAssets) {
        this.logger.log('从缓存获取资产库');
        return cachedAssets;
      }

      // 从资产库服务获取
      const assetLibraryUrl = this.configService.get('ASSET_LIBRARY_SERVICE_URL');

      if (assetLibraryUrl) {
        try {
          const response = await this.httpClientService.get(`${assetLibraryUrl}/assets`, {
            timeout: 10000,
            params: {
              limit: 1000,
              status: 'active'
            }
          });

          if (response.data && (response.data as any).assets) {
            const assets = (response.data as any).assets;
            // 缓存结果
            await this.cacheService.set(cacheKey, assets, 3600); // 缓存1小时
            this.logger.log(`从资产库服务获取 ${assets.length} 个资产`);
            return assets;
          }
        } catch (error) {
          this.logger.warn('资产库服务调用失败，使用模拟数据:', error);
        }
      }

      // 返回模拟资产数据
      return this.getMockAssets();

    } catch (error) {
      this.logger.error('获取可用资产失败:', error);
      return this.getMockAssets();
    }
  }

  /**
   * 为单个条件选择资产
   */
  private async selectAssetsForCriteria(
    criterion: AssetCriteria,
    availableAssets: Asset[],
    analysisResult?: TextAnalysisResult,
    stylePreferences?: StylePreferences
  ): Promise<Asset[]> {
    try {
      // 按分类过滤
      let filteredAssets = availableAssets.filter(asset =>
        asset.category === criterion.category ||
        asset.subcategory === criterion.category
      );

      // 按风格过滤
      filteredAssets = this.filterByStyle(filteredAssets, criterion.style);

      // 按标签过滤
      if (criterion.tags && criterion.tags.length > 0) {
        filteredAssets = this.filterByTags(filteredAssets, criterion.tags);
      }

      // 按尺寸过滤
      if (criterion.dimensions) {
        filteredAssets = this.filterByDimensions(filteredAssets, criterion.dimensions);
      }

      // 按材质过滤
      if (criterion.materials && criterion.materials.length > 0) {
        filteredAssets = this.filterByMaterials(filteredAssets, criterion.materials);
      }

      // 按颜色过滤
      if (criterion.colors && criterion.colors.length > 0) {
        filteredAssets = this.filterByColors(filteredAssets, criterion.colors);
      }

      // 按质量过滤
      filteredAssets = this.filterByQuality(filteredAssets, criterion.quality);

      // 按性能要求过滤
      if (criterion.performance) {
        filteredAssets = this.filterByPerformance(filteredAssets, criterion.performance);
      }

      // 排序和选择最佳资产
      const scoredAssets = this.scoreAssets(filteredAssets, criterion, analysisResult, stylePreferences);

      // 选择前N个最佳资产
      const maxAssets = this.getMaxAssetsForCategory(criterion.category);
      return scoredAssets.slice(0, maxAssets);

    } catch (error) {
      this.logger.error('为条件选择资产失败:', error);
      return [];
    }
  }

  /**
   * 按风格过滤资产
   */
  private filterByStyle(assets: Asset[], style: string): Asset[] {
    const styleKeywords = this.getStyleKeywords(style);

    return assets.filter(asset => {
      return styleKeywords.some(keyword =>
        asset.style.toLowerCase().includes(keyword.toLowerCase()) ||
        asset.tags.some(tag => tag.toLowerCase().includes(keyword.toLowerCase()))
      );
    });
  }

  /**
   * 按标签过滤资产
   */
  private filterByTags(assets: Asset[], tags: string[]): Asset[] {
    return assets.filter(asset => {
      return tags.some(tag =>
        asset.tags.some(assetTag =>
          assetTag.toLowerCase().includes(tag.toLowerCase())
        )
      );
    });
  }

  /**
   * 按尺寸过滤资产
   */
  private filterByDimensions(assets: Asset[], dimensions: AssetCriteria['dimensions']): Asset[] {
    return assets.filter(asset => {
      const { width, height, depth } = asset.dimensions;

      if (dimensions.minWidth && width < dimensions.minWidth) return false;
      if (dimensions.maxWidth && width > dimensions.maxWidth) return false;
      if (dimensions.minHeight && height < dimensions.minHeight) return false;
      if (dimensions.maxHeight && height > dimensions.maxHeight) return false;
      if (dimensions.minDepth && depth < dimensions.minDepth) return false;
      if (dimensions.maxDepth && depth > dimensions.maxDepth) return false;

      return true;
    });
  }

  /**
   * 按材质过滤资产
   */
  private filterByMaterials(assets: Asset[], materials: string[]): Asset[] {
    return assets.filter(asset => {
      return materials.some(material =>
        asset.materials.some(assetMaterial =>
          assetMaterial.toLowerCase().includes(material.toLowerCase())
        )
      );
    });
  }

  /**
   * 按颜色过滤资产
   */
  private filterByColors(assets: Asset[], colors: string[]): Asset[] {
    return assets.filter(asset => {
      return colors.some(color =>
        asset.colors.some(assetColor =>
          assetColor.toLowerCase().includes(color.toLowerCase())
        )
      );
    });
  }

  /**
   * 按质量过滤资产
   */
  private filterByQuality(assets: Asset[], quality: 'low' | 'medium' | 'high'): Asset[] {
    return assets.filter(asset => {
      const qualityLevels = ['low', 'medium', 'high'];
      const requiredLevel = qualityLevels.indexOf(quality);
      const assetLevel = qualityLevels.indexOf(asset.quality.level);

      return assetLevel >= requiredLevel;
    });
  }

  /**
   * 按性能要求过滤资产
   */
  private filterByPerformance(assets: Asset[], performance: 'low' | 'medium' | 'high'): Asset[] {
    const thresholds = {
      low: { polygons: 20000, fileSize: 20 * 1024 * 1024 },
      medium: { polygons: 5000, fileSize: 5 * 1024 * 1024 },
      high: { polygons: 1000, fileSize: 1024 * 1024 }
    };

    const threshold = thresholds[performance];

    return assets.filter(asset => {
      return asset.fileInfo.polygonCount <= threshold.polygons &&
             asset.fileInfo.size <= threshold.fileSize;
    });
  }

  /**
   * 获取风格关键词
   */
  private getStyleKeywords(style: string): string[] {
    for (const [key, keywords] of this.styleMapping.entries()) {
      if (keywords.some(keyword => style.toLowerCase().includes(keyword.toLowerCase()))) {
        return keywords;
      }
    }
    return [style];
  }

  /**
   * 获取分类的最大资产数量
   */
  private getMaxAssetsForCategory(category: string): number {
    const limits = {
      furniture: 5,
      lighting: 3,
      decoration: 8,
      plants: 4,
      electronics: 3,
      textiles: 6
    };

    return limits[category] || 3;
  }

  /**
   * 为资产评分
   */
  private scoreAssets(
    assets: Asset[],
    criterion: AssetCriteria,
    analysisResult?: TextAnalysisResult,
    stylePreferences?: StylePreferences
  ): Asset[] {
    return assets
      .map(asset => ({
        asset,
        score: this.calculateAssetScore(asset, criterion, analysisResult, stylePreferences)
      }))
      .sort((a, b) => b.score - a.score)
      .map(item => item.asset);
  }

  /**
   * 计算资产评分
   */
  private calculateAssetScore(
    asset: Asset,
    criterion: AssetCriteria,
    analysisResult?: TextAnalysisResult,
    stylePreferences?: StylePreferences
  ): number {
    let score = 0;

    // 基础评分
    score += asset.rating.average * 10;
    score += Math.log(asset.usage.downloadCount + 1) * 2;

    // 风格匹配评分
    if (this.isStyleMatch(asset, criterion.style)) {
      score += 20;
    }

    // 标签匹配评分
    const tagMatches = criterion.tags.filter(tag =>
      asset.tags.some(assetTag => assetTag.toLowerCase().includes(tag.toLowerCase()))
    ).length;
    score += tagMatches * 5;

    // 质量评分
    const qualityBonus = { low: 0, medium: 5, high: 10 };
    score += qualityBonus[asset.quality.level];

    // 优化评分
    if (asset.quality.optimized) {
      score += 5;
    }

    // 文本分析匹配评分
    if (analysisResult) {
      score += this.calculateAnalysisMatchScore(asset, analysisResult);
    }

    // 风格偏好匹配评分
    if (stylePreferences) {
      score += this.calculateStylePreferenceScore(asset, stylePreferences);
    }

    return score;
  }

  /**
   * 检查风格匹配
   */
  private isStyleMatch(asset: Asset, style: string): boolean {
    const styleKeywords = this.getStyleKeywords(style);
    return styleKeywords.some(keyword =>
      asset.style.toLowerCase().includes(keyword.toLowerCase()) ||
      asset.tags.some(tag => tag.toLowerCase().includes(keyword.toLowerCase()))
    );
  }

  /**
   * 计算文本分析匹配评分
   */
  private calculateAnalysisMatchScore(asset: Asset, analysis: TextAnalysisResult): number {
    let score = 0;

    // 关键词匹配
    const keywordMatches = analysis.keywords.filter(keyword =>
      asset.name.toLowerCase().includes(keyword.toLowerCase()) ||
      asset.tags.some(tag => tag.toLowerCase().includes(keyword.toLowerCase()))
    ).length;
    score += keywordMatches * 3;

    // 情感匹配
    if (analysis.sentiment === 'positive' && asset.rating.average > 4) {
      score += 5;
    }

    // 复杂度匹配
    const complexityBonus = {
      simple: asset.fileInfo.polygonCount < 1000 ? 5 : 0,
      medium: asset.fileInfo.polygonCount < 5000 ? 3 : 0,
      complex: 0
    };
    score += complexityBonus[analysis.complexity];

    return score;
  }

  /**
   * 计算风格偏好评分
   */
  private calculateStylePreferenceScore(asset: Asset, preferences: StylePreferences): number {
    let score = 0;

    // 颜色匹配
    const colorMatches = preferences.colorScheme.filter(color =>
      asset.colors.some(assetColor => assetColor.toLowerCase().includes(color.toLowerCase()))
    ).length;
    score += colorMatches * 2;

    // 材质匹配
    const materialMatches = preferences.materials.filter(material =>
      asset.materials.some(assetMaterial => assetMaterial.toLowerCase().includes(material.toLowerCase()))
    ).length;
    score += materialMatches * 3;

    return score;
  }

  /**
   * 将文本分析结果转换为资产选择条件
   */
  private convertAnalysisToAssetCriteria(analysis: TextAnalysisResult): AssetCriteria[] {
    const criteria: AssetCriteria[] = [];

    // 为每个识别的对象创建选择条件
    analysis.sceneElements.objects.forEach(object => {
      const category = this.mapObjectToCategory(object);
      if (category) {
        criteria.push({
          category,
          style: analysis.sceneElements.style,
          tags: [object, ...analysis.keywords],
          quality: analysis.complexity === 'simple' ? 'low' :
                  analysis.complexity === 'medium' ? 'medium' : 'high',
          budget: 'medium',
          performance: 'medium'
        });
      }
    });

    // 如果没有识别到具体对象，创建通用条件
    if (criteria.length === 0) {
      criteria.push({
        category: 'furniture',
        style: analysis.sceneElements.style,
        tags: analysis.keywords,
        quality: 'medium',
        budget: 'medium',
        performance: 'medium'
      });
    }

    return criteria;
  }

  /**
   * 将对象映射到分类
   */
  private mapObjectToCategory(object: string): string | null {
    for (const [category, objects] of this.assetCategories.entries()) {
      if (objects.some(obj => obj.includes(object) || object.includes(obj))) {
        return category;
      }
    }
    return null;
  }

  /**
   * 获取模拟资产数据
   */
  private getMockAssets(): Asset[] {
    return [
      {
        id: 'asset_001',
        name: '现代沙发',
        category: 'furniture',
        subcategory: '沙发',
        description: '现代简约风格三人沙发',
        tags: ['沙发', '现代', '简约', '客厅'],
        style: '现代',
        dimensions: { width: 2.2, height: 0.8, depth: 0.9 },
        materials: ['布料', '金属'],
        colors: ['灰色', '白色'],
        textures: ['布纹', '金属'],
        fileInfo: {
          format: 'glb',
          size: 2048000,
          polygonCount: 3500,
          textureCount: 4,
          animationCount: 0
        },
        quality: { level: 'medium', detailLevel: 7, optimized: true },
        metadata: {
          author: 'Designer A',
          license: 'CC BY 4.0',
          version: '1.0',
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01'
        },
        urls: {
          preview: '/assets/sofa_001/preview.jpg',
          thumbnail: '/assets/sofa_001/thumb.jpg',
          download: '/assets/sofa_001/model.glb'
        },
        rating: { average: 4.5, count: 120 },
        usage: { downloadCount: 1500, popularity: 0.8 }
      }
      // 可以添加更多模拟资产...
    ];
  }

  // 其他辅助方法的临时实现
  private mergeSelectionResults(results: Asset[][]): Asset[] {
    const merged = results.flat();
    // 去重
    const unique = merged.filter((asset, index, self) =>
      index === self.findIndex(a => a.id === asset.id)
    );
    return unique;
  }

  private async optimizeAssetSelection(assets: Asset[], stylePreferences?: StylePreferences): Promise<Asset[]> {
    // 临时实现
    return assets;
  }

  private async generateRecommendations(assets: Asset[], criteria: AssetCriteria[], stylePreferences?: StylePreferences): Promise<AssetRecommendation[]> {
    // 临时实现
    return [];
  }

  private calculateStatistics(assets: Asset[]): AssetSelectionResult['statistics'] {
    const totalPolygons = assets.reduce((sum, asset) => sum + asset.fileInfo.polygonCount, 0);
    const totalFileSize = assets.reduce((sum, asset) => sum + asset.fileInfo.size, 0);

    const categoryCounts: Record<string, number> = {};
    assets.forEach(asset => {
      categoryCounts[asset.category] = (categoryCounts[asset.category] || 0) + 1;
    });

    return {
      totalAssets: assets.length,
      categoryCounts,
      totalPolygons,
      totalFileSize,
      estimatedLoadTime: totalFileSize / (1024 * 1024) * 2 // 估算：每MB需要2秒
    };
  }

  private async generateAlternatives(assets: Asset[], criteria: AssetCriteria[]): Promise<AssetSelectionResult['alternatives']> {
    // 临时实现
    return {
      budget: [],
      performance: [],
      style: []
    };
  }

  private validateSelection(assets: Asset[], criteria: AssetCriteria[]): AssetSelectionResult['validation'] {
    const warnings: string[] = [];
    const errors: string[] = [];

    if (assets.length === 0) {
      errors.push('未选择任何资产');
    }

    if (assets.length > 50) {
      warnings.push('选择的资产数量较多，可能影响性能');
    }

    return {
      isValid: errors.length === 0,
      warnings,
      errors
    };
  }
}
