/**
 * 权限日志服务测试文件
 * 用于验证修复后的服务是否正常工作
 */
import { permissionLogService, PermissionLogType, UserRole } from './PermissionLogService';
import { Permission } from './PermissionService';
import { CollaborationRole } from './PermissionService';

// 测试权限日志服务
function testPermissionLogService() {
  console.log('开始测试权限日志服务...');
  
  try {
    // 测试服务实例
    console.log('✓ 成功获取权限日志服务实例');
    
    // 测试配置方法
    permissionLogService.setEnabled(true);
    permissionLogService.setMaxLogs(100);
    console.log('✓ 成功配置权限日志服务');
    
    // 清空现有日志
    permissionLogService.clearLogs();
    console.log('✓ 成功清空日志');
    
    // 测试角色变更日志
    permissionLogService.logRoleChanged('user1', 'user2', CollaborationRole.EDITOR, CollaborationRole.VIEWER);
    console.log('✓ 成功记录角色变更日志');
    
    // 测试权限授予日志
    permissionLogService.logPermissionGranted('user1', 'user2', Permission.EDIT_SCENE);
    console.log('✓ 成功记录权限授予日志');
    
    // 测试权限撤销日志
    permissionLogService.logPermissionRevoked('user1', 'user2', Permission.DELETE_ENTITY);
    console.log('✓ 成功记录权限撤销日志');
    
    // 测试角色权限变更日志
    const newPermissions = [Permission.VIEW_SCENE, Permission.EDIT_SCENE];
    const oldPermissions = [Permission.VIEW_SCENE];
    permissionLogService.logRolePermissionsChanged('user1', CollaborationRole.EDITOR, newPermissions, oldPermissions);
    console.log('✓ 成功记录角色权限变更日志');
    
    // 测试权限检查失败日志
    permissionLogService.logPermissionCheckFailed('user2', Permission.DELETE_PROJECT, { action: 'deleteProject' });
    console.log('✓ 成功记录权限检查失败日志');
    
    // 测试用户会话日志
    permissionLogService.logUserSessionCreated('user3', UserRole.USER);
    permissionLogService.logUserSessionUpdated('user3', { lastActivity: Date.now() });
    permissionLogService.logUserSessionRemoved('user3');
    console.log('✓ 成功记录用户会话日志');
    
    // 测试用户权限拒绝日志
    permissionLogService.logUserPermissionDenied('user1', 'user4', Permission.MANAGE_USERS);
    console.log('✓ 成功记录用户权限拒绝日志');
    
    // 测试组织节点日志
    permissionLogService.logOrganizationNodeAdded('admin1', 'node1', 'department', { name: '开发部' });
    permissionLogService.logOrganizationNodeUpdated('admin1', 'node1', { name: '研发部' });
    permissionLogService.logOrganizationNodeRemoved('admin1', 'node1', 'department');
    console.log('✓ 成功记录组织节点日志');
    
    // 测试组织权限日志
    permissionLogService.logOrganizationPermissionsEnabled('admin1');
    permissionLogService.logOrganizationPermissionsDisabled('admin1');
    console.log('✓ 成功记录组织权限日志');
    
    // 测试权限继承日志
    permissionLogService.logPermissionInheritanceChanged('admin1', 'strict', 'loose');
    console.log('✓ 成功记录权限继承日志');
    
    // 测试权限策略日志
    permissionLogService.logPermissionPolicyCreated('admin1', 'policy1', '开发者策略', { description: '开发者权限策略' });
    permissionLogService.logPermissionPolicyUpdated('admin1', 'policy1', { description: '更新的开发者权限策略' });
    permissionLogService.logPermissionPolicyApplied('admin1', 'policy1', 'user5', 'user');
    permissionLogService.logPermissionPolicyDeleted('admin1', 'policy1', '开发者策略');
    console.log('✓ 成功记录权限策略日志');
    
    // 测试日志查询功能
    const allLogs = permissionLogService.getLogs();
    console.log(`✓ 获取所有日志，共 ${allLogs.length} 条`);
    
    const userLogs = permissionLogService.getUserLogs('user1');
    console.log(`✓ 获取用户1的日志，共 ${userLogs.length} 条`);
    
    const roleChangeLogs = permissionLogService.getLogsByType(PermissionLogType.ROLE_CHANGED);
    console.log(`✓ 获取角色变更日志，共 ${roleChangeLogs.length} 条`);
    
    const now = Date.now();
    const timeRangeLogs = permissionLogService.getLogsByTimeRange(now - 60000, now);
    console.log(`✓ 获取时间范围内的日志，共 ${timeRangeLogs.length} 条`);
    
    // 测试禁用日志记录
    permissionLogService.setEnabled(false);
    const logCountBefore = permissionLogService.getLogs().length;
    permissionLogService.logRoleChanged('user1', 'user2', CollaborationRole.ADMIN);
    const logCountAfter = permissionLogService.getLogs().length;
    
    if (logCountBefore === logCountAfter) {
      console.log('✓ 禁用日志记录功能正常');
    } else {
      console.log('✗ 禁用日志记录功能异常');
    }
    
    // 恢复日志记录
    permissionLogService.setEnabled(true);
    
    // 测试最大日志数量限制
    permissionLogService.setMaxLogs(5);
    permissionLogService.clearLogs();
    
    // 添加超过最大数量的日志
    for (let i = 0; i < 10; i++) {
      permissionLogService.logRoleChanged(`user${i}`, `target${i}`, CollaborationRole.VIEWER);
    }
    
    const finalLogs = permissionLogService.getLogs();
    if (finalLogs.length === 5) {
      console.log('✓ 最大日志数量限制功能正常');
    } else {
      console.log(`✗ 最大日志数量限制功能异常，期望5条，实际${finalLogs.length}条`);
    }
    
    console.log('✅ 所有测试通过！权限日志服务修复成功！');
    
  } catch (error) {
    console.error('✗ 测试过程中发生错误:', error);
  }
}

// 导出测试函数
export { testPermissionLogService };

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 浏览器环境
  (window as any).testPermissionLogService = testPermissionLogService;
  console.log('测试函数已添加到 window.testPermissionLogService');
  console.log('可以在浏览器控制台中运行: testPermissionLogService()');
}
