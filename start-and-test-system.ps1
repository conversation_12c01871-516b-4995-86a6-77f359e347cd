# 系统启动和测试脚本
# 用于启动整个系统并进行基本的功能测试

param(
    [switch]$SkipBuild,
    [switch]$TestOnly,
    [switch]$Verbose
)

$ErrorActionPreference = "Stop"

function Write-Header {
    param([string]$Message)
    Write-Host "`n" -NoNewline
    Write-Host "=" * 60 -ForegroundColor Cyan
    Write-Host " $Message" -ForegroundColor Yellow
    Write-Host "=" * 60 -ForegroundColor Cyan
}

function Write-Step {
    param([string]$Message)
    Write-Host "`n🔄 $Message" -ForegroundColor Green
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Test-ServiceHealth {
    param(
        [string]$ServiceName,
        [string]$Url,
        [int]$MaxRetries = 30,
        [int]$DelaySeconds = 2
    )
    
    Write-Step "检查 $ServiceName 健康状态..."
    
    for ($i = 1; $i -le $MaxRetries; $i++) {
        try {
            $response = Invoke-WebRequest -Uri $Url -Method GET -TimeoutSec 5 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Success "$ServiceName 服务正常运行"
                return $true
            }
        }
        catch {
            if ($Verbose) {
                Write-Host "  尝试 $i/$MaxRetries 失败: $($_.Exception.Message)" -ForegroundColor Gray
            }
        }
        
        if ($i -lt $MaxRetries) {
            Start-Sleep -Seconds $DelaySeconds
        }
    }
    
    Write-Error "$ServiceName 服务健康检查失败"
    return $false
}

function Test-DatabaseConnection {
    Write-Step "检查数据库连接..."
    
    try {
        # 检查MySQL容器是否运行
        $mysqlContainer = docker ps --filter "name=dl-engine-mysql-win" --format "{{.Status}}"
        if (-not $mysqlContainer -or $mysqlContainer -notlike "*Up*") {
            Write-Error "MySQL容器未运行"
            return $false
        }
        
        # 测试数据库连接
        $testResult = docker exec dl-engine-mysql-win mysqladmin ping -h localhost -u root -pDLEngine2024!@# 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "数据库连接正常"
            return $true
        } else {
            Write-Error "数据库连接失败"
            return $false
        }
    }
    catch {
        Write-Error "数据库连接检查失败: $($_.Exception.Message)"
        return $false
    }
}

function Start-System {
    if ($TestOnly) {
        Write-Header "仅执行测试，跳过系统启动"
        return
    }
    
    Write-Header "启动系统"
    
    # 检查Docker是否运行
    Write-Step "检查Docker状态..."
    try {
        docker version | Out-Null
        Write-Success "Docker正在运行"
    }
    catch {
        Write-Error "Docker未运行，请启动Docker Desktop"
        exit 1
    }
    
    # 停止现有容器
    Write-Step "停止现有容器..."
    docker-compose -f docker-compose.windows.yml down 2>$null
    
    # 构建镜像（如果需要）
    if (-not $SkipBuild) {
        Write-Step "构建Docker镜像..."
        docker-compose -f docker-compose.windows.yml build --no-cache
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Docker镜像构建失败"
            exit 1
        }
        Write-Success "Docker镜像构建完成"
    }
    
    # 启动基础服务
    Write-Step "启动基础服务（MySQL, Redis, MinIO）..."
    docker-compose -f docker-compose.windows.yml up -d mysql redis minio
    
    # 等待基础服务启动
    Start-Sleep -Seconds 10
    
    # 启动向量数据库和搜索引擎
    Write-Step "启动向量数据库和搜索引擎..."
    docker-compose -f docker-compose.windows.yml up -d chroma elasticsearch
    
    # 等待数据库服务启动
    Start-Sleep -Seconds 15
    
    # 启动后端服务
    Write-Step "启动后端微服务..."
    docker-compose -f docker-compose.windows.yml up -d service-registry user-service project-service asset-service
    
    # 等待微服务启动
    Start-Sleep -Seconds 20
    
    # 启动API网关
    Write-Step "启动API网关..."
    docker-compose -f docker-compose.windows.yml up -d api-gateway
    
    # 等待API网关启动
    Start-Sleep -Seconds 10
    
    # 启动前端
    Write-Step "启动前端服务..."
    docker-compose -f docker-compose.windows.yml up -d editor
    
    Write-Success "系统启动完成"
}

function Test-System {
    Write-Header "系统功能测试"
    
    $allTestsPassed = $true
    
    # 测试数据库连接
    if (-not (Test-DatabaseConnection)) {
        $allTestsPassed = $false
    }
    
    # 测试基础服务
    $services = @(
        @{ Name = "Redis"; Url = "http://localhost:6379" },
        @{ Name = "MinIO"; Url = "http://localhost:9000/minio/health/live" },
        @{ Name = "API网关"; Url = "http://localhost:3000/api" },
        @{ Name = "前端"; Url = "http://localhost" }
    )
    
    foreach ($service in $services) {
        if (-not (Test-ServiceHealth -ServiceName $service.Name -Url $service.Url)) {
            $allTestsPassed = $false
        }
    }
    
    # 测试API端点
    Write-Step "测试API端点..."
    try {
        $healthResponse = Invoke-WebRequest -Uri "http://localhost:3000/api" -Method GET -TimeoutSec 10 -UseBasicParsing
        if ($healthResponse.StatusCode -eq 200) {
            Write-Success "API网关响应正常"
        } else {
            Write-Error "API网关响应异常"
            $allTestsPassed = $false
        }
    }
    catch {
        Write-Error "API网关测试失败: $($_.Exception.Message)"
        $allTestsPassed = $false
    }
    
    # 创建测试用户
    Write-Step "创建测试用户..."
    try {
        node create-test-user.js
        Write-Success "测试用户创建完成"
    }
    catch {
        Write-Warning "测试用户创建失败，可能已存在"
    }
    
    return $allTestsPassed
}

function Show-SystemStatus {
    Write-Header "系统状态总览"
    
    Write-Host "`n📊 容器状态:" -ForegroundColor Cyan
    docker-compose -f docker-compose.windows.yml ps
    
    Write-Host "`n🌐 访问地址:" -ForegroundColor Cyan
    Write-Host "  前端应用: http://localhost" -ForegroundColor White
    Write-Host "  API网关: http://localhost:3000/api" -ForegroundColor White
    Write-Host "  API文档: http://localhost:3000/api/docs" -ForegroundColor White
    Write-Host "  MinIO控制台: http://localhost:9001" -ForegroundColor White
    
    Write-Host "`n🔑 测试账号:" -ForegroundColor Cyan
    Write-Host "  普通用户: <EMAIL> / 123456" -ForegroundColor White
    Write-Host "  管理员: <EMAIL> / admin123" -ForegroundColor White
}

# 主执行流程
try {
    Write-Header "DL引擎系统启动和测试"
    
    # 启动系统
    Start-System
    
    # 等待系统完全启动
    if (-not $TestOnly) {
        Write-Step "等待系统完全启动..."
        Start-Sleep -Seconds 30
    }
    
    # 测试系统
    $testResult = Test-System
    
    # 显示系统状态
    Show-SystemStatus
    
    if ($testResult) {
        Write-Header "🎉 系统启动和测试完成！"
        Write-Host "`n✅ 所有测试通过，系统运行正常" -ForegroundColor Green
        Write-Host "🌐 请访问 http://localhost 开始使用" -ForegroundColor Yellow
    } else {
        Write-Header "⚠️  系统启动完成，但部分测试失败"
        Write-Host "`n请检查上述错误信息并进行相应修复" -ForegroundColor Yellow
    }
    
}
catch {
    Write-Error "脚本执行失败: $($_.Exception.Message)"
    exit 1
}
