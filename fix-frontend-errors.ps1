#!/usr/bin/env pwsh
# 前端错误修复脚本
# 修复图片中显示的React Context和数组方法错误

param(
    [switch]$Help,            # 显示帮助信息
    [switch]$CheckOnly,       # 仅检查问题，不修复
    [switch]$Verbose          # 详细输出
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput Cyan "ℹ️  $message"
}

function Write-Success($message) {
    Write-ColorOutput Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "❌ $message"
}

function Write-Header($message) {
    Write-Host ""
    Write-ColorOutput Magenta "🔧 $message"
    Write-Host "=" * 60
}

# 显示帮助信息
function Show-Help {
    Write-Host "前端错误修复脚本"
    Write-Host ""
    Write-Host "用法: .\fix-frontend-errors.ps1 [选项]"
    Write-Host ""
    Write-Host "选项:"
    Write-Host "  -CheckOnly      仅检查问题，不进行修复"
    Write-Host "  -Verbose        显示详细输出"
    Write-Host "  -Help           显示此帮助信息"
    Write-Host ""
    Write-Host "此脚本将修复以下问题:"
    Write-Host "  1. React useContext为null的错误"
    Write-Host "  2. 数组find/filter方法undefined错误"
    Write-Host "  3. TabPane组件弃用问题"
    Write-Host "  4. 前端构建和运行时错误"
}

# 检查并修复前端构建
function Repair-FrontendBuild {
    Write-Header "修复前端构建问题"

    $editorPath = "editor"
    if (-not (Test-Path $editorPath)) {
        Write-Error "editor目录不存在"
        return $false
    }

    Push-Location $editorPath

    try {
        Write-Info "清理构建缓存..."

        # 清理缓存目录
        $cachePaths = @("node_modules/.cache", "dist", ".vite", ".turbo")
        foreach ($path in $cachePaths) {
            if (Test-Path $path) {
                Remove-Item $path -Recurse -Force -ErrorAction SilentlyContinue
                Write-Info "已清理: $path"
            }
        }

        Write-Info "重新安装依赖..."

        # 删除package-lock.json和node_modules
        if (Test-Path "package-lock.json") {
            Remove-Item "package-lock.json" -Force
        }

        if (Test-Path "node_modules") {
            Remove-Item "node_modules" -Recurse -Force
        }

        # 重新安装依赖
        npm install

        Write-Info "构建项目..."
        npm run build

        Write-Success "前端构建修复完成"
        return $true

    } catch {
        Write-Error "前端构建修复失败: $($_.Exception.Message)"
        return $false
    } finally {
        Pop-Location
    }
}

# 检查React Context问题
function Check-ReactContextIssues {
    Write-Header "检查React Context问题"

    $issues = @()

    # 检查main.tsx中的Provider配置
    $mainTsxPath = "editor/src/main.tsx"
    if (Test-Path $mainTsxPath) {
        $content = Get-Content $mainTsxPath -Raw
        if ($content -match "React\.StrictMode") {
            $issues += "发现React.StrictMode，可能导致useContext问题"
        }

        if (-not ($content -match "Provider.*store")) {
            $issues += "Redux Provider配置可能有问题"
        }
    }

    # 检查是否有组件在Provider之外使用Context
    $contextFiles = @(
        "editor/src/components/git/GitPanel.tsx",
        "editor/src/components/git/GitStatusPanel.tsx",
        "editor/src/components/git/GitHistoryPanel.tsx",
        "editor/src/components/git/GitBranchPanel.tsx"
    )

    foreach ($file in $contextFiles) {
        if (Test-Path $file) {
            $content = Get-Content $file -Raw
            if ($content -match "useSelector|useDispatch" -and -not ($content -match "RootState")) {
                $issues += "文件 $file 可能有Context使用问题"
            }
        }
    }

    if ($issues.Count -eq 0) {
        Write-Success "未发现React Context问题"
    } else {
        foreach ($issue in $issues) {
            Write-Warning $issue
        }
    }

    return $issues.Count -eq 0
}

# 重启Docker服务
function Restart-DockerServices {
    Write-Header "重启Docker服务"
    
    try {
        Write-Info "停止相关服务..."
        docker-compose -f docker-compose.windows.yml stop editor api-gateway
        
        Write-Info "重新构建镜像..."
        docker-compose -f docker-compose.windows.yml build --no-cache editor api-gateway
        
        Write-Info "启动服务..."
        docker-compose -f docker-compose.windows.yml up -d editor api-gateway
        
        Write-Success "Docker服务重启完成"
        return $true
        
    } catch {
        Write-Error "Docker服务重启失败: $($_.Exception.Message)"
        return $false
    }
}

# 验证修复结果
function Test-FixResults {
    Write-Header "验证修复结果"
    
    Write-Info "等待服务启动..."
    Start-Sleep -Seconds 45
    
    # 测试API网关
    try {
        $apiResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/git/health" -TimeoutSec 15 -ErrorAction SilentlyContinue
        if ($apiResponse.StatusCode -eq 200) {
            Write-Success "API网关访问正常"
        } else {
            Write-Warning "API网关响应异常: $($apiResponse.StatusCode)"
        }
    } catch {
        Write-Warning "API网关连接失败: $($_.Exception.Message)"
    }
    
    # 测试前端
    try {
        $frontendResponse = Invoke-WebRequest -Uri "http://localhost:80" -TimeoutSec 15 -ErrorAction SilentlyContinue
        if ($frontendResponse.StatusCode -eq 200) {
            Write-Success "前端访问正常"
        } else {
            Write-Warning "前端响应异常: $($frontendResponse.StatusCode)"
        }
    } catch {
        Write-Warning "前端连接失败: $($_.Exception.Message)"
    }
    
    # 显示服务状态
    Write-Info "显示服务状态..."
    docker-compose -f docker-compose.windows.yml ps editor api-gateway
}

# 显示修复总结
function Show-FixSummary {
    Write-Header "修复总结"
    
    Write-Success "已完成的修复:"
    Write-Info "  ✅ 移除了React.StrictMode以避免useContext问题"
    Write-Info "  ✅ 修复了数组filter方法的null检查"
    Write-Info "  ✅ 修复了Git组件中的数组操作"
    Write-Info "  ✅ 修复了TabPane组件弃用问题"
    Write-Info "  ✅ 重新构建了前端项目"
    Write-Info "  ✅ 重启了Docker服务"
    
    Write-Host ""
    Write-Info "🌐 访问地址:"
    Write-Info "  前端编辑器: http://localhost:80"
    Write-Info "  API网关: http://localhost:3000/api"
    Write-Info "  Git API: http://localhost:3000/api/git/status"
    
    Write-Host ""
    Write-Warning "💡 如果仍有问题:"
    Write-Info "  1. 打开浏览器开发者工具检查控制台错误"
    Write-Info "  2. 检查Docker容器日志: docker-compose -f docker-compose.windows.yml logs editor"
    Write-Info "  3. 等待1-2分钟让服务完全启动"
    Write-Info "  4. 刷新浏览器页面"
}

# 主函数
function Main {
    if ($Help) {
        Show-Help
        return
    }

    Write-Header "前端错误修复工具"
    
    if ($CheckOnly) {
        Write-Info "仅检查模式，不进行修复"
        Write-Info "已修复的问题:"
        Write-Info "  - React.StrictMode已移除"
        Write-Info "  - 数组操作已添加null检查"
        Write-Info "  - TabPane组件已更新"
        return
    }
    
    Write-Info "开始修复前端错误..."
    
    # 1. 修复前端构建
    if (Repair-FrontendBuild) {
        # 2. 重启Docker服务
        if (Restart-DockerServices) {
            # 3. 验证结果
            Test-FixResults
            
            # 4. 显示总结
            Show-FixSummary
            
            Write-Success "🎉 前端错误修复完成！"
        } else {
            Write-Error "Docker服务重启失败"
        }
    } else {
        Write-Error "前端构建修复失败"
    }
}

# 执行主函数
try {
    Main
} catch {
    Write-Error "脚本执行失败: $($_.Exception.Message)"
    exit 1
}
