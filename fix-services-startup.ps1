# 数字人系统服务启动修复脚本
# 修复服务启动问题和健康检查超时问题

Write-Host "开始修复服务启动问题..." -ForegroundColor Green

# 停止所有服务
Write-Host "停止所有服务..." -ForegroundColor Yellow
docker-compose -f docker-compose.windows.yml down

# 清理数据卷（可选，谨慎使用）
$cleanVolumes = Read-Host "是否清理数据卷？这将删除所有数据 (y/N)"
if ($cleanVolumes -eq "y" -or $cleanVolumes -eq "Y") {
    Write-Host "清理数据卷..." -ForegroundColor Red
    docker volume prune -f
    
    # 创建必要的数据目录
    Write-Host "创建数据目录..." -ForegroundColor Yellow
    $dataDirs = @(
        "data/mysql",
        "data/redis", 
        "data/minio",
        "data/chroma",
        "data/elasticsearch",
        "data/uploads/assets",
        "data/uploads/knowledge",
        "data/uploads/asset-library",
        "data/outputs/renders",
        "data/models",
        "data/scene-generation",
        "data/scene-templates",
        "data/logs/scene-generation",
        "data/logs/monitoring",
        "data/prometheus",
        "data/grafana"
    )
    
    foreach ($dir in $dataDirs) {
        if (!(Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force
            Write-Host "创建目录: $dir" -ForegroundColor Cyan
        }
    }
}

# 第一阶段：启动基础服务
Write-Host "第一阶段：启动基础服务..." -ForegroundColor Yellow
docker-compose -f docker-compose.windows.yml up -d mysql redis minio

# 等待MySQL启动
Write-Host "等待MySQL启动..." -ForegroundColor Yellow
$maxWait = 120
$waited = 0
do {
    Start-Sleep -Seconds 5
    $waited += 5
    $mysqlStatus = docker-compose -f docker-compose.windows.yml ps mysql
    Write-Host "等待MySQL启动... ($waited/$maxWait 秒)" -ForegroundColor Cyan
} while ($waited -lt $maxWait -and $mysqlStatus -notmatch "healthy")

# 检查MySQL是否启动成功
if ($mysqlStatus -match "healthy") {
    Write-Host "MySQL启动成功" -ForegroundColor Green
    
    # 验证数据库创建
    Write-Host "验证数据库创建..." -ForegroundColor Yellow
    docker exec dl-engine-mysql-win mysql -u root -pDLEngine2024!@# -e "SHOW DATABASES;"
} else {
    Write-Host "MySQL启动失败，请检查日志" -ForegroundColor Red
    docker-compose -f docker-compose.windows.yml logs mysql
    exit 1
}

# 第二阶段：启动其他基础服务
Write-Host "第二阶段：启动其他基础服务..." -ForegroundColor Yellow
docker-compose -f docker-compose.windows.yml up -d elasticsearch chroma

# 等待基础服务启动
Write-Host "等待基础服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 60

# 第三阶段：启动服务注册中心
Write-Host "第三阶段：启动服务注册中心..." -ForegroundColor Yellow
docker-compose -f docker-compose.windows.yml up -d service-registry

# 等待服务注册中心启动
Write-Host "等待服务注册中心启动..." -ForegroundColor Yellow
$maxWait = 60
$waited = 0
do {
    Start-Sleep -Seconds 5
    $waited += 5
    $registryStatus = docker-compose -f docker-compose.windows.yml ps service-registry
    Write-Host "等待服务注册中心启动... ($waited/$maxWait 秒)" -ForegroundColor Cyan
} while ($waited -lt $maxWait -and $registryStatus -notmatch "healthy")

if ($registryStatus -match "healthy") {
    Write-Host "服务注册中心启动成功" -ForegroundColor Green
} else {
    Write-Host "服务注册中心启动可能有问题，继续执行..." -ForegroundColor Yellow
    docker-compose -f docker-compose.windows.yml logs service-registry
}

# 第四阶段：启动核心微服务
Write-Host "第四阶段：启动核心微服务..." -ForegroundColor Yellow
docker-compose -f docker-compose.windows.yml up -d user-service project-service asset-service render-service

# 等待核心服务启动
Write-Host "等待核心服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 90

# 第五阶段：启动API网关
Write-Host "第五阶段：启动API网关..." -ForegroundColor Yellow
docker-compose -f docker-compose.windows.yml up -d api-gateway

# 等待API网关启动
Write-Host "等待API网关启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# 第六阶段：启动其他服务
Write-Host "第六阶段：启动其他服务..." -ForegroundColor Yellow
docker-compose -f docker-compose.windows.yml up -d

Write-Host "所有服务启动命令已执行！" -ForegroundColor Green

# 等待服务稳定
Write-Host "等待服务稳定..." -ForegroundColor Yellow
Start-Sleep -Seconds 60

# 检查服务状态
Write-Host "检查服务状态..." -ForegroundColor Yellow
docker-compose -f docker-compose.windows.yml ps

Write-Host "`n修复脚本执行完成！" -ForegroundColor Green
Write-Host "如果有服务显示为不健康，请等待几分钟后再次检查，或查看具体服务的日志。" -ForegroundColor Yellow
Write-Host "使用以下命令查看服务日志：" -ForegroundColor Cyan
Write-Host "docker-compose -f docker-compose.windows.yml logs [服务名]" -ForegroundColor Cyan
