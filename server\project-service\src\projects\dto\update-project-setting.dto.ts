/**
 * 更新项目设置DTO
 */
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateProjectSettingDto {
  @ApiProperty({ description: '设置值', example: 'light' })
  @IsString()
  @IsNotEmpty()
  value: string;

  @ApiProperty({ description: '设置描述', required: false, example: '主题设置' })
  @IsString()
  @IsOptional()
  description?: string;
}
