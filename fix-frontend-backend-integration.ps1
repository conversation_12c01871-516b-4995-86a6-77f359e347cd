# 前后端集成修复脚本
# 修复前端和后端之间的连接问题，确保能正常进入登录界面

param(
    [switch]$Verbose,
    [switch]$SkipBuild
)

$ErrorActionPreference = "Stop"

function Write-Header {
    param([string]$Message)
    Write-Host "`n" -NoNewline
    Write-Host "=" * 60 -ForegroundColor Cyan
    Write-Host " $Message" -ForegroundColor Yellow
    Write-Host "=" * 60 -ForegroundColor Cyan
}

function Write-Step {
    param([string]$Message)
    Write-Host "`n🔄 $Message" -ForegroundColor Green
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Stop-ExistingServices {
    Write-Step "停止现有服务..."
    try {
        docker-compose -f docker-compose.windows.yml down 2>$null
        Write-Success "现有服务已停止"
    }
    catch {
        Write-Warning "停止服务时出现警告: $($_.Exception.Message)"
    }
}

function Start-DatabaseServices {
    Write-Step "启动数据库服务..."
    
    # 启动MySQL
    docker-compose -f docker-compose.windows.yml up -d mysql
    Write-Host "等待MySQL启动..." -ForegroundColor Yellow
    Start-Sleep -Seconds 15
    
    # 启动Redis
    docker-compose -f docker-compose.windows.yml up -d redis
    Write-Host "等待Redis启动..." -ForegroundColor Yellow
    Start-Sleep -Seconds 5
    
    # 启动MinIO
    docker-compose -f docker-compose.windows.yml up -d minio
    Write-Host "等待MinIO启动..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
    
    Write-Success "数据库服务启动完成"
}

function Create-Databases {
    Write-Step "创建数据库..."
    
    $maxRetries = 10
    $retryCount = 0
    
    while ($retryCount -lt $maxRetries) {
        try {
            # 检查MySQL是否就绪
            $result = docker exec dl-engine-mysql-win mysqladmin ping -h localhost -u root -pDLEngine2024!@# 2>$null
            if ($LASTEXITCODE -eq 0) {
                break
            }
        }
        catch {
            # 继续重试
        }
        
        $retryCount++
        Write-Host "等待MySQL就绪... ($retryCount/$maxRetries)" -ForegroundColor Yellow
        Start-Sleep -Seconds 3
    }
    
    if ($retryCount -eq $maxRetries) {
        Write-Error "MySQL启动超时"
        return $false
    }
    
    # 创建数据库
    $databases = @(
        "dl_engine_registry",
        "dl_engine_users", 
        "dl_engine_projects",
        "dl_engine_assets",
        "dl_engine_render",
        "dl_engine_knowledge",
        "dl_engine_ai",
        "dl_engine_asset_library",
        "dl_engine_binding",
        "dl_engine_scene_generation",
        "dl_engine_scene_templates",
        "dl_engine_monitoring"
    )
    
    foreach ($db in $databases) {
        try {
            docker exec dl-engine-mysql-win mysql -u root -pDLEngine2024!@# -e "CREATE DATABASE IF NOT EXISTS $db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>$null
            if ($Verbose) {
                Write-Host "  创建数据库: $db" -ForegroundColor Gray
            }
        }
        catch {
            Write-Warning "创建数据库 $db 失败: $($_.Exception.Message)"
        }
    }
    
    Write-Success "数据库创建完成"
    return $true
}

function Start-BackendServices {
    Write-Step "启动后端服务..."
    
    # 启动服务注册中心
    docker-compose -f docker-compose.windows.yml up -d service-registry
    Write-Host "等待服务注册中心启动..." -ForegroundColor Yellow
    Start-Sleep -Seconds 15
    
    # 启动用户服务
    docker-compose -f docker-compose.windows.yml up -d user-service
    Write-Host "等待用户服务启动..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
    
    # 启动其他核心服务
    docker-compose -f docker-compose.windows.yml up -d project-service asset-service
    Write-Host "等待其他服务启动..." -ForegroundColor Yellow
    Start-Sleep -Seconds 15
    
    # 启动API网关
    docker-compose -f docker-compose.windows.yml up -d api-gateway
    Write-Host "等待API网关启动..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
    
    Write-Success "后端服务启动完成"
}

function Build-Frontend {
    if ($SkipBuild) {
        Write-Step "跳过前端构建"
        return
    }
    
    Write-Step "构建前端应用..."
    
    try {
        # 进入前端目录
        Push-Location "editor"
        
        # 安装依赖（如果需要）
        if (-not (Test-Path "node_modules")) {
            Write-Host "安装前端依赖..." -ForegroundColor Yellow
            npm install --silent
        }
        
        # 构建前端
        Write-Host "构建前端应用..." -ForegroundColor Yellow
        npm run build
        
        if ($LASTEXITCODE -ne 0) {
            throw "前端构建失败"
        }
        
        Write-Success "前端构建完成"
    }
    catch {
        Write-Error "前端构建失败: $($_.Exception.Message)"
        return $false
    }
    finally {
        Pop-Location
    }
    
    return $true
}

function Start-Frontend {
    Write-Step "启动前端服务..."
    
    # 构建前端Docker镜像
    docker-compose -f docker-compose.windows.yml build editor
    
    # 启动前端服务
    docker-compose -f docker-compose.windows.yml up -d editor
    Write-Host "等待前端服务启动..." -ForegroundColor Yellow
    Start-Sleep -Seconds 15
    
    Write-Success "前端服务启动完成"
}

function Create-TestUser {
    Write-Step "创建测试用户..."
    
    try {
        # 检查是否有Node.js
        node --version | Out-Null
        
        # 安装必要的依赖
        if (-not (Test-Path "node_modules/mysql2")) {
            npm install mysql2 bcrypt --save-dev --silent
        }
        
        # 运行创建用户脚本
        node create-test-user.js
        Write-Success "测试用户创建完成"
    }
    catch {
        Write-Warning "创建测试用户失败: $($_.Exception.Message)"
        Write-Host "可以稍后手动创建测试用户" -ForegroundColor Yellow
    }
}

function Test-Services {
    Write-Step "测试服务连接..."
    
    $services = @(
        @{ Name = "API网关"; Url = "http://localhost:3000"; Expected = 200 },
        @{ Name = "前端"; Url = "http://localhost"; Expected = 200 }
    )
    
    $allPassed = $true
    
    foreach ($service in $services) {
        $maxRetries = 10
        $success = $false
        
        for ($i = 1; $i -le $maxRetries; $i++) {
            try {
                $response = Invoke-WebRequest -Uri $service.Url -Method GET -TimeoutSec 5 -UseBasicParsing
                if ($response.StatusCode -eq $service.Expected) {
                    Write-Success "$($service.Name) 服务正常"
                    $success = $true
                    break
                }
            }
            catch {
                if ($Verbose) {
                    Write-Host "  $($service.Name) 测试 $i/$maxRetries 失败" -ForegroundColor Gray
                }
            }
            
            if ($i -lt $maxRetries) {
                Start-Sleep -Seconds 3
            }
        }
        
        if (-not $success) {
            Write-Error "$($service.Name) 服务测试失败"
            $allPassed = $false
        }
    }
    
    return $allPassed
}

function Show-Results {
    Write-Header "修复完成"
    
    Write-Host "`n🌐 访问地址:" -ForegroundColor Cyan
    Write-Host "  前端应用: http://localhost" -ForegroundColor White
    Write-Host "  API网关: http://localhost:3000/api" -ForegroundColor White
    Write-Host "  API文档: http://localhost:3000/api/docs" -ForegroundColor White
    
    Write-Host "`n🔑 测试账号:" -ForegroundColor Cyan
    Write-Host "  邮箱: <EMAIL>" -ForegroundColor White
    Write-Host "  密码: 123456" -ForegroundColor White
    
    Write-Host "`n📊 容器状态:" -ForegroundColor Cyan
    docker-compose -f docker-compose.windows.yml ps
    
    Write-Host "`n🎉 修复完成！请访问 http://localhost 测试登录功能" -ForegroundColor Green
}

# 主执行流程
try {
    Write-Header "前后端集成修复"
    
    # 停止现有服务
    Stop-ExistingServices
    
    # 启动数据库服务
    Start-DatabaseServices
    
    # 创建数据库
    if (-not (Create-Databases)) {
        throw "数据库创建失败"
    }
    
    # 启动后端服务
    Start-BackendServices
    
    # 构建前端
    if (-not (Build-Frontend)) {
        throw "前端构建失败"
    }
    
    # 启动前端
    Start-Frontend
    
    # 创建测试用户
    Create-TestUser
    
    # 测试服务
    Write-Step "等待所有服务完全启动..."
    Start-Sleep -Seconds 20
    
    $testResult = Test-Services
    
    # 显示结果
    Show-Results
    
    if ($testResult) {
        Write-Host "`n✅ 所有服务测试通过！" -ForegroundColor Green
    } else {
        Write-Host "`n⚠️  部分服务测试失败，请检查日志" -ForegroundColor Yellow
    }
    
}
catch {
    Write-Error "修复过程中出现错误: $($_.Exception.Message)"
    Write-Host "`n查看容器日志:" -ForegroundColor Yellow
    Write-Host "docker-compose -f docker-compose.windows.yml logs" -ForegroundColor White
    exit 1
}
