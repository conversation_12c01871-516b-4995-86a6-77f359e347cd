import { System } from '../../core/System';
import { type World } from '../../core/World';
import { FacialExpressionType } from '../components/FacialAnimationComponent';
/**
 * 表情预设类型
 */
export declare enum ExpressionPresetType {
    BASIC = "basic",
    COMPLEX = "complex",
    CULTURAL = "cultural",
    EMOTIONAL = "emotional",
    MICRO = "micro",
    DYNAMIC = "dynamic"
}
/**
 * 表情强度级别
 */
export declare enum ExpressionIntensity {
    SUBTLE = "subtle",
    MILD = "mild",
    MODERATE = "moderate",
    STRONG = "strong",
    EXTREME = "extreme"
}
/**
 * 表情混合模式
 */
export declare enum ExpressionBlendMode {
    REPLACE = "replace",
    ADD = "add",
    MULTIPLY = "multiply",
    OVERLAY = "overlay",
    SMOOTH = "smooth"
}
/**
 * 表情关键帧
 */
export interface ExpressionKeyframe {
    /** 时间 */
    time: number;
    /** 表情类型 */
    expression: FacialExpressionType;
    /** 权重 */
    weight: number;
    /** 持续时间 */
    duration?: number;
    /** 缓动函数 */
    easing?: string;
    /** 额外参数 */
    parameters?: Map<string, number>;
}
/**
 * 表情序列
 */
export interface ExpressionSequence {
    /** 序列ID */
    id: string;
    /** 序列名称 */
    name: string;
    /** 关键帧 */
    keyframes: ExpressionKeyframe[];
    /** 总时长 */
    duration: number;
    /** 是否循环 */
    loop: boolean;
    /** 混合模式 */
    blendMode: ExpressionBlendMode;
    /** 标签 */
    tags: string[];
}
/**
 * 表情预设
 */
export interface ExpressionPreset {
    /** 预设ID */
    id: string;
    /** 预设名称 */
    name: string;
    /** 预设类型 */
    type: ExpressionPresetType;
    /** 描述 */
    description: string;
    /** 表情组合 */
    expressions: Array<{
        type: FacialExpressionType;
        weight: number;
        delay?: number;
        duration?: number;
    }>;
    /** 强度级别 */
    intensity: ExpressionIntensity;
    /** 文化背景 */
    culture?: string;
    /** 标签 */
    tags: string[];
    /** 预览图 */
    thumbnail?: string;
}
/**
 * 动作预设
 */
export interface ActionPreset {
    /** 预设ID */
    id: string;
    /** 预设名称 */
    name: string;
    /** 描述 */
    description: string;
    /** 表情序列 */
    expressionSequence: ExpressionSequence;
    /** 身体动作 */
    bodyAnimation?: any;
    /** 音频 */
    audio?: string;
    /** 持续时间 */
    duration: number;
    /** 标签 */
    tags: string[];
}
/**
 * 实时控制参数
 */
export interface RealTimeControlParams {
    /** 表情强度 */
    intensity: number;
    /** 混合速度 */
    blendSpeed: number;
    /** 随机变化 */
    randomVariation: number;
    /** 微表情频率 */
    microExpressionFrequency: number;
    /** 眨眼频率 */
    blinkFrequency: number;
    /** 呼吸强度 */
    breathingIntensity: number;
}
/**
 * 高级表情和动作系统配置
 */
export interface AdvancedExpressionSystemConfig {
    /** 是否启用微表情 */
    enableMicroExpressions: boolean;
    /** 是否启用自动眨眼 */
    enableAutoBlink: boolean;
    /** 是否启用呼吸动画 */
    enableBreathing: boolean;
    /** 是否启用情感分析 */
    enableEmotionAnalysis: boolean;
    /** 是否启用文化适应 */
    enableCulturalAdaptation: boolean;
    /** 默认混合速度 */
    defaultBlendSpeed: number;
    /** 是否启用调试 */
    debug: boolean;
}
/**
 * 高级表情和动作系统
 * 实现高级表情控制和动作管理系统，支持表情预设、动作混合和实时控制
 */
export declare class AdvancedExpressionSystem extends System {
    /** 系统类型 */
    static readonly TYPE = "AdvancedExpressionSystem";
    /** 事件发射器 */
    private eventEmitter;
    /** 系统配置 */
    private config;
    /** 表情预设库 */
    private expressionPresets;
    /** 动作预设库 */
    private actionPresets;
    /** 表情序列库 */
    private expressionSequences;
    /** 活跃的表情控制 */
    private activeExpressions;
    /** 实时控制参数 */
    private realTimeParams;
    /** 微表情定时器 */
    private microExpressionTimers;
    /** 眨眼定时器 */
    private blinkTimers;
    /** 呼吸动画状态 */
    private breathingStates;
    /**
     * 构造函数
     * @param world 世界实例
     * @param config 系统配置
     */
    constructor(world: World, config?: Partial<AdvancedExpressionSystemConfig>);
    /**
     * 获取系统类型
     * @returns 系统类型
     */
    getType(): string;
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 更新系统
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void;
    /**
     * 销毁系统
     */
    dispose(): void;
    /**
     * 初始化默认预设
     */
    private initializeDefaultPresets;
    /**
     * 创建基础表情预设
     */
    private createBasicExpressionPresets;
    /**
     * 创建复杂表情预设
     */
    private createComplexExpressionPresets;
    /**
     * 创建文化特定预设
     */
    private createCulturalPresets;
    /**
     * 创建情感预设
     */
    private createEmotionalPresets;
    /**
     * 创建微表情预设
     */
    private createMicroExpressionPresets;
    /**
     * 创建动态预设
     */
    private createDynamicPresets;
    /**
     * 应用表情预设
     * @param entityId 实体ID
     * @param presetId 预设ID
     * @param duration 持续时间
     * @param blendMode 混合模式
     * @returns 是否成功应用
     */
    applyExpressionPreset(entityId: string, presetId: string, duration?: number, blendMode?: ExpressionBlendMode): boolean;
    /**
     * 播放表情序列
     * @param entityId 实体ID
     * @param sequenceId 序列ID
     * @returns 是否成功播放
     */
    playExpressionSequence(entityId: string, sequenceId: string): boolean;
    /**
     * 停止表情序列
     * @param entityId 实体ID
     */
    stopExpressionSequence(entityId: string): void;
    /**
     * 设置实时控制参数
     * @param entityId 实体ID
     * @param params 控制参数
     */
    setRealTimeControlParams(entityId: string, params: Partial<RealTimeControlParams>): void;
    /**
     * 创建自定义表情预设
     * @param preset 表情预设
     */
    createCustomExpressionPreset(preset: ExpressionPreset): void;
    /**
     * 创建自定义表情序列
     * @param sequence 表情序列
     */
    createCustomExpressionSequence(sequence: ExpressionSequence): void;
    /**
     * 获取所有表情预设
     * @param type 预设类型过滤
     * @returns 表情预设列表
     */
    getExpressionPresets(type?: ExpressionPresetType): ExpressionPreset[];
    /**
     * 获取所有表情序列
     * @returns 表情序列列表
     */
    getExpressionSequences(): ExpressionSequence[];
    /**
     * 搜索表情预设
     * @param query 搜索条件
     * @returns 匹配的预设
     */
    searchExpressionPresets(query: {
        name?: string;
        type?: ExpressionPresetType;
        intensity?: ExpressionIntensity;
        culture?: string;
        tags?: string[];
    }): ExpressionPreset[];
    /**
     * 实体添加事件处理
     * @param entity 实体
     */
    private onEntityAdded;
    /**
     * 实体移除事件处理
     * @param entity 实体
     */
    private onEntityRemoved;
    /**
     * 更新活跃的表情序列
     * @param deltaTime 时间增量
     */
    private updateActiveExpressions;
    /**
     * 更新序列关键帧
     * @param entity 实体
     * @param sequence 序列
     * @param elapsedTime 已过时间
     * @param activeExpression 活跃表情
     */
    private updateSequenceKeyframe;
    /**
     * 更新微表情
     * @param deltaTime 时间增量
     */
    private updateMicroExpressions;
    /**
     * 更新呼吸动画
     * @param deltaTime 时间增量
     */
    private updateBreathingAnimation;
    /**
     * 更新实时控制
     * @param deltaTime 时间增量
     */
    private updateRealTimeControl;
    /**
     * 更新微表情定时器
     * @param entityId 实体ID
     * @param frequency 频率
     */
    private updateMicroExpressionTimer;
    /**
     * 更新眨眼定时器
     * @param entityId 实体ID
     * @param frequency 频率
     */
    private updateBlinkTimer;
    /**
     * 触发微表情
     * @param entityId 实体ID
     */
    private triggerMicroExpression;
    /**
     * 触发眨眼
     * @param entityId 实体ID
     */
    private triggerBlink;
}
