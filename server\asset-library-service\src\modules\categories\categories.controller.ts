import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { CategoriesService, CreateCategoryDto, UpdateCategoryDto } from './categories.service';
import { Category } from './entities/category.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../auth/entities/user.entity';

@ApiTags('categories')
@Controller('categories')
export class CategoriesController {
  constructor(private readonly categoriesService: CategoriesService) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MODERATOR)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建分类' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '分类创建成功',
    type: Category,
  })
  async create(@Body() createCategoryDto: CreateCategoryDto): Promise<Category> {
    return await this.categoriesService.create(createCategoryDto);
  }

  @Get()
  @ApiOperation({ summary: '获取分类树' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '分类树获取成功',
    type: [Category],
  })
  async findAll(): Promise<Category[]> {
    return await this.categoriesService.findAll();
  }

  @Get('roots')
  @ApiOperation({ summary: '获取根分类' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '根分类获取成功',
    type: [Category],
  })
  async findRoots(): Promise<Category[]> {
    return await this.categoriesService.findRoots();
  }

  @Get(':id')
  @ApiOperation({ summary: '获取分类详情' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '分类详情获取成功',
    type: Category,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '分类不存在',
  })
  @ApiParam({ name: 'id', description: '分类ID' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Category> {
    return await this.categoriesService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MODERATOR)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新分类' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '分类更新成功',
    type: Category,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '分类不存在',
  })
  @ApiParam({ name: 'id', description: '分类ID' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateCategoryDto: UpdateCategoryDto,
  ): Promise<Category> {
    return await this.categoriesService.update(id, updateCategoryDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MODERATOR)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除分类' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: '分类删除成功',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '分类不存在',
  })
  @ApiParam({ name: 'id', description: '分类ID' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    await this.categoriesService.remove(id);
  }
}
