/**
 * 增强版雨水预设
 * 提供各种类型的增强雨水预设配置
 */
import * as THREE from 'three';
import { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { RainWaterType } from './RainWaterComponent';
import { EnhancedRainWaterComponent, EnhancedRainWaterConfig, EnhancedRainWaterType, RaindropShape } from './EnhancedRainWaterComponent';
import { Debug } from '../../utils/Debug';

/**
 * 增强版雨水预设配置
 */
export interface EnhancedRainWaterPresetConfig {
  /** 预设类型 */
  type: EnhancedRainWaterType;
  /** 位置 */
  position?: THREE.Vector3;
  /** 尺寸 */
  size?: {
    width: number;
    height: number;
    depth: number;
  };
  /** 颜色 */
  color?: THREE.Color;
  /** 不透明度 */
  opacity?: number;
  /** 是否启用风力影响 */
  enableWindEffect?: boolean;
  /** 风力强度 */
  windStrength?: number;
  /** 风力方向 */
  windDirection?: THREE.Vector2;
  /** 是否启用雨滴轨迹 */
  enableRaindropTrails?: boolean;
  /** 是否启用雨滴涟漪 */
  enableRaindropRipples?: boolean;
  /** 是否启用雨滴积水 */
  enableRaindropPuddles?: boolean;
  /** 是否启用雨滴声音 */
  enableRaindropSounds?: boolean;
  /** 是否启用GPU加速 */
  enableGPUAcceleration?: boolean;
}

/**
 * 增强版雨水预设
 */
export class EnhancedRainWaterPresets {
  /**
   * 创建雨水预设
   * @param world 世界
   * @param config 配置
   * @returns 雨水实体
   */
  public static createPreset(world: World, config: EnhancedRainWaterPresetConfig): Entity {
    // 创建实体
    const entity = new Entity(`EnhancedRainWater_${config.type}`);

    // 默认位置和尺寸
    const position = config.position || new THREE.Vector3(0, 0, 0);
    const size = config.size || { width: 100, height: 10, depth: 100 };

    // 创建基础配置
    const rainWaterConfig: EnhancedRainWaterConfig = {
      position,
      width: size.width,
      height: size.height,
      depth: size.depth,
      color: config.color || new THREE.Color(0x88ccff),
      opacity: config.opacity !== undefined ? config.opacity : 0.8,
      enableWindEffect: config.enableWindEffect !== undefined ? config.enableWindEffect : true,
      windStrength: config.windStrength !== undefined ? config.windStrength : 1.0,
      windDirection: config.windDirection || new THREE.Vector2(1, 0),
      enableRaindropTrails: config.enableRaindropTrails !== undefined ? config.enableRaindropTrails : true,
      enableRaindropRipples: config.enableRaindropRipples !== undefined ? config.enableRaindropRipples : true,
      enableRaindropPuddles: config.enableRaindropPuddles !== undefined ? config.enableRaindropPuddles : true,
      enableRaindropSounds: config.enableRaindropSounds !== undefined ? config.enableRaindropSounds : true,
      enableGPUAcceleration: config.enableGPUAcceleration !== undefined ? config.enableGPUAcceleration : true
    };

    // 根据预设类型应用特定配置
    switch (config.type) {
      case EnhancedRainWaterType.LIGHT:
        this.applyLightPreset(rainWaterConfig);
        break;
      case EnhancedRainWaterType.MEDIUM:
        this.applyMediumPreset(rainWaterConfig);
        break;
      case EnhancedRainWaterType.HEAVY:
        this.applyHeavyPreset(rainWaterConfig);
        break;
      case EnhancedRainWaterType.THUNDERSTORM:
        this.applyThunderstormPreset(rainWaterConfig);
        break;
      case EnhancedRainWaterType.MONSOON:
        this.applyMonsoonPreset(rainWaterConfig);
        break;
      case EnhancedRainWaterType.SPRING_RAIN:
        this.applySpringRainPreset(rainWaterConfig);
        break;
      case EnhancedRainWaterType.SUMMER_RAIN:
        this.applySummerRainPreset(rainWaterConfig);
        break;
      case EnhancedRainWaterType.AUTUMN_RAIN:
        this.applyAutumnRainPreset(rainWaterConfig);
        break;
      case EnhancedRainWaterType.WINTER_RAIN:
        this.applyWinterRainPreset(rainWaterConfig);
        break;
      case EnhancedRainWaterType.SLEET:
        this.applySleetPreset(rainWaterConfig);
        break;
      case EnhancedRainWaterType.ACID_RAIN:
        this.applyAcidRainPreset(rainWaterConfig);
        break;
      case EnhancedRainWaterType.TROPICAL_RAIN:
        this.applyTropicalRainPreset(rainWaterConfig);
        break;
      default:
        this.applyMediumPreset(rainWaterConfig);
        break;
    }

    // 创建雨水组件
    const rainWaterComponent = new EnhancedRainWaterComponent(entity, rainWaterConfig);

    // 将组件添加到实体
    entity.addComponent(rainWaterComponent);

    // 添加到世界
    world.addEntity(entity);

    Debug.log('EnhancedRainWaterPresets', `创建增强版雨水预设: ${config.type}`);

    return entity;
  }

  /**
   * 应用轻雨预设
   * @param config 配置
   */
  private static applyLightPreset(config: EnhancedRainWaterConfig): void {
    config.rainWaterType = RainWaterType.LIGHT;
    config.raindropShape = RaindropShape.TEARDROP;
    config.raindropDeformationFactor = 0.8;
    config.raindropCollisionPrecision = 0.8;
    config.windStrength = 0.5;
    config.raindropTrailLength = 0.5;
    config.raindropRippleStrength = 0.7;
    config.raindropPuddleMaxDepth = 0.05;
    config.raindropReflectionStrength = 0.8;
    config.raindropRefractionStrength = 0.8;
    config.raindropScatteringStrength = 0.5;
    config.raindropSoundVolume = 0.5;
  }

  /**
   * 应用中雨预设
   * @param config 配置
   */
  private static applyMediumPreset(config: EnhancedRainWaterConfig): void {
    config.rainWaterType = RainWaterType.MEDIUM;
    config.raindropShape = RaindropShape.TEARDROP;
    config.raindropDeformationFactor = 1.0;
    config.raindropCollisionPrecision = 1.0;
    config.windStrength = 1.0;
    config.raindropTrailLength = 1.0;
    config.raindropRippleStrength = 1.0;
    config.raindropPuddleMaxDepth = 0.1;
    config.raindropReflectionStrength = 1.0;
    config.raindropRefractionStrength = 1.0;
    config.raindropScatteringStrength = 1.0;
    config.raindropSoundVolume = 0.8;
  }

  /**
   * 应用暴雨预设
   * @param config 配置
   */
  private static applyHeavyPreset(config: EnhancedRainWaterConfig): void {
    config.rainWaterType = RainWaterType.HEAVY;
    config.raindropShape = RaindropShape.TEARDROP;
    config.raindropDeformationFactor = 1.2;
    config.raindropCollisionPrecision = 1.2;
    config.windStrength = 1.5;
    config.raindropTrailLength = 1.5;
    config.raindropRippleStrength = 1.5;
    config.raindropPuddleMaxDepth = 0.15;
    config.raindropReflectionStrength = 1.2;
    config.raindropRefractionStrength = 1.2;
    config.raindropScatteringStrength = 1.5;
    config.raindropSoundVolume = 1.0;
  }

  /**
   * 应用雷雨预设
   * @param config 配置
   */
  private static applyThunderstormPreset(config: EnhancedRainWaterConfig): void {
    config.rainWaterType = RainWaterType.THUNDERSTORM;
    config.raindropShape = RaindropShape.TEARDROP;
    config.raindropDeformationFactor = 1.5;
    config.raindropCollisionPrecision = 1.5;
    config.windStrength = 2.0;
    config.raindropTrailLength = 2.0;
    config.raindropRippleStrength = 2.0;
    config.raindropPuddleMaxDepth = 0.2;
    config.raindropReflectionStrength = 1.5;
    config.raindropRefractionStrength = 1.5;
    config.raindropScatteringStrength = 2.0;
    config.raindropSoundVolume = 1.2;
  }

  /**
   * 应用季风雨预设
   * @param config 配置
   */
  private static applyMonsoonPreset(config: EnhancedRainWaterConfig): void {
    config.rainWaterType = RainWaterType.MONSOON;
    config.raindropShape = RaindropShape.TEARDROP;
    config.raindropDeformationFactor = 1.8;
    config.raindropCollisionPrecision = 1.8;
    config.windStrength = 2.5;
    config.raindropTrailLength = 2.5;
    config.raindropRippleStrength = 2.5;
    config.raindropPuddleMaxDepth = 0.25;
    config.raindropReflectionStrength = 1.8;
    config.raindropRefractionStrength = 1.8;
    config.raindropScatteringStrength = 2.5;
    config.raindropSoundVolume = 1.5;
  }

  /**
   * 应用春雨预设
   * @param config 配置
   */
  private static applySpringRainPreset(config: EnhancedRainWaterConfig): void {
    config.rainWaterType = RainWaterType.LIGHT;
    config.raindropShape = RaindropShape.TEARDROP;
    config.raindropDeformationFactor = 0.7;
    config.raindropCollisionPrecision = 0.7;
    config.windStrength = 0.7;
    config.windDirection = new THREE.Vector2(0.7, 0.7);
    config.raindropTrailLength = 0.7;
    config.raindropRippleStrength = 0.8;
    config.raindropPuddleMaxDepth = 0.07;
    config.raindropReflectionStrength = 0.9;
    config.raindropRefractionStrength = 0.9;
    config.raindropScatteringStrength = 0.7;
    config.raindropSoundVolume = 0.6;
    config.color = new THREE.Color(0x99ccff);
  }

  /**
   * 应用夏雨预设
   * @param config 配置
   */
  private static applySummerRainPreset(config: EnhancedRainWaterConfig): void {
    config.rainWaterType = RainWaterType.HEAVY;
    config.raindropShape = RaindropShape.TEARDROP;
    config.raindropDeformationFactor = 1.3;
    config.raindropCollisionPrecision = 1.3;
    config.windStrength = 1.0;
    config.windDirection = new THREE.Vector2(0.5, 1.0);
    config.raindropTrailLength = 1.3;
    config.raindropRippleStrength = 1.5;
    config.raindropPuddleMaxDepth = 0.15;
    config.raindropReflectionStrength = 1.3;
    config.raindropRefractionStrength = 1.3;
    config.raindropScatteringStrength = 1.5;
    config.raindropSoundVolume = 1.0;
    config.color = new THREE.Color(0x77aaff);
  }

  /**
   * 应用秋雨预设
   * @param config 配置
   */
  private static applyAutumnRainPreset(config: EnhancedRainWaterConfig): void {
    config.rainWaterType = RainWaterType.MEDIUM;
    config.raindropShape = RaindropShape.TEARDROP;
    config.raindropDeformationFactor = 1.1;
    config.raindropCollisionPrecision = 1.1;
    config.windStrength = 1.5;
    config.windDirection = new THREE.Vector2(1.0, 0.5);
    config.raindropTrailLength = 1.1;
    config.raindropRippleStrength = 1.2;
    config.raindropPuddleMaxDepth = 0.12;
    config.raindropReflectionStrength = 1.1;
    config.raindropRefractionStrength = 1.1;
    config.raindropScatteringStrength = 1.2;
    config.raindropSoundVolume = 0.9;
    config.color = new THREE.Color(0x6699cc);
  }

  /**
   * 应用冬雨预设
   * @param config 配置
   */
  private static applyWinterRainPreset(config: EnhancedRainWaterConfig): void {
    config.rainWaterType = RainWaterType.MEDIUM;
    config.raindropShape = RaindropShape.TEARDROP;
    config.raindropDeformationFactor = 0.9;
    config.raindropCollisionPrecision = 0.9;
    config.windStrength = 2.0;
    config.windDirection = new THREE.Vector2(1.5, 0.2);
    config.raindropTrailLength = 0.9;
    config.raindropRippleStrength = 0.9;
    config.raindropPuddleMaxDepth = 0.09;
    config.raindropReflectionStrength = 0.9;
    config.raindropRefractionStrength = 0.9;
    config.raindropScatteringStrength = 0.9;
    config.raindropSoundVolume = 0.7;
    config.color = new THREE.Color(0x5588aa);
  }

  /**
   * 应用雪雨混合预设
   * @param config 配置
   */
  private static applySleetPreset(config: EnhancedRainWaterConfig): void {
    config.rainWaterType = RainWaterType.MEDIUM;
    config.raindropShape = RaindropShape.IRREGULAR;
    config.raindropDeformationFactor = 0.6;
    config.raindropCollisionPrecision = 1.0;
    config.windStrength = 1.2;
    config.windDirection = new THREE.Vector2(1.0, 0.3);
    config.raindropTrailLength = 0.6;
    config.raindropRippleStrength = 0.8;
    config.raindropPuddleMaxDepth = 0.08;
    config.raindropReflectionStrength = 0.8;
    config.raindropRefractionStrength = 0.8;
    config.raindropScatteringStrength = 0.8;
    config.raindropSoundVolume = 0.6;
    config.color = new THREE.Color(0xaabbcc);
  }

  /**
   * 应用酸雨预设
   * @param config 配置
   */
  private static applyAcidRainPreset(config: EnhancedRainWaterConfig): void {
    config.rainWaterType = RainWaterType.MEDIUM;
    config.raindropShape = RaindropShape.TEARDROP;
    config.raindropDeformationFactor = 1.0;
    config.raindropCollisionPrecision = 1.0;
    config.windStrength = 1.0;
    config.windDirection = new THREE.Vector2(0.8, 0.8);
    config.raindropTrailLength = 1.0;
    config.raindropRippleStrength = 1.0;
    config.raindropPuddleMaxDepth = 0.1;
    config.raindropReflectionStrength = 1.0;
    config.raindropRefractionStrength = 1.0;
    config.raindropScatteringStrength = 1.0;
    config.raindropSoundVolume = 0.8;
    config.color = new THREE.Color(0xaaff77); // 酸性绿色
  }

  /**
   * 应用热带雨预设
   * @param config 配置
   */
  private static applyTropicalRainPreset(config: EnhancedRainWaterConfig): void {
    config.rainWaterType = RainWaterType.HEAVY;
    config.raindropShape = RaindropShape.TEARDROP;
    config.raindropDeformationFactor = 1.4;
    config.raindropCollisionPrecision = 1.4;
    config.windStrength = 1.2;
    config.windDirection = new THREE.Vector2(0.3, 1.0);
    config.raindropTrailLength = 1.4;
    config.raindropRippleStrength = 1.6;
    config.raindropPuddleMaxDepth = 0.18;
    config.raindropReflectionStrength = 1.4;
    config.raindropRefractionStrength = 1.4;
    config.raindropScatteringStrength = 1.6;
    config.raindropSoundVolume = 1.1;
    config.color = new THREE.Color(0x66aaff);
  }
}
