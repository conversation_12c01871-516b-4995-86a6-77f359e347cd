#!/usr/bin/env pwsh
# 快速端口冲突检查脚本
# 用于快速验证端口3003冲突修复是否成功

Write-Host "🔍 快速端口冲突检查 - 2024年12月19日修复验证" -ForegroundColor Magenta
Write-Host "=" * 60

# 检查关键端口
Write-Host "📊 检查关键端口状态..." -ForegroundColor Cyan

# 端口3003 - 资产服务
$port3003 = Test-NetConnection -ComputerName localhost -Port 3003 -WarningAction SilentlyContinue
if ($port3003.TcpTestSucceeded) {
    Write-Host "✅ 端口3003: 正在使用 (资产服务)" -ForegroundColor Green
} else {
    Write-Host "❌ 端口3003: 未使用 (资产服务未启动)" -ForegroundColor Red
}

# 端口3033 - 游戏服务器微服务
$port3033 = Test-NetConnection -ComputerName localhost -Port 3033 -WarningAction SilentlyContinue
if ($port3033.TcpTestSucceeded) {
    Write-Host "✅ 端口3033: 正在使用 (游戏服务器微服务)" -ForegroundColor Green
} else {
    Write-Host "❌ 端口3033: 未使用 (游戏服务器微服务未启动)" -ForegroundColor Red
}

# 端口3030 - 游戏服务器HTTP
$port3030 = Test-NetConnection -ComputerName localhost -Port 3030 -WarningAction SilentlyContinue
if ($port3030.TcpTestSucceeded) {
    Write-Host "✅ 端口3030: 正在使用 (游戏服务器HTTP)" -ForegroundColor Green
} else {
    Write-Host "❌ 端口3030: 未使用 (游戏服务器HTTP未启动)" -ForegroundColor Red
}

Write-Host ""
Write-Host "🐳 检查Docker容器状态..." -ForegroundColor Cyan

# 检查游戏服务器容器
try {
    $gameServerStatus = docker inspect dl-engine-game-server-win --format "{{.State.Status}}" 2>$null
    if ($gameServerStatus -eq "running") {
        Write-Host "✅ 游戏服务器容器: 运行中" -ForegroundColor Green
    } else {
        Write-Host "❌ 游戏服务器容器: $gameServerStatus" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 游戏服务器容器: 不存在或无法访问" -ForegroundColor Red
}

# 检查资产服务容器
try {
    $assetServiceStatus = docker inspect dl-engine-asset-service-win --format "{{.State.Status}}" 2>$null
    if ($assetServiceStatus -eq "running") {
        Write-Host "✅ 资产服务容器: 运行中" -ForegroundColor Green
    } else {
        Write-Host "❌ 资产服务容器: $assetServiceStatus" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 资产服务容器: 不存在或无法访问" -ForegroundColor Red
}

Write-Host ""
Write-Host "🌐 测试服务健康检查..." -ForegroundColor Cyan

# 测试游戏服务器健康检查
try {
    $gameServerHealth = Invoke-WebRequest -Uri "http://localhost:3030/api/health" -Method GET -TimeoutSec 5
    if ($gameServerHealth.StatusCode -eq 200) {
        Write-Host "✅ 游戏服务器健康检查: 通过" -ForegroundColor Green
    } else {
        Write-Host "⚠️  游戏服务器健康检查: HTTP $($gameServerHealth.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ 游戏服务器健康检查: 失败" -ForegroundColor Red
}

# 测试资产服务健康检查
try {
    $assetServiceHealth = Invoke-WebRequest -Uri "http://localhost:4003/health" -Method GET -TimeoutSec 5
    if ($assetServiceHealth.StatusCode -eq 200) {
        Write-Host "✅ 资产服务健康检查: 通过" -ForegroundColor Green
    } else {
        Write-Host "⚠️  资产服务健康检查: HTTP $($assetServiceHealth.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ 资产服务健康检查: 失败" -ForegroundColor Red
}

Write-Host ""
Write-Host "📋 修复状态总结:" -ForegroundColor Magenta

# 判断修复是否成功
$fixSuccess = $true

if ($port3003.TcpTestSucceeded -and $port3033.TcpTestSucceeded) {
    Write-Host "🎉 端口冲突修复成功！" -ForegroundColor Green
    Write-Host "   - 端口3003: 资产服务 ✅" -ForegroundColor Green
    Write-Host "   - 端口3033: 游戏服务器微服务 ✅" -ForegroundColor Green
} elseif ($port3003.TcpTestSucceeded -and -not $port3033.TcpTestSucceeded) {
    Write-Host "⚠️  部分修复成功，游戏服务器微服务未启动" -ForegroundColor Yellow
    $fixSuccess = $false
} elseif (-not $port3003.TcpTestSucceeded -and $port3033.TcpTestSucceeded) {
    Write-Host "⚠️  部分修复成功，资产服务未启动" -ForegroundColor Yellow
    $fixSuccess = $false
} else {
    Write-Host "❌ 服务未启动，无法验证修复效果" -ForegroundColor Red
    $fixSuccess = $false
}

Write-Host ""
if ($fixSuccess) {
    Write-Host "✅ 端口冲突修复验证完成！所有服务正常运行。" -ForegroundColor Green
} else {
    Write-Host "⚠️  需要进一步检查服务状态。建议运行:" -ForegroundColor Yellow
    Write-Host "   .\verify-port-fixes.ps1 -All" -ForegroundColor Cyan
    Write-Host "   或" -ForegroundColor Yellow
    Write-Host "   docker-compose -f docker-compose.windows.yml restart game-server asset-service" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "📚 相关文档:" -ForegroundColor Cyan
Write-Host "   - PORT_CONFLICT_FIXES_2024-12-19.md (详细修复报告)" -ForegroundColor White
Write-Host "   - verify-port-fixes.ps1 (完整验证脚本)" -ForegroundColor White
