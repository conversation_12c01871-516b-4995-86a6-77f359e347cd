/**
 * 物理驱动的面部动画系统
 * 使用物理模拟来驱动面部动画，实现更自然的面部表情
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import { EventEmitter } from '../../utils/EventEmitter';
import { FacialAnimationComponent, FacialExpressionType } from '../components/FacialAnimationComponent';
import { PhysicalFacialAnimationComponent } from '../components/PhysicalFacialAnimationComponent';

/**
 * 物理驱动的面部动画系统配置
 */
export interface PhysicalFacialAnimationSystemConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 物理更新频率 */
  physicsUpdateRate?: number;
  /** 重力 */
  gravity?: THREE.Vector3;
  /** 阻尼 */
  damping?: number;
  /** 是否使用GPU加速 */
  useGPU?: boolean;
  /** 是否显示调试信息 */
  showDebugInfo?: boolean;
  /** 是否使用高级物理 */
  useAdvancedPhysics?: boolean;
  /** 是否使用软体物理 */
  useSoftBodyPhysics?: boolean;
  /** 是否使用碰撞检测 */
  useCollisionDetection?: boolean;
}

/**
 * 肌肉类型
 */
export enum MuscleType {
  JAW = 'jaw',
  CHEEK = 'cheek',
  EYEBROW = 'eyebrow',
  LIP = 'lip',
  EYELID = 'eyelid',
  NOSE = 'nose',
  FOREHEAD = 'forehead',
  TONGUE = 'tongue'
}

/**
 * 肌肉数据
 */
export interface MuscleData {
  /** 肌肉类型 */
  type: MuscleType;
  /** 肌肉名称 */
  name: string;
  /** 起始点 */
  start: THREE.Vector3;
  /** 结束点 */
  end: THREE.Vector3;
  /** 质量 */
  mass: number;
  /** 半径 */
  radius: number;
  /** 刚度 */
  stiffness: number;
  /** 阻尼 */
  damping: number;
  /** 起始点是否固定 */
  fixedStart: boolean;
  /** 结束点是否固定 */
  fixedEnd: boolean;
  /** 最大拉伸 */
  maxStretch?: number;
  /** 最大压缩 */
  maxCompress?: number;
  /** 休息长度 */
  restLength?: number;
  /** 控制点 */
  controlPoints?: THREE.Vector3[];
}

/**
 * 物理驱动的面部动画系统
 */
export class PhysicalFacialAnimationSystem extends System {
  /** 系统名称 */
  static readonly NAME = 'PhysicalFacialAnimationSystem';

  /** 配置 */
  private config: PhysicalFacialAnimationSystemConfig;

  /** 事件发射器 */
  private _eventEmitter: EventEmitter = new EventEmitter();

  /** 物理面部动画组件映射 */
  private components: Map<Entity, PhysicalFacialAnimationComponent> = new Map();

  /** 物理世界 */
  private physicsWorld: any = null;

  /** 上次物理更新时间 */
  private _lastPhysicsUpdateTime: number = 0;

  /** 物理更新间隔 */
  private physicsUpdateInterval: number = 1 / 60;

  /** 累积时间 */
  private accumulatedTime: number = 0;

  /** 调试渲染器 */
  private debugRenderer: THREE.LineSegments | null = null;

  /** 调试场景 */
  private debugScene: THREE.Scene | null = null;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: PhysicalFacialAnimationSystemConfig = {}) {
    super(270); // 设置优先级

    this.config = {
      debug: false,
      physicsUpdateRate: 60,
      gravity: new THREE.Vector3(0, -9.8, 0),
      damping: 0.1,
      useGPU: false,
      showDebugInfo: false,
      useAdvancedPhysics: false,
      useSoftBodyPhysics: false,
      useCollisionDetection: false,
      ...config
    };

    // 计算物理更新间隔
    this.physicsUpdateInterval = 1 / (this.config.physicsUpdateRate || 60);

    if (this.config.debug) {
      console.log('物理驱动的面部动画系统初始化');
    }

    // 初始化物理世界
    this.initPhysicsWorld();

    // 初始化调试渲染器
    if (this.config.showDebugInfo) {
      this.initDebugRenderer();
    }
  }

  /**
   * 初始化物理世界
   */
  private initPhysicsWorld(): void {
    // 这里是简化的物理世界实现
    // 实际应用中应使用专业的物理引擎，如Cannon.js、Ammo.js等
    this.physicsWorld = {
      gravity: this.config.gravity?.clone() || new THREE.Vector3(0, -9.8, 0),
      damping: this.config.damping || 0.1,
      bodies: [],
      constraints: [],
      update: (deltaTime: number) => {
        // 更新所有物理体
        for (const body of this.physicsWorld.bodies) {
          // 应用重力
          const gravityForce = this.physicsWorld.gravity.clone().multiplyScalar(body.mass);
          body.applyForce(gravityForce);

          // 应用阻尼
          const dampingForce = body.velocity.clone().multiplyScalar(-this.physicsWorld.damping);
          body.applyForce(dampingForce);

          // 更新速度
          body.velocity.add(body.force.clone().multiplyScalar(deltaTime / body.mass));

          // 更新位置
          body.position.add(body.velocity.clone().multiplyScalar(deltaTime));

          // 重置力
          body.force.set(0, 0, 0);
        }

        // 更新所有约束
        for (const constraint of this.physicsWorld.constraints) {
          constraint.update(deltaTime);
        }
      }
    };

    if (this.config.debug) {
      console.log('物理世界已初始化');
    }
  }

  /**
   * 初始化调试渲染器
   */
  private initDebugRenderer(): void {
    // 创建调试场景
    this.debugScene = new THREE.Scene();

    // 创建调试渲染器
    const geometry = new THREE.BufferGeometry();
    const material = new THREE.LineBasicMaterial({ color: 0xff0000, linewidth: 2 });
    this.debugRenderer = new THREE.LineSegments(geometry, material);

    // 添加到调试场景
    if (this.debugScene) {
      this.debugScene.add(this.debugRenderer);
    }

    if (this.config.debug) {
      console.log('调试渲染器已初始化');
    }
  }

  /**
   * 创建物理面部动画组件
   * @param entity 实体
   * @returns 物理面部动画组件
   */
  public createPhysicalFacialAnimation(entity: Entity): PhysicalFacialAnimationComponent {
    // 检查是否已存在组件
    if (this.components.has(entity)) {
      return this.components.get(entity)!;
    }

    // 创建新组件
    const component = new PhysicalFacialAnimationComponent(entity);
    this.components.set(entity, component);

    if (this.config.debug) {
      console.log(`创建物理面部动画组件: ${entity.id}`);
    }

    return component;
  }

  /**
   * 移除物理面部动画组件
   * @param entity 实体
   */
  public removePhysicalFacialAnimation(entity: Entity): void {
    if (this.components.has(entity)) {
      const component = this.components.get(entity)!;

      // 清理物理资源
      this.cleanupPhysicsResources(component);

      // 移除组件
      this.components.delete(entity);

      if (this.config.debug) {
        console.log(`移除物理面部动画组件: ${entity.id}`);
      }
    }
  }

  /**
   * 清理物理资源
   * @param component 物理面部动画组件
   */
  private cleanupPhysicsResources(component: PhysicalFacialAnimationComponent): void {
    // 移除物理体
    for (const body of component.getBodies()) {
      const index = this.physicsWorld.bodies.indexOf(body);
      if (index !== -1) {
        this.physicsWorld.bodies.splice(index, 1);
      }
    }

    // 移除约束
    for (const constraint of component.getConstraints()) {
      const index = this.physicsWorld.constraints.indexOf(constraint);
      if (index !== -1) {
        this.physicsWorld.constraints.splice(index, 1);
      }
    }
  }

  /**
   * 获取物理面部动画组件
   * @param entity 实体
   * @returns 物理面部动画组件
   */
  public getPhysicalFacialAnimation(entity: Entity): PhysicalFacialAnimationComponent | undefined {
    return this.components.get(entity);
  }

  /**
   * 添加肌肉
   * @param entity 实体
   * @param muscleData 肌肉数据
   * @returns 是否成功添加
   */
  public addMuscle(entity: Entity, muscleData: MuscleData): boolean {
    const component = this.getPhysicalFacialAnimation(entity);
    if (!component) return false;

    // 添加肌肉
    return component.addMuscle(muscleData);
  }

  /**
   * 应用表情
   * @param entity 实体
   * @param expression 表情类型
   * @param weight 权重
   * @returns 是否成功应用
   */
  public applyExpression(entity: Entity, expression: FacialExpressionType, weight: number = 1.0): boolean {
    const component = this.getPhysicalFacialAnimation(entity);
    if (!component) return false;

    // 应用表情
    return component.applyExpression(expression, weight);
  }

  /**
   * 应用肌肉力
   * @param entity 实体
   * @param muscleName 肌肉名称
   * @param force 力
   * @returns 是否成功应用
   */
  public applyMuscleForce(entity: Entity, muscleName: string, force: THREE.Vector3): boolean {
    const component = this.getPhysicalFacialAnimation(entity);
    if (!component) return false;

    // 应用肌肉力
    return component.applyMuscleForce(muscleName, force);
  }

  /**
   * 设置肌肉刚度
   * @param entity 实体
   * @param muscleName 肌肉名称
   * @param stiffness 刚度
   * @returns 是否成功设置
   */
  public setMuscleStiffness(entity: Entity, muscleName: string, stiffness: number): boolean {
    const component = this.getPhysicalFacialAnimation(entity);
    if (!component) return false;

    // 设置肌肉刚度
    return component.setMuscleStiffness(muscleName, stiffness);
  }

  /**
   * 设置肌肉阻尼
   * @param entity 实体
   * @param muscleName 肌肉名称
   * @param damping 阻尼
   * @returns 是否成功设置
   */
  public setMuscleDamping(entity: Entity, muscleName: string, damping: number): boolean {
    const component = this.getPhysicalFacialAnimation(entity);
    if (!component) return false;

    // 设置肌肉阻尼
    return component.setMuscleDamping(muscleName, damping);
  }

  /**
   * 重置所有肌肉
   * @param entity 实体
   * @returns 是否成功重置
   */
  public resetAllMuscles(entity: Entity): boolean {
    const component = this.getPhysicalFacialAnimation(entity);
    if (!component) return false;

    // 重置所有肌肉
    return component.resetAllMuscles();
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 累积时间
    this.accumulatedTime += deltaTime;

    // 更新物理
    while (this.accumulatedTime >= this.physicsUpdateInterval) {
      this.updatePhysics(this.physicsUpdateInterval);
      this.accumulatedTime -= this.physicsUpdateInterval;
    }

    // 更新组件
    for (const [entity, component] of this.components.entries()) {
      // 更新组件
      component.update(deltaTime);

      // 同步到面部动画组件
      this.syncToFacialAnimation(entity, component);
    }

    // 更新调试渲染器
    if (this.config.showDebugInfo) {
      this.updateDebugRenderer();
    }
  }

  /**
   * 更新物理
   * @param deltaTime 帧间隔时间（秒）
   */
  private updatePhysics(deltaTime: number): void {
    // 更新物理世界
    this.physicsWorld.update(deltaTime);

    // 更新上次物理更新时间
    this._lastPhysicsUpdateTime = Date.now();
  }

  /**
   * 同步到面部动画组件
   * @param entity 实体
   * @param physicalComponent 物理面部动画组件
   */
  private syncToFacialAnimation(entity: Entity, physicalComponent: PhysicalFacialAnimationComponent): void {
    // 获取面部动画组件
    const facialAnimation = entity.getComponent(FacialAnimationComponent.TYPE) as FacialAnimationComponent;
    if (!facialAnimation) return;

    // 获取物理驱动的混合形状权重
    const blendShapeWeights = physicalComponent.getBlendShapeWeights();

    // 设置混合形状权重
    for (const [name, weight] of blendShapeWeights.entries()) {
      // 使用组件的方法设置混合形状权重
      if (typeof (facialAnimation as any).setBlendShapeWeight === 'function') {
        (facialAnimation as any).setBlendShapeWeight(name, weight);
      }
    }
  }

  /**
   * 更新调试渲染器
   */
  private updateDebugRenderer(): void {
    if (!this.debugRenderer || !this.debugScene) return;

    // 收集所有肌肉线段
    const positions: number[] = [];

    for (const component of this.components.values()) {
      // 获取肌肉
      const muscles = component.getMuscles();

      // 添加肌肉线段
      for (const muscle of muscles) {
        // 添加起始点和结束点
        positions.push(
          muscle.start.x, muscle.start.y, muscle.start.z,
          muscle.end.x, muscle.end.y, muscle.end.z
        );

        // 添加控制点（如果有）
        if (muscle.controlPoints && muscle.controlPoints.length > 0) {
          // 添加起始点到第一个控制点
          positions.push(
            muscle.start.x, muscle.start.y, muscle.start.z,
            muscle.controlPoints[0].x, muscle.controlPoints[0].y, muscle.controlPoints[0].z
          );

          // 添加控制点之间的线段
          for (let i = 0; i < muscle.controlPoints.length - 1; i++) {
            positions.push(
              muscle.controlPoints[i].x, muscle.controlPoints[i].y, muscle.controlPoints[i].z,
              muscle.controlPoints[i + 1].x, muscle.controlPoints[i + 1].y, muscle.controlPoints[i + 1].z
            );
          }

          // 添加最后一个控制点到结束点
          positions.push(
            muscle.controlPoints[muscle.controlPoints.length - 1].x,
            muscle.controlPoints[muscle.controlPoints.length - 1].y,
            muscle.controlPoints[muscle.controlPoints.length - 1].z,
            muscle.end.x, muscle.end.y, muscle.end.z
          );
        }
      }
    }

    // 更新调试渲染器几何体
    const geometry = this.debugRenderer.geometry;
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
    geometry.attributes.position.needsUpdate = true;
  }

  /**
   * 创建默认肌肉配置
   * @param entity 实体
   * @returns 是否成功创建
   */
  public createDefaultMuscleConfiguration(entity: Entity): boolean {
    const component = this.getPhysicalFacialAnimation(entity);
    if (!component) return false;

    // 创建下巴肌肉
    component.addMuscle({
      type: MuscleType.JAW,
      name: 'jaw_center',
      start: new THREE.Vector3(0, 0.05, 0.1),
      end: new THREE.Vector3(0, 0, 0.12),
      mass: 0.05,
      radius: 0.01,
      stiffness: 100,
      damping: 0.5,
      fixedStart: true,
      fixedEnd: false
    });

    // 创建左脸颊肌肉
    component.addMuscle({
      type: MuscleType.CHEEK,
      name: 'cheek_left',
      start: new THREE.Vector3(-0.07, 0.03, 0.1),
      end: new THREE.Vector3(-0.09, 0.02, 0.1),
      mass: 0.01,
      radius: 0.006,
      stiffness: 80,
      damping: 0.4,
      fixedStart: true,
      fixedEnd: false
    });

    // 创建右脸颊肌肉
    component.addMuscle({
      type: MuscleType.CHEEK,
      name: 'cheek_right',
      start: new THREE.Vector3(0.07, 0.03, 0.1),
      end: new THREE.Vector3(0.09, 0.02, 0.1),
      mass: 0.01,
      radius: 0.006,
      stiffness: 80,
      damping: 0.4,
      fixedStart: true,
      fixedEnd: false
    });

    // 创建左眉毛肌肉
    component.addMuscle({
      type: MuscleType.EYEBROW,
      name: 'eyebrow_left',
      start: new THREE.Vector3(-0.04, 0.08, 0.12),
      end: new THREE.Vector3(-0.06, 0.09, 0.12),
      mass: 0.005,
      radius: 0.004,
      stiffness: 120,
      damping: 0.3,
      fixedStart: true,
      fixedEnd: false
    });

    // 创建右眉毛肌肉
    component.addMuscle({
      type: MuscleType.EYEBROW,
      name: 'eyebrow_right',
      start: new THREE.Vector3(0.04, 0.08, 0.12),
      end: new THREE.Vector3(0.06, 0.09, 0.12),
      mass: 0.005,
      radius: 0.004,
      stiffness: 120,
      damping: 0.3,
      fixedStart: true,
      fixedEnd: false
    });

    // 创建上嘴唇肌肉
    component.addMuscle({
      type: MuscleType.LIP,
      name: 'lip_upper',
      start: new THREE.Vector3(0, 0.02, 0.14),
      end: new THREE.Vector3(0, 0.01, 0.15),
      mass: 0.008,
      radius: 0.005,
      stiffness: 90,
      damping: 0.4,
      fixedStart: true,
      fixedEnd: false
    });

    // 创建下嘴唇肌肉
    component.addMuscle({
      type: MuscleType.LIP,
      name: 'lip_lower',
      start: new THREE.Vector3(0, 0, 0.14),
      end: new THREE.Vector3(0, -0.01, 0.15),
      mass: 0.008,
      radius: 0.005,
      stiffness: 90,
      damping: 0.4,
      fixedStart: true,
      fixedEnd: false
    });

    // 初始化组件
    component.initialize();

    return true;
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (...args: any[]) => void): void {
    this._eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (...args: any[]) => void): void {
    this._eventEmitter.off(event, callback);
  }

  /**
   * 发射事件
   * @param event 事件名称
   * @param data 事件数据
   */
  public emitEvent(event: string, data?: any): void {
    this._eventEmitter.emit(event, data);
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 清理所有组件
    for (const [_, component] of this.components.entries()) {
      this.cleanupPhysicsResources(component);
    }
    this.components.clear();

    // 清理调试渲染器
    if (this.debugRenderer) {
      (this.debugRenderer.geometry as any).dispose();
      if (this.debugRenderer.material instanceof THREE.Material) {
        (this.debugRenderer.material as any).dispose();
      }
      this.debugRenderer = null;
    }

    if (this.debugScene) {
      this.debugScene.clear();
      this.debugScene = null;
    }

    // 清理物理世界
    if (this.physicsWorld) {
      this.physicsWorld.bodies = [];
      this.physicsWorld.constraints = [];
      this.physicsWorld = null;
    }

    if (this.config.debug) {
      console.log('物理驱动的面部动画系统已销毁');
    }
  }
}
