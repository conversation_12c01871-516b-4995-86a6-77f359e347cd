/**
 * 海洋生成器
 * 用于生成大型海洋水体
 */
import * as THREE from 'three';
import { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { WaterBodyComponent, WaterBodyType } from './WaterBodyComponent';
// import { WaterPresets, WaterPresetType } from './WaterPresets'; // 暂时不使用
import { TerrainComponent } from '../../terrain/components/TerrainComponent';
import { Debug } from '../../utils/Debug';

/**
 * 海洋生成配置
 */
export interface OceanGeneratorConfig {
  /** 海洋位置 */
  position: THREE.Vector3;
  /** 海洋尺寸 */
  size: {
    width: number;
    depth: number;
  };
  /** 海洋深度 */
  depth: number;
  /** 分辨率 */
  resolution?: number;
  /** 是否跟随地形 */
  followTerrain?: boolean;
  /** 地形偏移 */
  terrainOffset?: number;
  /** 是否生成海床 */
  generateSeabed?: boolean;
  /** 海床材质 */
  seabedMaterial?: any;
  /** 是否生成水下植被 */
  generateUnderwaterVegetation?: boolean;
  /** 水下植被密度 */
  underwaterVegetationDensity?: number;
  /** 是否生成水下粒子 */
  generateUnderwaterParticles?: boolean;
  /** 水下粒子数量 */
  underwaterParticleCount?: number;
  /** 是否生成波浪 */
  generateWaves?: boolean;
  /** 波浪高度 */
  waveHeight?: number;
  /** 波浪频率 */
  waveFrequency?: number;
  /** 波浪速度 */
  waveSpeed?: number;
  /** 波浪方向 */
  waveDirection?: THREE.Vector2;
  /** 是否生成海岸线 */
  generateCoastline?: boolean;
  /** 海岸线宽度 */
  coastlineWidth?: number;
  /** 海岸线高度 */
  coastlineHeight?: number;
  /** 是否生成海洋泡沫 */
  generateFoam?: boolean;
  /** 泡沫密度 */
  foamDensity?: number;
  /** 是否生成反射 */
  generateReflection?: boolean;
  /** 反射强度 */
  reflectionStrength?: number;
  /** 是否生成折射 */
  generateRefraction?: boolean;
  /** 折射强度 */
  refractionStrength?: number;
  /** 是否生成因果波纹 */
  generateCaustics?: boolean;
  /** 因果波纹强度 */
  causticsStrength?: number;
}

/**
 * 海洋生成器
 */
export class OceanGenerator {
  /** 世界 */
  private world: World;
  /** 配置 */
  private config: OceanGeneratorConfig;
  /** 地形组件 */
  private terrainComponent: TerrainComponent | null = null;
  /** 海洋实体 */
  private oceanEntity: Entity | null = null;
  /** 海洋水体组件 */
  private oceanWaterBody: WaterBodyComponent | null = null;
  /** 海洋几何体 */
  private oceanGeometry: THREE.BufferGeometry | null = null;
  /** 海床几何体 */
  private seabedGeometry: THREE.BufferGeometry | null = null;
  /** 海岸线几何体 */
  private coastlineGeometry: THREE.BufferGeometry | null = null;

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config: OceanGeneratorConfig) {
    this.world = world;
    this.config = {
      position: config.position,
      size: config.size,
      depth: config.depth,
      resolution: config.resolution || 64,
      followTerrain: config.followTerrain !== undefined ? config.followTerrain : true,
      terrainOffset: config.terrainOffset || 0.5,
      generateSeabed: config.generateSeabed !== undefined ? config.generateSeabed : true,
      generateUnderwaterVegetation: config.generateUnderwaterVegetation !== undefined ? config.generateUnderwaterVegetation : true,
      underwaterVegetationDensity: config.underwaterVegetationDensity || 0.001,
      generateUnderwaterParticles: config.generateUnderwaterParticles !== undefined ? config.generateUnderwaterParticles : true,
      underwaterParticleCount: config.underwaterParticleCount || 2000,
      generateWaves: config.generateWaves !== undefined ? config.generateWaves : true,
      waveHeight: config.waveHeight || 1.0,
      waveFrequency: config.waveFrequency || 0.1,
      waveSpeed: config.waveSpeed || 0.5,
      waveDirection: config.waveDirection || new THREE.Vector2(1, 1),
      generateCoastline: config.generateCoastline !== undefined ? config.generateCoastline : true,
      coastlineWidth: config.coastlineWidth || 20.0,
      coastlineHeight: config.coastlineHeight || 2.0,
      generateFoam: config.generateFoam !== undefined ? config.generateFoam : true,
      foamDensity: config.foamDensity || 0.5,
      generateReflection: config.generateReflection !== undefined ? config.generateReflection : true,
      reflectionStrength: config.reflectionStrength || 0.5,
      generateRefraction: config.generateRefraction !== undefined ? config.generateRefraction : true,
      refractionStrength: config.refractionStrength || 0.5,
      generateCaustics: config.generateCaustics !== undefined ? config.generateCaustics : true,
      causticsStrength: config.causticsStrength || 0.5
    };
  }

  /**
   * 设置地形组件
   * @param terrainComponent 地形组件
   */
  public setTerrainComponent(terrainComponent: TerrainComponent): void {
    this.terrainComponent = terrainComponent;
  }

  /**
   * 生成海洋
   * @returns 海洋实体
   */
  public generate(): Entity {
    // 创建海洋实体
    this.oceanEntity = new Entity();

    // 创建海洋几何体
    this.createOceanGeometry();

    // 创建海洋水体
    this.createOceanWaterBody();

    // 如果需要生成海床，创建海床
    if (this.config.generateSeabed) {
      this.createSeabed();
    }

    // 如果需要生成海岸线，创建海岸线
    if (this.config.generateCoastline) {
      this.createCoastline();
    }

    // 如果需要生成水下植被，创建水下植被
    if (this.config.generateUnderwaterVegetation) {
      this.createUnderwaterVegetation();
    }

    // 如果需要生成水下粒子，创建水下粒子
    if (this.config.generateUnderwaterParticles) {
      this.createUnderwaterParticles();
    }

    // 添加到世界
    this.world.addEntity(this.oceanEntity!);

    return this.oceanEntity!;
  }

  /**
   * 创建海洋几何体
   */
  private createOceanGeometry(): void {
    // 创建平面几何体
    const geometry = new THREE.PlaneGeometry(
      this.config.size.width,
      this.config.size.depth,
      this.config.resolution!,
      this.config.resolution!
    );

    // 旋转几何体使其水平
    geometry.rotateX(-Math.PI / 2);

    // 移动几何体到指定位置
    geometry.translate(
      (this.config as any).getPosition().x,
      (this.config as any).getPosition().y,
      (this.config as any).getPosition().z
    );

    // 如果需要跟随地形，调整高度
    if (this.config.followTerrain && this.terrainComponent) {
      const positions = geometry.getAttribute('position');

      for (let i = 0; i < positions.count; i++) {
        const x = positions.getX(i);
        const z = positions.getZ(i);

        const height = this.getTerrainHeight(x, z);
        if (height !== null) {
          positions.setY(i, height + this.config.terrainOffset!);
        }
      }

      positions.needsUpdate = true;
    }

    // 保存几何体
    this.oceanGeometry = geometry;
  }

  /**
   * 创建海洋水体
   */
  private createOceanWaterBody(): void {
    if (!this.oceanGeometry) {
      Debug.error('OceanGenerator', '海洋几何体未创建，无法创建海洋水体');
      return;
    }

    // 创建海洋水体组件
    this.oceanWaterBody = new WaterBodyComponent(this.oceanEntity!, {
      type: WaterBodyType.OCEAN,
      waveParams: this.config.generateWaves ? {
        amplitude: this.config.waveHeight!,
        frequency: this.config.waveFrequency!,
        speed: this.config.waveSpeed!,
        direction: {
          x: this.config.waveDirection!.x,
          z: this.config.waveDirection!.y
        }
      } : undefined
    });

    // 添加到实体
    this.oceanEntity!.addComponent(this.oceanWaterBody);
  }

  /**
   * 创建海床
   */
  private createSeabed(): void {
    if (!this.oceanGeometry) {
      Debug.error('OceanGenerator', '海洋几何体未创建，无法创建海床');
      return;
    }

    // 复制海洋几何体
    const geometry = this.oceanGeometry.clone();

    // 获取位置属性
    const positions = geometry.getAttribute('position');

    // 降低海床高度
    for (let i = 0; i < positions.count; i++) {
      const y = positions.getY(i);
      positions.setY(i, y - this.config.depth);
    }

    // 更新位置属性
    positions.needsUpdate = true;

    // 保存几何体
    this.seabedGeometry = geometry;

    // 创建海床材质
    const material = this.config.seabedMaterial || new THREE.MeshStandardMaterial({
      color: 0x7B5E3D,
      roughness: 0.9,
      metalness: 0.1
    });

    // 创建海床网格
    const mesh = new THREE.Mesh(this.seabedGeometry, material);

    // 添加到实体的Transform组件
    this.oceanEntity!.getTransform().getObject3D().add(mesh);
  }

  /**
   * 创建海岸线
   */
  private createCoastline(): void {
    if (!this.oceanGeometry || !this.terrainComponent) {
      Debug.error('OceanGenerator', '海洋几何体或地形组件未创建，无法创建海岸线');
      return;
    }

    // 获取海洋边缘点
    const edgePoints = this.getOceanEdgePoints();

    // 如果没有边缘点，跳过
    if (edgePoints.length === 0) {
      Debug.warn('OceanGenerator', '未找到海洋边缘点，无法创建海岸线');
      return;
    }

    // 创建几何体
    const geometry = new THREE.BufferGeometry();

    // 创建顶点和索引
    const vertices: number[] = [];
    const indices: number[] = [];
    const uvs: number[] = [];

    // 遍历边缘点
    for (let i = 0; i < edgePoints.length; i++) {
      const point = edgePoints[i];

      // 获取海洋高度
      const oceanHeight = point.y;

      // 计算海岸线内外点
      const normal = new THREE.Vector3(point.x - (this.config as any).getPosition().x, 0, point.z - (this.config as any).getPosition().z).normalize();
      const innerPoint = new THREE.Vector3(point.x, oceanHeight, point.z);
      const outerPoint = new THREE.Vector3(
        point.x + normal.x * this.config.coastlineWidth!,
        oceanHeight - this.config.coastlineHeight!,
        point.z + normal.z * this.config.coastlineWidth!
      );

      // 如果需要跟随地形，调整高度
      if (this.config.followTerrain && this.terrainComponent) {
        const height = this.getTerrainHeight(outerPoint.x, outerPoint.z);
        if (height !== null) {
          outerPoint.y = height;
        }
      }

      // 添加顶点
      vertices.push(innerPoint.x, innerPoint.y, innerPoint.z);
      vertices.push(outerPoint.x, outerPoint.y, outerPoint.z);

      // 添加UV
      const u = i / edgePoints.length;
      uvs.push(0, u);
      uvs.push(1, u);

      // 添加索引（创建三角形）
      if (i < edgePoints.length - 1) {
        const base = i * 2;
        indices.push(base, base + 1, base + 2);
        indices.push(base + 1, base + 3, base + 2);
      }
    }

    // 设置几何体属性
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
    geometry.setIndex(indices);

    // 计算法线
    geometry.computeVertexNormals();

    // 保存几何体
    this.coastlineGeometry = geometry;

    // 创建海岸线材质
    const material = new THREE.MeshStandardMaterial({
      color: 0xD2B48C,
      roughness: 0.8,
      metalness: 0.1
    });

    // 创建海岸线网格
    const mesh = new THREE.Mesh(this.coastlineGeometry, material);

    // 添加到实体的Transform组件
    this.oceanEntity!.getTransform().getObject3D().add(mesh);
  }

  /**
   * 获取海洋边缘点
   * @returns 边缘点数组
   */
  private getOceanEdgePoints(): THREE.Vector3[] {
    if (!this.oceanGeometry) {
      return [];
    }

    // 获取位置属性
    const positions = this.oceanGeometry.getAttribute('position');

    // 创建边缘点数组
    const edgePoints: THREE.Vector3[] = [];

    // 获取海洋尺寸
    // const width = this.config.size.width; // 暂时不使用
    // const depth = this.config.size.depth; // 暂时不使用

    // 获取分辨率
    const resolution = this.config.resolution!;

    // 遍历边缘点
    for (let i = 0; i <= resolution; i++) {
      // 左边缘
      const leftIndex = i * (resolution + 1);
      edgePoints.push(new THREE.Vector3(
        positions.getX(leftIndex),
        positions.getY(leftIndex),
        positions.getZ(leftIndex)
      ));

      // 右边缘
      const rightIndex = i * (resolution + 1) + resolution;
      edgePoints.push(new THREE.Vector3(
        positions.getX(rightIndex),
        positions.getY(rightIndex),
        positions.getZ(rightIndex)
      ));

      // 上边缘
      const topIndex = i;
      edgePoints.push(new THREE.Vector3(
        positions.getX(topIndex),
        positions.getY(topIndex),
        positions.getZ(topIndex)
      ));

      // 下边缘
      const bottomIndex = resolution * (resolution + 1) + i;
      edgePoints.push(new THREE.Vector3(
        positions.getX(bottomIndex),
        positions.getY(bottomIndex),
        positions.getZ(bottomIndex)
      ));
    }

    return edgePoints;
  }

  /**
   * 创建水下植被
   */
  private createUnderwaterVegetation(): void {
    if (!this.oceanGeometry) {
      Debug.error('OceanGenerator', '海洋几何体未创建，无法创建水下植被');
      return;
    }

    // 计算植被数量
    const area = this.config.size.width * this.config.size.depth;
    const vegetationCount = Math.floor(area * this.config.underwaterVegetationDensity!);

    // 创建随机数生成器
    const random = this.seededRandom(12345);

    // 创建植被
    for (let i = 0; i < vegetationCount; i++) {
      // 随机位置
      const x = (this.config as any).getPosition().x + (random() - 0.5) * this.config.size.width * 0.8;
      const z = (this.config as any).getPosition().z + (random() - 0.5) * this.config.size.depth * 0.8;

      // 获取海洋高度
      let y = (this.config as any).getPosition().y;
      if (this.config.followTerrain && this.terrainComponent) {
        const height = this.getTerrainHeight(x, z);
        if (height !== null) {
          y = height + this.config.terrainOffset!;
        }
      }

      // 随机深度
      y = y - this.config.depth * (0.2 + random() * 0.8);

      // 创建植被几何体
      const geometry = new THREE.CylinderGeometry(0.1, 0.5, 1 + random() * 5, 4, 1);

      // 创建植被材质
      const material = new THREE.MeshStandardMaterial({
        color: new THREE.Color(0.1 + random() * 0.2, 0.5 + random() * 0.3, 0.1 + random() * 0.2),
        roughness: 0.8,
        metalness: 0.1
      });

      // 创建植被网格
      const mesh = new THREE.Mesh(geometry, material);

      // 设置位置
      mesh.position.set(x, y, z);

      // 随机旋转
      (mesh as any).setRotationQuaternion(random() * 0.3, random() * Math.PI * 2, random() * 0.3);

      // 添加到实体的Transform组件
      this.oceanEntity!.getTransform().getObject3D().add(mesh);
    }
  }

  /**
   * 获取指定位置的地形高度
   * @param x X坐标
   * @param z Z坐标
   * @returns 地形高度，如果无法获取则返回null
   */
  private getTerrainHeight(x: number, z: number): number | null {
    if (!this.terrainComponent) {
      return null;
    }

    // 调用地形组件的获取高度方法
    return this.terrainComponent.getHeight(x, z);
  }

  /**
   * 创建水下粒子
   */
  private createUnderwaterParticles(): void {
    if (!this.oceanWaterBody) {
      Debug.error('OceanGenerator', '海洋水体未创建，无法创建水下粒子');
      return;
    }

    // 创建粒子系统
    const particleSystem = new THREE.Points(
      new THREE.BufferGeometry(),
      new THREE.PointsMaterial({
        color: 0xFFFFFF,
        size: 0.05,
        transparent: true,
        opacity: 0.3,
        map: new THREE.TextureLoader().load('/assets/textures/particle.png'),
        blending: THREE.AdditiveBlending,
        depthWrite: false
      })
    );

    // 创建随机数生成器
    const random = this.seededRandom(54321);

    // 创建粒子位置
    const positions: number[] = [];
    const velocities: number[] = [];
    const lifetimes: number[] = [];

    // 创建粒子
    for (let i = 0; i < this.config.underwaterParticleCount!; i++) {
      // 随机位置
      const x = (this.config as any).getPosition().x + (random() - 0.5) * this.config.size.width * 0.8;
      const z = (this.config as any).getPosition().z + (random() - 0.5) * this.config.size.depth * 0.8;

      // 获取海洋高度
      let y = (this.config as any).getPosition().y;
      if (this.config.followTerrain && this.terrainComponent) {
        const height = this.getTerrainHeight(x, z);
        if (height !== null) {
          y = height + this.config.terrainOffset!;
        }
      }

      // 随机深度
      y = y - random() * this.config.depth;

      // 添加位置
      positions.push(x, y, z);

      // 添加速度（缓慢上升）
      const vx = (random() * 2 - 1) * 0.01;
      const vy = 0.01 + random() * 0.02;
      const vz = (random() * 2 - 1) * 0.01;
      velocities.push(vx, vy, vz);

      // 添加生命周期
      lifetimes.push(random() * 5 + 5);
    }

    // 设置几何体属性
    particleSystem.geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
    particleSystem.geometry.setAttribute('velocity', new THREE.Float32BufferAttribute(velocities, 3));
    particleSystem.geometry.setAttribute('lifetime', new THREE.Float32BufferAttribute(lifetimes, 1));

    // 添加到实体的Transform组件
    this.oceanEntity!.getTransform().getObject3D().add(particleSystem);

    // TODO: 添加更新函数（暂时注释，因为没有回调机制）
    /*
    const updateParticles = (deltaTime: number) => {
      const positions = particleSystem.geometry.getAttribute('position') as THREE.BufferAttribute;
      const velocities = particleSystem.geometry.getAttribute('velocity') as THREE.BufferAttribute;
      const lifetimes = particleSystem.geometry.getAttribute('lifetime') as THREE.BufferAttribute;

      for (let i = 0; i < positions.count; i++) {
        // 更新位置
        positions.setXYZ(
          i,
          positions.getX(i) + velocities.getX(i) * deltaTime,
          positions.getY(i) + velocities.getY(i) * deltaTime,
          positions.getZ(i) + velocities.getZ(i) * deltaTime
        );

        // 更新生命周期
        lifetimes.setX(i, lifetimes.getX(i) - deltaTime);

        // 如果生命周期结束或者粒子超出水面，重置粒子
        if (lifetimes.getX(i) <= 0 || positions.getY(i) > (this.config as any).getPosition().y) {
          // 随机位置
          const x = (this.config as any).getPosition().x + (random() - 0.5) * this.config.size.width * 0.8;
          const z = (this.config as any).getPosition().z + (random() - 0.5) * this.config.size.depth * 0.8;

          // 获取海洋高度
          let y = (this.config as any).getPosition().y;
          if (this.config.followTerrain && this.terrainComponent) {
            const height = this.getTerrainHeight(x, z);
            if (height !== null) {
              y = height + this.config.terrainOffset!;
            }
          }

          // 随机深度
          y = y - random() * this.config.depth * 0.2;

          // 重置位置
          positions.setXYZ(i, x, y, z);

          // 重置速度
          const vx = (random() * 2 - 1) * 0.01;
          const vy = 0.01 + random() * 0.02;
          const vz = (random() * 2 - 1) * 0.01;
          velocities.setXYZ(i, vx, vy, vz);

          // 重置生命周期
          lifetimes.setX(i, random() * 5 + 5);
        }
      }

      // 更新几何体
      positions.needsUpdate = true;
      lifetimes.needsUpdate = true;
    };
    */

    // TODO: 添加更新函数到水体组件
    // this.oceanWaterBody.addUpdateCallback(updateParticles); // 方法不存在，需要实现
  }

  /**
   * 基于种子的随机数生成器
   * @param seed 种子
   * @returns 随机数生成函数
   */
  private seededRandom(seed: number): () => number {
    let s = seed;
    return function() {
      s = Math.sin(s) * 10000;
      return s - Math.floor(s);
    };
  }
}
