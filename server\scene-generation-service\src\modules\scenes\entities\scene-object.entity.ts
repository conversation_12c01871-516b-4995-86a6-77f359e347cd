import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  Index,
} from 'typeorm';
import { Scene } from './scene.entity';

export enum ObjectType {
  MESH = 'mesh',
  LIGHT = 'light',
  CAMERA = 'camera',
  GROUP = 'group',
  PARTICLE_SYSTEM = 'particle_system',
  AUDIO_SOURCE = 'audio_source',
  COLLIDER = 'collider',
  TRIGGER = 'trigger',
}

export enum ObjectCategory {
  FURNITURE = 'furniture',
  DECORATION = 'decoration',
  LIGHTING = 'lighting',
  VEGETATION = 'vegetation',
  ARCHITECTURE = 'architecture',
  VEHICLE = 'vehicle',
  CHARACTER = 'character',
  PROP = 'prop',
  ENVIRONMENT = 'environment',
  EFFECT = 'effect',
}

@Entity('scene_objects')
@Index(['sceneId', 'type'])
@Index(['category', 'type'])
export class SceneObject {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: ObjectType,
    default: ObjectType.MESH,
  })
  @Index()
  type: ObjectType;

  @Column({
    type: 'enum',
    enum: ObjectCategory,
    nullable: true,
  })
  @Index()
  category: ObjectCategory;

  // 变换信息
  @Column({ type: 'json' })
  transform: {
    position: { x: number; y: number; z: number };
    rotation: { x: number; y: number; z: number; w: number }; // 四元数
    scale: { x: number; y: number; z: number };
  };

  // 几何信息
  @Column({ type: 'json', nullable: true })
  geometry: {
    type: string; // box, sphere, plane, custom等
    parameters: Record<string, any>;
    vertices?: number;
    faces?: number;
    boundingBox?: {
      min: { x: number; y: number; z: number };
      max: { x: number; y: number; z: number };
    };
  };

  // 材质信息
  @Column({ type: 'json', nullable: true })
  material: {
    type: string; // standard, pbr, unlit等
    properties: Record<string, any>;
    textures?: Record<string, string>; // 纹理URL映射
    color?: { r: number; g: number; b: number; a: number };
    metallic?: number;
    roughness?: number;
    emission?: { r: number; g: number; b: number };
  };

  // 物理属性
  @Column({ type: 'json', nullable: true })
  physics: {
    enabled: boolean;
    type: 'static' | 'dynamic' | 'kinematic';
    mass?: number;
    friction?: number;
    restitution?: number;
    collisionShape?: string;
    isTrigger?: boolean;
  };

  // 动画信息
  @Column({ type: 'json', nullable: true })
  animation: {
    clips: Array<{
      name: string;
      duration: number;
      loop: boolean;
      autoPlay: boolean;
    }>;
    currentClip?: string;
    speed?: number;
  };

  // 交互属性
  @Column({ type: 'json', nullable: true })
  interaction: {
    clickable: boolean;
    hoverable: boolean;
    draggable: boolean;
    events?: Record<string, string>; // 事件处理器
  };

  // 渲染属性
  @Column({ type: 'json', nullable: true })
  rendering: {
    visible: boolean;
    castShadow: boolean;
    receiveShadow: boolean;
    renderOrder: number;
    frustumCulled: boolean;
    matrixAutoUpdate: boolean;
  };

  // 资源引用
  @Column({ name: 'asset_id', nullable: true })
  assetId: string; // 关联的资源ID

  @Column({ name: 'asset_url', nullable: true })
  assetUrl: string; // 资源文件URL

  @Column({ name: 'asset_type', nullable: true })
  assetType: string; // 资源类型

  // 层级关系
  @Column({ name: 'parent_id', nullable: true })
  parentId: string; // 父对象ID

  @Column({ name: 'child_ids', type: 'text', array: true, default: [] })
  childIds: string[]; // 子对象ID数组

  @Column({ name: 'layer_index', default: 0 })
  layerIndex: number; // 层级索引

  // 标签和元数据
  @Column({ type: 'text', array: true, default: [] })
  tags: string[];

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>; // 自定义元数据

  // 性能信息
  @Column({ name: 'polygon_count', type: 'integer', nullable: true })
  polygonCount: number;

  @Column({ name: 'vertex_count', type: 'integer', nullable: true })
  vertexCount: number;

  @Column({ name: 'texture_memory_mb', type: 'float', nullable: true })
  textureMemoryMb: number;

  @Column({ name: 'geometry_memory_mb', type: 'float', nullable: true })
  geometryMemoryMb: number;

  // 状态信息
  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'is_locked', default: false })
  isLocked: boolean; // 是否锁定编辑

  @Column({ name: 'is_selected', default: false })
  isSelected: boolean; // 是否选中

  @Column({ name: 'is_hidden', default: false })
  isHidden: boolean; // 是否隐藏

  // 关联关系
  @ManyToOne(() => Scene, scene => scene.objects, { onDelete: 'CASCADE' })
  scene: Scene;

  @Column({ name: 'scene_id' })
  @Index()
  sceneId: string;

  // 时间戳
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 计算属性
  get displayName(): string {
    return this.name || `${this.type}_${this.id.slice(0, 8)}`;
  }

  get typeDisplayName(): string {
    const typeNames = {
      [ObjectType.MESH]: '网格',
      [ObjectType.LIGHT]: '光源',
      [ObjectType.CAMERA]: '相机',
      [ObjectType.GROUP]: '组',
      [ObjectType.PARTICLE_SYSTEM]: '粒子系统',
      [ObjectType.AUDIO_SOURCE]: '音频源',
      [ObjectType.COLLIDER]: '碰撞器',
      [ObjectType.TRIGGER]: '触发器',
    };
    return typeNames[this.type] || this.type;
  }

  get categoryDisplayName(): string {
    if (!this.category) return '未分类';
    
    const categoryNames = {
      [ObjectCategory.FURNITURE]: '家具',
      [ObjectCategory.DECORATION]: '装饰',
      [ObjectCategory.LIGHTING]: '照明',
      [ObjectCategory.VEGETATION]: '植物',
      [ObjectCategory.ARCHITECTURE]: '建筑',
      [ObjectCategory.VEHICLE]: '载具',
      [ObjectCategory.CHARACTER]: '角色',
      [ObjectCategory.PROP]: '道具',
      [ObjectCategory.ENVIRONMENT]: '环境',
      [ObjectCategory.EFFECT]: '特效',
    };
    return categoryNames[this.category] || this.category;
  }

  get position(): { x: number; y: number; z: number } {
    return this.transform.position;
  }

  get rotation(): { x: number; y: number; z: number; w: number } {
    return this.transform.rotation;
  }

  get scale(): { x: number; y: number; z: number } {
    return this.transform.scale;
  }

  get isVisible(): boolean {
    return !this.isHidden && this.rendering?.visible !== false;
  }

  get hasPhysics(): boolean {
    return this.physics?.enabled === true;
  }

  get hasAnimation(): boolean {
    return this.animation?.clips && this.animation.clips.length > 0;
  }

  get hasInteraction(): boolean {
    return this.interaction?.clickable || this.interaction?.hoverable || this.interaction?.draggable;
  }

  get memoryUsageMb(): number {
    return (this.textureMemoryMb || 0) + (this.geometryMemoryMb || 0);
  }

  get complexityScore(): number {
    const polygonScore = Math.min(1, (this.polygonCount || 0) / 10000);
    const textureScore = Math.min(1, (this.textureMemoryMb || 0) / 100);
    return (polygonScore + textureScore) / 2;
  }

  // 更新变换
  updateTransform(
    position?: { x: number; y: number; z: number },
    rotation?: { x: number; y: number; z: number; w: number },
    scale?: { x: number; y: number; z: number }
  ): void {
    if (position) {
      this.transform.position = position;
    }
    if (rotation) {
      this.transform.rotation = rotation;
    }
    if (scale) {
      this.transform.scale = scale;
    }
    this.updatedAt = new Date();
  }

  // 设置可见性
  setVisible(visible: boolean): void {
    this.isHidden = !visible;
    if (this.rendering) {
      this.rendering.visible = visible;
    } else {
      this.rendering = { visible, castShadow: true, receiveShadow: true, renderOrder: 0, frustumCulled: true, matrixAutoUpdate: true };
    }
    this.updatedAt = new Date();
  }

  // 设置选中状态
  setSelected(selected: boolean): void {
    this.isSelected = selected;
    this.updatedAt = new Date();
  }

  // 锁定/解锁
  setLocked(locked: boolean): void {
    this.isLocked = locked;
    this.updatedAt = new Date();
  }

  // 添加标签
  addTag(tag: string): void {
    if (!this.tags.includes(tag)) {
      this.tags.push(tag);
      this.updatedAt = new Date();
    }
  }

  // 移除标签
  removeTag(tag: string): void {
    const index = this.tags.indexOf(tag);
    if (index > -1) {
      this.tags.splice(index, 1);
      this.updatedAt = new Date();
    }
  }

  // 更新材质
  updateMaterial(material: Partial<SceneObject['material']>): void {
    this.material = { ...this.material, ...material };
    this.updatedAt = new Date();
  }

  // 启用/禁用物理
  setPhysicsEnabled(enabled: boolean): void {
    if (!this.physics) {
      this.physics = {
        enabled,
        type: 'static',
        mass: 1,
        friction: 0.5,
        restitution: 0.3,
      };
    } else {
      this.physics.enabled = enabled;
    }
    this.updatedAt = new Date();
  }
}
