/**
 * 场景服务
 */
import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Scene } from './entities/scene.entity';
import { SceneEntity } from './entities/scene-entity.entity';
import { CreateSceneDto } from './dto/create-scene.dto';
import { UpdateSceneDto } from './dto/update-scene.dto';
import { CreateSceneEntityDto } from './dto/create-scene-entity.dto';
import { UpdateSceneEntityDto } from './dto/update-scene-entity.dto';
import { ProjectsService } from '../projects/projects.service';
import { ProjectMemberRole } from '../projects/entities/project-member.entity';

@Injectable()
export class ScenesService {
  constructor(
    @InjectRepository(Scene)
    private readonly sceneRepository: Repository<Scene>,
    @InjectRepository(SceneEntity)
    private readonly sceneEntityRepository: Repository<SceneEntity>,
    private readonly projectsService: ProjectsService,
  ) {}

  /**
   * 创建场景
   */
  async create(projectId: string, userId: string, createSceneDto: CreateSceneDto): Promise<Scene> {
    // 检查项目权限
    await this.projectsService.checkPermission(projectId, userId, [
      ProjectMemberRole.OWNER,
      ProjectMemberRole.ADMIN,
      ProjectMemberRole.EDITOR,
    ]);

    // 如果设置为默认场景，需要将其他场景设置为非默认
    if (createSceneDto.isDefault) {
      await this.sceneRepository.update({ projectId, isDefault: true }, { isDefault: false });
    }

    // 创建场景
    const scene = this.sceneRepository.create({
      ...createSceneDto,
      projectId,
    });

    return this.sceneRepository.save(scene);
  }

  /**
   * 查找项目的所有场景
   */
  async findAll(projectId: string, userId: string): Promise<Scene[]> {
    // 检查项目权限
    await this.projectsService.checkPermission(projectId, userId, [
      ProjectMemberRole.OWNER,
      ProjectMemberRole.ADMIN,
      ProjectMemberRole.EDITOR,
      ProjectMemberRole.VIEWER,
    ]);

    return this.sceneRepository.find({
      where: { projectId },
      relations: ['entities'],
      order: {
        isDefault: 'DESC',
        createdAt: 'ASC',
      },
    });
  }

  /**
   * 查找单个场景
   */
  async findOne(id: string, userId: string): Promise<Scene> {
    const scene = await this.sceneRepository.findOne({
      where: { id },
      relations: ['entities'],
    });

    if (!scene) {
      throw new NotFoundException(`场景ID ${id} 不存在`);
    }

    // 检查项目权限
    await this.projectsService.checkPermission(scene.projectId, userId, [
      ProjectMemberRole.OWNER,
      ProjectMemberRole.ADMIN,
      ProjectMemberRole.EDITOR,
      ProjectMemberRole.VIEWER,
    ]);

    return scene;
  }

  /**
   * 更新场景
   */
  async update(id: string, userId: string, updateSceneDto: UpdateSceneDto): Promise<Scene> {
    const scene = await this.findOne(id, userId);

    // 检查项目权限
    await this.projectsService.checkPermission(scene.projectId, userId, [
      ProjectMemberRole.OWNER,
      ProjectMemberRole.ADMIN,
      ProjectMemberRole.EDITOR,
    ]);

    // 如果设置为默认场景，需要将其他场景设置为非默认
    if (updateSceneDto.isDefault) {
      await this.sceneRepository.update({ projectId: scene.projectId, isDefault: true }, { isDefault: false });
    }

    // 更新场景
    Object.assign(scene, updateSceneDto);
    return this.sceneRepository.save(scene);
  }

  /**
   * 删除场景
   */
  async remove(id: string, userId: string): Promise<void> {
    const scene = await this.findOne(id, userId);

    // 检查项目权限
    await this.projectsService.checkPermission(scene.projectId, userId, [
      ProjectMemberRole.OWNER,
      ProjectMemberRole.ADMIN,
      ProjectMemberRole.EDITOR,
    ]);

    // 不能删除默认场景
    if (scene.isDefault) {
      throw new ForbiddenException('不能删除默认场景');
    }

    await this.sceneRepository.remove(scene);
  }

  /**
   * 创建场景实体
   */
  async createEntity(sceneId: string, userId: string, createEntityDto: CreateSceneEntityDto): Promise<SceneEntity> {
    const scene = await this.findOne(sceneId, userId);

    // 检查项目权限
    await this.projectsService.checkPermission(scene.projectId, userId, [
      ProjectMemberRole.OWNER,
      ProjectMemberRole.ADMIN,
      ProjectMemberRole.EDITOR,
    ]);

    // 如果指定了父实体，检查父实体是否存在
    if (createEntityDto.parentId) {
      const parentEntity = await this.sceneEntityRepository.findOne({
        where: { id: createEntityDto.parentId, sceneId },
      });

      if (!parentEntity) {
        throw new NotFoundException(`父实体ID ${createEntityDto.parentId} 不存在`);
      }
    }

    // 创建实体
    const entity = this.sceneEntityRepository.create({
      ...createEntityDto,
      sceneId,
    });

    return this.sceneEntityRepository.save(entity);
  }

  /**
   * 查找场景的所有实体
   */
  async findAllEntities(sceneId: string, userId: string): Promise<SceneEntity[]> {
    const _scene = await this.findOne(sceneId, userId);

    return this.sceneEntityRepository.find({
      where: { sceneId },
      order: {
        createdAt: 'ASC',
      },
    });
  }

  /**
   * 查找单个实体
   */
  async findOneEntity(id: string, userId: string): Promise<SceneEntity> {
    const entity = await this.sceneEntityRepository.findOne({
      where: { id },
      relations: ['scene'],
    });

    if (!entity) {
      throw new NotFoundException(`实体ID ${id} 不存在`);
    }

    // 检查项目权限
    await this.projectsService.checkPermission(entity.scene.projectId, userId, [
      ProjectMemberRole.OWNER,
      ProjectMemberRole.ADMIN,
      ProjectMemberRole.EDITOR,
      ProjectMemberRole.VIEWER,
    ]);

    return entity;
  }

  /**
   * 更新实体
   */
  async updateEntity(id: string, userId: string, updateEntityDto: UpdateSceneEntityDto): Promise<SceneEntity> {
    const entity = await this.findOneEntity(id, userId);

    // 检查项目权限
    await this.projectsService.checkPermission(entity.scene.projectId, userId, [
      ProjectMemberRole.OWNER,
      ProjectMemberRole.ADMIN,
      ProjectMemberRole.EDITOR,
    ]);

    // 如果指定了父实体，检查父实体是否存在
    if (updateEntityDto.parentId) {
      const parentEntity = await this.sceneEntityRepository.findOne({
        where: { id: updateEntityDto.parentId, sceneId: entity.sceneId },
      });

      if (!parentEntity) {
        throw new NotFoundException(`父实体ID ${updateEntityDto.parentId} 不存在`);
      }

      // 防止循环引用
      if (updateEntityDto.parentId === entity.id) {
        throw new ForbiddenException('不能将实体设置为自己的父实体');
      }
    }

    // 更新实体
    if (updateEntityDto.transform) {
      entity.transform = {
        ...entity.transform,
        ...updateEntityDto.transform,
      };
      delete updateEntityDto.transform;
    }

    if (updateEntityDto.components) {
      entity.components = {
        ...entity.components,
        ...updateEntityDto.components,
      };
      delete updateEntityDto.components;
    }

    if (updateEntityDto.metadata) {
      entity.metadata = {
        ...entity.metadata,
        ...updateEntityDto.metadata,
      };
      delete updateEntityDto.metadata;
    }

    Object.assign(entity, updateEntityDto);
    return this.sceneEntityRepository.save(entity);
  }

  /**
   * 删除实体
   */
  async removeEntity(id: string, userId: string): Promise<void> {
    const entity = await this.findOneEntity(id, userId);

    // 检查项目权限
    await this.projectsService.checkPermission(entity.scene.projectId, userId, [
      ProjectMemberRole.OWNER,
      ProjectMemberRole.ADMIN,
      ProjectMemberRole.EDITOR,
    ]);

    await this.sceneEntityRepository.remove(entity);
  }
}
