import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { CategoriesService } from './categories.service';
import { CreateCategoryDto, UpdateCategoryDto, CategoryOrderDto } from './dto/create-category.dto';
import { TemplateCategory } from './entities/template-category.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../auth/entities/user.entity';

@ApiTags('categories')
@Controller('categories')
export class CategoriesController {
  constructor(private readonly categoriesService: CategoriesService) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MODERATOR)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建分类' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '分类创建成功',
    type: TemplateCategory,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: '权限不足',
  })
  async create(@Body() createCategoryDto: CreateCategoryDto): Promise<TemplateCategory> {
    return await this.categoriesService.create(createCategoryDto);
  }

  @Get()
  @ApiOperation({ summary: '获取分类树' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '分类树获取成功',
    type: [TemplateCategory],
  })
  async findAll(): Promise<TemplateCategory[]> {
    return await this.categoriesService.findAll();
  }

  @Get('flat')
  @ApiOperation({ summary: '获取扁平化分类列表' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '分类列表获取成功',
    type: [TemplateCategory],
  })
  async findFlat(): Promise<TemplateCategory[]> {
    return await this.categoriesService.findFlat();
  }

  @Get('roots')
  @ApiOperation({ summary: '获取根分类' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '根分类获取成功',
    type: [TemplateCategory],
  })
  async findRoots(): Promise<TemplateCategory[]> {
    return await this.categoriesService.findRoots();
  }

  @Get('slug/:slug')
  @ApiOperation({ summary: '根据标识获取分类' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '分类获取成功',
    type: TemplateCategory,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '分类不存在',
  })
  @ApiParam({ name: 'slug', description: '分类标识' })
  async findBySlug(@Param('slug') slug: string): Promise<TemplateCategory> {
    return await this.categoriesService.findBySlug(slug);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取分类详情' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '分类详情获取成功',
    type: TemplateCategory,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '分类不存在',
  })
  @ApiParam({ name: 'id', description: '分类ID' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<TemplateCategory> {
    return await this.categoriesService.findOne(id);
  }

  @Get(':id/children')
  @ApiOperation({ summary: '获取分类的子分类' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '子分类获取成功',
    type: [TemplateCategory],
  })
  @ApiParam({ name: 'id', description: '分类ID' })
  async findChildren(@Param('id', ParseUUIDPipe) id: string): Promise<TemplateCategory[]> {
    return await this.categoriesService.findChildren(id);
  }

  @Get(':id/ancestors')
  @ApiOperation({ summary: '获取分类的祖先分类' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '祖先分类获取成功',
    type: [TemplateCategory],
  })
  @ApiParam({ name: 'id', description: '分类ID' })
  async findAncestors(@Param('id', ParseUUIDPipe) id: string): Promise<TemplateCategory[]> {
    return await this.categoriesService.findAncestors(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MODERATOR)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新分类' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '分类更新成功',
    type: TemplateCategory,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '分类不存在',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: '权限不足',
  })
  @ApiParam({ name: 'id', description: '分类ID' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateCategoryDto: UpdateCategoryDto,
  ): Promise<TemplateCategory> {
    return await this.categoriesService.update(id, updateCategoryDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MODERATOR)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除分类' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: '分类删除成功',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '分类不存在',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '分类下有子分类或模板，无法删除',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: '权限不足',
  })
  @ApiParam({ name: 'id', description: '分类ID' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    await this.categoriesService.remove(id);
  }

  @Patch(':id/toggle-active')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MODERATOR)
  @ApiBearerAuth()
  @ApiOperation({ summary: '切换分类启用状态' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '状态切换成功',
    type: TemplateCategory,
  })
  @ApiParam({ name: 'id', description: '分类ID' })
  async toggleActive(@Param('id', ParseUUIDPipe) id: string): Promise<TemplateCategory> {
    return await this.categoriesService.toggleActive(id);
  }

  @Patch('order')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MODERATOR)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新分类排序' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '排序更新成功',
  })
  @ApiBody({
    description: '分类排序',
    schema: {
      type: 'object',
      properties: {
        orders: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', description: '分类ID' },
              sortOrder: { type: 'number', description: '排序顺序' },
            },
          },
        },
      },
    },
  })
  async updateOrder(@Body('orders') orders: CategoryOrderDto[]): Promise<void> {
    await this.categoriesService.updateOrder(orders);
  }
}
