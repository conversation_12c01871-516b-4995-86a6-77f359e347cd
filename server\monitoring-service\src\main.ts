import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppModule } from './app.module';

/**
 * 监控服务启动入口
 */
async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // 获取配置服务
  const configService = app.get(ConfigService);

  // 启用全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    }),
  );

  // 启用CORS
  app.enableCors({
    origin: configService.get('CORS_ORIGIN', '*'),
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization'],
  });

  // 设置全局前缀
  app.setGlobalPrefix('api/v1');

  // 获取端口配置
  const port = configService.get<number>('PORT', 3012);

  // 启动服务
  await app.listen(port);

  console.log(`监控服务已启动，端口: ${port}`);
  console.log(`健康检查地址: http://localhost:${port}/api/v1/health`);
  console.log(`Prometheus指标地址: http://localhost:${port}/metrics`);
}

bootstrap().catch((error) => {
  console.error('监控服务启动失败:', error);
  process.exit(1);
});
