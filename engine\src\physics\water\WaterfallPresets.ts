/**
 * 瀑布预设（简化版）
 * 提供各种类型的瀑布预设配置
 */
import * as THREE from 'three';
import { Entity } from '../../core/Entity';
import { type World  } from '../../core/World';
import { WaterfallComponent, WaterfallConfig, WaterfallType } from './WaterfallComponent';
import { Debug } from '../../utils/Debug';

/**
 * 瀑布预设类型
 */
export enum WaterfallPresetType {
  /** 标准瀑布 */
  STANDARD = 'standard',
  /** 高瀑布 */
  HIGH = 'high',
  /** 宽瀑布 */
  WIDE = 'wide',
  /** 小瀑布 */
  SMALL = 'small'
}

/**
 * 瀑布预设配置（简化版）
 */
export interface WaterfallPresetConfig {
  /** 预设类型 */
  type: WaterfallPresetType;
  /** 瀑布宽度 */
  width?: number;
  /** 瀑布高度 */
  height?: number;
  /** 瀑布深度 */
  depth?: number;
  /** 瀑布位置 */
  position?: THREE.Vector3;
  /** 瀑布旋转 */
  rotation?: THREE.Euler;
  /** 瀑布颜色 */
  color?: THREE.Color;
  /** 瀑布不透明度 */
  opacity?: number;
  /** 瀑布流速 */
  flowSpeed?: number;
  /** 瀑布流向 */
  flowDirection?: { x: number; y: number; z: number };
  /** 瀑布湍流强度 */
  turbulenceStrength?: number;
  /** 瀑布湍流频率 */
  turbulenceFrequency?: number;
  /** 瀑布湍流速度 */
  turbulenceSpeed?: number;
}

/**
 * 瀑布预设（简化版）
 */
export class WaterfallPresets {
  /**
   * 将预设类型转换为瀑布类型
   * @param presetType 预设类型
   * @returns 瀑布类型
   */
  private static convertPresetTypeToWaterfallType(presetType: WaterfallPresetType): WaterfallType {
    switch (presetType) {
      case WaterfallPresetType.HIGH:
        return WaterfallType.HIGH;
      case WaterfallPresetType.WIDE:
        return WaterfallType.WIDE;
      case WaterfallPresetType.SMALL:
        return WaterfallType.SMALL;
      case WaterfallPresetType.STANDARD:
      default:
        return WaterfallType.HIGH; // 默认使用高瀑布类型
    }
  }

  /**
   * 创建瀑布预设
   * @param world 世界
   * @param config 预设配置
   * @returns 瀑布实体
   */
  public static createPreset(world: World, config: WaterfallPresetConfig): Entity {
    // 创建瀑布实体
    const entity = new Entity();

    // 创建瀑布配置
    const waterfallConfig: WaterfallConfig = {
      type: this.convertPresetTypeToWaterfallType(config.type)
    };

    // 应用基本配置
    if (config.width !== undefined) waterfallConfig.width = config.width;
    if (config.height !== undefined) waterfallConfig.height = config.height;
    if (config.depth !== undefined) waterfallConfig.depth = config.depth;
    if (config.position) waterfallConfig.position = config.position;
    if (config.rotation) waterfallConfig.rotation = config.rotation;
    if (config.color) waterfallConfig.color = config.color;
    if (config.opacity !== undefined) waterfallConfig.opacity = config.opacity;
    if (config.flowSpeed !== undefined) waterfallConfig.flowSpeed = config.flowSpeed;
    if (config.flowDirection) waterfallConfig.flowDirection = config.flowDirection;
    if (config.turbulenceStrength !== undefined) waterfallConfig.turbulenceStrength = config.turbulenceStrength;
    if (config.turbulenceFrequency !== undefined) waterfallConfig.turbulenceFrequency = config.turbulenceFrequency;
    if (config.turbulenceSpeed !== undefined) waterfallConfig.turbulenceSpeed = config.turbulenceSpeed;

    // 根据预设类型应用特定配置
    switch (config.type) {
      case WaterfallPresetType.STANDARD:
        this.applyStandardPreset(waterfallConfig);
        break;
      case WaterfallPresetType.HIGH:
        this.applyHighPreset(waterfallConfig);
        break;
      case WaterfallPresetType.WIDE:
        this.applyWidePreset(waterfallConfig);
        break;
      case WaterfallPresetType.SMALL:
        this.applySmallPreset(waterfallConfig);
        break;
      default:
        this.applyStandardPreset(waterfallConfig);
        break;
    }

    // 创建瀑布组件
    new WaterfallComponent(entity, waterfallConfig);

    // 添加到世界
    world.addEntity(entity);

    Debug.log('WaterfallPresets', `创建瀑布预设: ${config.type}`);

    return entity;
  }

  /**
   * 应用标准瀑布预设
   * @param config 瀑布配置
   */
  private static applyStandardPreset(config: WaterfallConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 10;
    if (!config.height) config.height = 20;
    if (!config.depth) config.depth = 3;

    // 设置默认颜色和不透明度
    if (!config.color) config.color = new THREE.Color(0xaaddff);
    if (config.opacity === undefined) config.opacity = 0.7;

    // 设置默认流速和流向
    if (config.flowSpeed === undefined) config.flowSpeed = 2.0;
    if (!config.flowDirection) config.flowDirection = { x: 0, y: -1, z: 0 };

    // 设置默认湍流参数
    if (config.turbulenceStrength === undefined) config.turbulenceStrength = 1.0;
    if (config.turbulenceFrequency === undefined) config.turbulenceFrequency = 2.0;
    if (config.turbulenceSpeed === undefined) config.turbulenceSpeed = 1.0;
  }

  /**
   * 应用高瀑布预设
   * @param config 瀑布配置
   */
  private static applyHighPreset(config: WaterfallConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 8;
    if (!config.height) config.height = 50;
    if (!config.depth) config.depth = 3;

    // 设置默认颜色和不透明度
    if (!config.color) config.color = new THREE.Color(0x99ccff);
    if (config.opacity === undefined) config.opacity = 0.6;

    // 设置默认流速和流向
    if (config.flowSpeed === undefined) config.flowSpeed = 3.0;
    if (!config.flowDirection) config.flowDirection = { x: 0, y: -1, z: 0 };

    // 设置默认湍流参数
    if (config.turbulenceStrength === undefined) config.turbulenceStrength = 1.5;
    if (config.turbulenceFrequency === undefined) config.turbulenceFrequency = 1.5;
    if (config.turbulenceSpeed === undefined) config.turbulenceSpeed = 1.2;
  }

  /**
   * 应用宽瀑布预设
   * @param config 瀑布配置
   */
  private static applyWidePreset(config: WaterfallConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 30;
    if (!config.height) config.height = 15;
    if (!config.depth) config.depth = 3;

    // 设置默认颜色和不透明度
    if (!config.color) config.color = new THREE.Color(0xbbddff);
    if (config.opacity === undefined) config.opacity = 0.65;

    // 设置默认流速和流向
    if (config.flowSpeed === undefined) config.flowSpeed = 1.8;
    if (!config.flowDirection) config.flowDirection = { x: 0, y: -1, z: 0 };

    // 设置默认湍流参数
    if (config.turbulenceStrength === undefined) config.turbulenceStrength = 0.8;
    if (config.turbulenceFrequency === undefined) config.turbulenceFrequency = 1.0;
    if (config.turbulenceSpeed === undefined) config.turbulenceSpeed = 0.8;
  }

  /**
   * 应用小瀑布预设
   * @param config 瀑布配置
   */
  private static applySmallPreset(config: WaterfallConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 5;
    if (!config.height) config.height = 8;
    if (!config.depth) config.depth = 2;

    // 设置默认颜色和不透明度
    if (!config.color) config.color = new THREE.Color(0xccddff);
    if (config.opacity === undefined) config.opacity = 0.75;

    // 设置默认流速和流向
    if (config.flowSpeed === undefined) config.flowSpeed = 1.2;
    if (!config.flowDirection) config.flowDirection = { x: 0, y: -1, z: 0 };

    // 设置默认湍流参数
    if (config.turbulenceStrength === undefined) config.turbulenceStrength = 0.6;
    if (config.turbulenceFrequency === undefined) config.turbulenceFrequency = 2.5;
    if (config.turbulenceSpeed === undefined) config.turbulenceSpeed = 1.2;
  }
}
