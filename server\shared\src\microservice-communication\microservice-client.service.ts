import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { CircuitBreakerService } from '../circuit-breaker/circuit-breaker.service';
import { CacheService } from '../../cache/cache.service';

export interface ServiceEndpoint {
  name: string;
  url: string;
  version: string;
  health: boolean;
  lastCheck: Date;
}

export interface MicroserviceConfig {
  timeout: number;
  retries: number;
  circuitBreaker: {
    failureThreshold: number;
    resetTimeout: number;
  };
  cache: {
    enabled: boolean;
    ttl: number;
  };
}

@Injectable()
export class MicroserviceClientService implements OnModuleInit {
  private readonly logger = new Logger(MicroserviceClientService.name);
  private readonly httpClient: AxiosInstance;
  private readonly services = new Map<string, ServiceEndpoint>();
  private readonly config: MicroserviceConfig;

  constructor(
    private readonly circuitBreakerService: CircuitBreakerService,
    private readonly cacheService: CacheService,
  ) {
    this.config = {
      timeout: parseInt(process.env.MICROSERVICE_TIMEOUT || '30000'),
      retries: parseInt(process.env.MICROSERVICE_RETRIES || '3'),
      circuitBreaker: {
        failureThreshold: parseInt(process.env.CIRCUIT_BREAKER_FAILURE_THRESHOLD || '5'),
        resetTimeout: parseInt(process.env.CIRCUIT_BREAKER_RESET_TIMEOUT || '60000'),
      },
      cache: {
        enabled: process.env.MICROSERVICE_CACHE_ENABLED !== 'false',
        ttl: parseInt(process.env.MICROSERVICE_CACHE_TTL || '300'),
      },
    };

    this.httpClient = axios.create({
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'DL-Engine-Microservice-Client/1.0',
      },
    });

    this.setupInterceptors();
  }

  async onModuleInit() {
    await this.discoverServices();
    this.startHealthChecking();
  }

  /**
   * 注册微服务端点
   */
  registerService(name: string, url: string, version: string = '1.0.0'): void {
    this.services.set(name, {
      name,
      url,
      version,
      health: true,
      lastCheck: new Date(),
    });
    this.logger.log(`已注册微服务: ${name} -> ${url}`);
  }

  /**
   * 获取服务端点
   */
  getService(name: string): ServiceEndpoint | undefined {
    return this.services.get(name);
  }

  /**
   * 发送GET请求
   */
  async get<T = any>(
    serviceName: string,
    path: string,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    return this.request<T>('GET', serviceName, path, undefined, config);
  }

  /**
   * 发送POST请求
   */
  async post<T = any>(
    serviceName: string,
    path: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    return this.request<T>('POST', serviceName, path, data, config);
  }

  /**
   * 发送PUT请求
   */
  async put<T = any>(
    serviceName: string,
    path: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    return this.request<T>('PUT', serviceName, path, data, config);
  }

  /**
   * 发送DELETE请求
   */
  async delete<T = any>(
    serviceName: string,
    path: string,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    return this.request<T>('DELETE', serviceName, path, undefined, config);
  }

  /**
   * 发送PATCH请求
   */
  async patch<T = any>(
    serviceName: string,
    path: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    return this.request<T>('PATCH', serviceName, path, data, config);
  }

  /**
   * 通用请求方法
   */
  private async request<T = any>(
    method: string,
    serviceName: string,
    path: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    const service = this.getService(serviceName);
    if (!service) {
      throw new Error(`未找到微服务: ${serviceName}`);
    }

    if (!service.health) {
      throw new Error(`微服务不健康: ${serviceName}`);
    }

    const url = `${service.url}${path}`;
    const cacheKey = this.generateCacheKey(method, url, data);

    // 检查缓存
    if (method === 'GET' && this.config.cache.enabled) {
      try {
        const cached = await this.cacheService.get<T>(cacheKey, async () => {
          // 执行实际请求
          const response = await this.circuitBreakerService.execute(
            serviceName,
            async () => {
              return this.httpClient.request<T>({
                method,
                url,
                data,
                headers: config?.headers,
                timeout: this.config.timeout,
              });
            },
            this.config.circuitBreaker,
          );
          return response.data;
        }, this.config.cache.ttl * 1000);

        this.logger.debug(`缓存命中: ${cacheKey}`);
        return cached;
      } catch (error) {
        this.logger.error(`缓存获取失败: ${error.message}`);
        // 继续执行非缓存请求
      }
    }

    // 使用熔断器执行请求
    const response = await this.circuitBreakerService.execute(
      serviceName,
      async () => {
        return this.httpClient.request<T>({
          method,
          url,
          data,
          ...config,
        });
      },
      {
        failureThreshold: this.config.circuitBreaker.failureThreshold,
        resetTimeout: this.config.circuitBreaker.resetTimeout,
      },
    );

    const result = response.data;

    // 缓存GET请求结果
    if (method === 'GET' && this.config.cache.enabled) {
      await this.cacheService.set(cacheKey, result, this.config.cache.ttl);
    }

    return result;
  }

  /**
   * 批量请求
   */
  async batchRequest<T = any>(
    requests: Array<{
      serviceName: string;
      method: string;
      path: string;
      data?: any;
      config?: AxiosRequestConfig;
    }>,
  ): Promise<Array<{ success: boolean; data?: T; error?: string }>> {
    const promises = requests.map(async (req) => {
      try {
        const data = await this.request<T>(
          req.method,
          req.serviceName,
          req.path,
          req.data,
          req.config,
        );
        return { success: true, data };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    return Promise.all(promises);
  }

  /**
   * 健康检查
   */
  async checkHealth(serviceName: string): Promise<boolean> {
    try {
      const service = this.getService(serviceName);
      if (!service) return false;

      const response = await this.httpClient.get(`${service.url}/health`, {
        timeout: 5000,
      });

      const isHealthy = response.status === 200;
      service.health = isHealthy;
      service.lastCheck = new Date();

      return isHealthy;
    } catch (error) {
      const service = this.getService(serviceName);
      if (service) {
        service.health = false;
        service.lastCheck = new Date();
      }
      return false;
    }
  }

  /**
   * 获取所有服务状态
   */
  getServicesStatus(): Array<ServiceEndpoint> {
    return Array.from(this.services.values());
  }

  /**
   * 设置请求拦截器
   */
  private setupInterceptors(): void {
    // 请求拦截器
    this.httpClient.interceptors.request.use(
      (config) => {
        const requestId = this.generateRequestId();
        config.headers['X-Request-ID'] = requestId;
        config.headers['X-Service-Name'] = process.env.SERVICE_NAME || 'unknown';
        
        this.logger.debug(`发送请求: ${config.method?.toUpperCase()} ${config.url}`, {
          requestId,
          headers: config.headers,
        });
        
        return config;
      },
      (error) => {
        this.logger.error('请求拦截器错误', error);
        return Promise.reject(error);
      },
    );

    // 响应拦截器
    this.httpClient.interceptors.response.use(
      (response: AxiosResponse) => {
        const requestId = response.config.headers['X-Request-ID'];
        this.logger.debug(`收到响应: ${response.status} ${response.config.url}`, {
          requestId,
          status: response.status,
          duration: this.calculateDuration(response.config),
        });
        
        return response;
      },
      (error) => {
        const requestId = error.config?.headers['X-Request-ID'];
        this.logger.error(`请求失败: ${error.config?.url}`, {
          requestId,
          status: error.response?.status,
          message: error.message,
        });
        
        return Promise.reject(error);
      },
    );
  }

  /**
   * 发现服务
   */
  private async discoverServices(): Promise<void> {
    // 从配置中注册已知服务
    const services = [
      { name: 'asset-library-service', url: process.env.ASSET_LIBRARY_SERVICE_URL || 'http://localhost:8003' },
      { name: 'scene-template-service', url: process.env.SCENE_TEMPLATE_SERVICE_URL || 'http://localhost:8004' },
      { name: 'ai-model-service', url: process.env.AI_MODEL_SERVICE_URL || 'http://localhost:8002' },
      { name: 'scene-generation-service', url: process.env.SCENE_GENERATION_SERVICE_URL || 'http://localhost:8005' },
      { name: 'user-service', url: process.env.USER_SERVICE_URL || 'http://localhost:8001' },
      { name: 'project-service', url: process.env.PROJECT_SERVICE_URL || 'http://localhost:8006' },
      { name: 'render-service', url: process.env.RENDER_SERVICE_URL || 'http://localhost:8007' },
    ];

    for (const service of services) {
      this.registerService(service.name, service.url);
    }

    this.logger.log(`已发现 ${services.length} 个微服务`);
  }

  /**
   * 开始健康检查
   */
  private startHealthChecking(): void {
    setInterval(async () => {
      const services = Array.from(this.services.keys());
      for (const serviceName of services) {
        await this.checkHealth(serviceName);
      }
    }, 30000); // 每30秒检查一次
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(method: string, url: string, data?: any): string {
    const dataHash = data ? JSON.stringify(data) : '';
    return `microservice:${method}:${url}:${dataHash}`;
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 计算请求耗时
   */
  private calculateDuration(config: any): number {
    const startTime = config.metadata?.startTime;
    return startTime ? Date.now() - startTime : 0;
  }
}
