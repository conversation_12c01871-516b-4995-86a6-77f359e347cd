import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface RedisOptions {
  url: string;
  password?: string;
  database: number;
  keyPrefix: string;
  retryAttempts: number;
  retryDelay: number;
  maxRetriesPerRequest: number;
  lazyConnect: boolean;
  keepAlive: number;
  family: number;
  connectTimeout: number;
  commandTimeout: number;
}

@Injectable()
export class RedisConfig {
  constructor(private configService: ConfigService) {}

  createRedisOptions(): RedisOptions {
    const redisHost = this.configService.get('REDIS_HOST', 'localhost');
    const redisPort = parseInt(this.configService.get('REDIS_PORT', '6379'));
    const redisPassword = this.configService.get('REDIS_PASSWORD');
    const redisDb = parseInt(this.configService.get('REDIS_DB', '0'));

    // 构建Redis URL
    let redisUrl = this.configService.get('REDIS_URL');
    if (!redisUrl) {
      redisUrl = redisPassword
        ? `redis://:${redisPassword}@${redisHost}:${redisPort}/${redisDb}`
        : `redis://${redisHost}:${redisPort}/${redisDb}`;
    }

    return {
      url: redisUrl,
      password: redisPassword,
      database: redisDb,
      keyPrefix: this.configService.get('REDIS_KEY_PREFIX', 'asset-lib:'),
      retryAttempts: parseInt(this.configService.get('REDIS_RETRY_ATTEMPTS', '5')),
      retryDelay: parseInt(this.configService.get('REDIS_RETRY_DELAY', '1000')),
      maxRetriesPerRequest: parseInt(this.configService.get('REDIS_MAX_RETRIES_PER_REQUEST', '3')),
      lazyConnect: true,
      keepAlive: parseInt(this.configService.get('REDIS_KEEP_ALIVE', '30000')),
      family: 4,
      connectTimeout: parseInt(this.configService.get('REDIS_CONNECT_TIMEOUT', '15000')),
      commandTimeout: parseInt(this.configService.get('REDIS_COMMAND_TIMEOUT', '10000')),
    };
  }
}
