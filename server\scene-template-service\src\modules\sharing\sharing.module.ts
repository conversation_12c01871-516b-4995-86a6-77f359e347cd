import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SharingService } from './sharing.service';
import { TemplateShare } from './entities/template-share.entity';
import { SceneTemplate } from '../templates/entities/scene-template.entity';
import { LoggerService } from '../../common/services/logger.service';

@Module({
  imports: [TypeOrmModule.forFeature([TemplateShare, SceneTemplate])],
  providers: [
    SharingService,
    LoggerService,
  ],
  exports: [SharingService, TypeOrmModule],
})
export class SharingModule {}
