import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { TagsService, CreateTagDto, UpdateTagDto, TagQueryDto } from './tags.service';
import { Tag } from './entities/tag.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../auth/entities/user.entity';

@ApiTags('tags')
@Controller('tags')
export class TagsController {
  constructor(private readonly tagsService: TagsService) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MODERATOR)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建标签' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '标签创建成功',
    type: Tag,
  })
  async create(@Body() createTagDto: CreateTagDto): Promise<Tag> {
    return await this.tagsService.create(createTagDto);
  }

  @Get()
  @ApiOperation({ summary: '获取标签列表' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '标签列表获取成功',
  })
  @ApiQuery({ name: 'search', required: false, description: '搜索关键词' })
  @ApiQuery({ name: 'type', required: false, description: '标签类型' })
  @ApiQuery({ name: 'featured', required: false, description: '是否特色标签' })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  async findAll(@Query() query: TagQueryDto): Promise<{ tags: Tag[]; total: number }> {
    return await this.tagsService.findAll(query);
  }

  @Get('popular')
  @ApiOperation({ summary: '获取热门标签' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '热门标签获取成功',
    type: [Tag],
  })
  @ApiQuery({ name: 'limit', required: false, description: '数量限制' })
  async getPopular(@Query('limit') limit?: number): Promise<Tag[]> {
    return await this.tagsService.getPopularTags(limit);
  }

  @Get('featured')
  @ApiOperation({ summary: '获取特色标签' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '特色标签获取成功',
    type: [Tag],
  })
  async getFeatured(): Promise<Tag[]> {
    return await this.tagsService.getFeaturedTags();
  }

  @Get('search')
  @ApiOperation({ summary: '搜索标签' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '搜索结果',
    type: [Tag],
  })
  @ApiQuery({ name: 'q', required: true, description: '搜索关键词' })
  @ApiQuery({ name: 'limit', required: false, description: '数量限制' })
  async search(
    @Query('q') query: string,
    @Query('limit') limit?: number,
  ): Promise<Tag[]> {
    return await this.tagsService.searchTags(query, limit);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取标签详情' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '标签详情获取成功',
    type: Tag,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '标签不存在',
  })
  @ApiParam({ name: 'id', description: '标签ID' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Tag> {
    return await this.tagsService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MODERATOR)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新标签' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '标签更新成功',
    type: Tag,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '标签不存在',
  })
  @ApiParam({ name: 'id', description: '标签ID' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateTagDto: UpdateTagDto,
  ): Promise<Tag> {
    return await this.tagsService.update(id, updateTagDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MODERATOR)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除标签' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: '标签删除成功',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '标签不存在',
  })
  @ApiParam({ name: 'id', description: '标签ID' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    await this.tagsService.remove(id);
  }
}
