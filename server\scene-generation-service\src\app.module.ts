import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ScheduleModule } from '@nestjs/schedule';
import { ClientsModule, Transport } from '@nestjs/microservices';

// 核心模块
import { GenerationModule } from './modules/generation/generation.module';
import { TasksModule } from './modules/tasks/tasks.module';
import { ScenesModule } from './modules/scenes/scenes.module';
import { TemplatesModule } from './modules/templates/templates.module';
import { AssetsModule } from './modules/assets/assets.module';
import { AiModelsModule } from './modules/ai-models/ai-models.module';
import { WebsocketModule } from './modules/websocket/websocket.module';
import { AuthModule } from './modules/auth/auth.module';

// 公共服务
import { LoggerService } from './common/services/logger.service';
import { CacheService } from './common/services/cache.service';
import { StorageService } from './common/services/storage.service';
import { HttpClientService } from './common/services/http-client.service';
import { HealthController } from './common/controllers/health.controller';
import { RedisModule } from './common/modules/redis.module';
import { AppService } from './app.service';

// 实体
import { GenerationTask } from './modules/tasks/entities/generation-task.entity';
import { Scene } from './modules/scenes/entities/scene.entity';
import { SceneObject } from './modules/scenes/entities/scene-object.entity';
import { SceneTemplate } from './modules/templates/entities/scene-template.entity';
import { Asset } from './modules/assets/entities/asset.entity';
import { AiModel } from './modules/ai-models/entities/ai-model.entity';
import { User } from './modules/auth/entities/user.entity';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // Redis模块
    RedisModule,
    
    // 调度模块
    ScheduleModule.forRoot(),
    
    // 数据库模块
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get('DB_HOST', 'localhost'),
        port: configService.get('DB_PORT', 3306),
        username: configService.get('DB_USERNAME', 'root'),
        password: configService.get('DB_PASSWORD', 'password'),
        database: configService.get('DB_DATABASE_SCENE_GENERATION') || configService.get('DB_DATABASE', 'dl_engine_scene_generation'),
        entities: [
          GenerationTask,
          Scene,
          SceneObject,
          SceneTemplate,
          Asset,
          AiModel,
          User,
        ],
        charset: 'utf8mb4',
        timezone: '+08:00',
        retryAttempts: 5,
        retryDelay: 3000,
        autoLoadEntities: true,
        keepConnectionAlive: true,
        authPlugin: 'mysql_native_password',
        extra: {
          ssl: false,
          connectionLimit: 10,
          acquireTimeout: 60000,
          timeout: 60000,
        },
        synchronize: configService.get('NODE_ENV') !== 'production',
        logging: configService.get('NODE_ENV') === 'development',
      }),
      inject: [ConfigService],
    }),
    
    // JWT模块
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET', 'scene-generation-secret'),
        signOptions: { expiresIn: '24h' },
      }),
      inject: [ConfigService],
    }),
    
    // Passport模块
    PassportModule.register({ defaultStrategy: 'jwt' }),

    // 微服务客户端
    ClientsModule.registerAsync([
      {
        name: 'SERVICE_REGISTRY',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('SERVICE_REGISTRY_HOST', 'localhost'),
            port: configService.get<number>('SERVICE_REGISTRY_PORT', 3010),
          },
        }),
        inject: [ConfigService],
      },
    ]),

    // 业务模块
    GenerationModule,
    TasksModule,
    ScenesModule,
    TemplatesModule,
    AssetsModule,
    AiModelsModule,
    WebsocketModule,
    AuthModule,
  ],
  controllers: [HealthController],
  providers: [
    AppService,
    LoggerService,
    CacheService,
    StorageService,
    HttpClientService,
  ],
  exports: [
    LoggerService,
    CacheService,
    StorageService,
    HttpClientService,
  ],
})
export class AppModule {}
