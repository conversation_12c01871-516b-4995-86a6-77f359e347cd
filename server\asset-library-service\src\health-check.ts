import * as http from 'http';

const options = {
  hostname: 'localhost',
  port: parseInt(process.env.PORT || '8003'),
  path: '/health',
  method: 'GET',
  timeout: 15000, // 增加超时时间到15秒
};

const req = http.request(options, (res) => {
  let data = '';

  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    try {
      if (res.statusCode === 200) {
        const healthData = JSON.parse(data);
        // 接受 ok 和 degraded 状态，只有 error 才认为不健康
        if (healthData.status === 'ok' || healthData.status === 'degraded') {
          process.exit(0);
        } else {
          console.error('健康检查失败:', healthData);
          process.exit(1);
        }
      } else {
        console.error('健康检查HTTP状态码错误:', res.statusCode);
        process.exit(1);
      }
    } catch (error) {
      console.error('解析健康检查响应失败:', error);
      process.exit(1);
    }
  });
});

req.on('error', (error) => {
  console.error('健康检查请求失败:', error);
  process.exit(1);
});

req.on('timeout', () => {
  console.error('健康检查超时');
  req.destroy();
  process.exit(1);
});

req.end();
