/**
 * API网关主控制器
 */
import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { AppService } from './app.service';

@ApiTags('API网关')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({ summary: '获取API网关信息' })
  getInfo() {
    return this.appService.getInfo();
  }

  @Get('health')
  @ApiOperation({ summary: '健康检查' })
  healthCheck() {
    return this.appService.healthCheck();
  }

  @Get('api/health')
  @ApiOperation({ summary: 'API健康检查' })
  apiHealthCheck() {
    return this.appService.healthCheck();
  }
}
