/**
 * 动作捕捉系统
 * 用于处理动作捕捉数据并应用到角色骨骼上
 */
import { System } from '../core/System';
import type { World } from '../core/World';
import type { Entity } from '../core/Entity';
import { EventEmitter } from '../utils/EventEmitter';
import { Debug } from '../utils/Debug';
import { MotionCaptureComponent } from './components/MotionCaptureComponent';
import { MotionCapturePoseComponent } from './components/MotionCapturePoseComponent';
import { LandmarkData, WorldLandmarkData   } from './types/LandmarkData';
import { solveMotionCapturePose } from './utils/solveMotionCapturePose';
import { evaluatePose } from './utils/evaluatePose';
// TODO: 网络系统暂时禁用
// import { NetworkSystem } from '../network/NetworkSystem';
import { AvatarComponent } from '../avatar/components/AvatarComponent';
import { AvatarRigComponent } from '../avatar/components/AvatarRigComponent';

/**
 * 动作捕捉系统配置
 */
export interface MotionCaptureSystemConfig {
  /** 是否启用调试模式 */
  debug?: boolean;
  /** 是否启用网络同步 */
  enableNetworkSync?: boolean;
  /** 是否启用姿势评估 */
  enablePoseEvaluation?: boolean;
  /** 平滑系数 */
  smoothingFactor?: number;
  /** 可见度阈值 */
  visibilityThreshold?: number;
}

/**
 * 动作捕捉结果
 */
export interface MotionCaptureResults {
  /** 世界坐标系中的关键点数据 */
  worldLandmarks: WorldLandmarkData[];
  /** 屏幕坐标系中的关键点数据 */
  landmarks: LandmarkData[];
}

/**
 * 动作捕捉系统
 * 处理动作捕捉数据并应用到角色骨骼上
 */
export class MotionCaptureSystem extends System {
  /** 系统名称 */
  public static readonly NAME = 'MotionCaptureSystem';

  /** 系统配置 */
  private config: MotionCaptureSystemConfig;

  /** 网络系统引用 */
  private networkSystem: any | null = null;

  /** 动作捕捉组件映射 */
  private motionCaptureComponents: Map<Entity, MotionCaptureComponent> = new Map();

  /** 姿势组件映射 */
  private poseComponents: Map<Entity, MotionCapturePoseComponent> = new Map();

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 动作捕捉数据缓存 */
  private captureDataCache: Map<string, MotionCaptureResults> = new Map();

  /** 最后一次数据接收时间 */
  private lastDataReceiveTime: Map<string, number> = new Map();

  /** 数据通道类型 */
  public static readonly DATA_CHANNEL_TYPE = 'mocap.dataChannel';

  /**
   * 构造函数
   * @param world 世界引用
   * @param config 系统配置
   */
  constructor(world: World, config: MotionCaptureSystemConfig = {}) {
    super();
    // 设置世界引用
    this.setWorld(world);
    this.config = {
      debug: config.debug !== undefined ? config.debug : false,
      enableNetworkSync: config.enableNetworkSync !== undefined ? config.enableNetworkSync : true,
      enablePoseEvaluation: config.enablePoseEvaluation !== undefined ? config.enablePoseEvaluation : true,
      smoothingFactor: config.smoothingFactor || 0.5,
      visibilityThreshold: config.visibilityThreshold || 0.1
    };

    // TODO: 网络系统获取暂时禁用，等待网络系统完善
    // 获取网络系统
    // this.networkSystem = world.getSystem(NetworkSystem);
    if (!this.networkSystem && this.config.enableNetworkSync && this.config.debug) {
      Debug.warn('MotionCaptureSystem', 'NetworkSystem not found, network sync will not work');
    }

    // TODO: 网络同步功能暂时禁用，等待网络系统完善
    // 如果启用网络同步，注册数据处理器
    // if (this.config.enableNetworkSync && this.networkSystem) {
    //   this.registerNetworkHandlers();
    // }
  }

  /**
   * 注册网络处理器
   * TODO: 网络功能暂时禁用
   * @private
   * @unused 此方法暂时未使用，等待网络系统完善后启用
   */
  private registerNetworkHandlers(): void {
    // TODO: 实现网络处理器注册
    if (this.config.debug) {
      Debug.log('MotionCaptureSystem', 'Network handlers registration is disabled');
    }
  }

  /**
   * 处理动作捕捉数据
   * @param fromPeerId 发送者ID
   * @param message 消息数据
   * @private
   * @unused 此方法暂时未使用，等待网络系统完善后启用
   */
  private handleMotionCaptureData(fromPeerId: string, message: ArrayBuffer): void {
    try {
      // 解码数据
      const data = this.decodeMotionCaptureData(message);

      // 更新缓存
      this.captureDataCache.set(fromPeerId, data.results);
      this.lastDataReceiveTime.set(fromPeerId, Date.now());

      // TODO: 网络广播功能暂时禁用
      // 如果是主机，广播给所有客户端
      // if (this.networkSystem && this.networkSystem.isHost()) {
      //   this.networkSystem.broadcastData(
      //     MotionCaptureSystem.DATA_CHANNEL_TYPE,
      //     message,
      //     fromPeerId
      //   );
      // }

      if (this.config.debug) {
        Debug.log('MotionCaptureSystem', `Received motion capture data from ${fromPeerId}`);
      }
    } catch (error) {
      Debug.error('MotionCaptureSystem', `Error handling motion capture data: ${error}`);
    }
  }

  /**
   * 解码动作捕捉数据
   * @param data 二进制数据
   * @returns 解码后的数据
   */
  private decodeMotionCaptureData(data: ArrayBuffer): { timestamp: number; results: MotionCaptureResults } {
    // 这里应该实现实际的解码逻辑
    // 为简化示例，这里直接使用JSON解析
    const decoder = new TextDecoder();
    const jsonString = decoder.decode(data);
    return JSON.parse(jsonString);
  }

  /**
   * 编码动作捕捉数据
   * @param results 动作捕捉结果
   * @returns 编码后的二进制数据
   */
  public encodeMotionCaptureData(results: MotionCaptureResults): ArrayBuffer {
    // 这里应该实现实际的编码逻辑
    // 为简化示例，这里直接使用JSON序列化
    const data = {
      timestamp: Date.now(),
      results
    };
    const encoder = new TextEncoder();
    return encoder.encode(JSON.stringify(data)).buffer;
  }

  /**
   * 发送动作捕捉结果
   * @param results 动作捕捉结果
   * TODO: 网络发送功能暂时禁用
   */
  public sendMotionCaptureResults(results: MotionCaptureResults): void {
    // 避免未使用参数警告
    void results;

    if (!this.networkSystem || !this.config.enableNetworkSync) return;

    // TODO: 实现网络发送功能
    // const encodedData = this.encodeMotionCaptureData(results);
    // this.networkSystem.sendData(MotionCaptureSystem.DATA_CHANNEL_TYPE, encodedData);

    if (this.config.debug) {
      Debug.log('MotionCaptureSystem', 'Motion capture data sending is disabled');
    }
  }

  /**
   * 注册动作捕捉组件
   * @param entity 实体
   * @param component 动作捕捉组件
   */
  public registerMotionCaptureComponent(entity: Entity, component: MotionCaptureComponent): void {
    this.motionCaptureComponents.set(entity, component);

    if (this.config.debug) {
      Debug.log('MotionCaptureSystem', `Registered motion capture component for entity ${entity.id}`);
    }
  }

  /**
   * 注册姿势组件
   * @param entity 实体
   * @param component 姿势组件
   */
  public registerPoseComponent(entity: Entity, component: MotionCapturePoseComponent): void {
    this.poseComponents.set(entity, component);

    if (this.config.debug) {
      Debug.log('MotionCaptureSystem', `Registered pose component for entity ${entity.id}`);
    }
  }

  /**
   * 更新系统
   * @param deltaTime 时间增量（秒）
   */
  public update(deltaTime: number): void {
    // 避免未使用参数警告
    void deltaTime;

    // 清理过期数据
    this.cleanupExpiredData();

    // 处理动作捕捉数据
    this.processMotionCaptureData();

    // 评估姿势
    if (this.config.enablePoseEvaluation) {
      this.evaluatePoses();
    }
  }

  /**
   * 清理过期数据
   */
  private cleanupExpiredData(): void {
    const now = Date.now();
    const timeout = 1000; // 1秒超时

    for (const [peerId, lastTime] of this.lastDataReceiveTime.entries()) {
      if (now - lastTime > timeout) {
        this.captureDataCache.delete(peerId);
        this.lastDataReceiveTime.delete(peerId);

        if (this.config.debug) {
          Debug.log('MotionCaptureSystem', `Removed expired data for peer ${peerId}`);
        }
      }
    }
  }

  /**
   * 处理动作捕捉数据
   */
  private processMotionCaptureData(): void {
    // 处理每个实体的动作捕捉数据
    for (const [entity, component] of this.motionCaptureComponents.entries()) {
      // 获取实体的用户ID
      const avatarComponent = entity.getComponent<AvatarComponent>(AvatarComponent.TYPE);
      if (!avatarComponent) continue;

      const userId = avatarComponent.userId;
      // 避免未使用变量警告
      void userId;

      // 查找该用户的动作捕捉数据
      let captureData: MotionCaptureResults | undefined;

      // TODO: 网络用户数据获取功能暂时禁用
      // 如果是网络用户，从缓存中获取数据
      // if (this.networkSystem && userId !== this.networkSystem.getLocalUserId()) {
      //   // 查找该用户对应的对等连接ID
      //   const peerId = this.networkSystem.getPeerIdByUserId(userId);
      //   if (peerId) {
      //     captureData = this.captureDataCache.get(peerId);
      //   }
      // } else {
      if (true) {
        // 如果是本地用户，使用组件中的数据
        captureData = {
          worldLandmarks: component.worldLandmarks,
          landmarks: component.landmarks
        };
      }

      // 如果没有数据，跳过
      if (!captureData) continue;

      // 解算姿势
      this.solveEntityPose(entity, captureData);
    }
  }

  /**
   * 解算实体姿势
   * @param entity 实体
   * @param captureData 捕捉数据
   */
  private solveEntityPose(entity: Entity, captureData: MotionCaptureResults): void {
    // 获取骨骼组件
    const rigComponent = entity.getComponent<AvatarRigComponent>('AvatarRigComponent');
    if (!rigComponent) return;

    // 解算姿势
    solveMotionCapturePose(
      entity,
      captureData.worldLandmarks,
      captureData.landmarks,
      this.config.smoothingFactor!,
      this.config.visibilityThreshold!
    );
  }

  /**
   * 评估姿势
   */
  private evaluatePoses(): void {
    // 评估每个实体的姿势
    for (const [entity, component] of this.poseComponents.entries()) {
      // 获取动作捕捉组件
      const motionCaptureComponent = this.motionCaptureComponents.get(entity);
      if (!motionCaptureComponent) continue;

      // 评估姿势
      evaluatePose(entity, component);
    }
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // TODO: 网络处理器注销功能暂时禁用
    // 取消注册网络处理器
    // if (this.networkSystem && this.config.enableNetworkSync) {
    //   this.networkSystem.unregisterDataChannelHandler(
    //     MotionCaptureSystem.DATA_CHANNEL_TYPE,
    //     this.handleMotionCaptureData.bind(this)
    //   );
    // }

    // 清空组件映射
    this.motionCaptureComponents.clear();
    this.poseComponents.clear();

    // 清空数据缓存
    this.captureDataCache.clear();
    this.lastDataReceiveTime.clear();

    // 清空事件监听器
    this.eventEmitter.removeAllListeners();
  }
}
