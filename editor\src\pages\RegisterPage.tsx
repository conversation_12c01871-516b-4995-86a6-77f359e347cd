/**
 * 注册页面
 */
import React, { useEffect } from 'react';
import { Form, Input, Button, Checkbox, Card, Typography, Divider, message, Alert } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, GithubOutlined, GoogleOutlined, FacebookOutlined } from '@ant-design/icons';
import { Link, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../store';
import { register, clearError } from '../store/auth/authSlice';

const { Title, Text } = Typography;

export const RegisterPage: React.FC = () => {
  const { t } = useTranslation('auth');
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  const { isAuthenticated, isLoading, error } = useAppSelector((state) => state.auth);
  
  // 如果已经登录，重定向到首页
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/projects', { replace: true });
    }
  }, [isAuthenticated, navigate]);
  
  // 清除错误
  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);
  
  // 处理注册
  const handleRegister = (values: { username: string; email: string; password: string; confirm: string; agreement: boolean }) => {
    dispatch(register({ username: values.username, email: values.email, password: values.password }))
      .unwrap()
      .then(() => {
        message.success(t('registerSuccess'));
        navigate('/projects');
      })
      .catch(() => {
        // 错误已经在状态中处理
      });
  };
  
  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        background: '#f0f2f5'}}
    >
      <Card style={{ width: 400, boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)' }}>
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          <Title level={2} style={{ margin: 0 }}>
            {t('registerTitle')}
          </Title>
          <Text type="secondary">{t('registerSubtitle')}</Text>
        </div>
        
        {error && (
          <Alert
            message={error}
            type="error"
            showIcon
            style={{ marginBottom: 24 }}
            closable
            onClose={() => dispatch(clearError())}
          />
        )}
        
        <Form
          name="register"
          initialValues={{ agreement: true }}
          onFinish={handleRegister}
          layout="vertical"
        >
          <Form.Item
            name="username"
            rules={[
              { required: true, message: t('usernameRequired') as string },
              { min: 3, message: t('usernameTooShort') as string },
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder={t('usernamePlaceholder') as string}
              size="large"
            />
          </Form.Item>
          
          <Form.Item
            name="email"
            rules={[
              { required: true, message: t('emailRequired') as string },
              { type: 'email', message: t('emailInvalid') as string },
            ]}
          >
            <Input
              prefix={<MailOutlined />}
              placeholder={t('emailPlaceholder') as string}
              size="large"
            />
          </Form.Item>
          
          <Form.Item
            name="password"
            rules={[
              { required: true, message: t('passwordRequired') as string },
              { min: 6, message: t('passwordTooShort') as string },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder={t('passwordPlaceholder') as string}
              size="large"
            />
          </Form.Item>
          
          <Form.Item
            name="confirm"
            dependencies={['password']}
            rules={[
              { required: true, message: t('confirmPasswordRequired') as string },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error(t('passwordMismatch') as string));
                }}),
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder={t('confirmPasswordPlaceholder') as string}
              size="large"
            />
          </Form.Item>
          
          <Form.Item
            name="agreement"
            valuePropName="checked"
            rules={[
              {
                validator: (_, value) =>
                  value ? Promise.resolve() : Promise.reject(new Error(t('agreementRequired') as string))},
            ]}
          >
            <Checkbox>
              {t('agreement')}{' '}
              <Link to="/terms">{t('terms')}</Link> {t('and')}{' '}
              <Link to="/privacy">{t('privacy')}</Link>
            </Checkbox>
          </Form.Item>
          
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              size="large"
              block
              loading={isLoading}
            >
              {t('register')}
            </Button>
          </Form.Item>
          
          <Form.Item style={{ textAlign: 'center', marginBottom: 0 }}>
            <Text>
              {t('haveAccount')}{' '}
              <Link to="/login">{t('login')}</Link>
            </Text>
          </Form.Item>
        </Form>
        
        <Divider plain>{t('orRegisterWith')}</Divider>
        
        <div style={{ display: 'flex', justifyContent: 'center', gap: 16 }}>
          <Button icon={<GithubOutlined />} size="large" shape="circle" />
          <Button icon={<GoogleOutlined />} size="large" shape="circle" />
          <Button icon={<FacebookOutlined />} size="large" shape="circle" />
        </div>
      </Card>
    </div>
  );
};
