import { Injectable, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as Minio from 'minio';
import * as crypto from 'crypto';
import * as path from 'path';

export interface UploadOptions {
  knowledgeBaseId: string;
  uploadId: string;
  metadata?: Record<string, string>;
}

export interface UploadResult {
  filePath: string;
  etag: string;
  size: number;
  url: string;
}

@Injectable()
export class StorageService {
  private readonly bucket: string;
  private readonly cdnDomain?: string;

  constructor(
    @Inject('MINIO_CLIENT') private readonly minioClient: Minio.Client,
    private readonly configService: ConfigService,
  ) {
    this.bucket = this.configService.get('MINIO_BUCKET', 'knowledge-bases');
    this.cdnDomain = this.configService.get('CDN_DOMAIN');
    this.initializeBucket();
  }

  /**
   * 初始化存储桶
   */
  private async initializeBucket(): Promise<void> {
    try {
      const exists = await this.minioClient.bucketExists(this.bucket);
      if (!exists) {
        await this.minioClient.makeBucket(this.bucket);
        console.log(`Bucket ${this.bucket} created successfully`);
      }
    } catch (error) {
      console.error('Failed to initialize bucket:', error);
    }
  }

  /**
   * 上传文件到对象存储
   */
  async uploadFile(
    file: Express.Multer.File,
    options: UploadOptions,
  ): Promise<UploadResult> {
    const fileName = `${options.uploadId}_${file.originalname}`;
    const filePath = `knowledge-bases/${options.knowledgeBaseId}/documents/${fileName}`;

    const metadata = {
      'Content-Type': file.mimetype,
      'X-Upload-ID': options.uploadId,
      'X-Knowledge-Base-ID': options.knowledgeBaseId,
      'X-Original-Name': file.originalname,
      'X-Upload-Time': new Date().toISOString(),
      ...options.metadata,
    };

    try {
      const result = await this.minioClient.putObject(
        this.bucket,
        filePath,
        file.buffer,
        file.size,
        metadata,
      );

      const url = this.getFileUrl(filePath);

      return {
        filePath,
        etag: result.etag,
        size: file.size,
        url,
      };
    } catch (error) {
      console.error('Failed to upload file:', error);
      throw new Error(`文件上传失败: ${error.message}`);
    }
  }

  /**
   * 下载文件
   */
  async downloadFile(filePath: string): Promise<Buffer> {
    try {
      const stream = await this.minioClient.getObject(this.bucket, filePath);
      const chunks: Buffer[] = [];

      return new Promise((resolve, reject) => {
        stream.on('data', (chunk) => chunks.push(chunk));
        stream.on('end', () => resolve(Buffer.concat(chunks)));
        stream.on('error', reject);
      });
    } catch (error) {
      console.error('Failed to download file:', error);
      throw new Error(`文件下载失败: ${error.message}`);
    }
  }

  /**
   * 获取文件信息
   */
  async getFileInfo(filePath: string): Promise<Minio.BucketItemStat> {
    try {
      return await this.minioClient.statObject(this.bucket, filePath);
    } catch (error) {
      console.error('Failed to get file info:', error);
      throw new Error(`获取文件信息失败: ${error.message}`);
    }
  }

  /**
   * 删除文件
   */
  async deleteFile(filePath: string): Promise<void> {
    try {
      await this.minioClient.removeObject(this.bucket, filePath);
    } catch (error) {
      console.error('Failed to delete file:', error);
      throw new Error(`删除文件失败: ${error.message}`);
    }
  }

  /**
   * 批量删除文件
   */
  async deleteFiles(filePaths: string[]): Promise<void> {
    try {
      await this.minioClient.removeObjects(this.bucket, filePaths);
    } catch (error) {
      console.error('Failed to delete files:', error);
      throw new Error(`批量删除文件失败: ${error.message}`);
    }
  }

  /**
   * 检查文件是否存在
   */
  async fileExists(filePath: string): Promise<boolean> {
    try {
      await this.minioClient.statObject(this.bucket, filePath);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取文件URL
   */
  getFileUrl(filePath: string): string {
    if (this.cdnDomain) {
      return `${this.cdnDomain}/${this.bucket}/${filePath}`;
    }

    const endpoint = this.configService.get('MINIO_ENDPOINT', 'localhost:9000');
    const protocol = endpoint.includes('https') ? 'https' : 'http';
    const cleanEndpoint = endpoint.replace(/^https?:\/\//, '');
    return `${protocol}://${cleanEndpoint}/${this.bucket}/${filePath}`;
  }

  /**
   * 生成预签名URL
   */
  async getPresignedUrl(
    filePath: string,
    expiry: number = 3600,
  ): Promise<string> {
    try {
      return await this.minioClient.presignedGetObject(
        this.bucket,
        filePath,
        expiry,
      );
    } catch (error) {
      console.error('Failed to generate presigned URL:', error);
      throw new Error(`生成预签名URL失败: ${error.message}`);
    }
  }

  /**
   * 计算文件哈希
   */
  calculateFileHash(buffer: Buffer): string {
    return crypto.createHash('sha256').update(buffer).digest('hex');
  }

  /**
   * 验证文件类型
   */
  validateFileType(file: Express.Multer.File): boolean {
    const allowedTypes = this.configService.get(
      'SUPPORTED_DOCUMENT_TYPES',
      'application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,text/plain,text/markdown'
    ).split(',');
    return allowedTypes.includes(file.mimetype);
  }

  /**
   * 验证文件大小
   */
  validateFileSize(file: Express.Multer.File): boolean {
    const maxSize = this.configService.get(
      'MAX_FILE_SIZE',
      104857600 // 100MB 默认值
    );
    return file.size <= maxSize;
  }

  /**
   * 列出知识库的所有文档
   */
  async listKnowledgeBaseDocuments(knowledgeBaseId: string): Promise<Minio.BucketItem[]> {
    const prefix = `knowledge-bases/${knowledgeBaseId}/documents/`;
    const objects: Minio.BucketItem[] = [];

    return new Promise((resolve, reject) => {
      const stream = this.minioClient.listObjects(this.bucket, prefix, true);
      
      stream.on('data', (obj) => objects.push(obj));
      stream.on('end', () => resolve(objects));
      stream.on('error', reject);
    });
  }
}
