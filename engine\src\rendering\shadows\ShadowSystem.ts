/**
 * 阴影系统
 * 管理场景中的阴影效果
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import { CSM } from './CSM';
import type { Entity } from '../../core/Entity';
import type { Camera   } from '../Camera';
import { Scene } from '../../scene/Scene';


/**
 * 阴影系统配置接口
 */
export interface ShadowSystemOptions {
  /** 是否启用阴影 */
  enabled?: boolean;
  /** 是否启用级联阴影映射 */
  useCSM?: boolean;
  /** 级联数量 */
  cascades?: number;
  /** 阴影贴图大小 */
  shadowMapSize?: number;
  /** 阴影偏移 */
  shadowBias?: number;
  /** 最大阴影距离 */
  maxShadowDistance?: number;
  /** 是否启用阴影淡入淡出 */
  fade?: boolean;
}

/**
 * 阴影组件接口
 */
export interface ShadowComponent {
  /** 是否投射阴影 */
  castShadow: boolean;
  /** 是否接收阴影 */
  receiveShadow: boolean;
}

/**
 * 阴影系统类
 */
export class ShadowSystem extends System {
  /** 系统类型 */
  public static readonly type: string = 'ShadowSystem';

  /** 是否启用阴影 */
  private shadowEnabled: boolean;

  /** 是否启用级联阴影映射 */
  private useCSM: boolean;

  /** 级联阴影映射实例 */
  private csm: CSM | null = null;

  /** 级联数量 */
  private cascades: number;

  /** 阴影贴图大小 */
  private shadowMapSize: number;

  /** 阴影偏移 */
  private shadowBias: number;

  /** 最大阴影距离 */
  private maxShadowDistance: number;

  /** 是否启用阴影淡入淡出 */
  private fade: boolean;

  /** 活跃相机 */
  private activeCamera: Camera | null = null;

  /** 活跃场景 */
  private activeScene: Scene | null = null;

  /** 阴影实体列表 */
  private shadowEntities: Map<Entity, ShadowComponent> = new Map();

  /** 方向光实体列表 */
  private directionalLights: Entity[] = [];

  /**
   * 创建阴影系统
   * @param options 阴影系统配置
   */
  constructor(options: ShadowSystemOptions = {}) {
    super();

    this.shadowEnabled = options.enabled !== undefined ? options.enabled : true;
    this.useCSM = options.useCSM !== undefined ? options.useCSM : true;
    this.cascades = options.cascades || 4;
    this.shadowMapSize = options.shadowMapSize || 2048;
    this.shadowBias = options.shadowBias || -0.000001;
    this.maxShadowDistance = options.maxShadowDistance || 100;
    this.fade = options.fade !== undefined ? options.fade : true;
  }

  /**
   * 设置渲染器
   * @param renderer Three.js渲染器
   */
  public setRenderer(renderer: THREE.WebGLRenderer): void {
    // 配置渲染器的阴影设置但不存储引用

    // 配置渲染器的阴影设置
    if (this.shadowEnabled) {
      renderer.shadowMap.enabled = true;
      renderer.shadowMap.type = THREE.PCFSoftShadowMap;
      renderer.shadowMap.autoUpdate = true;
    } else {
      renderer.shadowMap.enabled = false;
    }
  }

  /**
   * 设置活跃相机
   * @param camera 相机
   */
  public setActiveCamera(camera: Camera): void {
    this.activeCamera = camera;
    this.updateCSM();
  }

  /**
   * 设置活跃场景
   * @param scene 场景
   */
  public setActiveScene(scene: Scene): void {
    this.activeScene = scene;
    this.updateCSM();
  }

  /**
   * 添加阴影组件
   * @param entity 实体
   * @param component 阴影组件
   */
  public addShadowComponent(entity: Entity, component: ShadowComponent): void {
    this.shadowEntities.set(entity, component);

    // 获取实体的Three.js对象
    const transformComponent = entity.getComponent('Transform') as any as any as any as any as any;
    const object3D = transformComponent?.getObject3D();
    if (object3D) {
      // 设置阴影属性
      object3D.traverse((object: THREE.Object3D) => {
        if (object instanceof THREE.Mesh) {
          object.castShadow = component.castShadow;
          object.receiveShadow = component.receiveShadow;

          // 如果使用CSM且实体接收阴影，设置材质
          if (this.csm && component.receiveShadow && object.material) {
            this.csm.setupMaterial(object);
          }
        }
      });
    }
  }

  /**
   * 移除阴影组件
   * @param entity 实体
   */
  public removeShadowComponent(entity: Entity): void {
    this.shadowEntities.delete(entity);

    // 获取实体的Three.js对象
    const transformComponent = entity.getComponent('Transform') as any as any as any as any as any;
    const object3D = transformComponent?.getObject3D();
    if (object3D && this.csm) {
      // 清理材质
      object3D.traverse((object: THREE.Object3D) => {
        if (object instanceof THREE.Mesh && object.material) {
          if (Array.isArray(object.material)) {
            for (const material of object.material) {
              this.csm?.teardownMaterial(material);
            }
          } else {
            this.csm?.teardownMaterial(object.material);
          }
        }
      });
    }
  }

  /**
   * 添加方向光
   * @param entity 实体
   */
  public addDirectionalLight(entity: Entity): void {
    if (!this.directionalLights.includes(entity)) {
      this.directionalLights.push(entity);
      this.updateCSM();
    }
  }

  /**
   * 移除方向光
   * @param entity 实体
   */
  public removeDirectionalLight(entity: Entity): void {
    const index = this.directionalLights.indexOf(entity);
    if (index !== -1) {
      this.directionalLights.splice(index, 1);
      this.updateCSM();
    }
  }

  /**
   * 更新CSM
   */
  private updateCSM(): void {
    // 如果不使用CSM或没有相机或场景，则销毁现有CSM
    if (!this.useCSM || !this.activeCamera || !this.activeScene || this.directionalLights.length === 0) {
      if (this.csm) {
        (this.csm as any).dispose();
        this.csm = null;
      }
      return;
    }

    // 获取主方向光
    const mainLight = this.getMainDirectionalLight();
    if (!mainLight) return;

    // 如果已有CSM，更新它
    if (this.csm) {
      this.csm.changeLights(mainLight);
    } else {
      // 创建新的CSM
      this.csm = new CSM({
        light: mainLight,
        cascades: this.cascades,
        maxFar: this.maxShadowDistance,
        shadowMapSize: this.shadowMapSize,
        shadowBias: this.shadowBias,
        fade: this.fade
      });

      // 为所有接收阴影的实体设置材质
      for (const [entity, component] of Array.from(this.shadowEntities.entries())) {
        if (component.receiveShadow) {
          const transformComponent = entity.getComponent('Transform') as any as any as any as any as any;
          const object3D = transformComponent?.getObject3D();
          if (object3D) {
            object3D.traverse((object: THREE.Object3D) => {
              if (object instanceof THREE.Mesh && object.material) {
                this.csm?.setupMaterial(object);
              }
            });
          }
        }
      }
    }
  }

  /**
   * 获取主方向光
   * @returns Three.js方向光
   */
  private getMainDirectionalLight(): THREE.DirectionalLight | null {
    if (this.directionalLights.length === 0) return null;

    // 使用第一个方向光作为主光源
    const mainLightEntity = this.directionalLights[0];
    const lightComponent = mainLightEntity.getComponent('Light') as any as any as any as any;

    if (lightComponent && lightComponent.getType() === 'directional') {
      return lightComponent.getThreeLight() as THREE.DirectionalLight;
    }

    return null;
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(_deltaTime: number): void {
    if (!this.shadowEnabled || !this.csm || !this.activeCamera) return;

    // 更新CSM
    this.csm.update((this.activeCamera as any).getThreeCamera());
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    if (this.csm) {
      (this.csm as any).dispose();
      this.csm = null;
    }

    this.shadowEntities.clear();
    this.directionalLights = [];

    super.dispose();
  }
}
