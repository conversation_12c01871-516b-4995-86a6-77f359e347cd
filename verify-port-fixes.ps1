#!/usr/bin/env pwsh
# 端口冲突修复验证脚本
# 用于验证2024年12月19日的端口冲突修复是否成功

param(
    [switch]$CheckPorts,     # 检查端口占用情况
    [switch]$TestServices,   # 测试服务连接
    [switch]$RebuildGame,    # 重新构建游戏服务器
    [switch]$RestartAll,     # 重启相关服务
    [switch]$All             # 执行所有验证步骤
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput Cyan "ℹ️  $message"
}

function Write-Success($message) {
    Write-ColorOutput Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "❌ $message"
}

function Write-Header($message) {
    Write-Host ""
    Write-ColorOutput Magenta "🔍 $message"
    Write-Host "=" * 60
}

# 端口配置定义
$portConfig = @{
    "Asset Service" = @{ Port = 3003; Container = "dl-engine-asset-service-win" }
    "Game Server HTTP" = @{ Port = 3030; Container = "dl-engine-game-server-win" }
    "Game Server Microservice" = @{ Port = 3033; Container = "dl-engine-game-server-win" }
    "API Gateway" = @{ Port = 3000; Container = "dl-engine-api-gateway-win" }
    "User Service" = @{ Port = 3001; Container = "dl-engine-user-service-win" }
    "Project Service" = @{ Port = 3002; Container = "dl-engine-project-service-win" }
    "Monitoring Service" = @{ Port = 3012; Container = "dl-engine-monitoring-service-win" }
}

# 检查Docker是否运行
function Test-DockerRunning {
    try {
        docker info | Out-Null
        return $true
    } catch {
        return $false
    }
}

# 获取Docker Compose命令
function Get-DockerComposeCommand {
    try {
        docker-compose --version | Out-Null
        return "docker-compose"
    } catch {
        return "docker compose"
    }
}

# 检查端口占用情况
function Test-PortOccupation {
    Write-Header "检查端口占用情况"
    
    $conflictFound = $false
    
    foreach ($service in $portConfig.GetEnumerator()) {
        $serviceName = $service.Key
        $port = $service.Value.Port
        
        Write-Info "检查 $serviceName (端口 $port)..."
        
        try {
            $connection = Test-NetConnection -ComputerName localhost -Port $port -WarningAction SilentlyContinue
            if ($connection.TcpTestSucceeded) {
                Write-Success "$serviceName 端口 $port 正在使用"
            } else {
                Write-Warning "$serviceName 端口 $port 未被使用"
            }
        } catch {
            Write-Error "$serviceName 端口 $port 检查失败: $_"
        }
    }
    
    # 特别检查3003端口冲突
    Write-Info "特别检查端口3003冲突情况..."
    $port3003 = Test-NetConnection -ComputerName localhost -Port 3003 -WarningAction SilentlyContinue
    $port3033 = Test-NetConnection -ComputerName localhost -Port 3033 -WarningAction SilentlyContinue
    
    if ($port3003.TcpTestSucceeded -and $port3033.TcpTestSucceeded) {
        Write-Success "端口分离成功：3003(资产服务) 和 3033(游戏服务器微服务) 都在使用"
    } elseif ($port3003.TcpTestSucceeded -and -not $port3033.TcpTestSucceeded) {
        Write-Warning "资产服务(3003)运行正常，但游戏服务器微服务(3033)未运行"
    } elseif (-not $port3003.TcpTestSucceeded -and $port3033.TcpTestSucceeded) {
        Write-Warning "游戏服务器微服务(3033)运行正常，但资产服务(3003)未运行"
    } else {
        Write-Error "端口3003和3033都未被使用，服务可能未启动"
    }
}

# 测试服务连接
function Test-ServiceConnections {
    Write-Header "测试服务连接"
    
    $serviceUrls = @{
        "Asset Service" = "http://localhost:4003/health"
        "Game Server" = "http://localhost:3030/api/health"
        "API Gateway" = "http://localhost:3000/api/health"
        "User Service" = "http://localhost:4001/health"
        "Project Service" = "http://localhost:4002/health"
    }
    
    foreach ($service in $serviceUrls.GetEnumerator()) {
        $serviceName = $service.Key
        $url = $service.Value
        
        Write-Info "测试 $serviceName 连接..."
        
        try {
            $response = Invoke-WebRequest -Uri $url -Method GET -TimeoutSec 10
            if ($response.StatusCode -eq 200) {
                Write-Success "$serviceName 健康检查通过"
            } else {
                Write-Warning "$serviceName 响应异常: HTTP $($response.StatusCode)"
            }
        } catch {
            Write-Error "$serviceName 连接失败: $_"
        }
    }
}

# 重新构建游戏服务器
function Rebuild-GameServer {
    Write-Header "重新构建游戏服务器"
    
    $composeCmd = Get-DockerComposeCommand
    
    try {
        Write-Info "停止游戏服务器..."
        & $composeCmd -f docker-compose.windows.yml stop game-server
        
        Write-Info "重新构建游戏服务器镜像..."
        & $composeCmd -f docker-compose.windows.yml build --no-cache game-server
        
        Write-Info "启动游戏服务器..."
        & $composeCmd -f docker-compose.windows.yml up -d game-server
        
        Write-Success "游戏服务器重新构建完成"
        
        # 等待服务启动
        Write-Info "等待服务启动..."
        Start-Sleep -Seconds 30
        
    } catch {
        Write-Error "游戏服务器重新构建失败: $_"
    }
}

# 重启相关服务
function Restart-RelatedServices {
    Write-Header "重启相关服务"
    
    $composeCmd = Get-DockerComposeCommand
    $services = @("game-server", "asset-service")
    
    foreach ($service in $services) {
        try {
            Write-Info "重启 $service..."
            & $composeCmd -f docker-compose.windows.yml restart $service
            Write-Success "$service 重启成功"
            Start-Sleep -Seconds 15
        } catch {
            Write-Error "$service 重启失败: $_"
        }
    }
}

# 检查容器状态
function Check-ContainerStatus {
    Write-Header "检查容器状态"
    
    foreach ($service in $portConfig.GetEnumerator()) {
        $serviceName = $service.Key
        $containerName = $service.Value.Container
        
        Write-Info "检查 $serviceName 容器状态..."
        
        try {
            $containerInfo = docker inspect $containerName --format "{{.State.Status}}" 2>$null
            if ($containerInfo -eq "running") {
                Write-Success "$serviceName 容器运行正常"
            } else {
                Write-Error "$serviceName 容器状态异常: $containerInfo"
            }
        } catch {
            Write-Error "$serviceName 容器不存在或无法访问"
        }
    }
}

# 主函数
function Main {
    Write-Header "端口冲突修复验证 - 2024年12月19日"
    
    # 检查Docker状态
    if (-not (Test-DockerRunning)) {
        Write-Error "Docker Desktop 未运行，请先启动 Docker Desktop"
        exit 1
    }
    Write-Success "Docker Desktop 运行正常"
    
    # 检查docker-compose.windows.yml文件
    if (-not (Test-Path "docker-compose.windows.yml")) {
        Write-Error "docker-compose.windows.yml文件不存在"
        exit 1
    }
    Write-Success "Docker Compose配置文件存在"
    
    # 执行验证步骤
    if ($All -or $RebuildGame) {
        Rebuild-GameServer
    }
    
    if ($All -or $RestartAll) {
        Restart-RelatedServices
    }
    
    if ($All -or $CheckPorts) {
        Test-PortOccupation
    }
    
    Check-ContainerStatus
    
    if ($All -or $TestServices) {
        Test-ServiceConnections
    }
    
    Write-Header "验证完成"
    Write-Success "🎉 端口冲突修复验证完成！"
    Write-Info "💡 如果发现问题，请查看上面的详细信息"
    Write-Info "💡 建议运行: .\verify-port-fixes.ps1 -All 进行完整验证"
}

# 执行主函数
try {
    Main
} catch {
    Write-Error "验证脚本执行失败: $($_.Exception.Message)"
    exit 1
}
