/**
 * Git服务
 * 提供Git版本控制相关的功能实现
 */
import { Injectable, Logger } from '@nestjs/common';
import { exec } from 'child_process';
import { promisify } from 'util';
import * as path from 'path';

const execAsync = promisify(exec);

@Injectable()
export class GitService {
  private readonly logger = new Logger(GitService.name);
  private readonly workingDir: string;

  constructor() {
    // 设置工作目录为项目根目录
    this.workingDir = process.cwd();
    this.logger.log(`Git service initialized with working directory: ${this.workingDir}`);
  }

  /**
   * 执行Git命令
   */
  private async execGitCommand(command: string): Promise<string> {
    try {
      const { stdout, stderr } = await execAsync(`git ${command}`, {
        cwd: this.workingDir,
        encoding: 'utf8',
      });
      
      if (stderr && !stderr.includes('warning')) {
        this.logger.warn(`Git command warning: ${stderr}`);
      }
      
      return stdout.trim();
    } catch (error) {
      this.logger.error(`Git command failed: git ${command}`, error);
      throw new Error(`Git command failed: ${error.message}`);
    }
  }

  /**
   * 检查是否为Git仓库
   */
  private async isGitRepository(): Promise<boolean> {
    try {
      await this.execGitCommand('rev-parse --git-dir');
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取Git状态
   */
  async getStatus() {
    try {
      const isRepo = await this.isGitRepository();
      
      if (!isRepo) {
        return {
          isRepo: false,
          branch: '',
          ahead: 0,
          behind: 0,
          staged: 0,
          unstaged: 0,
          untracked: 0,
          conflicted: 0,
          files: [],
        };
      }

      // 获取当前分支
      const branch = await this.execGitCommand('branch --show-current');
      
      // 获取状态信息
      const statusOutput = await this.execGitCommand('status --porcelain');
      const files = this.parseStatusOutput(statusOutput);
      
      // 统计文件状态
      const staged = files.filter(f => f.staged).length;
      const unstaged = files.filter(f => !f.staged && f.status !== '??').length;
      const untracked = files.filter(f => f.status === '??').length;
      const conflicted = files.filter(f => f.status.includes('U') || f.status.includes('A')).length;

      // 获取ahead/behind信息
      let ahead = 0;
      let behind = 0;
      try {
        const trackingOutput = await this.execGitCommand('status --porcelain=v1 --branch');
        const trackingMatch = trackingOutput.match(/ahead (\d+)|behind (\d+)/g);
        if (trackingMatch) {
          trackingMatch.forEach(match => {
            if (match.includes('ahead')) {
              ahead = parseInt(match.match(/\d+/)[0]);
            } else if (match.includes('behind')) {
              behind = parseInt(match.match(/\d+/)[0]);
            }
          });
        }
      } catch (error) {
        this.logger.warn('Failed to get ahead/behind info:', error.message);
      }

      return {
        isRepo: true,
        branch,
        ahead,
        behind,
        staged,
        unstaged,
        untracked,
        conflicted,
        files,
      };
    } catch (error) {
      this.logger.error('Failed to get git status:', error);
      throw error;
    }
  }

  /**
   * 解析Git状态输出
   */
  private parseStatusOutput(output: string) {
    if (!output) return [];
    
    return output.split('\n').map(line => {
      const status = line.substring(0, 2);
      const path = line.substring(3);
      const staged = status[0] !== ' ' && status[0] !== '?';
      
      return {
        path,
        status,
        staged,
      };
    });
  }

  /**
   * 获取分支列表
   */
  async getBranches() {
    try {
      const isRepo = await this.isGitRepository();
      if (!isRepo) {
        return [];
      }

      const branchOutput = await this.execGitCommand('branch -a');
      const branches = branchOutput.split('\n').map(line => {
        const trimmed = line.trim();
        const current = trimmed.startsWith('*');
        const name = trimmed.replace(/^\*\s*/, '').replace(/^remotes\//, '');
        const remote = line.includes('remotes/');
        
        return {
          name,
          current,
          remote,
          isDetached: false,
        };
      }).filter(branch => branch.name && !branch.name.includes('->'));

      return branches;
    } catch (error) {
      this.logger.error('Failed to get branches:', error);
      throw error;
    }
  }

  /**
   * 获取文件状态
   */
  async getFiles() {
    try {
      const status = await this.getStatus();
      return {
        unstaged: status.files.filter(f => !f.staged),
        staged: status.files.filter(f => f.staged),
        conflicted: status.files.filter(f => f.status.includes('U')),
      };
    } catch (error) {
      this.logger.error('Failed to get file status:', error);
      throw error;
    }
  }

  /**
   * 获取提交历史
   */
  async getHistory(limit: number = 20) {
    try {
      const isRepo = await this.isGitRepository();
      if (!isRepo) {
        return [];
      }

      const logOutput = await this.execGitCommand(
        `log --oneline --format="%H|%h|%ai|%s|%an|%ae" -${limit}`
      );
      
      if (!logOutput) return [];

      return logOutput.split('\n').map(line => {
        const [hash, shortHash, date, message, author, email] = line.replace(/"/g, '').split('|');
        return {
          hash,
          shortHash,
          date,
          message,
          author,
          email,
        };
      });
    } catch (error) {
      this.logger.error('Failed to get commit history:', error);
      throw error;
    }
  }

  /**
   * 获取远程仓库
   */
  async getRemotes() {
    try {
      const isRepo = await this.isGitRepository();
      if (!isRepo) {
        return [];
      }

      const remoteOutput = await this.execGitCommand('remote -v');
      if (!remoteOutput) return [];

      const remotes = [];
      const lines = remoteOutput.split('\n');
      
      for (let i = 0; i < lines.length; i += 2) {
        const line = lines[i];
        if (line) {
          const [name, url] = line.split('\t');
          remotes.push({
            name: name.trim(),
            url: url.replace(' (fetch)', '').trim(),
          });
        }
      }

      return remotes;
    } catch (error) {
      this.logger.error('Failed to get remotes:', error);
      throw error;
    }
  }

  /**
   * 提交更改
   */
  async commit(message: string) {
    try {
      const result = await this.execGitCommand(`commit -m "${message}"`);
      this.logger.log(`Commit successful: ${message}`);
      return { success: true, message: result };
    } catch (error) {
      this.logger.error('Failed to commit:', error);
      throw error;
    }
  }

  /**
   * 添加文件到暂存区
   */
  async addFiles(files: string[]) {
    try {
      const fileList = files.join(' ');
      const result = await this.execGitCommand(`add ${fileList}`);
      this.logger.log(`Files added to staging: ${fileList}`);
      return { success: true, message: result };
    } catch (error) {
      this.logger.error('Failed to add files:', error);
      throw error;
    }
  }

  /**
   * 切换分支
   */
  async checkout(branch: string) {
    try {
      const result = await this.execGitCommand(`checkout ${branch}`);
      this.logger.log(`Checked out branch: ${branch}`);
      return { success: true, message: result };
    } catch (error) {
      this.logger.error('Failed to checkout branch:', error);
      throw error;
    }
  }

  /**
   * 拉取远程更新
   */
  async pull() {
    try {
      const result = await this.execGitCommand('pull');
      this.logger.log('Pull successful');
      return { success: true, message: result };
    } catch (error) {
      this.logger.error('Failed to pull:', error);
      throw error;
    }
  }

  /**
   * 推送到远程仓库
   */
  async push() {
    try {
      const result = await this.execGitCommand('push');
      this.logger.log('Push successful');
      return { success: true, message: result };
    } catch (error) {
      this.logger.error('Failed to push:', error);
      throw error;
    }
  }
}
