/**
 * 软体物理系统
 * 基于粒子和约束实现软体物理模拟
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { PhysicsSystem } from '../PhysicsSystem';
import { PhysicsBodyComponent   } from '../components/PhysicsBodyComponent';
import { SoftBodyComponent } from './SoftBodyComponent';

import { SpatialPartitioning } from './optimization/SpatialPartitioning';
import { SoftBodyLOD } from './optimization/SoftBodyLOD';
import { SoftRigidInteraction } from './interaction/SoftRigidInteraction';
import { SoftBodyCutter } from './deformation/SoftBodyCutter';

/**
 * 软体物理系统选项
 */
export interface SoftBodySystemOptions {
  /** 是否显示调试信息 */
  debug?: boolean;
  /** 迭代次数 */
  iterations?: number;
  /** 物理系统引用 */
  physicsSystem?: PhysicsSystem;
  /** 是否启用空间分区 */
  useSpatialPartitioning?: boolean;
  /** 是否启用LOD系统 */
  useLOD?: boolean;
  /** 是否启用软体与刚体交互 */
  useSoftRigidInteraction?: boolean;
  /** 是否启用软体切割 */
  useSoftBodyCutter?: boolean;
}

/**
 * 软体物理系统
 * 管理所有软体物理组件
 */
export class SoftBodySystem extends System {
  /** 物理系统引用 */
  private physicsSystem: PhysicsSystem;

  /** 物理世界引用 */
  private physicsWorld: CANNON.World;

  /** 软体组件映射 */
  private softBodies: Map<string, SoftBodyComponent> = new Map();

  /** 是否显示调试信息 */
  private debug: boolean = false;

  /** 迭代次数 */
  private iterations: number = 10;

  /** 调试渲染器 */
  private debugRenderer: THREE.Object3D;

  /** 空间分区系统 */
  private spatialPartitioning: SpatialPartitioning | null = null;

  /** LOD系统 */
  private lodSystem: SoftBodyLOD | null = null;

  /** 软体与刚体交互系统 */
  private softRigidInteraction: SoftRigidInteraction | null = null;

  /** 是否启用空间分区 */
  private useSpatialPartitioning: boolean = false;

  /** 是否启用LOD系统 */
  private useLOD: boolean = false;

  /** 是否启用软体与刚体交互 */
  private useSoftRigidInteraction: boolean = false;

  /** 软体切割系统 */
  private softBodyCutter: SoftBodyCutter | null = null;

  /** 是否启用软体切割 */
  private useSoftBodyCutter: boolean = false;

  /**
   * 创建软体物理系统
   * @param options 软体物理系统选项
   */
  constructor(options: SoftBodySystemOptions = {}) {
    super(2); // 优先级2，在物理系统之后更新

    this.debug = options.debug || false;
    this.iterations = options.iterations || 10;
    this.useSpatialPartitioning = options.useSpatialPartitioning || false;
    this.useLOD = options.useLOD || false;
    this.useSoftRigidInteraction = options.useSoftRigidInteraction || false;
    this.useSoftBodyCutter = options.useSoftBodyCutter || false;

    // 如果提供了物理系统，直接使用
    if (options.physicsSystem) {
      this.physicsSystem = options.physicsSystem;
      this.physicsWorld = this.physicsSystem.getPhysicsWorld();
    }

    // 创建空间分区系统
    if (this.useSpatialPartitioning) {
      this.spatialPartitioning = new SpatialPartitioning();
    }

    // 创建LOD系统
    if (this.useLOD) {
      this.lodSystem = new SoftBodyLOD();
    }

    // 创建软体与刚体交互系统
    if (this.useSoftRigidInteraction) {
      this.softRigidInteraction = new SoftRigidInteraction({
        useSpatialPartitioning: this.useSpatialPartitioning
      });
    }

    // 创建软体切割系统
    if (this.useSoftBodyCutter) {
      this.softBodyCutter = new SoftBodyCutter();
    }
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    // 如果没有物理系统，尝试从引擎获取
    if (!this.physicsSystem) {
      this.physicsSystem = this.engine?.getSystem('PhysicsSystem') as PhysicsSystem;
      if (this.physicsSystem) {
        this.physicsWorld = this.physicsSystem.getPhysicsWorld();
      } else {
        console.error('SoftBodySystem 需要 PhysicsSystem 才能工作');
      }
    }

    // 创建调试渲染器
    if (this.debug) {
      this.debugRenderer = new THREE.Object3D();
      this.debugRenderer.name = 'SoftBodyDebugRenderer';
      const scene = this.engine?.getWorld()?.getActiveScene()?.getThreeScene();
      if (scene) {
        scene.add(this.debugRenderer);
      }
    }
  }

  /**
   * 添加软体组件
   * @param softBody 软体组件
   */
  public addSoftBody(softBody: SoftBodyComponent): void {
    const entity = softBody.getEntity();
    if (!entity) return;

    const entityId = entity.id;
    this.softBodies.set(entityId, softBody);

    // 初始化软体
    if (this.physicsWorld) {
      softBody.initialize(this.physicsWorld);
    }

    // 添加到LOD系统
    if (this.useLOD && this.lodSystem) {
      this.lodSystem.addSoftBody(softBody);
    }

    // 添加到软体与刚体交互系统
    if (this.useSoftRigidInteraction && this.softRigidInteraction) {
      this.softRigidInteraction.addSoftBody(softBody);
    }
  }

  /**
   * 移除软体组件
   * @param entityId 实体ID
   */
  public removeSoftBody(entityId: string): void {
    const softBody = this.softBodies.get(entityId);
    if (softBody) {
      // 从LOD系统移除
      if (this.useLOD && this.lodSystem) {
        this.lodSystem.removeSoftBody(softBody);
      }

      // 从软体与刚体交互系统移除
      if (this.useSoftRigidInteraction && this.softRigidInteraction) {
        this.softRigidInteraction.removeSoftBody(softBody);
      }

      softBody.destroy();
      this.softBodies.delete(entityId);
    }
  }

  /**
   * 获取软体组件
   * @param entityId 实体ID
   * @returns 软体组件
   */
  public getSoftBody(entityId: string): SoftBodyComponent | undefined {
    return this.softBodies.get(entityId);
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 更新LOD系统
    if (this.useLOD && this.lodSystem) {
      this.lodSystem.update();
    }

    // 更新所有软体
    for (const [_entityId, softBody] of this.softBodies.entries()) {
      if (softBody.isInitialized()) {
        // 获取当前软体的迭代次数（考虑LOD）
        let iterations = this.iterations;
        if (this.useLOD && this.lodSystem) {
          const level = this.lodSystem.getLODLevel(softBody);
          iterations = this.lodSystem.getIterationsForLevel(level);
        }

        // 执行内部约束求解迭代
        for (let i = 0; i < iterations; i++) {
          softBody.solveConstraints(deltaTime / iterations);
        }

        // 更新网格
        softBody.updateMesh();
      }
    }

    // 更新空间分区
    if (this.useSpatialPartitioning && this.spatialPartitioning) {
      // 收集所有粒子
      const allParticles: CANNON.Body[] = [];
      for (const [_entityId, softBody] of this.softBodies.entries()) {
        const particles = softBody.getParticles();
        if (particles) {
          allParticles.push(...particles);
        }
      }

      // 更新空间分区
      this.spatialPartitioning.updateAll(allParticles);
    }

    // 更新软体与刚体交互
    if (this.useSoftRigidInteraction && this.softRigidInteraction) {
      this.softRigidInteraction.update(deltaTime);
    }

    // 检查软体撕裂
    if (this.useSoftBodyCutter && this.softBodyCutter) {
      for (const [_entityId, softBody] of this.softBodies.entries()) {
        this.softBodyCutter.checkTearing(softBody);
      }
    }

    // 更新调试渲染
    if (this.debug && this.debugRenderer) {
      this.updateDebugRenderer();
    }
  }

  /**
   * 更新调试渲染器
   */
  private updateDebugRenderer(): void {
    // 清除旧的调试对象
    while (this.debugRenderer.children.length > 0) {
      const child = this.debugRenderer.children[0];
      this.debugRenderer.remove(child);
      if (child instanceof THREE.Mesh) {
        (child.geometry as any).dispose();
        if (Array.isArray(child.material)) {
          child.material.forEach(material => (material as any).dispose());
        } else {
          (child.material as any).dispose();
        }
      }
    }

    // 为每个软体创建调试可视化
    for (const [_entityId, softBody] of this.softBodies.entries()) {
      const debugMesh = softBody.createDebugMesh();
      if (debugMesh) {
        this.debugRenderer.add(debugMesh);
      }
    }
  }

  /**
   * 销毁系统
   */
  public destroy(): void {
    // 销毁所有软体
    for (const [entityId, _softBody] of this.softBodies.entries()) {
      this.removeSoftBody(entityId);
    }

    // 清除调试渲染器
    if (this.debug && this.debugRenderer) {
      if (this.debugRenderer.parent) {
        this.debugRenderer.parent.remove(this.debugRenderer);
      }

      while (this.debugRenderer.children.length > 0) {
        const child = this.debugRenderer.children[0];
        this.debugRenderer.remove(child);
        if (child instanceof THREE.Mesh) {
          (child.geometry as any).dispose();
          if (Array.isArray(child.material)) {
            child.material.forEach(material => (material as any).dispose());
          } else {
            (child.material as any).dispose();
          }
        }
      }
    }

    // 清空空间分区
    if (this.spatialPartitioning) {
      this.spatialPartitioning.clear();
      this.spatialPartitioning = null;
    }

    // 清空LOD系统
    this.lodSystem = null;

    // 清空软体与刚体交互系统
    this.softRigidInteraction = null;

    // 清空软体切割系统
    this.softBodyCutter = null;

    this.softBodies.clear();
  }

  /**
   * 添加刚体到交互系统
   * @param rigidBody 刚体组件
   */
  public addRigidBody(rigidBody: PhysicsBodyComponent): void {
    if (this.useSoftRigidInteraction && this.softRigidInteraction) {
      this.softRigidInteraction.addRigidBody(rigidBody);
    }
  }

  /**
   * 移除刚体从交互系统
   * @param rigidBody 刚体组件
   */
  public removeRigidBody(rigidBody: PhysicsBodyComponent): void {
    if (this.useSoftRigidInteraction && this.softRigidInteraction) {
      this.softRigidInteraction.removeRigidBody(rigidBody);
    }
  }

  /**
   * 设置相机实体（用于LOD系统）
   * @param cameraEntity 相机实体
   */
  public setCameraEntity(cameraEntity: Entity): void {
    if (this.useLOD && this.lodSystem) {
      this.lodSystem.setCameraEntity(cameraEntity);
    }
  }

  /**
   * 使用平面切割软体
   * @param entityId 实体ID
   * @param plane 切割平面
   * @returns 是否成功切割
   */
  public cutSoftBodyWithPlane(entityId: string, plane: { normal: THREE.Vector3; point: THREE.Vector3 }): boolean {
    if (!this.useSoftBodyCutter || !this.softBodyCutter) return false;

    const softBody = this.softBodies.get(entityId);
    if (!softBody) return false;

    return this.softBodyCutter.cutWithPlane(softBody, plane);
  }

  /**
   * 使用射线切割软体
   * @param entityId 实体ID
   * @param ray 切割射线
   * @returns 是否成功切割
   */
  public cutSoftBodyWithRay(entityId: string, ray: { origin: THREE.Vector3; direction: THREE.Vector3; length: number }): boolean {
    if (!this.useSoftBodyCutter || !this.softBodyCutter) return false;

    const softBody = this.softBodies.get(entityId);
    if (!softBody) return false;

    return this.softBodyCutter.cutWithRay(softBody, ray);
  }

  /**
   * 设置撕裂阈值
   * @param threshold 撕裂阈值
   */
  public setTearingThreshold(threshold: number): void {
    if (this.softBodyCutter) {
      this.softBodyCutter.setTearingThreshold(threshold);
    }
  }

  /**
   * 启用/禁用撕裂
   * @param enabled 是否启用
   */
  public setTearingEnabled(enabled: boolean): void {
    if (this.softBodyCutter) {
      if (enabled) {
        this.softBodyCutter.enableTearing();
      } else {
        this.softBodyCutter.disableTearing();
      }
    }
  }
}
