import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface MinioOptions {
  endPoint: string;
  port: number;
  useSSL: boolean;
  accessKey: string;
  secretKey: string;
  defaultBucket: string;
  region?: string;
  partSize?: number;
  maxFileSize: number;
  allowedMimeTypes: string[];
  thumbnailSizes: { width: number; height: number; suffix: string }[];
}

@Injectable()
export class MinioConfig {
  constructor(private configService: ConfigService) {}

  createMinioOptions(): MinioOptions {
    // 解析MinIO端点配置
    const minioEndpoint = this.configService.get('MINIO_ENDPOINT', 'localhost:9000');
    let endPoint: string;
    let port: number;

    if (minioEndpoint.includes(':')) {
      const [host, portStr] = minioEndpoint.split(':');
      endPoint = host;
      port = parseInt(portStr) || 9000;
    } else {
      endPoint = minioEndpoint;
      port = parseInt(this.configService.get('MINIO_PORT', '9000'));
    }

    return {
      endPoint,
      port,
      useSSL: this.configService.get('MINIO_USE_SSL', 'false') === 'true',
      accessKey: this.configService.get('MINIO_ACCESS_KEY', 'minioadmin'),
      secretKey: this.configService.get('MINIO_SECRET_KEY', 'minioadmin'),
      defaultBucket: this.configService.get('MINIO_BUCKET', 'asset-library'),
      region: this.configService.get('MINIO_REGION', 'us-east-1'),
      partSize: parseInt(this.configService.get('MINIO_PART_SIZE', String(64 * 1024 * 1024))), // 64MB
      maxFileSize: parseInt(this.configService.get('MINIO_MAX_FILE_SIZE', String(500 * 1024 * 1024))), // 500MB
      allowedMimeTypes: [
        // 图片
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'image/svg+xml',
        // 3D模型
        'model/gltf-binary',
        'model/gltf+json',
        'application/octet-stream', // .fbx, .obj等
        // 纹理和材质
        'image/x-exr',
        'image/x-hdr',
        // 音频
        'audio/mpeg',
        'audio/wav',
        'audio/ogg',
        // 视频
        'video/mp4',
        'video/webm',
        // 压缩文件
        'application/zip',
        'application/x-rar-compressed',
        'application/x-7z-compressed',
        // 文档
        'application/pdf',
        'text/plain',
        'application/json',
      ],
      thumbnailSizes: [
        { width: 150, height: 150, suffix: '_thumb' },
        { width: 300, height: 300, suffix: '_medium' },
        { width: 800, height: 600, suffix: '_large' },
      ],
    };
  }

  getBucketNames(): { [key: string]: string } {
    return {
      assets: this.configService.get('MINIO_BUCKET_ASSETS', 'assets'),
      thumbnails: this.configService.get('MINIO_BUCKET_THUMBNAILS', 'thumbnails'),
      previews: this.configService.get('MINIO_BUCKET_PREVIEWS', 'previews'),
      temp: this.configService.get('MINIO_BUCKET_TEMP', 'temp'),
      backups: this.configService.get('MINIO_BUCKET_BACKUPS', 'backups'),
    };
  }
}
