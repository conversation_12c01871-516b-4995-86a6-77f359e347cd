/**
 * 协作WebSocket网关
 */
import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
} from '@nestjs/websockets';
import { Logger } from '@nestjs/common';
import { Server, WebSocket } from 'ws';
import { parse } from 'url';
import { CollaborationService } from './collaboration.service';
import { AuthService } from './auth/auth.service';
import { ProjectService } from './project/project.service';
import {
  Message,
  MessageType,
  CollaborationRole,
  Operation,
  JoinMessageData,
  UserStatusData,
} from './dto/message.dto';
import { MessageCompressor, CompressionAlgorithm } from '../utils/message-compressor';
import { MessageBatcher } from '../utils/message-batcher';

interface ExtendedWebSocket extends WebSocket {
  userId?: string;
  projectId?: string;
  sceneId?: string;
  isAlive?: boolean;
  supportsCompression?: boolean;
  compressionAlgorithm?: CompressionAlgorithm;
}

@WebSocketGateway({
  path: '/collaboration',
})
export class CollaborationGateway
  implements OnGatewayConnection, OnGatewayDisconnect, OnGatewayInit
{
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(CollaborationGateway.name);
  private readonly clients: Map<string, ExtendedWebSocket> = new Map();
  private heartbeatInterval: NodeJS.Timeout;
  private readonly messageCompressor: MessageCompressor;
  private readonly operationBatcher: MessageBatcher<Operation & { projectId: string; sceneId: string }>;
  private messageQueue: any[] = [];

  constructor(
    private readonly collaborationService: CollaborationService,
    private readonly authService: AuthService,
    private readonly projectService: ProjectService,
  ) {
    // 初始化消息压缩器
    this.messageCompressor = new MessageCompressor({
      algorithm: CompressionAlgorithm.DEFLATE,
      level: 6,
      minSize: 100,
    });

    // 初始化操作批处理器
    this.operationBatcher = new MessageBatcher<Operation & { projectId: string; sceneId: string }>(
      async (operations) => {
        // 批量处理操作
        await this.processBatchedOperations(operations);
      },
      {
        maxBatchSize: 50,
        maxWaitTime: 50,
        enabled: true,
      },
    );
  }

  /**
   * 初始化网关
   * @param server WebSocket服务器
   */
  afterInit(_server: Server) {
    this.logger.log('协作WebSocket网关已初始化');

    // 设置心跳检测
    this.heartbeatInterval = setInterval(() => {
      this.handleHeartbeat();
    }, 30000);
  }

  /**
   * 处理客户端连接
   * @param client WebSocket客户端
   * @param req 请求对象
   */
  async handleConnection(client: ExtendedWebSocket, req: any) {
    try {
      // 解析URL查询参数
      const { query } = parse(req.url, true);
      const { token, projectId, sceneId } = query;

      if (!token || !projectId || !sceneId) {
        this.sendError(client, '缺少必要参数');
        client.close(1008, '缺少必要参数');
        return;
      }

      // 验证令牌
      const user = await this.authService.validateToken(token as string);

      if (!user) {
        this.sendError(client, '无效的认证令牌');
        client.close(1008, '无效的认证令牌');
        return;
      }

      // 检查项目权限
      const hasPermission = await this.authService.checkProjectPermission(
        user.id,
        projectId as string,
      );

      if (!hasPermission) {
        this.sendError(client, '没有权限访问此项目');
        client.close(1008, '没有权限访问此项目');
        return;
      }

      // 获取项目成员角色
      const projectMembers = await this.projectService.getProjectMembers(projectId as string);
      const member = projectMembers.find(m => m.userId === user.id);
      // 忽略未使用的变量
      void member;

      // 设置用户角色
      // let userRole = CollaborationRole.VIEWER;
      // if (member) {
      //   switch (member.role) {
      //     case 'owner':
      //       userRole = CollaborationRole.OWNER;
      //       break;
      //     case 'admin':
      //       userRole = CollaborationRole.ADMIN;
      //       break;
      //     case 'editor':
      //       userRole = CollaborationRole.EDITOR;
      //       break;
      //     default:
      //       userRole = CollaborationRole.VIEWER;
      //   }
      // }

      // 设置客户端属性
      client.userId = user.id;
      client.projectId = projectId as string;
      client.sceneId = sceneId as string;
      client.isAlive = true;

      // 检查是否支持压缩
      const supportsCompression = query.compression === 'true';
      client.supportsCompression = supportsCompression;

      if (supportsCompression) {
        // 设置默认压缩算法
        client.compressionAlgorithm = CompressionAlgorithm.DEFLATE;

        // 发送压缩信息
        this.sendCompressionInfo(client);
      }

      // 添加到客户端映射表
      this.clients.set(user.id, client);

      this.logger.log(`客户端连接: ${user.username} (${user.id}), 支持压缩: ${supportsCompression}`);
    } catch (error) {
      this.logger.error('处理连接时出错:', error);
      this.sendError(client, '连接失败');
      client.close(1011, '连接失败');
    }
  }

  /**
   * 处理客户端断开连接
   * @param client WebSocket客户端
   */
  handleDisconnect(client: ExtendedWebSocket) {
    try {
      const { userId, projectId, sceneId } = client;

      if (userId && projectId && sceneId) {
        // 处理用户离开
        this.collaborationService.handleUserLeave(userId, projectId, sceneId);

        // 从客户端映射表中移除
        this.clients.delete(userId);

        this.logger.log(`客户端断开连接: ${userId}`);
      }
    } catch (error) {
      this.logger.error('处理断开连接时出错:', error);
    }
  }

  /**
   * 处理心跳检测
   */
  private handleHeartbeat() {
    this.server.clients.forEach((ws: ExtendedWebSocket) => {
      if (ws.isAlive === false) {
        ws.terminate();
        return;
      }

      ws.isAlive = false;
      ws.ping();
    });
  }

  /**
   * 处理加入消息
   * @param client WebSocket客户端
   * @param data 加入数据
   */
  @SubscribeMessage(MessageType.JOIN)
  async handleJoin(client: ExtendedWebSocket, data: JoinMessageData) {
    try {
      const { userId, projectId, sceneId } = client;

      if (!userId || !projectId || !sceneId) {
        this.sendError(client, '未授权的连接');
        return;
      }

      // 验证数据
      if (data.userId !== userId || data.projectId !== projectId || data.sceneId !== sceneId) {
        this.sendError(client, '数据不匹配');
        return;
      }

      // 获取用户角色
      const projectMembers = await this.projectService.getProjectMembers(projectId);
      const member = projectMembers.find(m => m.userId === userId);

      // 设置用户角色
      let userRole = CollaborationRole.VIEWER;
      if (member) {
        switch (member.role) {
          case 'owner':
            userRole = CollaborationRole.OWNER;
            break;
          case 'admin':
            userRole = CollaborationRole.ADMIN;
            break;
          case 'editor':
            userRole = CollaborationRole.EDITOR;
            break;
          default:
            userRole = CollaborationRole.VIEWER;
        }
      }

      // 处理用户加入
      this.collaborationService.handleUserJoin(data, client, userRole);
    } catch (error) {
      this.logger.error('处理加入消息时出错:', error);
      this.sendError(client, '处理加入消息时出错');
    }
  }

  /**
   * 处理用户状态消息
   * @param client WebSocket客户端
   * @param data 状态数据
   */
  @SubscribeMessage(MessageType.USER_STATUS)
  handleUserStatus(client: ExtendedWebSocket, data: UserStatusData) {
    try {
      const { userId, projectId, sceneId } = client;

      if (!userId || !projectId || !sceneId) {
        this.sendError(client, '未授权的连接');
        return;
      }

      // 验证数据
      if (data.userId !== userId) {
        this.sendError(client, '数据不匹配');
        return;
      }

      // 处理用户状态更新
      this.collaborationService.handleUserStatus(data, projectId, sceneId);
    } catch (error) {
      this.logger.error('处理用户状态消息时出错:', error);
      this.sendError(client, '处理用户状态消息时出错');
    }
  }

  /**
   * 处理操作消息
   * @param client WebSocket客户端
   * @param operation 操作
   */
  @SubscribeMessage(MessageType.OPERATION)
  handleOperation(client: ExtendedWebSocket, operation: Operation) {
    try {
      const { userId, projectId, sceneId } = client;

      if (!userId || !projectId || !sceneId) {
        this.sendError(client, '未授权的连接');
        return;
      }

      // 验证数据
      if (operation.userId !== userId) {
        this.sendError(client, '数据不匹配');
        return;
      }

      // 添加到批处理队列
      this.operationBatcher.add({
        ...operation,
        projectId,
        sceneId,
      } as any);
    } catch (error) {
      this.logger.error('处理操作消息时出错:', error);
      this.sendError(client, '处理操作消息时出错');
    }
  }

  /**
   * 批量处理操作
   * @param operations 操作数组
   */
  private async processBatchedOperations(operations: (Operation & { projectId: string; sceneId: string })[]): Promise<void> {
    try {
      if (operations.length === 0) {
        return;
      }

      // 按项目和场景分组
      const groupedOperations = new Map<string, Operation[]>();

      for (const operation of operations) {
        const key = `${operation.projectId}:${operation.sceneId}`;

        if (!groupedOperations.has(key)) {
          groupedOperations.set(key, []);
        }

        // 移除额外的属性
        const { projectId, sceneId, ...cleanOperation } = operation;
        // 忽略未使用的变量
        void projectId;
        void sceneId;
        groupedOperations.get(key)!.push(cleanOperation as Operation);
      }

      // 批量处理每个组
      for (const [key, ops] of groupedOperations.entries()) {
        const [projectId, sceneId] = key.split(':');

        // 批量处理操作
        await this.collaborationService.handleBatchOperations(ops, projectId, sceneId);
      }
    } catch (error) {
      this.logger.error('批量处理操作时出错:', error);
    }
  }

  /**
   * 处理心跳消息
   * @param client WebSocket客户端
   */
  @SubscribeMessage('heartbeat')
  handleHeartbeatMessage(client: ExtendedWebSocket) {
    client.isAlive = true;
  }

  /**
   * 处理压缩算法协商
   * @param client WebSocket客户端
   * @param data 压缩算法数据
   */
  @SubscribeMessage('compression_negotiate')
  handleCompressionNegotiate(client: ExtendedWebSocket, data: { algorithm: string }) {
    try {
      const { algorithm } = data;

      // 验证算法是否支持
      if (
        algorithm === CompressionAlgorithm.DEFLATE ||
        algorithm === CompressionAlgorithm.GZIP ||
        algorithm === CompressionAlgorithm.BROTLI
      ) {
        // 设置客户端压缩算法
        client.compressionAlgorithm = algorithm as CompressionAlgorithm;
        client.supportsCompression = true;

        this.logger.log(`客户端 ${client.userId} 协商压缩算法: ${algorithm}`);
      } else {
        // 不支持的算法，禁用压缩
        client.supportsCompression = false;
        client.compressionAlgorithm = undefined;

        this.logger.warn(`客户端 ${client.userId} 请求不支持的压缩算法: ${algorithm}`);
      }
    } catch (error) {
      this.logger.error('处理压缩算法协商时出错:', error);
    }
  }

  /**
   * 发送压缩信息
   * @param client WebSocket客户端
   */
  private sendCompressionInfo(client: ExtendedWebSocket): void {
    try {
      const compressionInfo: Message = {
        type: MessageType.COMPRESSION_INFO,
        data: {
          supported: true,
          algorithms: [
            CompressionAlgorithm.DEFLATE,
            CompressionAlgorithm.GZIP,
            CompressionAlgorithm.BROTLI,
          ],
          preferred: CompressionAlgorithm.DEFLATE,
        },
      };

      client.send(JSON.stringify(compressionInfo));
    } catch (error) {
      this.logger.error('发送压缩信息时出错:', error);
    }
  }

  /**
   * 发送错误消息
   * @param client WebSocket客户端
   * @param message 错误消息
   */
  private async sendError(client: ExtendedWebSocket, message: string) {
    try {
      const errorMessage: Message = {
        type: MessageType.ERROR,
        data: {
          message,
          code: 1000,
        },
      };

      // 检查客户端是否支持压缩
      if (client.supportsCompression && client.compressionAlgorithm) {
        // 压缩消息
        const { data, algorithm } = await this.messageCompressor.compress(errorMessage);

        // 添加压缩头信息
        const compressionHeader = Buffer.from([
          0x01, // 压缩标记
          algorithm === CompressionAlgorithm.DEFLATE ? 0x01 :
          algorithm === CompressionAlgorithm.GZIP ? 0x02 :
          algorithm === CompressionAlgorithm.BROTLI ? 0x03 : 0x00,
        ]);

        // 发送压缩消息
        const message = Buffer.concat([compressionHeader, data]);
        client.send(message);
      } else {
        // 发送未压缩消息
        client.send(JSON.stringify(errorMessage));
      }
    } catch (error) {
      this.logger.error('发送错误消息时出错:', error);
    }
  }

  /**
   * 获取活跃连接数
   * @returns 活跃连接数
   */
  public getActiveConnections(): number {
    return this.clients.size;
  }

  /**
   * 获取消息队列长度
   * @returns 消息队列长度
   */
  public getMessageQueueLength(): number {
    return this.messageQueue.length + this.operationBatcher.getQueueLength();
  }
}
