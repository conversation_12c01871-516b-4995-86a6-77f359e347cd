/**
 * 用户控制器
 */
import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { MessagePattern } from '@nestjs/microservices';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { CreateUserAvatarDto } from './dto/create-user-avatar.dto';
import { CreateUserSettingDto } from './dto/create-user-setting.dto';
import { User } from './entities/user.entity';
import { UserAvatar } from './entities/user-avatar.entity';
import { UserSetting } from './entities/user-setting.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from './entities/user.entity';

@ApiTags('用户')
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  @ApiOperation({ summary: '创建用户' })
  @ApiResponse({ status: 201, description: '用户创建成功', type: User })
  async create(@Body() createUserDto: CreateUserDto): Promise<User> {
    return this.usersService.create(createUserDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取所有用户' })
  @ApiResponse({ status: 200, description: '返回所有用户', type: [User] })
  async findAll(): Promise<User[]> {
    return this.usersService.findAll();
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '根据ID获取用户' })
  @ApiResponse({ status: 200, description: '返回用户信息', type: User })
  async findOne(@Param('id') id: string): Promise<User> {
    return this.usersService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新用户' })
  @ApiResponse({ status: 200, description: '用户更新成功', type: User })
  async update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto): Promise<User> {
    return this.usersService.update(id, updateUserDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除用户' })
  @ApiResponse({ status: 204, description: '用户删除成功' })
  async remove(@Param('id') id: string): Promise<void> {
    return this.usersService.remove(id);
  }

  @Post(':id/avatar')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建或更新用户头像' })
  @ApiResponse({ status: 201, description: '用户头像创建或更新成功', type: UserAvatar })
  async createAvatar(
    @Param('id') id: string,
    @Body() createUserAvatarDto: CreateUserAvatarDto,
  ): Promise<UserAvatar> {
    return this.usersService.createAvatar(id, createUserAvatarDto);
  }

  @Post(':id/settings')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建或更新用户设置' })
  @ApiResponse({ status: 201, description: '用户设置创建或更新成功', type: UserSetting })
  async createSetting(
    @Param('id') id: string,
    @Body() createUserSettingDto: CreateUserSettingDto,
  ): Promise<UserSetting> {
    return this.usersService.createSetting(id, createUserSettingDto);
  }

  @Get(':id/settings/:key')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取用户设置' })
  @ApiResponse({ status: 200, description: '返回用户设置', type: UserSetting })
  async getSetting(@Param('id') id: string, @Param('key') key: string): Promise<UserSetting> {
    return this.usersService.getSetting(id, key);
  }

  @Delete(':id/settings/:key')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除用户设置' })
  @ApiResponse({ status: 204, description: '用户设置删除成功' })
  async removeSetting(@Param('id') id: string, @Param('key') key: string): Promise<void> {
    return this.usersService.removeSetting(id, key);
  }

  // 微服务消息处理
  @MessagePattern({ cmd: 'findUserById' })
  async handleFindUserById(id: string): Promise<User> {
    return this.usersService.findOne(id);
  }

  @MessagePattern({ cmd: 'findUserByUsername' })
  async handleFindUserByUsername(username: string): Promise<User> {
    return this.usersService.findByUsername(username);
  }

  @MessagePattern({ cmd: 'findUserByEmail' })
  async handleFindUserByEmail(email: string): Promise<User> {
    return this.usersService.findByEmail(email);
  }

  @MessagePattern({ cmd: 'validateUser' })
  async handleValidateUser(data: { usernameOrEmail: string; password: string }): Promise<User> {
    return this.usersService.validateUser(data.usernameOrEmail, data.password);
  }

  @MessagePattern({ cmd: 'findAllUsers' })
  async handleFindAllUsers(): Promise<User[]> {
    return this.usersService.findAll();
  }

  @MessagePattern({ cmd: 'register' })
  async handleRegister(data: { username: string; email: string; password: string; displayName?: string }): Promise<any> {
    const user = await this.usersService.create({
      username: data.username,
      email: data.email,
      password: data.password,
      displayName: data.displayName,
    });

    // 返回用户信息（不包含密码）
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }

  @MessagePattern({ cmd: 'updateUser' })
  async handleUpdateUser(data: { id: string } & UpdateUserDto): Promise<User> {
    const { id, ...updateUserDto } = data;
    return this.usersService.update(id, updateUserDto as UpdateUserDto);
  }

  @MessagePattern({ cmd: 'removeUser' })
  async handleRemoveUser(id: string): Promise<void> {
    return this.usersService.remove(id);
  }

  @MessagePattern({ cmd: 'createUserAvatar' })
  async handleCreateUserAvatar(
    data: { userId: string } & CreateUserAvatarDto,
  ): Promise<UserAvatar> {
    const { userId, ...createUserAvatarDto } = data;
    return this.usersService.createAvatar(userId, createUserAvatarDto as CreateUserAvatarDto);
  }

  @MessagePattern({ cmd: 'createUserSetting' })
  async handleCreateUserSetting(
    data: { userId: string } & CreateUserSettingDto,
  ): Promise<UserSetting> {
    const { userId, ...createUserSettingDto } = data;
    return this.usersService.createSetting(userId, createUserSettingDto as CreateUserSettingDto);
  }

  @MessagePattern({ cmd: 'getUserSetting' })
  async handleGetUserSetting(data: { userId: string; key: string }): Promise<UserSetting> {
    return this.usersService.getSetting(data.userId, data.key);
  }

  @MessagePattern({ cmd: 'removeUserSetting' })
  async handleRemoveUserSetting(data: { userId: string; key: string }): Promise<void> {
    return this.usersService.removeSetting(data.userId, data.key);
  }
}
