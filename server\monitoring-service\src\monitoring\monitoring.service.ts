import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThan, Between } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { MetricsEntity } from './entities/metrics.entity';
import { ServiceMetricsEntity } from './entities/service-metrics.entity';
import { SystemMetricsEntity } from './entities/system-metrics.entity';
import { MetricsCollectorService } from './metrics-collector.service';
import { MetricsAggregatorService } from './metrics-aggregator.service';
import { MetricsStorageService } from './metrics-storage.service';

@Injectable()
export class MonitoringService implements OnModuleInit {
  private readonly logger = new Logger(MonitoringService.name);
  private readonly retentionPeriod: number;
  private readonly aggregationInterval: number;

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly metricsCollectorService: MetricsCollectorService,
    private readonly metricsAggregatorService: MetricsAggregatorService,
    private readonly metricsStorageService: MetricsStorageService,
    @InjectRepository(MetricsEntity)
    private readonly metricsRepository: Repository<MetricsEntity>,
    @InjectRepository(ServiceMetricsEntity)
    private readonly serviceMetricsRepository: Repository<ServiceMetricsEntity>,
    @InjectRepository(SystemMetricsEntity)
    private readonly systemMetricsRepository: Repository<SystemMetricsEntity>,
  ) {
    this.retentionPeriod = this.configService.get<number>('METRICS_RETENTION_DAYS', 30) * 24 * 60 * 60 * 1000;
    this.aggregationInterval = this.configService.get<number>('METRICS_AGGREGATION_MINUTES', 5) * 60 * 1000;
  }

  async onModuleInit() {
    this.logger.log('监控服务已初始化');
    // 初始化时收集一次指标
    await this.collectAllMetrics();
  }

  /**
   * 定时收集所有服务的指标
   */
  @Cron(CronExpression.EVERY_MINUTE)
  async collectAllMetrics() {
    try {
      this.logger.debug('开始收集所有服务指标');

      // 从服务注册中心获取所有服务实例
      const services = await this.metricsCollectorService.getRegisteredServices();

      // 收集每个服务的指标
      for (const service of services) {
        try {
          const metrics = await this.metricsCollectorService.collectServiceMetrics(service);
          await this.metricsStorageService.storeServiceMetrics(service, metrics);
        } catch (error) {
          this.logger.error(`收集服务 ${service.id} 指标失败: ${error.message}`, error.stack);
        }
      }

      // 收集系统指标
      const systemMetrics = await this.metricsCollectorService.collectSystemMetrics();
      await this.metricsStorageService.storeSystemMetrics(systemMetrics);

      this.logger.debug('所有服务指标收集完成');

      // 触发指标收集完成事件
      this.eventEmitter.emit('monitoring.metrics.collected');
    } catch (error) {
      this.logger.error(`收集指标失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 定时聚合指标数据
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async aggregateMetrics() {
    try {
      this.logger.debug('开始聚合指标数据');

      const now = new Date();
      const startTime = new Date(now.getTime() - this.aggregationInterval);

      // 聚合服务指标
      await this.metricsAggregatorService.aggregateServiceMetrics(startTime, now);

      // 聚合系统指标
      await this.metricsAggregatorService.aggregateSystemMetrics(startTime, now);

      this.logger.debug('指标数据聚合完成');
    } catch (error) {
      this.logger.error(`聚合指标数据失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 定时清理过期的指标数据
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async cleanupOldMetrics() {
    try {
      this.logger.debug('开始清理过期指标数据');

      const cutoffDate = new Date(Date.now() - this.retentionPeriod);

      // 清理原始指标数据
      await this.metricsRepository.delete({
        timestamp: LessThan(cutoffDate),
      });

      // 清理服务指标数据
      await this.serviceMetricsRepository.delete({
        timestamp: LessThan(cutoffDate),
      });

      // 清理系统指标数据
      await this.systemMetricsRepository.delete({
        timestamp: LessThan(cutoffDate),
      });

      this.logger.debug('过期指标数据清理完成');
    } catch (error) {
      this.logger.error(`清理过期指标数据失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取服务指标
   */
  async getServiceMetrics(serviceId: string, startTime: Date, endTime: Date) {
    return this.serviceMetricsRepository.find({
      where: {
        serviceId,
        timestamp: Between(startTime, endTime),
      },
      order: { timestamp: 'ASC' },
    });
  }

  /**
   * 获取系统指标
   */
  async getSystemMetrics(hostname: string, startTime: Date, endTime: Date) {
    return this.systemMetricsRepository.find({
      where: {
        hostname,
        timestamp: Between(startTime, endTime),
      },
      order: { timestamp: 'ASC' },
    });
  }
}
