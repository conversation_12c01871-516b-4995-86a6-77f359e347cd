import { <PERSON>, Get, Post, Body, Param, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { InstanceService, Instance } from './instance.service';
import { InstanceMigrationService } from './instance-migration.service';
import { LoadBalancerService } from './load-balancer.service';

@Controller('instances')
export class InstanceController {
  private readonly logger = new Logger(InstanceController.name);

  constructor(
    private readonly instanceService: InstanceService,
    private readonly instanceMigrationService: InstanceMigrationService,
    private readonly loadBalancerService: LoadBalancerService,
  ) {}

  /**
   * 获取所有实例
   */
  @Get()
  getAllInstances(): Instance[] {
    return this.instanceService.getAllInstances();
  }

  /**
   * 获取可用实例
   */
  @Get('available')
  getAvailableInstances(): Instance[] {
    return this.instanceService.getAvailableInstances();
  }

  /**
   * 获取指定实例
   */
  @Get(':id')
  getInstance(@Param('id') id: string): Instance {
    const instance = this.instanceService.getInstance(id);

    if (!instance) {
      throw new HttpException(`实例不存在: ${id}`, HttpStatus.NOT_FOUND);
    }

    return instance;
  }

  /**
   * 创建实例
   */
  @Post()
  async createInstance(@Body() createInstanceDto: {
    sceneId?: string;
    locationId?: string;
    channelId?: string;
    isMediaInstance?: boolean;
  }): Promise<Instance> {
    try {
      return await this.instanceService.createInstance(createInstanceDto);
    } catch (error) {
      this.logger.error(`创建实例失败: ${error.message}`, error.stack);
      throw new HttpException(`创建实例失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 加入实例
   */
  @Post(':id/join')
  async joinInstance(
    @Param('id') id: string,
    @Body() joinInstanceDto: { userId: string },
  ): Promise<{ success: boolean; message: string }> {
    const success = await this.instanceService.joinInstance(id, joinInstanceDto.userId);

    if (!success) {
      throw new HttpException('加入实例失败', HttpStatus.BAD_REQUEST);
    }

    return {
      success,
      message: '成功加入实例',
    };
  }

  /**
   * 离开实例
   */
  @Post(':id/leave')
  async leaveInstance(
    @Param('id') id: string,
    @Body() leaveInstanceDto: { userId: string },
  ): Promise<{ success: boolean; message: string }> {
    const success = await this.instanceService.leaveInstance(id, leaveInstanceDto.userId);

    if (!success) {
      throw new HttpException('离开实例失败', HttpStatus.BAD_REQUEST);
    }

    return {
      success,
      message: '成功离开实例',
    };
  }

  /**
   * 关闭实例
   */
  @Post(':id/close')
  async closeInstance(@Param('id') id: string): Promise<{ success: boolean; message: string }> {
    const success = await this.instanceService.closeInstance(id);

    if (!success) {
      throw new HttpException('关闭实例失败', HttpStatus.BAD_REQUEST);
    }

    return {
      success,
      message: '实例已关闭',
    };
  }

  /**
   * 获取实例用户
   */
  @Get(':id/users')
  async getInstanceUsers(@Param('id') id: string): Promise<string[]> {
    try {
      return await this.instanceService.getInstanceUsers(id);
    } catch (error) {
      this.logger.error(`获取实例用户失败: ${error.message}`, error.stack);
      throw new HttpException(`获取实例用户失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取实例用户详情
   */
  @Get(':id/users/details')
  getInstanceUserDetails(@Param('id') id: string) {
    try {
      return this.instanceService.getInstanceUserDetails(id);
    } catch (error) {
      this.logger.error(`获取实例用户详情失败: ${error.message}`, error.stack);
      throw new HttpException(`获取实例用户详情失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 更新实例指标
   */
  @Post(':id/metrics')
  updateInstanceMetrics(
    @Param('id') id: string,
    @Body() metricsDto: {
      cpuUsage: number;
      memoryUsage: number;
      networkUsage: number;
    },
  ): { success: boolean } {
    try {
      this.instanceService.updateInstanceMetrics(id, metricsDto);
      return { success: true };
    } catch (error) {
      this.logger.error(`更新实例指标失败: ${error.message}`, error.stack);
      throw new HttpException(`更新实例指标失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取实例指标
   */
  @Get(':id/metrics')
  getInstanceMetrics(@Param('id') id: string) {
    try {
      const metrics = this.instanceService.getInstanceMetrics(id);

      if (!metrics) {
        throw new HttpException(`实例 ${id} 的指标不存在`, HttpStatus.NOT_FOUND);
      }

      return metrics;
    } catch (error) {
      this.logger.error(`获取实例指标失败: ${error.message}`, error.stack);
      throw new HttpException(`获取实例指标失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 开始实例迁移
   */
  @Post(':id/migrate')
  async startMigration(
    @Param('id') id: string,
    @Body() migrateDto: { targetInstanceId: string },
  ): Promise<{ migrationId: string }> {
    try {
      const migrationId = await this.instanceMigrationService.startMigration(
        id,
        migrateDto.targetInstanceId,
      );

      return { migrationId };
    } catch (error) {
      this.logger.error(`开始实例迁移失败: ${error.message}`, error.stack);
      throw new HttpException(`开始实例迁移失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取迁移状态
   */
  @Get('migration/:migrationId')
  getMigrationStatus(@Param('migrationId') migrationId: string) {
    try {
      const status = this.instanceMigrationService.getMigrationStatus(migrationId);

      if (!status) {
        throw new HttpException(`迁移 ${migrationId} 不存在`, HttpStatus.NOT_FOUND);
      }

      return status;
    } catch (error) {
      this.logger.error(`获取迁移状态失败: ${error.message}`, error.stack);
      throw new HttpException(`获取迁移状态失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取所有迁移
   */
  @Get('migrations/all')
  getAllMigrations() {
    try {
      return this.instanceMigrationService.getAllMigrations();
    } catch (error) {
      this.logger.error(`获取所有迁移失败: ${error.message}`, error.stack);
      throw new HttpException(`获取所有迁移失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取实例负载
   */
  @Get(':id/load')
  getInstanceLoad(@Param('id') id: string) {
    try {
      const load = this.loadBalancerService.getInstanceLoad(id);

      if (!load) {
        throw new HttpException(`实例 ${id} 的负载信息不存在`, HttpStatus.NOT_FOUND);
      }

      return load;
    } catch (error) {
      this.logger.error(`获取实例负载失败: ${error.message}`, error.stack);
      throw new HttpException(`获取实例负载失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取所有实例负载
   */
  @Get('load/all')
  getAllInstancesLoad() {
    try {
      return this.loadBalancerService.getAllInstancesLoad();
    } catch (error) {
      this.logger.error(`获取所有实例负载失败: ${error.message}`, error.stack);
      throw new HttpException(`获取所有实例负载失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 手动触发负载均衡
   */
  @Post('balance')
  async triggerLoadBalancing(): Promise<{ success: boolean }> {
    try {
      await this.loadBalancerService.checkAndBalanceLoad();
      return { success: true };
    } catch (error) {
      this.logger.error(`触发负载均衡失败: ${error.message}`, error.stack);
      throw new HttpException(`触发负载均衡失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
