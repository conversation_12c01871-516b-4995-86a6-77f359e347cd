/**
 * 实体类
 * 游戏世界中的基本对象，由组件组成
 */
import { Component } from './Component';
import { type World } from './World';
import { Transform } from '../scene/Transform';
import { EventEmitter } from '../utils/EventEmitter';
export declare class Entity extends EventEmitter {
    /** 实体ID */
    id: string;
    /** 实体名称 */
    name: string;
    /** 是否活跃 */
    private active;
    /** 标签列表 */
    private tags;
    /** 组件映射 */
    private components;
    /** 子实体列表 */
    private children;
    /** 父实体 */
    private parent;
    /** 世界引用 */
    private world;
    /** 变换组件 */
    private _transform;
    /**
     * 创建实体实例
     * @param name 实体名称
     */
    constructor(name?: string);
    /**
     * 更新实体
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 固定时间步长更新
     * @param fixedDeltaTime 固定帧间隔时间（秒）
     */
    fixedUpdate(fixedDeltaTime: number): void;
    /**
     * 添加组件
     * @param component 组件实例
     * @returns 添加的组件
     */
    addComponent<T extends Component>(component: T): T;
    /**
     * 获取组件
     * @param type 组件类型
     * @returns 组件实例，如果不存在则返回null
     */
    getComponent<T extends Component>(type: string): T | null;
    /**
     * 移除组件
     * @param component 组件实例或类型
     * @returns 是否成功移除
     */
    removeComponent(component: Component | string): boolean;
    /**
     * 获取所有组件
     * @returns 组件数组
     */
    getAllComponents(): Component[];
    /**
     * 是否有组件
     * @param type 组件类型
     * @returns 是否有该类型的组件
     */
    hasComponent(type: string): boolean;
    /**
     * 添加子实体
     * @param child 子实体
     * @returns 添加的子实体
     */
    addChild(child: Entity): Entity;
    /**
     * 移除子实体
     * @param child 子实体
     * @returns 是否成功移除
     */
    removeChild(child: Entity): boolean;
    /**
     * 获取所有子实体
     * @returns 子实体数组
     */
    getChildren(): Entity[];
    /**
     * 根据名称获取子实体
     * @param name 子实体名称
     * @returns 子实体，如果不存在则返回null
     */
    getChildByName(name: string): Entity | null;
    /**
     * 获取父实体
     * @returns 父实体，如果没有则返回null
     */
    getParent(): Entity | null;
    /**
     * 获取变换组件
     * @returns 变换组件
     */
    getTransform(): Transform;
    /**
     * 获取变换组件（属性访问器）
     * @returns 变换组件
     */
    get transform(): Transform;
    /**
     * 设置世界引用
     * @param world 世界实例
     */
    setWorld(world: World): void;
    /**
     * 获取世界引用
     * @returns 世界实例
     */
    getWorld(): World | null;
    /**
     * 设置活跃状态
     * @param active 是否活跃
     */
    setActive(active: boolean): void;
    /**
     * 是否活跃
     * @returns 是否活跃
     */
    isActive(): boolean;
    /**
     * 添加标签
     * @param tag 标签
     */
    addTag(tag: string): void;
    /**
     * 移除标签
     * @param tag 标签
     * @returns 是否成功移除
     */
    removeTag(tag: string): boolean;
    /**
     * 是否有标签
     * @param tag 标签
     * @returns 是否有该标签
     */
    hasTag(tag: string): boolean;
    /**
     * 获取所有标签
     * @returns 标签数组
     */
    getTags(): string[];
    /**
     * 销毁实体
     */
    dispose(): void;
}
