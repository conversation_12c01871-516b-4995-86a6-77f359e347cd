/**
 * 用户实体
 */
import {
  <PERSON>tity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  OneToOne,
} from 'typeorm';
import { UserAvatar } from './user-avatar.entity';
import { UserSetting } from './user-setting.entity';

export enum UserRole {
  USER = 'user',
  ADMIN = 'admin',
}

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  username: string;

  @Column({ unique: true })
  email: string;

  @Column({ select: false })
  password: string;

  @Column({ nullable: true })
  displayName: string;

  @Column({ default: false })
  isVerified: boolean;

  @Column({ default: false })
  isGuest: boolean;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.USER,
  })
  role: UserRole;

  @Column({ nullable: true })
  inviteCode: string;

  @Column({ nullable: true })
  lastLoginAt: Date;

  @OneToOne(() => UserAvatar, avatar => avatar.user, { cascade: true })
  avatar: UserAvatar;

  @OneToMany(() => UserSetting, setting => setting.user, { cascade: true })
  settings: UserSetting[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
