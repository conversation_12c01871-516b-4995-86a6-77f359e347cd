import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpClientService } from '../../../common/services/http-client.service';
import { LoggerService } from '../../../common/services/logger.service';

export interface TextAnalysisResult {
  sentiment: 'positive' | 'negative' | 'neutral';
  keywords: string[];
  entities: {
    type: string;
    value: string;
    confidence: number;
  }[];
  sceneElements: {
    objects: string[];
    environment: string;
    lighting: string;
    mood: string;
    style: string;
  };
  complexity: 'simple' | 'medium' | 'complex';
  language: string;
}

@Injectable()
export class TextAnalysisService {
  constructor(
    private readonly configService: ConfigService,
    private readonly httpClientService: HttpClientService,
    private readonly logger: LoggerService,
  ) {}

  async analyzeText(text: string): Promise<TextAnalysisResult> {
    try {
      this.logger.log(`开始分析文本: ${text.substring(0, 100)}...`);

      // 基础文本预处理
      const cleanText = this.preprocessText(text);

      // 并行执行多种分析
      const [
        sentiment,
        keywords,
        entities,
        sceneElements,
        complexity,
        language
      ] = await Promise.all([
        this.analyzeSentiment(cleanText),
        this.extractKeywords(cleanText),
        this.extractEntities(cleanText),
        this.extractSceneElements(cleanText),
        this.analyzeComplexity(cleanText),
        this.detectLanguage(cleanText)
      ]);

      const result: TextAnalysisResult = {
        sentiment,
        keywords,
        entities,
        sceneElements,
        complexity,
        language
      };

      this.logger.log(`文本分析完成，提取到 ${keywords.length} 个关键词，${entities.length} 个实体`);
      return result;

    } catch (error) {
      this.logger.error('文本分析失败:', error);
      // 返回默认分析结果
      return this.getDefaultAnalysisResult(text);
    }
  }

  /**
   * 文本预处理
   */
  private preprocessText(text: string): string {
    return text
      .trim()
      .replace(/\s+/g, ' ')
      .replace(/[^\w\s\u4e00-\u9fff]/g, ' ')
      .toLowerCase();
  }

  /**
   * 情感分析
   */
  private async analyzeSentiment(text: string): Promise<'positive' | 'negative' | 'neutral'> {
    try {
      // 简单的情感词典方法
      const positiveWords = ['美丽', '漂亮', '温馨', '舒适', '明亮', '宽敞', '豪华', '精致', '优雅', '现代'];
      const negativeWords = ['黑暗', '阴森', '破旧', '狭窄', '脏乱', '恐怖', '废弃', '荒凉'];

      const positiveCount = positiveWords.filter(word => text.includes(word)).length;
      const negativeCount = negativeWords.filter(word => text.includes(word)).length;

      if (positiveCount > negativeCount) return 'positive';
      if (negativeCount > positiveCount) return 'negative';
      return 'neutral';
    } catch (error) {
      this.logger.warn('情感分析失败，使用默认值:', error);
      return 'neutral';
    }
  }

  /**
   * 关键词提取
   */
  private async extractKeywords(text: string): Promise<string[]> {
    try {
      // 场景相关关键词词典
      const sceneKeywords = [
        '房间', '客厅', '卧室', '厨房', '办公室', '花园', '公园', '森林', '海滩', '山脉',
        '建筑', '房子', '大楼', '桥梁', '道路', '街道', '广场', '商店', '餐厅', '咖啡厅',
        '桌子', '椅子', '沙发', '床', '书架', '电视', '电脑', '植物', '花朵', '树木',
        '阳光', '月亮', '星星', '云朵', '雨水', '雪花', '火焰', '水面', '石头', '草地'
      ];

      const foundKeywords = sceneKeywords.filter(keyword =>
        text.includes(keyword)
      );

      // 如果没有找到预定义关键词，使用简单的词频分析
      if (foundKeywords.length === 0) {
        const words = text.split(/\s+/).filter(word => word.length > 1);
        const wordCount = new Map<string, number>();

        words.forEach(word => {
          wordCount.set(word, (wordCount.get(word) || 0) + 1);
        });

        return Array.from(wordCount.entries())
          .sort((a, b) => b[1] - a[1])
          .slice(0, 10)
          .map(([word]) => word);
      }

      return foundKeywords.slice(0, 10);
    } catch (error) {
      this.logger.warn('关键词提取失败:', error);
      return ['场景', '生成'];
    }
  }

  /**
   * 实体提取
   */
  private async extractEntities(text: string): Promise<Array<{type: string; value: string; confidence: number}>> {
    try {
      const entities = [];

      // 颜色实体
      const colors = ['红色', '蓝色', '绿色', '黄色', '白色', '黑色', '灰色', '紫色', '橙色', '粉色'];
      colors.forEach(color => {
        if (text.includes(color)) {
          entities.push({ type: 'COLOR', value: color, confidence: 0.9 });
        }
      });

      // 材质实体
      const materials = ['木质', '金属', '玻璃', '石材', '布料', '皮革', '塑料', '陶瓷'];
      materials.forEach(material => {
        if (text.includes(material)) {
          entities.push({ type: 'MATERIAL', value: material, confidence: 0.8 });
        }
      });

      // 尺寸实体
      const sizes = ['大', '小', '巨大', '微小', '宽敞', '狭窄', '高', '低'];
      sizes.forEach(size => {
        if (text.includes(size)) {
          entities.push({ type: 'SIZE', value: size, confidence: 0.7 });
        }
      });

      return entities;
    } catch (error) {
      this.logger.warn('实体提取失败:', error);
      return [];
    }
  }

  /**
   * 场景元素提取
   */
  private async extractSceneElements(text: string): Promise<{
    objects: string[];
    environment: string;
    lighting: string;
    mood: string;
    style: string;
  }> {
    try {
      // 物体识别
      const objectKeywords = ['桌子', '椅子', '沙发', '床', '书架', '电视', '植物', '花朵', '树木', '汽车', '建筑'];
      const objects = objectKeywords.filter(obj => text.includes(obj));

      // 环境识别
      let environment = '室内';
      const outdoorKeywords = ['户外', '公园', '森林', '海滩', '山脉', '街道', '广场'];
      if (outdoorKeywords.some(keyword => text.includes(keyword))) {
        environment = '户外';
      }

      // 光照识别
      let lighting = '自然光';
      if (text.includes('夜晚') || text.includes('黑暗')) lighting = '夜间';
      if (text.includes('阳光') || text.includes('明亮')) lighting = '明亮';
      if (text.includes('昏暗') || text.includes('阴暗')) lighting = '昏暗';

      // 情绪识别
      let mood = '平静';
      if (text.includes('温馨') || text.includes('舒适')) mood = '温馨';
      if (text.includes('神秘') || text.includes('阴森')) mood = '神秘';
      if (text.includes('活泼') || text.includes('热闹')) mood = '活泼';

      // 风格识别
      let style = '现代';
      if (text.includes('古典') || text.includes('传统')) style = '古典';
      if (text.includes('科幻') || text.includes('未来')) style = '科幻';
      if (text.includes('自然') || text.includes('田园')) style = '自然';

      return { objects, environment, lighting, mood, style };
    } catch (error) {
      this.logger.warn('场景元素提取失败:', error);
      return {
        objects: [],
        environment: '室内',
        lighting: '自然光',
        mood: '平静',
        style: '现代'
      };
    }
  }

  /**
   * 复杂度分析
   */
  private async analyzeComplexity(text: string): Promise<'simple' | 'medium' | 'complex'> {
    try {
      const wordCount = text.split(/\s+/).length;
      const uniqueWords = new Set(text.split(/\s+/)).size;
      const complexity = uniqueWords / wordCount;

      if (wordCount < 20 && complexity < 0.7) return 'simple';
      if (wordCount < 50 && complexity < 0.8) return 'medium';
      return 'complex';
    } catch (error) {
      this.logger.warn('复杂度分析失败:', error);
      return 'medium';
    }
  }

  /**
   * 语言检测
   */
  private async detectLanguage(text: string): Promise<string> {
    try {
      // 简单的中英文检测
      const chineseChars = text.match(/[\u4e00-\u9fff]/g);
      const englishChars = text.match(/[a-zA-Z]/g);

      const chineseRatio = chineseChars ? chineseChars.length / text.length : 0;
      const englishRatio = englishChars ? englishChars.length / text.length : 0;

      if (chineseRatio > englishRatio) return 'zh';
      if (englishRatio > chineseRatio) return 'en';
      return 'mixed';
    } catch (error) {
      this.logger.warn('语言检测失败:', error);
      return 'zh';
    }
  }

  /**
   * 获取默认分析结果
   */
  private getDefaultAnalysisResult(text: string): TextAnalysisResult {
    return {
      sentiment: 'neutral',
      keywords: ['场景', '生成'],
      entities: [],
      sceneElements: {
        objects: [],
        environment: '室内',
        lighting: '自然光',
        mood: '平静',
        style: '现代'
      },
      complexity: 'medium',
      language: 'zh'
    };
  }
}
