/**
 * 语音场景生成控制器
 * 整合语音识别、场景生成和语音反馈，提供完整的语音交互场景生成体验
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { SpeechRecognitionService } from '../../rag/speech/SpeechRecognitionService';
import { SpeechSynthesisService } from '../../rag/speech/SpeechSynthesisService';
import { SceneGenerationAIManager } from './SceneGenerationAIManager';
import { SceneGenerationResult, SceneGenerationOptions } from './SceneGenerationTypes';

/**
 * 语音会话结果
 */
export interface VoiceSessionResult {
  /** 会话ID */
  sessionId: string;
  /** 会话状态 */
  status: 'active' | 'paused' | 'stopped' | 'error';
  /** 错误信息 */
  error?: string;
}

/**
 * 语音会话状态
 */
export interface VoiceSessionState {
  /** 会话ID */
  sessionId: string;
  /** 是否正在监听 */
  isListening: boolean;
  /** 是否正在生成 */
  isGenerating: boolean;
  /** 是否正在说话 */
  isSpeaking: boolean;
  /** 当前转录文本 */
  currentTranscript: string;
  /** 生成进度 */
  generationProgress: number;
  /** 当前场景 */
  currentScene?: any;
  /** 会话历史 */
  history: VoiceInteraction[];
}

/**
 * 语音交互记录
 */
export interface VoiceInteraction {
  /** 时间戳 */
  timestamp: number;
  /** 用户输入 */
  userInput: string;
  /** 系统响应 */
  systemResponse: string;
  /** 生成结果 */
  generationResult?: SceneGenerationResult;
  /** 置信度 */
  confidence: number;
}

/**
 * 对话管理器
 */
export class ConversationManager {
  private sessions: Map<string, VoiceSessionState> = new Map();

  /**
   * 创建新会话
   */
  createSession(): string {
    const sessionId = this.generateSessionId();
    const session: VoiceSessionState = {
      sessionId,
      isListening: false,
      isGenerating: false,
      isSpeaking: false,
      currentTranscript: '',
      generationProgress: 0,
      history: []
    };
    
    this.sessions.set(sessionId, session);
    return sessionId;
  }

  /**
   * 获取会话状态
   */
  getSession(sessionId: string): VoiceSessionState | undefined {
    return this.sessions.get(sessionId);
  }

  /**
   * 更新会话状态
   */
  updateSession(sessionId: string, updates: Partial<VoiceSessionState>): void {
    const session = this.sessions.get(sessionId);
    if (session) {
      Object.assign(session, updates);
    }
  }

  /**
   * 添加用户消息
   */
  addUserMessage(sessionId: string, message: string): void {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.currentTranscript = message;
    }
  }

  /**
   * 添加交互记录
   */
  addInteraction(sessionId: string, interaction: VoiceInteraction): void {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.history.push(interaction);
    }
  }

  /**
   * 删除会话
   */
  deleteSession(sessionId: string): void {
    this.sessions.delete(sessionId);
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `voice_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 语音场景生成控制器
 */
export class VoiceSceneGenerationController extends EventEmitter {
  private speechRecognition: SpeechRecognitionService;
  private sceneGenerator: SceneGenerationAIManager;
  private voiceFeedback: SpeechSynthesisService;
  private conversationManager: ConversationManager;
  private activeSessions: Set<string> = new Set();
  private world: any;

  constructor(world: any) {
    super();
    this.world = world;
    
    this.speechRecognition = new SpeechRecognitionService({
      language: 'zh-CN',
      continuous: true,
      interimResults: true,
      maxAlternatives: 3,
      noiseReduction: true,
      echoCancellation: true,
      autoGainControl: true,
      sampleRate: 16000,
      provider: 'web-speech'
    });

    this.sceneGenerator = new SceneGenerationAIManager(this.world, {
      enableRealTimeGeneration: true,
      qualityLevel: 'balanced'
    });

    this.voiceFeedback = new SpeechSynthesisService({
      language: 'zh-CN',
      voice: 'female',
      rate: 1.0,
      pitch: 1.0,
      volume: 1.0,
      provider: 'web-speech',
      enableEmotionalSynthesis: true,
      enableLipSync: true,
      audioFormat: 'mp3',
      sampleRate: 22050
    });

    this.conversationManager = new ConversationManager();
    
    this.initializeEventHandlers();
  }

  /**
   * 初始化事件处理器
   */
  private initializeEventHandlers(): void {
    // 语音识别结果处理
    this.speechRecognition.onTranscript = (transcript: string, confidence: number) => {
      this.handleSpeechRecognitionResult({ transcript, confidence });
    };

    // 语音识别错误处理
    this.speechRecognition.onError = (error: string) => {
      this.handleSpeechRecognitionError(error);
    };

    // 语音合成完成处理 - 使用回调函数
    // this.voiceFeedback.onEnd = () => {
    //   this.emit('speechEnd');
    // };
  }

  /**
   * 开始语音场景生成会话
   */
  async startVoiceSession(): Promise<VoiceSessionResult> {
    try {
      const sessionId = this.conversationManager.createSession();
      this.activeSessions.add(sessionId);

      // 初始化语音识别
      await this.speechRecognition.start();

      // 更新会话状态
      this.conversationManager.updateSession(sessionId, {
        isListening: true
      });

      // 欢迎语音
      await this.speakWelcomeMessage();

      this.emit('sessionStarted', { sessionId });

      return { sessionId, status: 'active' };
    } catch (error) {
      console.error('启动语音会话失败:', error);
      return { 
        sessionId: '', 
        status: 'error', 
        error: error instanceof Error ? error.message : '未知错误' 
      };
    }
  }

  /**
   * 停止语音会话
   */
  async stopVoiceSession(sessionId: string): Promise<void> {
    try {
      // 停止语音识别
      await this.speechRecognition.stop();

      // 更新会话状态
      this.conversationManager.updateSession(sessionId, {
        isListening: false,
        isGenerating: false,
        isSpeaking: false
      });

      // 移除活跃会话
      this.activeSessions.delete(sessionId);

      // 告别语音
      await this.speakFarewellMessage();

      this.emit('sessionStopped', { sessionId });
    } catch (error) {
      console.error('停止语音会话失败:', error);
    }
  }

  /**
   * 处理语音识别结果
   */
  private async handleSpeechRecognitionResult(result: any): Promise<void> {
    const transcript = result.transcript;
    const isFinal = result.isFinal;
    const sessionId = this.getActiveSessionId();

    if (!sessionId) return;

    // 更新当前转录文本
    this.conversationManager.addUserMessage(sessionId, transcript);

    this.emit('transcriptUpdate', { sessionId, transcript, isFinal });

    // 如果是最终结果，处理语音输入
    if (isFinal) {
      await this.handleVoiceInput(sessionId, transcript);
    } else {
      // 实时预览（可选）
      await this.previewSceneFromPartialInput(sessionId, transcript);
    }
  }

  /**
   * 处理语音识别错误
   */
  private handleSpeechRecognitionError(error: any): void {
    console.error('语音识别错误:', error);
    this.emit('speechRecognitionError', error);
  }

  /**
   * 处理语音输入
   */
  private async handleVoiceInput(sessionId: string, transcript: string): Promise<void> {
    try {
      // 更新会话状态
      this.conversationManager.updateSession(sessionId, {
        isGenerating: true,
        generationProgress: 0
      });

      // 语音反馈：开始生成
      await this.voiceFeedback.speak("正在为您生成场景，请稍候...");

      // 生成场景
      const result = await this.generateSceneFromVoice(sessionId, transcript);

      // 应用到编辑器
      await this.applySceneToEditor(result.scene);

      // 语音反馈：生成完成
      const feedback = this.generateVoiceFeedback(result);
      await this.voiceFeedback.speak(feedback);

      // 记录交互
      const interaction: VoiceInteraction = {
        timestamp: Date.now(),
        userInput: transcript,
        systemResponse: feedback,
        generationResult: result,
        confidence: result.confidence
      };
      this.conversationManager.addInteraction(sessionId, interaction);

      // 更新会话状态
      this.conversationManager.updateSession(sessionId, {
        isGenerating: false,
        generationProgress: 100,
        currentScene: result.scene
      });

      // 询问是否需要调整
      await this.voiceFeedback.speak("场景已生成完成。您是否需要进行调整？");

      this.emit('sceneGenerated', { sessionId, result });
    } catch (error) {
      console.error('处理语音输入失败:', error);
      await this.voiceFeedback.speak("抱歉，场景生成遇到了问题。请重新描述您的需求。");
      
      // 更新会话状态
      this.conversationManager.updateSession(sessionId, {
        isGenerating: false,
        generationProgress: 0
      });

      this.emit('generationError', { sessionId, error });
    }
  }

  /**
   * 从语音生成场景
   */
  private async generateSceneFromVoice(
    sessionId: string,
    description: string
  ): Promise<SceneGenerationResult> {
    const options: SceneGenerationOptions = {
      realTimePreview: true,
      voiceGuidance: true
    };

    // 使用流式生成以提供实时反馈
    const generator = this.sceneGenerator.generateSceneStream(description, options);
    let progress = 0;

    for await (const partialResult of generator) {
      progress += 25;
      this.conversationManager.updateSession(sessionId, {
        generationProgress: Math.min(progress, 90)
      });

      this.emit('generationProgress', { sessionId, progress: Math.min(progress, 90) });
    }

    // 获取最终结果
    const finalResult = await generator.next();

    this.conversationManager.updateSession(sessionId, {
      generationProgress: 100
    });

    // 确保返回完整的 SceneGenerationResult
    const result = finalResult.value;
    if (result && 'scene' in result) {
      return result as any;
    }
    return {
      scene: null,
      metadata: {},
      performance: { renderTime: 0, memoryUsage: 0 },
      assets: []
    } as any;
  }

  /**
   * 部分输入预览
   */
  private async previewSceneFromPartialInput(sessionId: string, partialInput: string): Promise<void> {
    // 如果输入长度足够，可以进行预览
    if (partialInput.length > 10) {
      try {
        // 快速验证描述
        const validation = await this.sceneGenerator.validateDescription(partialInput);
        
        this.emit('inputValidation', { sessionId, validation });
        
        // 如果描述有效，可以提供建议
        if (validation.isValid) {
          const suggestions = await this.sceneGenerator.getGenerationSuggestions(partialInput);
          this.emit('suggestions', { sessionId, suggestions });
        }
      } catch (error) {
        // 预览失败不影响主流程
        console.warn('预览失败:', error);
      }
    }
  }

  /**
   * 应用场景到编辑器
   */
  private async applySceneToEditor(scene: any): Promise<void> {
    // 这里应该调用实际的场景编辑器API
    // 目前只是发出事件
    this.emit('applyScene', { scene });
  }

  /**
   * 生成语音反馈
   */
  private generateVoiceFeedback(result: SceneGenerationResult): string {
    const elementCount = result.understanding.elements.length;
    const sceneType = result.understanding.intent.sceneType;
    const confidence = Math.round(result.confidence * 100);

    let feedback = `场景生成完成！我为您创建了一个${this.translateSceneType(sceneType)}，`;
    feedback += `包含${elementCount}个主要元素。`;
    
    if (confidence > 80) {
      feedback += "生成质量很高，符合您的描述。";
    } else if (confidence > 60) {
      feedback += "生成质量良好，基本符合您的需求。";
    } else {
      feedback += "生成质量一般，您可能需要进一步调整。";
    }

    return feedback;
  }

  /**
   * 翻译场景类型
   */
  private translateSceneType(sceneType: string): string {
    const translations: Record<string, string> = {
      'office': '办公室',
      'living_room': '客厅',
      'bedroom': '卧室',
      'kitchen': '厨房',
      'classroom': '教室',
      'meeting_room': '会议室',
      'restaurant': '餐厅',
      'general': '通用场景'
    };

    return translations[sceneType] || '场景';
  }

  /**
   * 说欢迎消息
   */
  private async speakWelcomeMessage(): Promise<void> {
    const welcomeMessage = "您好！我是智能场景生成助手。请描述您想要创建的场景，我会为您实时生成3D场景。";
    await this.voiceFeedback.speak(welcomeMessage);
  }

  /**
   * 说告别消息
   */
  private async speakFarewellMessage(): Promise<void> {
    const farewellMessage = "感谢使用智能场景生成助手，再见！";
    await this.voiceFeedback.speak(farewellMessage);
  }

  /**
   * 获取活跃会话ID
   */
  private getActiveSessionId(): string | undefined {
    return Array.from(this.activeSessions)[0];
  }

  /**
   * 获取会话状态
   */
  getSessionState(sessionId: string): VoiceSessionState | undefined {
    return this.conversationManager.getSession(sessionId);
  }

  /**
   * 暂停语音识别
   */
  async pauseListening(sessionId: string): Promise<void> {
    this.speechRecognition.stop();
    this.conversationManager.updateSession(sessionId, {
      isListening: false
    });
    this.emit('listeningPaused', { sessionId });
  }

  /**
   * 恢复语音识别
   */
  async resumeListening(sessionId: string): Promise<void> {
    this.speechRecognition.start();
    this.conversationManager.updateSession(sessionId, {
      isListening: true
    });
    this.emit('listeningResumed', { sessionId });
  }

  /**
   * 销毁控制器
   */
  async destroy(): Promise<void> {
    // 停止所有活跃会话
    for (const sessionId of this.activeSessions) {
      await this.stopVoiceSession(sessionId);
    }

    // 销毁服务
    (this.speechRecognition as any).dispose();
    (this.voiceFeedback as any).dispose();
    // this.sceneGenerator.destroy(); // 如果有的话

    // 清理事件监听器
    this.removeAllListeners();
  }
}
