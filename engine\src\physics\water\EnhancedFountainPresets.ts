/**
 * 增强版喷泉预设
 * 提供各种类型的增强喷泉预设配置
 */
import * as THREE from 'three';
import { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { FountainType, FountainMode } from './FountainComponent';
import { EnhancedFountainComponent, EnhancedFountainConfig, EnhancedFountainType, FountainJetShape } from './EnhancedFountainComponent';
import { Debug } from '../../utils/Debug';

/**
 * 增强版喷泉预设配置
 */
export interface EnhancedFountainPresetConfig {
  /** 预设类型 */
  type: EnhancedFountainType;
  /** 位置 */
  position?: THREE.Vector3;
  /** 尺寸 */
  size?: {
    width: number;
    height: number;
    depth: number;
  };
  /** 颜色 */
  color?: THREE.Color;
  /** 不透明度 */
  opacity?: number;
  /** 喷泉喷射形状 */
  jetShape?: FountainJetShape;
  /** 是否启用彩色照明 */
  enableColoredLighting?: boolean;
  /** 彩色照明颜色 */
  coloredLightingColors?: THREE.Color[];
  /** 是否启用音乐同步 */
  enableMusicSync?: boolean;
  /** 是否启用交互式控制 */
  enableInteractiveControl?: boolean;
  /** 是否启用GPU加速 */
  enableGPUAcceleration?: boolean;
}

/**
 * 增强版喷泉预设
 */
export class EnhancedFountainPresets {
  /**
   * 创建喷泉预设
   * @param world 世界
   * @param config 配置
   * @returns 喷泉实体
   */
  public static createPreset(world: World, config: EnhancedFountainPresetConfig): Entity {
    // 创建实体
    const entity = new Entity();
    entity.name = `EnhancedFountain_${config.type}`;

    // 默认位置
    const position = config.position || new THREE.Vector3(0, 0, 0);

    // 创建基础配置
    const fountainConfig: EnhancedFountainConfig = {
      position,
      color: config.color || new THREE.Color(0x88ccff),
      opacity: config.opacity !== undefined ? config.opacity : 0.8,
      jetShape: config.jetShape || FountainJetShape.CYLINDER,
      enableColoredLighting: config.enableColoredLighting !== undefined ? config.enableColoredLighting : false,
      coloredLightingColors: config.coloredLightingColors || [
        new THREE.Color(0xff0000),
        new THREE.Color(0x00ff00),
        new THREE.Color(0x0000ff),
        new THREE.Color(0xffff00),
        new THREE.Color(0xff00ff),
        new THREE.Color(0x00ffff)
      ],
      enableMusicSync: config.enableMusicSync !== undefined ? config.enableMusicSync : false,
      enableInteractiveControl: config.enableInteractiveControl !== undefined ? config.enableInteractiveControl : false,
      enableGPUAcceleration: config.enableGPUAcceleration !== undefined ? config.enableGPUAcceleration : true
    };

    // 根据预设类型应用特定配置
    switch (config.type) {
      case EnhancedFountainType.STANDARD:
        this.applyStandardPreset(fountainConfig);
        break;
      case EnhancedFountainType.HIGH:
        this.applyHighPreset(fountainConfig);
        break;
      case EnhancedFountainType.WIDE:
        this.applyWidePreset(fountainConfig);
        break;
      case EnhancedFountainType.MULTI_JET:
        this.applyMultiJetPreset(fountainConfig);
        break;
      case EnhancedFountainType.DANCING:
        this.applyDancingPreset(fountainConfig);
        break;
      case EnhancedFountainType.MUSICAL:
        this.applyMusicalPreset(fountainConfig);
        break;
      case EnhancedFountainType.PULSE:
        this.applyPulsePreset(fountainConfig);
        break;
      case EnhancedFountainType.ALTERNATING:
        this.applyAlternatingPreset(fountainConfig);
        break;
      case EnhancedFountainType.SEQUENCE:
        this.applySequencePreset(fountainConfig);
        break;
      case EnhancedFountainType.RANDOM:
        this.applyRandomPreset(fountainConfig);
        break;
      case EnhancedFountainType.SPIRAL:
        this.applySpiralPreset(fountainConfig);
        break;
      case EnhancedFountainType.CONE:
        this.applyConePreset(fountainConfig);
        break;
      case EnhancedFountainType.FLOWER:
        this.applyFlowerPreset(fountainConfig);
        break;
      case EnhancedFountainType.RAINBOW:
        this.applyRainbowPreset(fountainConfig);
        break;
      case EnhancedFountainType.INTERACTIVE:
        this.applyInteractivePreset(fountainConfig);
        break;
      default:
        this.applyStandardPreset(fountainConfig);
        break;
    }

    // 创建喷泉组件
    const fountainComponent = new EnhancedFountainComponent(entity, fountainConfig);
    entity.addComponent(fountainComponent);

    // 添加到世界
    world.addEntity(entity);

    Debug.log('EnhancedFountainPresets', `创建增强版喷泉预设: ${config.type}`);

    return entity;
  }

  /**
   * 应用标准喷泉预设
   * @param config 配置
   */
  private static applyStandardPreset(config: EnhancedFountainConfig): void {
    config.fountainType = FountainType.STANDARD;
    config.fountainMode = FountainMode.CONTINUOUS;
    config.jetShape = FountainJetShape.CYLINDER;
    config.jetHeightChangeSpeed = 1.0;
    config.jetWidthChangeSpeed = 1.0;
    config.jetAngleChangeSpeed = 1.0;
    config.jetDensity = 1.0;
    config.jetParticleSize = 0.1;
    config.jetParticleSizeVariation = 0.05;
    config.jetParticleLifetime = 2.0;
    config.jetParticleLifetimeVariation = 0.5;
    config.jetParticleSpeed = 5.0;
    config.jetParticleSpeedVariation = 1.0;
    config.jetParticleGravityFactor = 1.0;
    config.jetParticleDragFactor = 0.1;
    config.jetParticleRotationSpeed = 1.0;
    config.jetParticleRotationSpeedVariation = 0.5;
    config.enableJetParticleTrails = true;
    config.jetParticleTrailLength = 1.0;
    config.enableJetParticleScattering = true;
    config.jetParticleScatteringStrength = 1.0;
    config.enableJetParticleReflections = true;
    config.jetParticleReflectionStrength = 1.0;
    config.enableJetParticleRefractions = true;
    config.jetParticleRefractionStrength = 1.0;
    config.enableJetParticleSounds = true;
    config.jetParticleSoundVolume = 1.0;
    config.enableJetParticleOptimization = true;
  }

  /**
   * 应用高喷泉预设
   * @param config 配置
   */
  private static applyHighPreset(config: EnhancedFountainConfig): void {
    config.fountainType = FountainType.DOME;
    config.fountainMode = FountainMode.CONTINUOUS;
    config.jetShape = FountainJetShape.CYLINDER;
    config.jetHeightChangeSpeed = 0.8;
    config.jetWidthChangeSpeed = 0.8;
    config.jetAngleChangeSpeed = 0.8;
    config.jetDensity = 1.2;
    config.jetParticleSize = 0.12;
    config.jetParticleSizeVariation = 0.06;
    config.jetParticleLifetime = 3.0;
    config.jetParticleLifetimeVariation = 0.7;
    config.jetParticleSpeed = 8.0;
    config.jetParticleSpeedVariation = 1.5;
    config.jetParticleGravityFactor = 1.0;
    config.jetParticleDragFactor = 0.08;
    config.jetParticleRotationSpeed = 1.2;
    config.jetParticleRotationSpeedVariation = 0.6;
    config.enableJetParticleTrails = true;
    config.jetParticleTrailLength = 1.5;
    config.enableJetParticleScattering = true;
    config.jetParticleScatteringStrength = 1.2;
    config.enableJetParticleReflections = true;
    config.jetParticleReflectionStrength = 1.2;
    config.enableJetParticleRefractions = true;
    config.jetParticleRefractionStrength = 1.2;
    config.enableJetParticleSounds = true;
    config.jetParticleSoundVolume = 1.2;
    config.enableJetParticleOptimization = true;
  }

  /**
   * 应用宽喷泉预设
   * @param config 配置
   */
  private static applyWidePreset(config: EnhancedFountainConfig): void {
    config.fountainType = FountainType.WATERFALL;
    config.fountainMode = FountainMode.CONTINUOUS;
    config.jetShape = FountainJetShape.FLAT;
    config.jetHeightChangeSpeed = 1.0;
    config.jetWidthChangeSpeed = 1.0;
    config.jetAngleChangeSpeed = 1.0;
    config.jetDensity = 1.5;
    config.jetParticleSize = 0.08;
    config.jetParticleSizeVariation = 0.04;
    config.jetParticleLifetime = 2.0;
    config.jetParticleLifetimeVariation = 0.5;
    config.jetParticleSpeed = 4.0;
    config.jetParticleSpeedVariation = 1.0;
    config.jetParticleGravityFactor = 1.0;
    config.jetParticleDragFactor = 0.12;
    config.jetParticleRotationSpeed = 0.8;
    config.jetParticleRotationSpeedVariation = 0.4;
    config.enableJetParticleTrails = true;
    config.jetParticleTrailLength = 0.8;
    config.enableJetParticleScattering = true;
    config.jetParticleScatteringStrength = 0.8;
    config.enableJetParticleReflections = true;
    config.jetParticleReflectionStrength = 0.8;
    config.enableJetParticleRefractions = true;
    config.jetParticleRefractionStrength = 0.8;
    config.enableJetParticleSounds = true;
    config.jetParticleSoundVolume = 0.8;
    config.enableJetParticleOptimization = true;
  }

  /**
   * 应用多喷头喷泉预设
   * @param config 配置
   */
  private static applyMultiJetPreset(config: EnhancedFountainConfig): void {
    config.fountainType = FountainType.MULTI_JET;
    config.fountainMode = FountainMode.CONTINUOUS;
    config.jetShape = FountainJetShape.CYLINDER;
    config.jetHeightChangeSpeed = 1.2;
    config.jetWidthChangeSpeed = 1.2;
    config.jetAngleChangeSpeed = 1.2;
    config.jetDensity = 1.0;
    config.jetParticleSize = 0.1;
    config.jetParticleSizeVariation = 0.05;
    config.jetParticleLifetime = 2.0;
    config.jetParticleLifetimeVariation = 0.5;
    config.jetParticleSpeed = 5.0;
    config.jetParticleSpeedVariation = 1.0;
    config.jetParticleGravityFactor = 1.0;
    config.jetParticleDragFactor = 0.1;
    config.jetParticleRotationSpeed = 1.0;
    config.jetParticleRotationSpeedVariation = 0.5;
    config.enableJetParticleTrails = true;
    config.jetParticleTrailLength = 1.0;
    config.enableJetParticleScattering = true;
    config.jetParticleScatteringStrength = 1.0;
    config.enableJetParticleReflections = true;
    config.jetParticleReflectionStrength = 1.0;
    config.enableJetParticleRefractions = true;
    config.jetParticleRefractionStrength = 1.0;
    config.enableJetParticleSounds = true;
    config.jetParticleSoundVolume = 1.0;
    config.enableJetParticleOptimization = true;
  }

  /**
   * 应用舞蹈喷泉预设
   * @param config 配置
   */
  private static applyDancingPreset(config: EnhancedFountainConfig): void {
    config.fountainType = FountainType.DANCING;
    config.fountainMode = FountainMode.CONTINUOUS;
    config.jetShape = FountainJetShape.CYLINDER;
    config.jetHeightChangeSpeed = 2.0;
    config.jetWidthChangeSpeed = 2.0;
    config.jetAngleChangeSpeed = 2.0;
    config.jetDensity = 1.2;
    config.jetParticleSize = 0.1;
    config.jetParticleSizeVariation = 0.05;
    config.jetParticleLifetime = 2.0;
    config.jetParticleLifetimeVariation = 0.5;
    config.jetParticleSpeed = 5.0;
    config.jetParticleSpeedVariation = 1.0;
    config.jetParticleGravityFactor = 1.0;
    config.jetParticleDragFactor = 0.1;
    config.jetParticleRotationSpeed = 1.5;
    config.jetParticleRotationSpeedVariation = 0.7;
    config.enableJetParticleTrails = true;
    config.jetParticleTrailLength = 1.2;
    config.enableJetParticleScattering = true;
    config.jetParticleScatteringStrength = 1.2;
    config.enableJetParticleReflections = true;
    config.jetParticleReflectionStrength = 1.2;
    config.enableJetParticleRefractions = true;
    config.jetParticleRefractionStrength = 1.2;
    config.enableJetParticleSounds = true;
    config.jetParticleSoundVolume = 1.0;
    config.enableJetParticleOptimization = true;
    config.enableColoredLighting = true;
    config.coloredLightingChangeSpeed = 1.5;
  }

  /**
   * 应用音乐喷泉预设
   * @param config 配置
   */
  private static applyMusicalPreset(config: EnhancedFountainConfig): void {
    config.fountainType = FountainType.MUSICAL;
    config.fountainMode = FountainMode.CONTINUOUS;
    config.jetShape = FountainJetShape.CYLINDER;
    config.jetHeightChangeSpeed = 3.0;
    config.jetWidthChangeSpeed = 3.0;
    config.jetAngleChangeSpeed = 3.0;
    config.jetDensity = 1.0;
    config.jetParticleSize = 0.1;
    config.jetParticleSizeVariation = 0.05;
    config.jetParticleLifetime = 2.0;
    config.jetParticleLifetimeVariation = 0.5;
    config.jetParticleSpeed = 5.0;
    config.jetParticleSpeedVariation = 1.0;
    config.jetParticleGravityFactor = 1.0;
    config.jetParticleDragFactor = 0.1;
    config.jetParticleRotationSpeed = 1.0;
    config.jetParticleRotationSpeedVariation = 0.5;
    config.enableJetParticleTrails = true;
    config.jetParticleTrailLength = 1.0;
    config.enableJetParticleScattering = true;
    config.jetParticleScatteringStrength = 1.0;
    config.enableJetParticleReflections = true;
    config.jetParticleReflectionStrength = 1.0;
    config.enableJetParticleRefractions = true;
    config.jetParticleRefractionStrength = 1.0;
    config.enableJetParticleSounds = true;
    config.jetParticleSoundVolume = 1.0;
    config.enableJetParticleOptimization = true;
    config.enableColoredLighting = true;
    config.coloredLightingChangeSpeed = 2.0;
    config.enableMusicSync = true;
    config.musicSyncSensitivity = 1.5;
    config.musicSyncFrequencyRange = [20, 200];
  }

  /**
   * 应用脉冲喷泉预设
   * @param config 配置
   */
  private static applyPulsePreset(config: EnhancedFountainConfig): void {
    config.fountainType = FountainType.SPIRAL;
    config.fountainMode = FountainMode.CONTINUOUS;
    config.jetShape = FountainJetShape.CYLINDER;
    config.jetHeightChangeSpeed = 2.5;
    config.jetWidthChangeSpeed = 2.5;
    config.jetAngleChangeSpeed = 2.5;
    config.jetDensity = 1.0;
    config.jetParticleSize = 0.1;
    config.jetParticleSizeVariation = 0.05;
    config.jetParticleLifetime = 2.0;
    config.jetParticleLifetimeVariation = 0.5;
    config.jetParticleSpeed = 5.0;
    config.jetParticleSpeedVariation = 1.0;
    config.jetParticleGravityFactor = 1.0;
    config.jetParticleDragFactor = 0.1;
    config.jetParticleRotationSpeed = 1.0;
    config.jetParticleRotationSpeedVariation = 0.5;
    config.enableJetParticleTrails = true;
    config.jetParticleTrailLength = 1.0;
    config.enableJetParticleScattering = true;
    config.jetParticleScatteringStrength = 1.0;
    config.enableJetParticleReflections = true;
    config.jetParticleReflectionStrength = 1.0;
    config.enableJetParticleRefractions = true;
    config.jetParticleRefractionStrength = 1.0;
    config.enableJetParticleSounds = true;
    config.jetParticleSoundVolume = 1.0;
    config.enableJetParticleOptimization = true;
  }

  /**
   * 应用交替喷泉预设
   * @param config 配置
   */
  private static applyAlternatingPreset(config: EnhancedFountainConfig): void {
    config.fountainType = FountainType.FLOWER;
    config.fountainMode = FountainMode.CONTINUOUS;
    config.jetShape = FountainJetShape.CYLINDER;
    config.jetHeightChangeSpeed = 1.5;
    config.jetWidthChangeSpeed = 1.5;
    config.jetAngleChangeSpeed = 1.5;
    config.jetDensity = 1.0;
    config.jetParticleSize = 0.1;
    config.jetParticleSizeVariation = 0.05;
    config.jetParticleLifetime = 2.0;
    config.jetParticleLifetimeVariation = 0.5;
    config.jetParticleSpeed = 5.0;
    config.jetParticleSpeedVariation = 1.0;
    config.jetParticleGravityFactor = 1.0;
    config.jetParticleDragFactor = 0.1;
    config.jetParticleRotationSpeed = 1.0;
    config.jetParticleRotationSpeedVariation = 0.5;
    config.enableJetParticleTrails = true;
    config.jetParticleTrailLength = 1.0;
    config.enableJetParticleScattering = true;
    config.jetParticleScatteringStrength = 1.0;
    config.enableJetParticleReflections = true;
    config.jetParticleReflectionStrength = 1.0;
    config.enableJetParticleRefractions = true;
    config.jetParticleRefractionStrength = 1.0;
    config.enableJetParticleSounds = true;
    config.jetParticleSoundVolume = 1.0;
    config.enableJetParticleOptimization = true;
  }

  /**
   * 应用序列喷泉预设
   * @param config 配置
   */
  private static applySequencePreset(config: EnhancedFountainConfig): void {
    config.fountainType = FountainType.CROSSING;
    config.fountainMode = FountainMode.CONTINUOUS;
    config.jetShape = FountainJetShape.CYLINDER;
    config.jetHeightChangeSpeed = 2.0;
    config.jetWidthChangeSpeed = 2.0;
    config.jetAngleChangeSpeed = 2.0;
    config.jetDensity = 1.0;
    config.jetParticleSize = 0.1;
    config.jetParticleSizeVariation = 0.05;
    config.jetParticleLifetime = 2.0;
    config.jetParticleLifetimeVariation = 0.5;
    config.jetParticleSpeed = 5.0;
    config.jetParticleSpeedVariation = 1.0;
    config.jetParticleGravityFactor = 1.0;
    config.jetParticleDragFactor = 0.1;
    config.jetParticleRotationSpeed = 1.0;
    config.jetParticleRotationSpeedVariation = 0.5;
    config.enableJetParticleTrails = true;
    config.jetParticleTrailLength = 1.0;
    config.enableJetParticleScattering = true;
    config.jetParticleScatteringStrength = 1.0;
    config.enableJetParticleReflections = true;
    config.jetParticleReflectionStrength = 1.0;
    config.enableJetParticleRefractions = true;
    config.jetParticleRefractionStrength = 1.0;
    config.enableJetParticleSounds = true;
    config.jetParticleSoundVolume = 1.0;
    config.enableJetParticleOptimization = true;
    config.enableColoredLighting = true;
    config.coloredLightingChangeSpeed = 1.0;
  }

  /**
   * 应用随机喷泉预设
   * @param config 配置
   */
  private static applyRandomPreset(config: EnhancedFountainConfig): void {
    config.fountainType = FountainType.STANDARD;
    config.fountainMode = FountainMode.CONTINUOUS;
    config.jetShape = FountainJetShape.CYLINDER;
    config.jetHeightChangeSpeed = 2.0;
    config.jetWidthChangeSpeed = 2.0;
    config.jetAngleChangeSpeed = 2.0;
    config.jetDensity = 1.0;
    config.jetParticleSize = 0.1;
    config.jetParticleSizeVariation = 0.05;
    config.jetParticleLifetime = 2.0;
    config.jetParticleLifetimeVariation = 0.5;
    config.jetParticleSpeed = 5.0;
    config.jetParticleSpeedVariation = 1.0;
    config.jetParticleGravityFactor = 1.0;
    config.jetParticleDragFactor = 0.1;
    config.jetParticleRotationSpeed = 1.0;
    config.jetParticleRotationSpeedVariation = 0.5;
    config.enableJetParticleTrails = true;
    config.jetParticleTrailLength = 1.0;
    config.enableJetParticleScattering = true;
    config.jetParticleScatteringStrength = 1.0;
    config.enableJetParticleReflections = true;
    config.jetParticleReflectionStrength = 1.0;
    config.enableJetParticleRefractions = true;
    config.jetParticleRefractionStrength = 1.0;
    config.enableJetParticleSounds = true;
    config.jetParticleSoundVolume = 1.0;
    config.enableJetParticleOptimization = true;
    config.enableColoredLighting = true;
    config.coloredLightingChangeSpeed = 1.5;
  }

  /**
   * 应用螺旋喷泉预设
   * @param config 配置
   */
  private static applySpiralPreset(config: EnhancedFountainConfig): void {
    config.fountainType = FountainType.STANDARD;
    config.fountainMode = FountainMode.CONTINUOUS;
    config.jetShape = FountainJetShape.SPIRAL;
    config.jetHeightChangeSpeed = 1.0;
    config.jetWidthChangeSpeed = 1.0;
    config.jetAngleChangeSpeed = 1.0;
    config.jetDensity = 1.2;
    config.jetParticleSize = 0.08;
    config.jetParticleSizeVariation = 0.04;
    config.jetParticleLifetime = 2.5;
    config.jetParticleLifetimeVariation = 0.5;
    config.jetParticleSpeed = 4.0;
    config.jetParticleSpeedVariation = 1.0;
    config.jetParticleGravityFactor = 0.8;
    config.jetParticleDragFactor = 0.1;
    config.jetParticleRotationSpeed = 2.0;
    config.jetParticleRotationSpeedVariation = 0.5;
    config.enableJetParticleTrails = true;
    config.jetParticleTrailLength = 1.5;
    config.enableJetParticleScattering = true;
    config.jetParticleScatteringStrength = 1.2;
    config.enableJetParticleReflections = true;
    config.jetParticleReflectionStrength = 1.2;
    config.enableJetParticleRefractions = true;
    config.jetParticleRefractionStrength = 1.2;
    config.enableJetParticleSounds = true;
    config.jetParticleSoundVolume = 1.0;
    config.enableJetParticleOptimization = true;
    config.enableColoredLighting = true;
    config.coloredLightingChangeSpeed = 1.0;
  }

  /**
   * 应用圆锥喷泉预设
   * @param config 配置
   */
  private static applyConePreset(config: EnhancedFountainConfig): void {
    config.fountainType = FountainType.STANDARD;
    config.fountainMode = FountainMode.CONTINUOUS;
    config.jetShape = FountainJetShape.CONE;
    config.jetHeightChangeSpeed = 1.0;
    config.jetWidthChangeSpeed = 1.0;
    config.jetAngleChangeSpeed = 1.0;
    config.jetDensity = 1.5;
    config.jetParticleSize = 0.08;
    config.jetParticleSizeVariation = 0.04;
    config.jetParticleLifetime = 2.0;
    config.jetParticleLifetimeVariation = 0.5;
    config.jetParticleSpeed = 6.0;
    config.jetParticleSpeedVariation = 1.5;
    config.jetParticleGravityFactor = 1.0;
    config.jetParticleDragFactor = 0.1;
    config.jetParticleRotationSpeed = 1.0;
    config.jetParticleRotationSpeedVariation = 0.5;
    config.enableJetParticleTrails = true;
    config.jetParticleTrailLength = 1.0;
    config.enableJetParticleScattering = true;
    config.jetParticleScatteringStrength = 1.0;
    config.enableJetParticleReflections = true;
    config.jetParticleReflectionStrength = 1.0;
    config.enableJetParticleRefractions = true;
    config.jetParticleRefractionStrength = 1.0;
    config.enableJetParticleSounds = true;
    config.jetParticleSoundVolume = 1.0;
    config.enableJetParticleOptimization = true;
  }

  /**
   * 应用花朵喷泉预设
   * @param config 配置
   */
  private static applyFlowerPreset(config: EnhancedFountainConfig): void {
    config.fountainType = FountainType.STANDARD;
    config.fountainMode = FountainMode.CONTINUOUS;
    config.jetShape = FountainJetShape.FLOWER;
    config.jetHeightChangeSpeed = 1.0;
    config.jetWidthChangeSpeed = 1.0;
    config.jetAngleChangeSpeed = 1.0;
    config.jetDensity = 1.2;
    config.jetParticleSize = 0.08;
    config.jetParticleSizeVariation = 0.04;
    config.jetParticleLifetime = 2.5;
    config.jetParticleLifetimeVariation = 0.5;
    config.jetParticleSpeed = 5.0;
    config.jetParticleSpeedVariation = 1.0;
    config.jetParticleGravityFactor = 0.9;
    config.jetParticleDragFactor = 0.1;
    config.jetParticleRotationSpeed = 1.5;
    config.jetParticleRotationSpeedVariation = 0.5;
    config.enableJetParticleTrails = true;
    config.jetParticleTrailLength = 1.2;
    config.enableJetParticleScattering = true;
    config.jetParticleScatteringStrength = 1.2;
    config.enableJetParticleReflections = true;
    config.jetParticleReflectionStrength = 1.2;
    config.enableJetParticleRefractions = true;
    config.jetParticleRefractionStrength = 1.2;
    config.enableJetParticleSounds = true;
    config.jetParticleSoundVolume = 1.0;
    config.enableJetParticleOptimization = true;
    config.enableColoredLighting = true;
    config.coloredLightingChangeSpeed = 1.0;
  }

  /**
   * 应用彩虹喷泉预设
   * @param config 配置
   */
  private static applyRainbowPreset(config: EnhancedFountainConfig): void {
    config.fountainType = FountainType.STANDARD;
    config.fountainMode = FountainMode.CONTINUOUS;
    config.jetShape = FountainJetShape.CYLINDER;
    config.jetHeightChangeSpeed = 1.0;
    config.jetWidthChangeSpeed = 1.0;
    config.jetAngleChangeSpeed = 1.0;
    config.jetDensity = 1.2;
    config.jetParticleSize = 0.1;
    config.jetParticleSizeVariation = 0.05;
    config.jetParticleLifetime = 2.0;
    config.jetParticleLifetimeVariation = 0.5;
    config.jetParticleSpeed = 5.0;
    config.jetParticleSpeedVariation = 1.0;
    config.jetParticleGravityFactor = 1.0;
    config.jetParticleDragFactor = 0.1;
    config.jetParticleRotationSpeed = 1.0;
    config.jetParticleRotationSpeedVariation = 0.5;
    config.enableJetParticleTrails = true;
    config.jetParticleTrailLength = 1.0;
    config.enableJetParticleScattering = true;
    config.jetParticleScatteringStrength = 1.0;
    config.enableJetParticleReflections = true;
    config.jetParticleReflectionStrength = 1.0;
    config.enableJetParticleRefractions = true;
    config.jetParticleRefractionStrength = 1.0;
    config.enableJetParticleSounds = true;
    config.jetParticleSoundVolume = 1.0;
    config.enableJetParticleOptimization = true;
    config.enableColoredLighting = true;
    config.coloredLightingChangeSpeed = 2.0;
    config.coloredLightingColors = [
      new THREE.Color(0xff0000), // 红
      new THREE.Color(0xff7f00), // 橙
      new THREE.Color(0xffff00), // 黄
      new THREE.Color(0x00ff00), // 绿
      new THREE.Color(0x0000ff), // 蓝
      new THREE.Color(0x4b0082), // 靛
      new THREE.Color(0x9400d3)  // 紫
    ];
  }

  /**
   * 应用交互式喷泉预设
   * @param config 配置
   */
  private static applyInteractivePreset(config: EnhancedFountainConfig): void {
    config.fountainType = FountainType.INTERACTIVE;
    config.fountainMode = FountainMode.CONTINUOUS;
    config.jetShape = FountainJetShape.CYLINDER;
    config.jetHeightChangeSpeed = 3.0;
    config.jetWidthChangeSpeed = 3.0;
    config.jetAngleChangeSpeed = 3.0;
    config.jetDensity = 1.0;
    config.jetParticleSize = 0.1;
    config.jetParticleSizeVariation = 0.05;
    config.jetParticleLifetime = 2.0;
    config.jetParticleLifetimeVariation = 0.5;
    config.jetParticleSpeed = 5.0;
    config.jetParticleSpeedVariation = 1.0;
    config.jetParticleGravityFactor = 1.0;
    config.jetParticleDragFactor = 0.1;
    config.jetParticleRotationSpeed = 1.0;
    config.jetParticleRotationSpeedVariation = 0.5;
    config.enableJetParticleTrails = true;
    config.jetParticleTrailLength = 1.0;
    config.enableJetParticleScattering = true;
    config.jetParticleScatteringStrength = 1.0;
    config.enableJetParticleReflections = true;
    config.jetParticleReflectionStrength = 1.0;
    config.enableJetParticleRefractions = true;
    config.jetParticleRefractionStrength = 1.0;
    config.enableJetParticleSounds = true;
    config.jetParticleSoundVolume = 1.0;
    config.enableJetParticleOptimization = true;
    config.enableInteractiveControl = true;
    config.interactiveControlSensitivity = 2.0;
  }
}
