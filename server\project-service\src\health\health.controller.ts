/**
 * 健康检查控制器
 */
import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { HealthCheck, HealthCheckService, TypeOrmHealthIndicator } from '@nestjs/terminus';

@ApiTags('健康检查')
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private db: TypeOrmHealthIndicator,
  ) {}

  @Get()
  @HealthCheck()
  @ApiOperation({ summary: '健康检查' })
  async check() {
    try {
      return await this.health.check([() => this.db.pingCheck('database', { timeout: 3000 })]);
    } catch (error) {
      // 如果数据库检查失败，返回降级状态而不是完全失败
      return {
        status: 'error',
        info: {},
        error: {
          database: {
            status: 'down',
            message: error.message || '数据库连接失败',
          },
        },
        details: {
          database: {
            status: 'down',
            message: error.message || '数据库连接失败',
          },
        },
      };
    }
  }
}
