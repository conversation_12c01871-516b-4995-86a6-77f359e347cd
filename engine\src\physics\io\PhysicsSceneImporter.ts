/**
 * 物理场景导入器
 * 用于导入物理场景数据
 */
import * as THREE from 'three';
import { Scene } from '../../scene/Scene';
import type { Entity } from '../../core/Entity';
import { BodyType  } from '../components/PhysicsBodyComponent';
import { ColliderType } from '../components/PhysicsColliderComponent';
import { PhysicsBodyComponent    } from '../components/PhysicsBodyComponent';
import { PhysicsColliderComponent } from '../components/PhysicsColliderComponent';
import { PhysicsConstraintComponent } from '../components/PhysicsConstraintComponent';
import { PhysicsWorldComponent    } from '../components/PhysicsWorldComponent';
import { PhysicsMaterialFactory } from '../PhysicsMaterialFactory';
import { Debug } from '../../utils/Debug';
import { PhysicsSceneExportData } from './PhysicsSceneExporter';

/**
 * 物理场景导入选项
 */
export interface PhysicsSceneImportOptions {
  /** 是否导入物理体 */
  importBodies?: boolean;
  /** 是否导入碰撞器 */
  importColliders?: boolean;
  /** 是否导入约束 */
  importConstraints?: boolean;
  /** 是否导入物理世界 */
  importWorld?: boolean;
  /** 是否导入材质 */
  importMaterials?: boolean;
  /** 是否清除现有物理组件 */
  clearExisting?: boolean;
  /** 实体ID映射 */
  entityIdMap?: Map<string, string>;
}

/**
 * 物理场景导入器
 */
export class PhysicsSceneImporter {
  /**
   * 创建物理场景导入器
   */
  constructor() {
    // 导入器不需要特定的物理系统实例
  }

  /**
   * 导入场景
   * @param scene 场景
   * @param data 导入数据
   * @param options 导入选项
   * @returns 是否导入成功
   */
  public import(scene: Scene, data: PhysicsSceneExportData, options: PhysicsSceneImportOptions = {}): boolean {
    // 默认选项
    const defaultOptions: PhysicsSceneImportOptions = {
      importBodies: true,
      importColliders: true,
      importConstraints: true,
      importWorld: true,
      importMaterials: true,
      clearExisting: false,
      entityIdMap: new Map()
    };

    // 合并选项
    const importOptions = { ...defaultOptions, ...options };

    try {
      // 验证数据版本
      if (!data.version) {
        Debug.error('无效的物理场景数据：缺少版本信息');
        return false;
      }

      // 清除现有物理组件
      if (importOptions.clearExisting) {
        this.clearPhysicsComponents(scene);
      }

      // 导入材质
      if (importOptions.importMaterials && data.materials) {
        this.importMaterials(data.materials);
      }

      // 导入物理世界
      if (importOptions.importWorld && data.world) {
        this.importWorld(scene, data.world, importOptions.entityIdMap);
      }

      // 导入物理体
      if (importOptions.importBodies && data.bodies) {
        this.importBodies(scene, data.bodies, importOptions.entityIdMap);
      }

      // 导入碰撞器
      if (importOptions.importColliders && data.colliders) {
        this.importColliders(scene, data.colliders, importOptions.entityIdMap);
      }

      // 导入约束
      if (importOptions.importConstraints && data.constraints) {
        this.importConstraints(scene, data.constraints, importOptions.entityIdMap);
      }

      return true;
    } catch (error) {
      Debug.error('导入物理场景失败:', error);
      return false;
    }
  }

  /**
   * 从JSON导入
   * @param scene 场景
   * @param json JSON字符串
   * @param options 导入选项
   * @returns 是否导入成功
   */
  public importFromJSON(scene: Scene, json: string, options: PhysicsSceneImportOptions = {}): boolean {
    try {
      const data = JSON.parse(json, (_key, value) => {
        // 处理THREE.Vector3和THREE.Quaternion
        if (value && value.__type === 'Vector3') {
          return new THREE.Vector3(value.x, value.y, value.z);
        } else if (value && value.__type === 'Quaternion') {
          return new THREE.Quaternion(value.x, value.y, value.z, value.w);
        }
        return value;
      });

      return this.import(scene, data, options);
    } catch (error) {
      Debug.error('解析JSON失败:', error);
      return false;
    }
  }

  /**
   * 清除物理组件
   * @param scene 场景
   */
  private clearPhysicsComponents(scene: Scene): void {
    for (const entity of scene.getEntities()) {
      // 移除物理体组件
      if (entity.hasComponent(PhysicsBodyComponent.type)) {
        entity.removeComponent(PhysicsBodyComponent.type);
      }

      // 移除碰撞器组件
      if (entity.hasComponent(PhysicsColliderComponent.type)) {
        entity.removeComponent(PhysicsColliderComponent.type);
      }

      // 移除约束组件
      if (entity.hasComponent(PhysicsConstraintComponent.type)) {
        entity.removeComponent(PhysicsConstraintComponent.type);
      }

      // 移除物理世界组件
      if (entity.hasComponent(PhysicsWorldComponent.type)) {
        entity.removeComponent(PhysicsWorldComponent.type);
      }
    }
  }

  /**
   * 导入材质
   * @param materials 材质数据
   */
  private importMaterials(materials: any[]): void {
    // 初始化材质工厂
    PhysicsMaterialFactory.initialize();

    // 导入材质
    for (const materialData of materials) {
      // 创建材质
      PhysicsMaterialFactory.createMaterial(materialData.name);

      // 导入接触材质
      if (materialData.contactMaterials) {
        for (const contactMaterialData of materialData.contactMaterials) {
          // 获取材质
          const materialA = PhysicsMaterialFactory.getMaterial(contactMaterialData.materialA);
          const materialB = PhysicsMaterialFactory.getMaterial(contactMaterialData.materialB);

          // 创建接触材质
          PhysicsMaterialFactory.createContactMaterial(
            materialA,
            materialB,
            contactMaterialData.friction,
            contactMaterialData.restitution,
            {
              contactEquationStiffness: contactMaterialData.contactEquationStiffness,
              contactEquationRelaxation: contactMaterialData.contactEquationRelaxation,
              frictionEquationStiffness: contactMaterialData.frictionEquationStiffness,
              frictionEquationRelaxation: contactMaterialData.frictionEquationRelaxation
            }
          );
        }
      }
    }
  }

  /**
   * 导入物理世界
   * @param scene 场景
   * @param worldData 物理世界数据
   * @param entityIdMap 实体ID映射
   */
  private importWorld(scene: Scene, worldData: any, entityIdMap: Map<string, string>): void {
    // 查找实体
    const entityId = entityIdMap.get(worldData.entityId) || worldData.entityId;
    let entity = scene.findEntityById(entityId);

    // 如果实体不存在，使用场景根实体
    if (!entity) {
      entity = scene.getRootEntity();
    }

    // 创建物理世界组件
    const worldComponent = new PhysicsWorldComponent({
      gravity: worldData.gravity,
      allowSleep: worldData.allowSleep,
      iterations: worldData.iterations,
      broadphase: worldData.broadphase,
      gridBroadphaseSize: worldData.gridBroadphaseSize,
      defaultFriction: worldData.defaultFriction,
      defaultRestitution: worldData.defaultRestitution
    });

    // 添加到实体
    entity.addComponent(worldComponent);
  }

  /**
   * 导入物理体
   * @param scene 场景
   * @param bodiesData 物理体数据
   * @param entityIdMap 实体ID映射
   */
  private importBodies(scene: Scene, bodiesData: any[], entityIdMap: Map<string, string>): void {
    for (const bodyData of bodiesData) {
      // 查找实体
      const entityId = entityIdMap.get(bodyData.entityId) || bodyData.entityId;
      const entity = scene.findEntityById(entityId);

      if (!entity) {
        Debug.warn(`找不到实体 ${entityId}，跳过导入物理体`);
        continue;
      }

      // 获取材质
      const material = PhysicsMaterialFactory.getMaterial(bodyData.material);

      // 创建物理体组件
      const bodyComponent = new PhysicsBodyComponent({
        type: bodyData.type as BodyType,
        mass: bodyData.mass,
        position: bodyData.position,
        quaternion: bodyData.quaternion,
        linearDamping: bodyData.linearDamping,
        angularDamping: bodyData.angularDamping,
        allowSleep: bodyData.allowSleep,
        sleepSpeedLimit: bodyData.sleepSpeedLimit,
        sleepTimeLimit: bodyData.sleepTimeLimit,
        fixedRotation: bodyData.fixedRotation,
        collisionFilterGroup: bodyData.collisionFilterGroup,
        collisionFilterMask: bodyData.collisionFilterMask,
        material,
        autoUpdateTransform: bodyData.autoUpdateTransform
      });

      // 添加到实体
      entity.addComponent(bodyComponent);
    }
  }

  /**
   * 导入碰撞器
   * @param scene 场景
   * @param collidersData 碰撞器数据
   * @param entityIdMap 实体ID映射
   */
  private importColliders(scene: Scene, collidersData: any[], entityIdMap: Map<string, string>): void {
    for (const colliderData of collidersData) {
      // 查找实体
      const entityId = entityIdMap.get(colliderData.entityId) || colliderData.entityId;
      const entity = scene.findEntityById(entityId);

      if (!entity) {
        Debug.warn(`找不到实体 ${entityId}，跳过导入碰撞器`);
        continue;
      }

      // 创建碰撞器组件
      const colliderComponent = new PhysicsColliderComponent({
        type: colliderData.type as ColliderType,
        params: colliderData.params,
        offset: colliderData.offset,
        orientation: colliderData.orientation,
        isTrigger: colliderData.isTrigger
      });

      // 添加到实体
      entity.addComponent(colliderComponent);
    }
  }

  /**
   * 导入约束
   * @param scene 场景
   * @param constraintsData 约束数据
   * @param entityIdMap 实体ID映射
   */
  private importConstraints(scene: Scene, constraintsData: any[], entityIdMap: Map<string, string>): void {
    for (const constraintData of constraintsData) {
      // 查找实体
      const entityId = entityIdMap.get(constraintData.entityId) || constraintData.entityId;
      const entity = scene.findEntityById(entityId);

      if (!entity) {
        Debug.warn(`找不到实体 ${entityId}，跳过导入约束`);
        continue;
      }

      // 查找目标实体
      let targetEntity: Entity | null = null;
      if (constraintData.targetEntity) {
        const targetEntityId = entityIdMap.get(constraintData.targetEntity) || constraintData.targetEntity;
        targetEntity = scene.findEntityById(targetEntityId);

        if (!targetEntity) {
          Debug.warn(`找不到目标实体 ${targetEntityId}，跳过导入约束`);
          continue;
        }
      }

      // 创建约束组件
      const constraintComponent = new PhysicsConstraintComponent({
        type: constraintData.type,
        entityA: entity,
        entityB: targetEntity || undefined,
        pivotA: constraintData.pivotA,
        pivotB: constraintData.pivotB,
        axisA: constraintData.axisA,
        axisB: constraintData.axisB,
        collideConnected: constraintData.collideConnected,
        motorMaxForce: constraintData.maxForce
      });

      // 添加到实体
      entity.addComponent(constraintComponent);
    }
  }
}
