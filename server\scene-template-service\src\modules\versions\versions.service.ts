import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TemplateVersion, VersionStatus } from './entities/template-version.entity';
import { SceneTemplate } from '../templates/entities/scene-template.entity';
import { LoggerService } from '../../common/services/logger.service';

@Injectable()
export class VersionsService {
  constructor(
    @InjectRepository(TemplateVersion)
    private readonly versionRepository: Repository<TemplateVersion>,
    @InjectRepository(SceneTemplate)
    private readonly templateRepository: Repository<SceneTemplate>,
    private readonly logger: LoggerService,
  ) {}

  /**
   * 创建新版本
   */
  async createVersion(
    templateId: string,
    version: string,
    changelog: string,
    creatorId: string,
  ): Promise<TemplateVersion> {
    // 获取模板当前数据
    const template = await this.templateRepository.findOne({
      where: { id: templateId },
      relations: ['parameters'],
    });

    if (!template) {
      throw new NotFoundException('模板不存在');
    }

    // 创建版本快照
    const templateVersion = this.versionRepository.create({
      version,
      changelog,
      templateId,
      creatorId,
      sceneDataSnapshot: template.sceneData,
      parametersSnapshot: template.defaultParameters,
      metadataSnapshot: template.metadata,
      status: VersionStatus.DRAFT,
    });

    const savedVersion = await this.versionRepository.save(templateVersion);
    this.logger.log(`模板版本创建成功: ${templateId} v${version}`, 'VersionsService');
    
    return savedVersion;
  }

  /**
   * 获取模板的所有版本
   */
  async findByTemplate(templateId: string): Promise<TemplateVersion[]> {
    return await this.versionRepository.find({
      where: { templateId },
      order: { createdAt: 'DESC' },
      relations: ['creator'],
    });
  }

  /**
   * 获取模板的当前版本
   */
  async getCurrentVersion(templateId: string): Promise<TemplateVersion | null> {
    return await this.versionRepository.findOne({
      where: { templateId, isCurrent: true },
      relations: ['creator'],
    });
  }

  /**
   * 根据ID获取版本
   */
  async findOne(id: string): Promise<TemplateVersion> {
    const version = await this.versionRepository.findOne({
      where: { id },
      relations: ['template', 'creator'],
    });

    if (!version) {
      throw new NotFoundException('版本不存在');
    }

    return version;
  }

  /**
   * 发布版本
   */
  async publishVersion(id: string): Promise<TemplateVersion> {
    const version = await this.findOne(id);

    // 将其他版本设为非当前版本
    await this.versionRepository.update(
      { templateId: version.templateId, isCurrent: true },
      { isCurrent: false },
    );

    // 发布当前版本
    version.status = VersionStatus.PUBLISHED;
    version.isCurrent = true;
    version.publishedAt = new Date();

    const publishedVersion = await this.versionRepository.save(version);

    // 更新模板的当前版本号
    await this.templateRepository.update(
      { id: version.templateId },
      { currentVersion: version.version },
    );

    this.logger.log(`模板版本发布成功: ${id}`, 'VersionsService');
    return publishedVersion;
  }

  /**
   * 删除版本
   */
  async remove(id: string): Promise<void> {
    const version = await this.findOne(id);

    if (version.isCurrent) {
      throw new Error('无法删除当前版本');
    }

    version.status = VersionStatus.DELETED;
    await this.versionRepository.save(version);

    this.logger.log(`模板版本删除成功: ${id}`, 'VersionsService');
  }

  /**
   * 增加下载次数
   */
  async incrementDownloadCount(id: string): Promise<void> {
    await this.versionRepository.increment({ id }, 'downloadCount', 1);
  }

  /**
   * 比较两个版本
   */
  async compareVersions(
    version1Id: string,
    version2Id: string,
  ): Promise<{
    version1: TemplateVersion;
    version2: TemplateVersion;
    differences: {
      sceneData: boolean;
      parameters: boolean;
      metadata: boolean;
    };
  }> {
    const [version1, version2] = await Promise.all([
      this.findOne(version1Id),
      this.findOne(version2Id),
    ]);

    const differences = {
      sceneData: JSON.stringify(version1.sceneDataSnapshot) !== JSON.stringify(version2.sceneDataSnapshot),
      parameters: JSON.stringify(version1.parametersSnapshot) !== JSON.stringify(version2.parametersSnapshot),
      metadata: JSON.stringify(version1.metadataSnapshot) !== JSON.stringify(version2.metadataSnapshot),
    };

    return {
      version1,
      version2,
      differences,
    };
  }

  /**
   * 恢复到指定版本
   */
  async revertToVersion(templateId: string, versionId: string): Promise<SceneTemplate> {
    const version = await this.findOne(versionId);
    
    if (version.templateId !== templateId) {
      throw new Error('版本不属于指定模板');
    }

    const template = await this.templateRepository.findOne({
      where: { id: templateId },
    });

    if (!template) {
      throw new NotFoundException('模板不存在');
    }

    // 恢复模板数据
    template.sceneData = version.sceneDataSnapshot;
    template.defaultParameters = version.parametersSnapshot;
    template.metadata = version.metadataSnapshot;

    const updatedTemplate = await this.templateRepository.save(template);

    this.logger.log(`模板恢复到版本: ${templateId} -> ${versionId}`, 'VersionsService');
    return updatedTemplate;
  }

  /**
   * 获取版本统计
   */
  async getVersionStats(templateId: string): Promise<{
    totalVersions: number;
    publishedVersions: number;
    totalDownloads: number;
    latestVersion: string;
  }> {
    const versions = await this.findByTemplate(templateId);
    
    const publishedVersions = versions.filter(v => v.status === VersionStatus.PUBLISHED);
    const totalDownloads = versions.reduce((sum, v) => sum + v.downloadCount, 0);
    const latestVersion = versions.length > 0 ? versions[0].version : '1.0.0';

    return {
      totalVersions: versions.length,
      publishedVersions: publishedVersions.length,
      totalDownloads,
      latestVersion,
    };
  }
}
