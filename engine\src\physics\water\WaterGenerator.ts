/**
 * 水体生成器
 * 用于快速生成各种类型的水体
 */
import * as THREE from 'three';
import { Entity } from '../../core/Entity';
import { WaterBodyComponent } from './WaterBodyComponent';
import { WaterPresets, WaterPresetType, WaterPresetConfig } from './WaterPresets';

/**
 * 水体生成配置
 */
export interface WaterGenerationConfig {
  /** 预设类型 */
  preset: WaterPresetType;
  /** 位置 */
  position?: THREE.Vector3;
  /** 旋转 */
  rotation?: THREE.Euler;
  /** 缩放 */
  scale?: THREE.Vector3;
  /** 自定义尺寸 */
  size?: {
    width: number;
    height: number;
    depth: number;
  };
  /** 自定义物理参数 */
  physicsParams?: {
    density?: number;
    viscosity?: number;
    surfaceTension?: number;
  };
  /** 自定义波动参数 */
  waveParams?: {
    amplitude?: number;
    frequency?: number;
    speed?: number;
    direction?: THREE.Vector2;
  };
  /** 自定义流动参数 */
  flowParams?: {
    direction?: THREE.Vector3;
    speed?: number;
  };
  /** 自定义视觉参数 */
  visualParams?: {
    color?: THREE.Color;
    opacity?: number;
    reflectivity?: number;
    refractivity?: number;
  };
  /** 实体名称 */
  entityName?: string;
  /** 实体ID */
  entityId?: string;
}

/**
 * 水体生成器类
 */
export class WaterGenerator {
  private static nextId = 1;

  /**
   * 生成水体
   * @param config 生成配置
   * @returns 生成的水体实体和组件
   */
  public static generateWater(config: WaterGenerationConfig): { entity: Entity; waterBody: WaterBodyComponent } {
    // 创建实体
    const entityId = config.entityId || `water_${this.nextId++}`;
    const entityName = config.entityName || `Water_${WaterPresets.getPresetDisplayName(config.preset)}`;

    const entity = new Entity(entityName);
    entity.id = entityId;

    // 构建预设配置
    const presetConfig: WaterPresetConfig = {
      type: config.preset,
      size: config.size,
      position: config.position,
      rotation: config.rotation,
      physicsParams: config.physicsParams ? {
        density: config.physicsParams.density || 1000,
        viscosity: config.physicsParams.viscosity || 1.0,
        surfaceTension: config.physicsParams.surfaceTension
      } : undefined,
      waveParams: config.waveParams ? {
        amplitude: config.waveParams.amplitude || 0.1,
        frequency: config.waveParams.frequency || 0.5,
        speed: config.waveParams.speed || 0.3,
        direction: config.waveParams.direction || new THREE.Vector2(1, 1)
      } : undefined,
      flowParams: config.flowParams ? {
        direction: config.flowParams.direction || new THREE.Vector3(0, 0, 0),
        speed: config.flowParams.speed || 0
      } : undefined,
      color: config.visualParams?.color,
      opacity: config.visualParams?.opacity,
      reflectivity: config.visualParams?.reflectivity,
      refractivity: config.visualParams?.refractivity
    };

    // 创建水体组件
    const waterBody = WaterPresets.createPreset(entity, presetConfig);

    // 设置位置、旋转和缩放
    const transform = entity.getTransform();
    if (config.position) {
      transform.setPosition(config.position);
    }

    if (config.rotation) {
      transform.setRotation(config.rotation);
    }

    if (config.scale) {
      transform.setScale(config.scale);
    }

    return { entity, waterBody };
  }

  /**
   * 快速生成湖泊
   * @param position 位置
   * @param size 尺寸
   * @returns 生成的水体
   */
  public static generateLake(
    position: THREE.Vector3 = new THREE.Vector3(0, 0, 0),
    size: { width: number; height: number; depth: number } = { width: 20, height: 2, depth: 20 }
  ) {
    return this.generateWater({
      preset: WaterPresetType.LAKE,
      position,
      size,
      entityName: '湖泊'
    });
  }

  /**
   * 快速生成河流
   * @param position 位置
   * @param size 尺寸
   * @param flowDirection 流动方向
   * @returns 生成的水体
   */
  public static generateRiver(
    position: THREE.Vector3 = new THREE.Vector3(0, 0, 0),
    size: { width: number; height: number; depth: number } = { width: 5, height: 1, depth: 50 },
    flowDirection: THREE.Vector3 = new THREE.Vector3(1, 0, 0)
  ) {
    return this.generateWater({
      preset: WaterPresetType.RIVER,
      position,
      size,
      flowParams: {
        direction: flowDirection,
        speed: 0.5
      },
      entityName: '河流'
    });
  }

  /**
   * 快速生成海洋
   * @param position 位置
   * @param size 尺寸
   * @returns 生成的水体
   */
  public static generateOcean(
    position: THREE.Vector3 = new THREE.Vector3(0, 0, 0),
    size: { width: number; height: number; depth: number } = { width: 100, height: 5, depth: 100 }
  ) {
    return this.generateWater({
      preset: WaterPresetType.OCEAN,
      position,
      size,
      entityName: '海洋'
    });
  }

  /**
   * 快速生成游泳池
   * @param position 位置
   * @param size 尺寸
   * @returns 生成的水体
   */
  public static generatePool(
    position: THREE.Vector3 = new THREE.Vector3(0, 0, 0),
    size: { width: number; height: number; depth: number } = { width: 10, height: 2, depth: 5 }
  ) {
    return this.generateWater({
      preset: WaterPresetType.POOL,
      position,
      size,
      visualParams: {
        color: new THREE.Color(0x00bbff),
        opacity: 0.7
      },
      entityName: '游泳池'
    });
  }

  /**
   * 快速生成温泉
   * @param position 位置
   * @param size 尺寸
   * @returns 生成的水体
   */
  public static generateHotSpring(
    position: THREE.Vector3 = new THREE.Vector3(0, 0, 0),
    size: { width: number; height: number; depth: number } = { width: 3, height: 1, depth: 3 }
  ) {
    return this.generateWater({
      preset: WaterPresetType.HOT_SPRING,
      position,
      size,
      visualParams: {
        color: new THREE.Color(0x88ccff),
        opacity: 0.6
      },
      waveParams: {
        amplitude: 0.05,
        frequency: 1.2,
        speed: 0.4
      },
      entityName: '温泉'
    });
  }

  /**
   * 快速生成瀑布
   * @param position 位置
   * @param size 尺寸
   * @returns 生成的水体
   */
  public static generateWaterfall(
    position: THREE.Vector3 = new THREE.Vector3(0, 10, 0),
    size: { width: number; height: number; depth: number } = { width: 2, height: 10, depth: 1 }
  ) {
    return this.generateWater({
      preset: WaterPresetType.WATERFALL,
      position,
      size,
      flowParams: {
        direction: new THREE.Vector3(0, -1, 0),
        speed: 2.0
      },
      visualParams: {
        color: new THREE.Color(0xaaddff),
        opacity: 0.5
      },
      entityName: '瀑布'
    });
  }

  /**
   * 批量生成水体
   * @param configs 配置数组
   * @returns 生成的水体数组
   */
  public static generateMultipleWaters(configs: WaterGenerationConfig[]): Array<{ entity: Entity; waterBody: WaterBodyComponent }> {
    return configs.map(config => this.generateWater(config));
  }

  /**
   * 生成水体场景
   * @param sceneType 场景类型
   * @returns 生成的水体数组
   */
  public static generateWaterScene(sceneType: 'lake_scene' | 'river_scene' | 'ocean_scene' | 'pool_scene'): Array<{ entity: Entity; waterBody: WaterBodyComponent }> {
    switch (sceneType) {
      case 'lake_scene':
        return [
          this.generateLake(new THREE.Vector3(0, 0, 0), { width: 30, height: 3, depth: 30 }),
          this.generateRiver(new THREE.Vector3(35, 0, 0), { width: 3, height: 1, depth: 20 })
        ];
      
      case 'river_scene':
        return [
          this.generateRiver(new THREE.Vector3(0, 0, 0), { width: 5, height: 1, depth: 100 }),
          this.generateWaterfall(new THREE.Vector3(-10, 5, -50), { width: 2, height: 5, depth: 1 })
        ];
      
      case 'ocean_scene':
        return [
          this.generateOcean(new THREE.Vector3(0, 0, 0), { width: 200, height: 8, depth: 200 })
        ];
      
      case 'pool_scene':
        return [
          this.generatePool(new THREE.Vector3(0, 0, 0), { width: 15, height: 2, depth: 8 }),
          this.generateHotSpring(new THREE.Vector3(20, 0, 0), { width: 4, height: 1, depth: 4 })
        ];
      
      default:
        return [];
    }
  }

  /**
   * 获取推荐的水体配置
   * @param environment 环境类型
   * @returns 推荐配置
   */
  public static getRecommendedConfig(environment: 'indoor' | 'outdoor' | 'underground' | 'fantasy'): WaterGenerationConfig[] {
    switch (environment) {
      case 'indoor':
        return [
          { preset: WaterPresetType.POOL, entityName: '室内泳池' },
          { preset: WaterPresetType.HOT_SPRING, entityName: '室内温泉' }
        ];
      
      case 'outdoor':
        return [
          { preset: WaterPresetType.LAKE, entityName: '户外湖泊' },
          { preset: WaterPresetType.RIVER, entityName: '户外河流' },
          { preset: WaterPresetType.WATERFALL, entityName: '户外瀑布' }
        ];
      
      case 'underground':
        return [
          { preset: WaterPresetType.UNDERGROUND_LAKE, entityName: '地下湖泊' },
          { preset: WaterPresetType.UNDERGROUND_RIVER, entityName: '地下河流' }
        ];
      
      case 'fantasy':
        return [
          { preset: WaterPresetType.LAVA, entityName: '熔岩池' },
          { preset: WaterPresetType.ICE_LAKE, entityName: '冰湖' },
          { preset: WaterPresetType.SWAMP, entityName: '魔法沼泽' }
        ];
      
      default:
        return [{ preset: WaterPresetType.LAKE, entityName: '默认湖泊' }];
    }
  }
}
