import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MetricsEntity } from './entities/metrics.entity';
import { ServiceMetricsEntity } from './entities/service-metrics.entity';
import { SystemMetricsEntity } from './entities/system-metrics.entity';

@Injectable()
export class MetricsStorageService {
  private readonly logger = new Logger(MetricsStorageService.name);

  constructor(
    @InjectRepository(MetricsEntity)
    private readonly metricsRepository: Repository<MetricsEntity>,
    @InjectRepository(ServiceMetricsEntity)
    private readonly serviceMetricsRepository: Repository<ServiceMetricsEntity>,
    @InjectRepository(SystemMetricsEntity)
    private readonly systemMetricsRepository: Repository<SystemMetricsEntity>,
  ) {}

  /**
   * 存储服务指标
   */
  async storeServiceMetrics(service: any, metrics: any): Promise<void> {
    try {
      // 存储原始指标
      const rawMetrics = new MetricsEntity();
      rawMetrics.serviceId = service.serviceId;
      rawMetrics.serviceType = service.serviceType;
      rawMetrics.instanceId = service.id;
      rawMetrics.hostname = service.host;
      rawMetrics.metrics = metrics;
      rawMetrics.timestamp = new Date();

      await this.metricsRepository.save(rawMetrics);

      // 提取并存储服务指标
      const serviceMetrics = new ServiceMetricsEntity();
      serviceMetrics.serviceId = service.serviceId;
      serviceMetrics.serviceType = service.serviceType;
      serviceMetrics.instanceId = service.id;
      serviceMetrics.hostname = service.host;
      serviceMetrics.requestCount = metrics.requestCount || 0;
      serviceMetrics.errorCount = metrics.errorCount || 0;
      serviceMetrics.errorRate = metrics.errorRate || 0;
      serviceMetrics.averageResponseTime = metrics.averageResponseTime || 0;
      serviceMetrics.activeConnections = metrics.activeConnections || 0;
      serviceMetrics.queueLength = metrics.queueLength || 0;
      serviceMetrics.customMetrics = this.extractCustomMetrics(metrics);
      serviceMetrics.timestamp = new Date();

      await this.serviceMetricsRepository.save(serviceMetrics);
    } catch (error) {
      this.logger.error(`存储服务指标失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 存储系统指标
   */
  async storeSystemMetrics(metrics: any): Promise<void> {
    try {
      const systemMetrics = new SystemMetricsEntity();
      systemMetrics.hostname = metrics.hostname;
      systemMetrics.cpuUsage = metrics.cpuUsage;
      systemMetrics.memoryUsage = metrics.memoryUsage;
      systemMetrics.totalMemory = metrics.totalMemory;
      systemMetrics.freeMemory = metrics.freeMemory;
      systemMetrics.diskUsage = metrics.diskUsage;
      systemMetrics.totalDisk = metrics.totalDisk;
      systemMetrics.freeDisk = metrics.freeDisk;
      systemMetrics.loadAverage = metrics.loadAverage;
      systemMetrics.networkConnections = metrics.networkConnections;
      systemMetrics.timestamp = metrics.timestamp;

      await this.systemMetricsRepository.save(systemMetrics);
    } catch (error) {
      this.logger.error(`存储系统指标失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 提取自定义指标
   */
  private extractCustomMetrics(metrics: any): Record<string, any> {
    const standardMetrics = [
      'requestCount',
      'errorCount',
      'errorRate',
      'averageResponseTime',
      'activeConnections',
      'queueLength',
      'timestamp',
      'hostname',
    ];

    const customMetrics: Record<string, any> = {};

    for (const key in metrics) {
      if (!standardMetrics.includes(key)) {
        customMetrics[key] = metrics[key];
      }
    }

    return customMetrics;
  }
}
