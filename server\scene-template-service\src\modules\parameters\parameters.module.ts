import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ParametersService } from './parameters.service';
import { ParametersController, ParameterController } from './parameters.controller';
import { TemplateParameter } from './entities/template-parameter.entity';
import { SceneTemplate } from '../templates/entities/scene-template.entity';
import { LoggerService } from '../../common/services/logger.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([TemplateParameter, SceneTemplate]),
  ],
  controllers: [ParametersController, ParameterController],
  providers: [
    ParametersService,
    LoggerService,
  ],
  exports: [ParametersService, TypeOrmModule],
})
export class ParametersModule {}
