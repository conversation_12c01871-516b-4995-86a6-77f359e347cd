import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TemplateRating } from './entities/template-rating.entity';
import { SceneTemplate } from '../templates/entities/scene-template.entity';
import { LoggerService } from '../../common/services/logger.service';

export interface CreateRatingDto {
  rating: number;
  comment?: string;
  tags?: string[];
}

export interface UpdateRatingDto {
  rating?: number;
  comment?: string;
  tags?: string[];
}

export interface RatingStats {
  averageRating: number;
  totalRatings: number;
  ratingDistribution: { [key: number]: number };
  recentRatings: TemplateRating[];
}

@Injectable()
export class RatingsService {
  constructor(
    @InjectRepository(TemplateRating)
    private readonly ratingRepository: Repository<TemplateRating>,
    @InjectRepository(SceneTemplate)
    private readonly templateRepository: Repository<SceneTemplate>,
    private readonly logger: LoggerService,
  ) {}

  /**
   * 创建或更新评分
   */
  async createOrUpdateRating(
    templateId: string,
    userId: string,
    createRatingDto: CreateRatingDto,
  ): Promise<TemplateRating> {
    // 验证评分范围
    if (createRatingDto.rating < 1 || createRatingDto.rating > 5) {
      throw new BadRequestException('评分必须在1-5之间');
    }

    // 验证模板是否存在
    const template = await this.templateRepository.findOne({
      where: { id: templateId },
    });

    if (!template) {
      throw new NotFoundException('模板不存在');
    }

    // 检查是否已有评分
    let rating = await this.ratingRepository.findOne({
      where: { templateId, userId },
    });

    if (rating) {
      // 更新现有评分
      Object.assign(rating, createRatingDto);
    } else {
      // 创建新评分
      rating = this.ratingRepository.create({
        ...createRatingDto,
        templateId,
        userId,
      });
    }

    const savedRating = await this.ratingRepository.save(rating);

    // 更新模板的平均评分
    await this.updateTemplateRating(templateId);

    this.logger.log(`模板评分${rating.id ? '更新' : '创建'}成功: ${templateId}`, 'RatingsService');
    return savedRating;
  }

  /**
   * 获取模板的所有评分
   */
  async findByTemplate(
    templateId: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<{ ratings: TemplateRating[]; total: number }> {
    const [ratings, total] = await this.ratingRepository.findAndCount({
      where: { templateId },
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
      relations: ['user'],
    });

    return { ratings, total };
  }

  /**
   * 获取用户的评分
   */
  async findUserRating(templateId: string, userId: string): Promise<TemplateRating | null> {
    return await this.ratingRepository.findOne({
      where: { templateId, userId },
      relations: ['user'],
    });
  }

  /**
   * 删除评分
   */
  async remove(templateId: string, userId: string): Promise<void> {
    const rating = await this.ratingRepository.findOne({
      where: { templateId, userId },
    });

    if (!rating) {
      throw new NotFoundException('评分不存在');
    }

    await this.ratingRepository.remove(rating);

    // 更新模板的平均评分
    await this.updateTemplateRating(templateId);

    this.logger.log(`模板评分删除成功: ${templateId}`, 'RatingsService');
  }

  /**
   * 获取评分统计
   */
  async getRatingStats(templateId: string): Promise<RatingStats> {
    const ratings = await this.ratingRepository.find({
      where: { templateId },
      order: { createdAt: 'DESC' },
      relations: ['user'],
    });

    const totalRatings = ratings.length;
    const averageRating = totalRatings > 0 
      ? ratings.reduce((sum, r) => sum + r.rating, 0) / totalRatings 
      : 0;

    // 评分分布
    const ratingDistribution: { [key: number]: number } = {};
    for (let i = 1; i <= 5; i++) {
      ratingDistribution[i] = ratings.filter(r => Math.floor(r.rating) === i).length;
    }

    // 最近的评分
    const recentRatings = ratings.slice(0, 10);

    return {
      averageRating: Math.round(averageRating * 10) / 10,
      totalRatings,
      ratingDistribution,
      recentRatings,
    };
  }

  /**
   * 标记评分为有用/无用
   */
  async markHelpful(ratingId: string, helpful: boolean): Promise<TemplateRating> {
    const rating = await this.ratingRepository.findOne({
      where: { id: ratingId },
    });

    if (!rating) {
      throw new NotFoundException('评分不存在');
    }

    if (helpful) {
      rating.helpfulCount += 1;
    } else {
      rating.unhelpfulCount += 1;
    }

    return await this.ratingRepository.save(rating);
  }

  /**
   * 获取热门评分标签
   */
  async getPopularTags(templateId?: string): Promise<{ tag: string; count: number }[]> {
    const query = this.ratingRepository.createQueryBuilder('rating');
    
    if (templateId) {
      query.where('rating.templateId = :templateId', { templateId });
    }

    const ratings = await query.getMany();
    
    const tagCounts: { [key: string]: number } = {};
    
    ratings.forEach(rating => {
      if (rating.tags) {
        rating.tags.forEach(tag => {
          tagCounts[tag] = (tagCounts[tag] || 0) + 1;
        });
      }
    });

    return Object.entries(tagCounts)
      .map(([tag, count]) => ({ tag, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 20);
  }

  /**
   * 更新模板的平均评分
   */
  private async updateTemplateRating(templateId: string): Promise<void> {
    const stats = await this.getRatingStats(templateId);
    
    await this.templateRepository.update(
      { id: templateId },
      { 
        rating: stats.averageRating,
        ratingCount: stats.totalRatings,
      },
    );
  }

  /**
   * 获取用户的所有评分
   */
  async findByUser(
    userId: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<{ ratings: TemplateRating[]; total: number }> {
    const [ratings, total] = await this.ratingRepository.findAndCount({
      where: { userId },
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
      relations: ['template'],
    });

    return { ratings, total };
  }

  /**
   * 获取最高评分的模板
   */
  async getTopRatedTemplates(limit: number = 10): Promise<TemplateRating[]> {
    return await this.ratingRepository.find({
      order: { rating: 'DESC' },
      take: limit,
      relations: ['template', 'user'],
    });
  }
}
