/**
 * 用户测试状态切片
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '..';
import { TestTask, UserFeedback } from '../../services/UserTestingService';

/**
 * 用户测试状态接口
 */
export interface UserTestingState {
  isEnabled: boolean;
  currentSessionId: string | null;
  tasks: TestTask[];
  currentTaskId: string | null;
  feedback: UserFeedback[];
  isRecording: boolean;
  sessionsHistory: string[]; // 会话历史ID列表
  reports: Record<string, any>; // 报告数据，键为报告ID
}

/**
 * 初始状态
 */
const initialState: UserTestingState = {
  isEnabled: false,
  currentSessionId: null,
  tasks: [],
  currentTaskId: null,
  feedback: [],
  isRecording: false,
  sessionsHistory: [],
  reports: {}
};

/**
 * 创建用户测试切片
 */
export const userTestingSlice = createSlice({
  name: 'userTesting',
  initialState,
  reducers: {
    // 设置是否启用测试
    setTestingEnabled: (state, action: PayloadAction<boolean>) => {
      state.isEnabled = action.payload;

      // 如果禁用，重置状态
      if (!action.payload) {
        state.currentSessionId = null;
        state.tasks = [];
        state.currentTaskId = null;
        state.feedback = [];
        state.isRecording = false;
      }
    },

    // 开始测试会话
    startTestSession: (state, action: PayloadAction<{ sessionId: string, tasks: TestTask[] }>) => {
      state.currentSessionId = action.payload.sessionId;
      state.tasks = action.payload.tasks;
      state.currentTaskId = null;
      state.feedback = [];
    },

    // 结束测试会话
    endTestSession: (state) => {
      state.currentSessionId = null;
      state.tasks = [];
      state.currentTaskId = null;
      state.feedback = [];
      state.isRecording = false;
    },

    // 设置测试任务
    setTestTasks: (state, action: PayloadAction<TestTask[]>) => {
      state.tasks = action.payload;
    },

    // 设置当前任务
    setCurrentTask: (state, action: PayloadAction<string>) => {
      state.currentTaskId = action.payload;
    },

    // 完成任务
    completeTask: (state, action: PayloadAction<string>) => {
      const taskIndex = state.tasks.findIndex(task => task.id === action.payload);

      if (taskIndex !== -1) {
        state.tasks[taskIndex].completed = true;
        state.tasks[taskIndex].endTime = Date.now();

        // 计算花费时间
        if (state.tasks[taskIndex].startTime) {
          state.tasks[taskIndex].timeSpent = state.tasks[taskIndex].endTime! - state.tasks[taskIndex].startTime!;
        }
      }
    },

    // 添加反馈
    addFeedback: (state, action: PayloadAction<UserFeedback>) => {
      state.feedback.push(action.payload);
    },

    // 设置是否录制
    setRecording: (state, action: PayloadAction<boolean>) => {
      state.isRecording = action.payload;
    },

    // 添加测试会话到历史
    addTestSession: (state, action: PayloadAction<string>) => {
      if (!state.sessionsHistory.includes(action.payload)) {
        state.sessionsHistory.unshift(action.payload);

        // 限制历史记录大小
        if (state.sessionsHistory.length > 10) {
          state.sessionsHistory.pop();
        }
      }
    },

    // 添加测试报告
    addTestReport: (state, action: PayloadAction<{ reportId: string, report: any }>) => {
      state.reports[action.payload.reportId] = action.payload.report;
    },

    // 更新任务进度
    updateTaskProgress: (state, action: PayloadAction<{ taskId: string, progress: number }>) => {
      const taskIndex = state.tasks.findIndex(task => task.id === action.payload.taskId);

      if (taskIndex !== -1) {
        // @ts-ignore - 添加进度属性
        state.tasks[taskIndex].progress = action.payload.progress;
      }
    }
  }
});

// 导出操作
export const {
  setTestingEnabled,
  startTestSession,
  endTestSession,
  setTestTasks,
  setCurrentTask,
  completeTask,
  addFeedback,
  setRecording,
  addTestSession,
  addTestReport,
  updateTaskProgress
} = userTestingSlice.actions;

// 选择器
export const selectTestingEnabled = (state: RootState) => state.userTesting.isEnabled;
export const selectCurrentSession = (state: RootState) => state.userTesting.currentSessionId;
export const selectTasks = (state: RootState) => state.userTesting.tasks;
export const selectCurrentTaskId = (state: RootState) => state.userTesting.currentTaskId;
export const selectFeedback = (state: RootState) => state.userTesting.feedback;
export const selectIsRecording = (state: RootState) => state.userTesting.isRecording;
export const selectSessionsHistory = (state: RootState) => state.userTesting.sessionsHistory;
export const selectReports = (state: RootState) => state.userTesting.reports;
export const selectReport = (reportId: string) => (state: RootState) => state.userTesting.reports[reportId];

// 导出reducer
export default userTestingSlice.reducer;
