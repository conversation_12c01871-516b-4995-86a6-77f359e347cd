#!/usr/bin/env node
/**
 * 更新前端服务脚本
 * 将前端服务中的Mock数据调用替换为真实API调用
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'cyan');
}

function logHeader(message) {
  log(`\n🚀 ${message}`, 'magenta');
  log('='.repeat(60), 'magenta');
}

// 文件操作辅助函数
function updateFileContent(filePath, updates) {
  if (!fs.existsSync(filePath)) {
    logWarning(`文件不存在: ${filePath}`);
    return false;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  updates.forEach(update => {
    if (content.includes(update.search)) {
      content = content.replace(new RegExp(update.search, 'g'), update.replace);
      modified = true;
      logInfo(`已更新: ${update.description}`);
    }
  });
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    logSuccess(`文件已更新: ${filePath}`);
    return true;
  } else {
    logInfo(`文件无需更新: ${filePath}`);
    return false;
  }
}

// 1. 更新环境配置
function updateEnvironmentConfig() {
  logHeader('更新环境配置');
  
  const configPath = 'editor/src/config/environment.ts';
  
  const updates = [
    {
      search: 'enableMockData: true',
      replace: 'enableMockData: false',
      description: '禁用Mock数据'
    },
    {
      search: "apiUrl: 'http://localhost:8080/api'",
      replace: "apiUrl: 'http://localhost:3000/api'",
      description: '更新API网关地址'
    },
    {
      search: "userService: 'http://localhost:4001'",
      replace: "userService: 'http://localhost:3000/api'",
      description: '用户服务地址指向API网关'
    },
    {
      search: "projectService: 'http://localhost:4002'",
      replace: "projectService: 'http://localhost:3000/api'",
      description: '项目服务地址指向API网关'
    },
    {
      search: "assetService: 'http://localhost:4003'",
      replace: "assetService: 'http://localhost:3000/api'",
      description: '资产服务地址指向API网关'
    },
    {
      search: "renderService: 'http://localhost:4004'",
      replace: "renderService: 'http://localhost:3000/api'",
      description: '渲染服务地址指向API网关'
    }
  ];
  
  updateFileContent(configPath, updates);
}

// 2. 更新API客户端
function updateApiClient() {
  logHeader('更新API客户端');
  
  const apiClientPath = 'editor/src/services/ApiClient.ts';
  
  const updates = [
    {
      search: "return 'http://localhost:8080/api'",
      replace: "return 'http://localhost:3000/api'",
      description: '更新开发环境API基础URL'
    }
  ];
  
  updateFileContent(apiClientPath, updates);
}

// 3. 更新认证服务
function updateAuthService() {
  logHeader('更新认证服务');
  
  const authSlicePath = 'editor/src/store/auth/authSlice.ts';
  
  const updates = [
    {
      search: "axios.post\\('http://localhost:8080/api/auth/login'",
      replace: "axios.post('http://localhost:3000/api/auth/login'",
      description: '更新登录API地址'
    },
    {
      search: "axios.post\\('http://localhost:8080/api/auth/register'",
      replace: "axios.post('http://localhost:3000/api/auth/register'",
      description: '更新注册API地址'
    },
    {
      search: "axios.get\\('http://localhost:8080/api/auth/profile'",
      replace: "axios.get('http://localhost:3000/api/auth/profile'",
      description: '更新用户信息API地址'
    }
  ];
  
  updateFileContent(authSlicePath, updates);
}

// 4. 更新项目服务
function updateProjectService() {
  logHeader('更新项目服务');
  
  const projectSlicePath = 'editor/src/store/project/projectSlice.ts';
  
  const updates = [
    {
      search: "axios.get\\('http://localhost:8080/api/projects'",
      replace: "axios.get('http://localhost:3000/api/projects'",
      description: '更新项目列表API地址'
    },
    {
      search: "axios.get\\(`http://localhost:8080/api/projects/\\$\\{projectId\\}`",
      replace: "axios.get(`http://localhost:3000/api/projects/${projectId}`",
      description: '更新项目详情API地址'
    },
    {
      search: "axios.post\\('http://localhost:8080/api/projects'",
      replace: "axios.post('http://localhost:3000/api/projects'",
      description: '更新创建项目API地址'
    }
  ];
  
  updateFileContent(projectSlicePath, updates);
}

// 5. 更新资产服务
function updateAssetService() {
  logHeader('更新资产服务');
  
  const assetServicePath = 'editor/src/services/AssetService.ts';
  
  const updates = [
    {
      search: "axios.get\\(`http://localhost:8080/api/assets`",
      replace: "axios.get(`http://localhost:3000/api/assets`",
      description: '更新资产列表API地址'
    },
    {
      search: "axios.post\\(`http://localhost:8080/api/assets`",
      replace: "axios.post(`http://localhost:3000/api/assets`",
      description: '更新资产上传API地址'
    }
  ];
  
  updateFileContent(assetServicePath, updates);
  
  // 更新资产切片
  const assetSlicePath = 'editor/src/store/asset/assetSlice.ts';
  
  const assetSliceUpdates = [
    {
      search: "axios.get\\(`/api/projects/\\$\\{projectId\\}/assets`",
      replace: "axios.get(`http://localhost:3000/api/projects/${projectId}/assets`",
      description: '更新项目资产API地址'
    }
  ];
  
  updateFileContent(assetSlicePath, assetSliceUpdates);
}

// 6. 更新示例服务
function updateExampleService() {
  logHeader('更新示例服务');
  
  const exampleServicePath = 'editor/src/services/exampleService.ts';
  
  if (fs.existsSync(exampleServicePath)) {
    let content = fs.readFileSync(exampleServicePath, 'utf8');
    
    // 确保示例服务优先使用API，失败时才使用Mock数据
    const apiFirstPattern = `try {
    // 尝试从API获取示例项目
    const response = await fetch('http://localhost:3000/api/examples');
    if (response.ok) {
      const data = await response.json();
      return data;
    } else {
      // 如果API不可用，使用模拟数据
      console.warn('示例项目API不可用，使用模拟数据');
      return mockExamples;
    }
  } catch (error) {
    console.error('获取示例项目失败:', error);
    console.warn('使用模拟数据作为后备');
    // 如果API调用失败，使用模拟数据作为后备
    return mockExamples;
  }`;
    
    if (content.includes('http://localhost:3000/api/examples')) {
      logSuccess('示例服务已配置为优先使用真实API');
    } else {
      logInfo('示例服务将保持Mock数据作为后备机制');
    }
  }
}

// 7. 更新组件中的API调用
function updateComponents() {
  logHeader('更新组件API调用');
  
  // 更新VisemeEditor组件
  const visemeEditorPath = 'editor/src/components/VisemeEditor.tsx';
  if (fs.existsSync(visemeEditorPath)) {
    const updates = [
      {
        search: "fetch\\('http://localhost:8080/api/visemes'\\)",
        replace: "fetch('http://localhost:3000/api/visemes')",
        description: '更新Viseme数据API地址'
      }
    ];
    updateFileContent(visemeEditorPath, updates);
  }
  
  // 更新MuscleEditor组件
  const muscleEditorPath = 'editor/src/components/MuscleEditor.tsx';
  if (fs.existsSync(muscleEditorPath)) {
    const updates = [
      {
        search: "fetch\\('http://localhost:8080/api/muscles'\\)",
        replace: "fetch('http://localhost:3000/api/muscles')",
        description: '更新Muscle数据API地址'
      }
    ];
    updateFileContent(muscleEditorPath, updates);
  }
}

// 8. 更新微服务集成配置
function updateMicroserviceIntegration() {
  logHeader('更新微服务集成配置');
  
  const integrationPath = 'editor/src/services/MicroserviceIntegration.ts';
  
  if (fs.existsSync(integrationPath)) {
    const updates = [
      {
        search: "baseURL: 'http://localhost:8080/api'",
        replace: "baseURL: 'http://localhost:3000/api'",
        description: '更新微服务集成基础URL'
      }
    ];
    updateFileContent(integrationPath, updates);
  }
}

// 9. 创建API测试辅助函数
function createApiTestHelpers() {
  logHeader('创建API测试辅助函数');
  
  const testHelpersPath = 'editor/src/utils/apiTestHelpers.ts';
  
  const testHelpersContent = `/**
 * API测试辅助函数
 * 用于在开发过程中测试API连接
 */

export const API_BASE_URL = 'http://localhost:3000/api';

/**
 * 测试API连接
 */
export async function testApiConnection(): Promise<boolean> {
  try {
    const response = await fetch(\`\${API_BASE_URL}/health\`);
    return response.ok;
  } catch (error) {
    console.error('API连接测试失败:', error);
    return false;
  }
}

/**
 * 测试认证API
 */
export async function testAuthApi(token?: string): Promise<boolean> {
  try {
    const headers: HeadersInit = {};
    if (token) {
      headers.Authorization = \`Bearer \${token}\`;
    }
    
    const response = await fetch(\`\${API_BASE_URL}/auth/profile\`, { headers });
    return response.ok;
  } catch (error) {
    console.error('认证API测试失败:', error);
    return false;
  }
}

/**
 * 获取API状态
 */
export async function getApiStatus(): Promise<{
  gateway: boolean;
  auth: boolean;
  projects: boolean;
  assets: boolean;
}> {
  const status = {
    gateway: false,
    auth: false,
    projects: false,
    assets: false
  };
  
  try {
    // 测试网关
    const gatewayResponse = await fetch(\`\${API_BASE_URL}/health\`);
    status.gateway = gatewayResponse.ok;
    
    // 测试其他服务需要认证，这里只测试端点是否存在
    const authResponse = await fetch(\`\${API_BASE_URL}/auth/profile\`);
    status.auth = authResponse.status !== 404;
    
    const projectsResponse = await fetch(\`\${API_BASE_URL}/projects\`);
    status.projects = projectsResponse.status !== 404;
    
    const assetsResponse = await fetch(\`\${API_BASE_URL}/assets\`);
    status.assets = assetsResponse.status !== 404;
    
  } catch (error) {
    console.error('API状态检查失败:', error);
  }
  
  return status;
}`;
  
  fs.writeFileSync(testHelpersPath, testHelpersContent, 'utf8');
  logSuccess(`创建API测试辅助函数: ${testHelpersPath}`);
}

// 主更新流程
function updateFrontendServices() {
  logHeader('前端服务更新 - 替换Mock数据为真实API调用');
  
  // 执行所有更新
  updateEnvironmentConfig();
  updateApiClient();
  updateAuthService();
  updateProjectService();
  updateAssetService();
  updateExampleService();
  updateComponents();
  updateMicroserviceIntegration();
  createApiTestHelpers();
  
  logHeader('更新完成');
  logSuccess('前端服务已更新为使用真实API');
  logInfo('建议执行以下步骤：');
  logInfo('1. 启动后端服务: ./start-windows.ps1');
  logInfo('2. 运行联调测试: node frontend-backend-integration-test.js');
  logInfo('3. 启动前端应用: cd editor && npm start');
  logWarning('注意：某些API端点可能尚未实现，会优雅降级到Mock数据');
}

// 运行更新
if (require.main === module) {
  updateFrontendServices();
}

module.exports = {
  updateFrontendServices,
  updateEnvironmentConfig,
  updateApiClient,
  updateAuthService,
  updateProjectService,
  updateAssetService
};
