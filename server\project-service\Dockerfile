FROM node:22-alpine AS builder

WORKDIR /app

# 复制共享模块
COPY shared/ ./shared/

# 复制项目服务代码
COPY project-service/package*.json ./
RUN npm install

# 复制项目服务源代码
COPY project-service/src ./src
COPY project-service/tsconfig.json ./
COPY project-service/nest-cli.json ./
COPY project-service/health-check.js ./

RUN npm run build

FROM node:22-alpine

# 安装curl用于健康检查
RUN apk add --no-cache curl

WORKDIR /app

COPY --from=builder /app/package*.json ./
COPY --from=builder /app/dist ./dist

# 复制健康检查脚本
COPY --from=builder /app/health-check.js ./health-check.js

RUN npm install --only=production

EXPOSE 3002 4002

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD node health-check.js || exit 1

CMD ["node", "dist/main.js"]
