/**
 * 健康检查控制器
 */
import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import {
  HealthCheckService,
  HealthCheck,
  TypeOrmHealthIndicator,
  MemoryHealthIndicator,
  DiskHealthIndicator,
} from '@nestjs/terminus';

@ApiTags('health')
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private db: TypeOrmHealthIndicator,
    private memory: MemoryHealthIndicator,
    private disk: DiskHealthIndicator,
  ) {}

  @Get()
  @ApiOperation({ summary: '健康检查' })
  @ApiResponse({ status: 200, description: '服务健康' })
  @ApiResponse({ status: 503, description: '服务不健康' })
  @HealthCheck()
  check() {
    return this.health.check([
      // 数据库健康检查
      () => this.db.pingCheck('database'),
      
      // 内存使用检查 (不超过 1.5GB)
      () => this.memory.checkHeap('memory_heap', 1500 * 1024 * 1024),
      
      // RSS 内存检查 (不超过 1.5GB)
      () => this.memory.checkRSS('memory_rss', 1500 * 1024 * 1024),
      
      // 磁盘空间检查 (至少 1GB 可用空间)
      () => this.disk.checkStorage('storage', {
        path: '/',
        thresholdPercent: 0.9,
      }),
    ]);
  }

  @Get('simple')
  @ApiOperation({ summary: '简单健康检查' })
  @ApiResponse({ status: 200, description: '服务运行正常' })
  simple() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'ai-model-service',
      version: '1.0.0',
    };
  }
}
