/**
 * InteractionHighlightComponent.ts
 *
 * 交互高亮组件，用于高亮可交互对象
 */

import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { Color, Object3D, Mesh, MeshBasicMaterial, ShaderMaterial, Vector3 } from 'three';
import type { Transform } from '../../scene/Transform';

/**
 * 高亮类型枚举
 */
export enum HighlightType {
  /** 轮廓高亮 */
  OUTLINE = 'outline',
  /** 发光高亮 */
  GLOW = 'glow',
  /** 颜色高亮 */
  COLOR = 'color',
  /** 自定义高亮 */
  CUSTOM = 'custom'
}

/**
 * 交互高亮组件配置
 */
export interface InteractionHighlightComponentConfig {
  /** 高亮类型 */
  highlightType?: HighlightType;
  /** 高亮颜色 */
  highlightColor?: Color | string;
  /** 高亮强度 */
  highlightIntensity?: number;
  /** 高亮宽度（轮廓高亮） */
  outlineWidth?: number;
  /** 高亮脉冲（是否呼吸效果） */
  pulse?: boolean;
  /** 脉冲速度 */
  pulseSpeed?: number;
  /** 是否启用 */
  enabled?: boolean;
  /** 是否高亮 */
  highlighted?: boolean;
}

/**
 * 交互高亮组件
 * 用于高亮可交互对象
 */
export class InteractionHighlightComponent extends Component {
  /** 高亮类型 */
  private _highlightType: HighlightType;

  /** 高亮颜色 */
  private _highlightColor: Color;

  /** 高亮强度 */
  private _highlightIntensity: number;

  /** 高亮宽度（轮廓高亮） */
  private _outlineWidth: number;

  /** 高亮脉冲（是否呼吸效果） */
  private _pulse: boolean;

  /** 脉冲速度 */
  private _pulseSpeed: number;

  /** 是否高亮 */
  private _highlighted: boolean;

  /** 原始材质映射 */
  private originalMaterials: Map<Mesh, MeshBasicMaterial | ShaderMaterial> = new Map();

  /** 高亮材质映射 */
  private highlightMaterials: Map<Mesh, MeshBasicMaterial | ShaderMaterial> = new Map();

  /** 脉冲计时器 */
  private pulseTime: number = 0;

  /** 当前脉冲值（0-1） */
  private pulseValue: number = 0;

  /**
   * 构造函数
   * @param config 组件配置
   */
  constructor(config: InteractionHighlightComponentConfig = {}) {
    // 调用基类构造函数，传入组件类型名称
    super('InteractionHighlight');

    // 初始化属性
    this._highlightType = config.highlightType || HighlightType.OUTLINE;
    this._highlightColor = new Color(config.highlightColor || '#ffff00');
    this._highlightIntensity = config.highlightIntensity !== undefined ? config.highlightIntensity : 1.0;
    this._outlineWidth = config.outlineWidth !== undefined ? config.outlineWidth : 2.0;
    this._pulse = config.pulse !== undefined ? config.pulse : true;
    this._pulseSpeed = config.pulseSpeed !== undefined ? config.pulseSpeed : 1.0;
    this.enabled = config.enabled !== undefined ? config.enabled : true;
    this._highlighted = config.highlighted !== undefined ? config.highlighted : false;

    // 如果初始高亮，则应用高亮
    if (this.enabled && this._highlighted) {
      this.applyHighlight();
    }
  }

  /**
   * 获取高亮类型
   */
  get highlightType(): HighlightType {
    return this._highlightType;
  }

  /**
   * 设置高亮类型
   */
  set highlightType(value: HighlightType) {
    // 如果类型相同，则返回
    if (this._highlightType === value) return;

    // 如果当前正在高亮，则先移除高亮
    const wasHighlighted = this._highlighted;
    if (wasHighlighted) {
      this.removeHighlight();
    }

    // 更新类型
    this._highlightType = value;

    // 如果之前在高亮，则重新应用高亮
    if (wasHighlighted) {
      this.applyHighlight();
    }
  }

  /**
   * 获取高亮颜色
   */
  get highlightColor(): Color {
    return this._highlightColor;
  }

  /**
   * 设置高亮颜色
   */
  set highlightColor(value: Color | string) {
    // 转换为Color对象
    const newColor = value instanceof Color ? value : new Color(value);

    // 如果颜色相同，则返回
    if (this._highlightColor.equals(newColor)) return;

    // 更新颜色
    this._highlightColor = newColor;

    // 如果当前正在高亮，则更新高亮材质
    if (this._highlighted) {
      this.updateHighlightMaterials();
    }
  }

  /**
   * 获取高亮强度
   */
  get highlightIntensity(): number {
    return this._highlightIntensity;
  }

  /**
   * 设置高亮强度
   */
  set highlightIntensity(value: number) {
    // 如果强度相同，则返回
    if (this._highlightIntensity === value) return;

    // 更新强度
    this._highlightIntensity = value;

    // 如果当前正在高亮，则更新高亮材质
    if (this._highlighted) {
      this.updateHighlightMaterials();
    }
  }

  /**
   * 设置是否启用
   */
  setEnabled(value: boolean) {
    // 如果状态相同，则返回
    if (this.enabled === value) return;

    // 如果禁用且当前正在高亮，则移除高亮
    if (!value && this._highlighted) {
      this.removeHighlight();
    }

    // 更新状态
    this.enabled = value;

    // 如果启用且应该高亮，则应用高亮
    if (value && this._highlighted) {
      this.applyHighlight();
    }
  }

  /**
   * 获取是否高亮
   */
  get highlighted(): boolean {
    return this._highlighted;
  }

  /**
   * 设置是否高亮
   */
  set highlighted(value: boolean) {
    // 如果状态相同，则返回
    if (this._highlighted === value) return;

    // 更新状态
    this._highlighted = value;

    // 如果启用，则应用或移除高亮
    if (this.enabled) {
      if (value) {
        this.applyHighlight();
      } else {
        this.removeHighlight();
      }
    }
  }

  /**
   * 应用高亮
   */
  private applyHighlight(): void {
    // 获取对象的3D表示
    const object3D = this.getObject3D();
    if (!object3D) return;

    // 根据高亮类型应用高亮
    switch (this._highlightType) {
      case HighlightType.OUTLINE:
        this.applyOutlineHighlight(object3D);
        break;

      case HighlightType.GLOW:
        this.applyGlowHighlight(object3D);
        break;

      case HighlightType.COLOR:
        this.applyColorHighlight(object3D);
        break;

      case HighlightType.CUSTOM:
        this.applyCustomHighlight(object3D);
        break;
    }
  }

  /**
   * 应用轮廓高亮
   * @param object3D 3D对象
   */
  private applyOutlineHighlight(object3D: Object3D): void {
    // 遍历所有网格
    object3D.traverse((child) => {
      if (child instanceof Mesh && child.material) {
        // 保存原始材质
        if (!this.originalMaterials.has(child)) {
          this.originalMaterials.set(child, child.material as MeshBasicMaterial);
        }

        // 创建轮廓材质
        // 这里使用简单的线框材质模拟轮廓效果
        // 在实际项目中，应该使用后处理效果实现更好的轮廓效果
        const outlineMaterial = new MeshBasicMaterial({
          color: this._highlightColor,
          wireframe: true,
          transparent: true,
          opacity: this._highlightIntensity,
          depthTest: true
        });

        // 应用轮廓材质
        child.material = outlineMaterial;

        // 保存高亮材质
        this.highlightMaterials.set(child, outlineMaterial);
      }
    });

    // 注意：这只是一个简单的实现
    // 在实际项目中，应该使用后处理效果实现更好的轮廓效果
    // 例如使用OutlinePass或自定义着色器
  }

  /**
   * 应用发光高亮
   * @param object3D 3D对象
   */
  private applyGlowHighlight(object3D: Object3D): void {
    // 遍历所有网格
    object3D.traverse((child) => {
      if (child instanceof Mesh && child.material) {
        // 保存原始材质
        if (!this.originalMaterials.has(child)) {
          this.originalMaterials.set(child, child.material as MeshBasicMaterial);
        }

        // 创建发光材质
        // 这里使用简单的自发光材质模拟发光效果
        // 在实际项目中，应该使用后处理效果实现更好的发光效果
        const glowMaterial = new MeshBasicMaterial({
          color: this._highlightColor,
          transparent: true,
          opacity: this._highlightIntensity,
          wireframe: false,
          depthTest: true
        });

        // 应用发光材质
        child.material = glowMaterial;

        // 保存高亮材质
        this.highlightMaterials.set(child, glowMaterial);
      }
    });

    // 注意：这只是一个简单的实现
    // 在实际项目中，应该使用后处理效果实现更好的发光效果
    // 例如使用UnrealBloomPass或自定义着色器
  }

  /**
   * 应用颜色高亮
   * @param object3D 3D对象
   */
  private applyColorHighlight(object3D: Object3D): void {
    // 遍历所有网格
    object3D.traverse((child) => {
      if (child instanceof Mesh && child.material) {
        // 保存原始材质
        if (!this.originalMaterials.has(child)) {
          this.originalMaterials.set(child, child.material as MeshBasicMaterial);
        }

        // 创建高亮材质
        const highlightMaterial = new MeshBasicMaterial({
          color: this._highlightColor,
          transparent: true,
          opacity: this._highlightIntensity,
          wireframe: false,
          depthTest: true
        });

        // 应用高亮材质
        child.material = highlightMaterial;

        // 保存高亮材质
        this.highlightMaterials.set(child, highlightMaterial);
      }
    });
  }

  /**
   * 应用自定义高亮
   * @param object3D 3D对象
   */
  private applyCustomHighlight(object3D: Object3D): void {
    // 这里提供一个简单的自定义高亮实现
    // 在实际项目中，可以根据需求实现更复杂的自定义高亮效果

    // 遍历所有网格
    object3D.traverse((child) => {
      if (child instanceof Mesh && child.material) {
        // 保存原始材质
        if (!this.originalMaterials.has(child)) {
          this.originalMaterials.set(child, child.material as MeshBasicMaterial);
        }

        // 创建自定义高亮材质
        // 这里使用简单的着色器材质
        const customMaterial = new ShaderMaterial({
          uniforms: {
            highlightColor: { value: new Color(this._highlightColor) },
            highlightIntensity: { value: this._highlightIntensity },
            time: { value: 0.0 }
          },
          vertexShader: `
            varying vec2 vUv;
            varying vec3 vNormal;

            void main() {
              vUv = uv;
              vNormal = normalize(normalMatrix * normal);
              gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
          `,
          fragmentShader: `
            uniform vec3 highlightColor;
            uniform float highlightIntensity;
            uniform float time;

            varying vec2 vUv;
            varying vec3 vNormal;

            void main() {
              // 计算边缘发光效果
              float rim = 1.0 - max(dot(vNormal, vec3(0.0, 0.0, 1.0)), 0.0);
              rim = pow(rim, 2.0) * highlightIntensity;

              // 添加脉冲效果
              float pulse = 0.5 + 0.5 * sin(time * 2.0);

              // 最终颜色
              vec3 finalColor = highlightColor * rim * pulse;

              gl_FragColor = vec4(finalColor, rim * pulse);
            }
          `,
          transparent: true,
          depthTest: true
        });

        // 应用自定义高亮材质
        child.material = customMaterial;

        // 保存高亮材质
        this.highlightMaterials.set(child, customMaterial);
      }
    });
  }

  /**
   * 移除高亮
   */
  private removeHighlight(): void {
    // 恢复原始材质
    for (const [mesh, material] of this.originalMaterials) {
      mesh.material = material;
    }

    // 清空高亮材质
    this.highlightMaterials.clear();
  }

  /**
   * 更新高亮材质
   */
  private updateHighlightMaterials(): void {
    // 更新所有高亮材质
    for (const material of this.highlightMaterials.values()) {
      if (material instanceof MeshBasicMaterial) {
        material.color = this._highlightColor;
        material.opacity = this._highlightIntensity * this.pulseValue;
      } else if (material instanceof ShaderMaterial) {
        // 更新着色器材质的颜色和强度
        if (material.uniforms.highlightColor) {
          material.uniforms.highlightColor.value = this._highlightColor;
        }
        if (material.uniforms.highlightIntensity) {
          material.uniforms.highlightIntensity.value = this._highlightIntensity * this.pulseValue;
        }
      }
    }
  }

  /**
   * 获取对象的3D表示
   * @returns 3D对象
   */
  private getObject3D(): Object3D | null {
    const entity = this.getEntity();
    if (!entity) return null;

    // 从实体中获取Transform组件
    const transform = entity.getComponent('Transform') as any as any as any as any as Transform;
    if (transform) {
      // 使用Transform组件的getObject3D方法获取3D对象
      return transform.getObject3D();
    }

    // 如果没有Transform组件，尝试从实体中获取mesh属性（用于示例代码兼容）
    if ((entity as any).mesh instanceof Object3D) {
      return (entity as any).mesh;
    }

    return null;
  }

  /**
   * 更新组件
   * @param deltaTime 时间增量（秒）
   */
  update(deltaTime: number): void {
    // 如果未启用或未高亮，则返回
    if (!this.enabled || !this._highlighted) return;

    // 更新脉冲效果
    if (this._pulse) {
      // 更新脉冲计时器
      this.pulseTime += deltaTime * this._pulseSpeed;

      // 计算脉冲值（0.5-1.0范围内的正弦波）
      this.pulseValue = 0.5 + 0.5 * Math.sin(this.pulseTime);

      // 更新高亮材质
      this.updateHighlightMaterials();
    }

    // 更新自定义着色器材质的时间参数
    for (const material of this.highlightMaterials.values()) {
      if (material instanceof ShaderMaterial && material.uniforms.time) {
        material.uniforms.time.value = this.pulseTime;
      }
    }
  }

  /**
   * 销毁组件
   */
  dispose(): void {
    // 移除高亮
    this.removeHighlight();

    // 清空材质映射
    this.originalMaterials.clear();
    this.highlightMaterials.clear();

    // 调用基类的销毁方法
    super.dispose();
  }
}
