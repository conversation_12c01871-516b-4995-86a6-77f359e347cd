/**
 * 变换组件
 * 处理实体的位置、旋转和缩放
 */
import * as THREE from 'three';
import { Component } from '../core/Component';

export class Transform extends Component {
  /** 组件类型 */
  public static readonly type: string = 'Transform';

  /** Three.js对象 */
  private object3D: THREE.Object3D;

  /** 本地位置 */
  private localPosition: THREE.Vector3 = new THREE.Vector3();

  /** 本地旋转 */
  private localRotation: THREE.Euler = new THREE.Euler();

  /** 本地旋转四元数 */
  private localQuaternion: THREE.Quaternion = new THREE.Quaternion();

  /** 本地缩放 */
  private localScale: THREE.Vector3 = new THREE.Vector3(1, 1, 1);

  /** 世界位置 */
  private worldPosition: THREE.Vector3 = new THREE.Vector3();

  /** 世界旋转 */
  private worldRotation: THREE.Euler = new THREE.Euler();

  /** 世界旋转四元数 */
  private worldQuaternion: THREE.Quaternion = new THREE.Quaternion();

  /** 世界缩放 */
  private worldScale: THREE.Vector3 = new THREE.Vector3(1, 1, 1);

  /** 父变换 */
  private parent: Transform | null = null;

  /** 子变换列表 */
  private children: Transform[] = [];

  /** 是否需要更新 */
  private dirty: boolean = true;

  /**
   * 创建变换组件
   */
  constructor(options?: {
    position?: { x: number; y: number; z: number } | THREE.Vector3;
    rotation?: { x: number; y: number; z: number } | THREE.Euler;
    scale?: { x: number; y: number; z: number } | THREE.Vector3;
  }) {
    super(Transform.type);
    this.object3D = new THREE.Object3D();

    // 应用初始化选项
    if (options) {
      if (options.position) {
        this.setPosition(options.position);
      }
      if (options.rotation) {
        this.setRotation(options.rotation);
      }
      if (options.scale) {
        this.setScale(options.scale);
      }
    }
  }

  /**
   * 当组件附加到实体时调用
   */
  protected onAttach(): void {
    // 设置对象名称
    if (this.entity) {
      this.object3D.name = this.entity.name;
    }
  }

  /**
   * 设置本地位置
   * @param x X坐标或位置向量
   * @param y Y坐标
   * @param z Z坐标
   */
  public setPosition(x: number | THREE.Vector3 | { x: number; y: number; z: number }, y?: number, z?: number): void {
    if (x instanceof THREE.Vector3) {
      this.localPosition.copy(x);
    } else if (typeof x === 'object' && 'x' in x && 'y' in x && 'z' in x) {
      this.localPosition.set(x.x, x.y, x.z);
    } else {
      this.localPosition.set(x as number, y || 0, z || 0);
    }

    this.object3D.position.copy(this.localPosition);
    this.dirty = true;

    // 发出位置变更事件
    this.emit('positionChanged', this.localPosition);
  }

  /**
   * 获取本地位置
   * @returns 本地位置
   */
  public getPosition(): THREE.Vector3 {
    return this.localPosition.clone();
  }

  /**
   * 位置属性 getter
   */
  public get position(): { x: number; y: number; z: number } {
    return {
      x: this.localPosition.x,
      y: this.localPosition.y,
      z: this.localPosition.z
    };
  }

  /**
   * 设置世界位置
   * @param x X坐标或位置向量
   * @param y Y坐标
   * @param z Z坐标
   */
  public setWorldPosition(x: number | THREE.Vector3, y?: number, z?: number): void {
    if (x instanceof THREE.Vector3) {
      this.worldPosition.copy(x);
    } else {
      this.worldPosition.set(x, y || 0, z || 0);
    }

    // 如果有父变换，转换为本地位置
    if (this.parent) {
      const parentWorldMatrix = this.parent.getWorldMatrix();
      const parentWorldMatrixInverse = new THREE.Matrix4().copy(parentWorldMatrix).invert();

      const localPosition = this.worldPosition.clone()
        .applyMatrix4(parentWorldMatrixInverse);

      this.setPosition(localPosition);
    } else {
      this.setPosition(this.worldPosition);
    }
  }

  /**
   * 获取世界位置
   * @returns 世界位置
   */
  public getWorldPosition(): THREE.Vector3 {
    this.updateWorldMatrix();
    return this.worldPosition.clone();
  }

  /**
   * 设置本地旋转
   * @param x X轴旋转角度（弧度）或欧拉角
   * @param y Y轴旋转角度（弧度）
   * @param z Z轴旋转角度（弧度）
   * @param order 旋转顺序
   */
  public setRotation(x: number | THREE.Euler | { x: number; y: number; z: number }, y?: number, z?: number, order?: THREE.EulerOrder): void {
    if (x instanceof THREE.Euler) {
      this.localRotation.copy(x);
    } else if (typeof x === 'object' && 'x' in x && 'y' in x && 'z' in x) {
      this.localRotation.set(x.x, x.y, x.z, order || this.localRotation.order);
    } else {
      this.localRotation.set(x as number, y || 0, z || 0, order || this.localRotation.order);
    }

    // 更新四元数
    this.localQuaternion.setFromEuler(this.localRotation);

    this.object3D.rotation.copy(this.localRotation);
    this.dirty = true;

    // 发出旋转变更事件
    this.emit('rotationChanged', this.localRotation);
  }

  /**
   * 设置本地旋转四元数
   * @param x X分量或四元数
   * @param y Y分量
   * @param z Z分量
   * @param w W分量
   */
  public setRotationQuaternion(x: number | THREE.Quaternion, y?: number, z?: number, w?: number): void {
    if (x instanceof THREE.Quaternion) {
      this.localQuaternion.copy(x);
    } else {
      this.localQuaternion.set(x, y || 0, z || 0, w || 1);
    }

    // 更新欧拉角
    this.localRotation.setFromQuaternion(this.localQuaternion);

    this.object3D.quaternion.copy(this.localQuaternion);
    this.dirty = true;

    // 发出旋转变更事件
    this.emit('rotationChanged', this.localRotation);
  }

  /**
   * 获取本地旋转四元数
   * @returns 本地旋转四元数
   */
  public getRotationQuaternion(): THREE.Quaternion {
    return this.localQuaternion.clone();
  }

  /**
   * 获取本地旋转
   * @returns 本地旋转
   */
  public getRotation(): THREE.Euler {
    return this.localRotation.clone();
  }

  /**
   * 旋转属性 getter
   */
  public get rotation(): { x: number; y: number; z: number } {
    return {
      x: this.localRotation.x,
      y: this.localRotation.y,
      z: this.localRotation.z
    };
  }

  /**
   * 设置世界旋转
   * @param x X轴旋转角度（弧度）或欧拉角
   * @param y Y轴旋转角度（弧度）
   * @param z Z轴旋转角度（弧度）
   * @param order 旋转顺序
   */
  public setWorldRotation(x: number | THREE.Euler, y?: number, z?: number, order?: THREE.EulerOrder): void {
    if (x instanceof THREE.Euler) {
      this.worldRotation.copy(x);
    } else {
      this.worldRotation.set(x, y || 0, z || 0, order || this.worldRotation.order);
    }

    // 更新世界四元数
    this.worldQuaternion.setFromEuler(this.worldRotation);

    // 如果有父变换，转换为本地旋转
    if (this.parent) {
      const parentWorldQuaternion = new THREE.Quaternion();
      const parentWorldMatrix = this.parent.getWorldMatrix();
      const parentRotationMatrix = new THREE.Matrix4().extractRotation(parentWorldMatrix);
      parentWorldQuaternion.setFromRotationMatrix(parentRotationMatrix);

      const worldQuaternion = new THREE.Quaternion().setFromEuler(this.worldRotation);
      const localQuaternion = worldQuaternion.clone().premultiply(parentWorldQuaternion.invert());

      const localRotation = new THREE.Euler().setFromQuaternion(localQuaternion);
      this.setRotation(localRotation);
    } else {
      this.setRotation(this.worldRotation);
    }
  }

  /**
   * 设置世界旋转四元数
   * @param x X分量或四元数
   * @param y Y分量
   * @param z Z分量
   * @param w W分量
   */
  public setWorldRotationQuaternion(x: number | THREE.Quaternion, y?: number, z?: number, w?: number): void {
    if (x instanceof THREE.Quaternion) {
      this.worldQuaternion.copy(x);
    } else {
      this.worldQuaternion.set(x, y || 0, z || 0, w || 1);
    }

    // 更新世界欧拉角
    this.worldRotation.setFromQuaternion(this.worldQuaternion);

    // 如果有父变换，转换为本地旋转
    if (this.parent) {
      const parentWorldQuaternion = new THREE.Quaternion();
      const parentWorldMatrix = this.parent.getWorldMatrix();
      const parentRotationMatrix = new THREE.Matrix4().extractRotation(parentWorldMatrix);
      parentWorldQuaternion.setFromRotationMatrix(parentRotationMatrix);

      const localQuaternion = this.worldQuaternion.clone().premultiply(parentWorldQuaternion.invert());
      this.setRotationQuaternion(localQuaternion);
    } else {
      this.setRotationQuaternion(this.worldQuaternion);
    }
  }

  /**
   * 获取世界旋转四元数
   * @returns 世界旋转四元数
   */
  public getWorldRotationQuaternion(): THREE.Quaternion {
    this.updateWorldMatrix();
    return this.worldQuaternion.clone();
  }

  /**
   * 获取世界旋转
   * @returns 世界旋转
   */
  public getWorldRotation(): THREE.Euler {
    this.updateWorldMatrix();
    return this.worldRotation.clone();
  }

  /**
   * 设置本地缩放
   * @param x X轴缩放或缩放向量
   * @param y Y轴缩放
   * @param z Z轴缩放
   */
  public setScale(x: number | THREE.Vector3 | { x: number; y: number; z: number }, y?: number, z?: number): void {
    if (x instanceof THREE.Vector3) {
      this.localScale.copy(x);
    } else if (typeof x === 'object' && 'x' in x && 'y' in x && 'z' in x) {
      this.localScale.set(x.x, x.y, x.z);
    } else {
      this.localScale.set(x as number, y !== undefined ? y : x as number, z !== undefined ? z : x as number);
    }

    this.object3D.scale.copy(this.localScale);
    this.dirty = true;

    // 发出缩放变更事件
    this.emit('scaleChanged', this.localScale);
  }

  /**
   * 获取本地缩放
   * @returns 本地缩放
   */
  public getScale(): THREE.Vector3 {
    return this.localScale.clone();
  }

  /**
   * 缩放属性 getter
   */
  public get scale(): { x: number; y: number; z: number } {
    return {
      x: this.localScale.x,
      y: this.localScale.y,
      z: this.localScale.z
    };
  }

  /**
   * 设置世界缩放
   * @param x X轴缩放或缩放向量
   * @param y Y轴缩放
   * @param z Z轴缩放
   */
  public setWorldScale(x: number | THREE.Vector3, y?: number, z?: number): void {
    if (x instanceof THREE.Vector3) {
      this.worldScale.copy(x);
    } else {
      this.worldScale.set(x, y || x, z || x);
    }

    // 如果有父变换，转换为本地缩放
    if (this.parent) {
      const parentWorldScale = this.parent.getWorldScale();

      const localScale = new THREE.Vector3(
        this.worldScale.x / parentWorldScale.x,
        this.worldScale.y / parentWorldScale.y,
        this.worldScale.z / parentWorldScale.z
      );

      this.setScale(localScale);
    } else {
      this.setScale(this.worldScale);
    }
  }

  /**
   * 获取世界缩放
   * @returns 世界缩放
   */
  public getWorldScale(): THREE.Vector3 {
    this.updateWorldMatrix();
    return this.worldScale.clone();
  }

  /**
   * 向前移动
   * @param distance 距离
   */
  public moveForward(distance: number): void {
    const direction = new THREE.Vector3(0, 0, -1);
    direction.applyEuler(this.localRotation);

    this.localPosition.addScaledVector(direction, distance);
    this.object3D.position.copy(this.localPosition);
    this.dirty = true;

    // 发出位置变更事件
    this.emit('positionChanged', this.localPosition);
  }

  /**
   * 向右移动
   * @param distance 距离
   */
  public moveRight(distance: number): void {
    const direction = new THREE.Vector3(1, 0, 0);
    direction.applyEuler(this.localRotation);

    this.localPosition.addScaledVector(direction, distance);
    this.object3D.position.copy(this.localPosition);
    this.dirty = true;

    // 发出位置变更事件
    this.emit('positionChanged', this.localPosition);
  }

  /**
   * 向上移动
   * @param distance 距离
   */
  public moveUp(distance: number): void {
    const direction = new THREE.Vector3(0, 1, 0);
    direction.applyEuler(this.localRotation);

    this.localPosition.addScaledVector(direction, distance);
    this.object3D.position.copy(this.localPosition);
    this.dirty = true;

    // 发出位置变更事件
    this.emit('positionChanged', this.localPosition);
  }

  /**
   * 绕X轴旋转
   * @param angle 角度（弧度）
   */
  public rotateX(angle: number): void {
    this.localRotation.x += angle;
    this.object3D.rotation.copy(this.localRotation);
    this.dirty = true;

    // 发出旋转变更事件
    this.emit('rotationChanged', this.localRotation);
  }

  /**
   * 绕Y轴旋转
   * @param angle 角度（弧度）
   */
  public rotateY(angle: number): void {
    this.localRotation.y += angle;
    this.object3D.rotation.copy(this.localRotation);
    this.dirty = true;

    // 发出旋转变更事件
    this.emit('rotationChanged', this.localRotation);
  }

  /**
   * 绕Z轴旋转
   * @param angle 角度（弧度）
   */
  public rotateZ(angle: number): void {
    this.localRotation.z += angle;
    this.object3D.rotation.copy(this.localRotation);
    this.dirty = true;

    // 发出旋转变更事件
    this.emit('rotationChanged', this.localRotation);
  }

  /**
   * 注视点
   * @param target 目标位置
   * @param up 上方向
   */
  public lookAt(
    target: THREE.Vector3 | { x: number; y: number; z: number } | number,
    y?: number,
    z?: number,
    up: THREE.Vector3 = new THREE.Vector3(0, 1, 0)
  ): void {
    let targetVector: THREE.Vector3;

    if (target instanceof THREE.Vector3) {
      targetVector = target;
    } else if (typeof target === 'object' && 'x' in target && 'y' in target && 'z' in target) {
      targetVector = new THREE.Vector3(target.x, target.y, target.z);
    } else {
      targetVector = new THREE.Vector3(target as number, y || 0, z || 0);
    }

    // 使用 Three.js 的 lookAt 方法
    this.object3D.lookAt(targetVector);

    // 更新本地旋转
    this.localRotation.copy(this.object3D.rotation);
    this.localQuaternion.copy(this.object3D.quaternion);

    this.dirty = true;
    this.emit('rotationChanged', this.localRotation);
  }

  /**
   * 设置父变换
   * @param parent 父变换
   */
  public setParent(parent: Transform | null): void {
    // 如果已经是父变换，则不做任何操作
    if (this.parent === parent) {
      return;
    }

    // 从旧父变换中移除
    if (this.parent) {
      const index = this.parent.children.indexOf(this);
      if (index !== -1) {
        this.parent.children.splice(index, 1);
      }

      this.parent.object3D.remove(this.object3D);
    }

    // 设置新父变换
    this.parent = parent;

    // 添加到新父变换
    if (parent) {
      parent.children.push(this);
      parent.object3D.add(this.object3D);
    }

    this.dirty = true;
  }

  /**
   * 获取父变换
   * @returns 父变换
   */
  public getParent(): Transform | null {
    return this.parent;
  }

  /**
   * 获取子变换列表
   * @returns 子变换数组
   */
  public getChildren(): Transform[] {
    return [...this.children];
  }

  /**
   * 获取本地矩阵
   * @returns 本地矩阵
   */
  public getLocalMatrix(): THREE.Matrix4 {
    return this.object3D.matrix.clone();
  }

  /**
   * 获取世界矩阵
   * @returns 世界矩阵
   */
  public getWorldMatrix(): THREE.Matrix4 {
    this.updateWorldMatrix();
    return this.object3D.matrixWorld.clone();
  }

  /**
   * 更新世界矩阵
   */
  private updateWorldMatrix(): void {
    if (!this.dirty) {
      return;
    }

    // 更新Three.js对象的世界矩阵
    this.object3D.updateMatrixWorld(true);

    // 提取世界位置
    this.worldPosition.setFromMatrixPosition(this.object3D.matrixWorld);

    // 提取世界旋转
    const quaternion = new THREE.Quaternion();
    const position = new THREE.Vector3();
    const scale = new THREE.Vector3();

    this.object3D.matrixWorld.decompose(position, quaternion, scale);
    this.worldQuaternion.copy(quaternion);
    this.worldRotation.setFromQuaternion(quaternion);

    // 提取世界缩放
    this.worldScale.copy(scale);

    this.dirty = false;
  }

  /**
   * 获取Three.js对象
   * @returns Three.js对象
   */
  public getObject3D(): THREE.Object3D {
    return this.object3D;
  }

  /**
   * 平移
   * @param x X轴偏移或偏移向量
   * @param y Y轴偏移
   * @param z Z轴偏移
   */
  public translate(x: number | THREE.Vector3 | { x: number; y: number; z: number }, y?: number, z?: number): void {
    let offset: THREE.Vector3;

    if (x instanceof THREE.Vector3) {
      offset = x;
    } else if (typeof x === 'object' && 'x' in x && 'y' in x && 'z' in x) {
      offset = new THREE.Vector3(x.x, x.y, x.z);
    } else {
      offset = new THREE.Vector3(x as number, y || 0, z || 0);
    }

    this.localPosition.add(offset);
    this.object3D.position.copy(this.localPosition);
    this.dirty = true;
    this.emit('positionChanged', this.localPosition);
  }

  /**
   * 旋转
   * @param x X轴旋转角度（弧度）或旋转向量
   * @param y Y轴旋转角度（弧度）
   * @param z Z轴旋转角度（弧度）
   */
  public rotate(x: number | { x: number; y: number; z: number }, y?: number, z?: number): void {
    if (typeof x === 'object' && 'x' in x && 'y' in x && 'z' in x) {
      this.localRotation.x += x.x;
      this.localRotation.y += x.y;
      this.localRotation.z += x.z;
    } else {
      this.localRotation.x += x as number;
      this.localRotation.y += y || 0;
      this.localRotation.z += z || 0;
    }

    this.localQuaternion.setFromEuler(this.localRotation);
    this.object3D.rotation.copy(this.localRotation);
    this.dirty = true;
    this.emit('rotationChanged', this.localRotation);
  }

  /**
   * 获取前方向
   * @returns 前方向向量
   */
  public getForward(): THREE.Vector3 {
    const forward = new THREE.Vector3(0, 0, -1);
    forward.applyQuaternion(this.localQuaternion);
    return forward;
  }

  /**
   * 获取右方向
   * @returns 右方向向量
   */
  public getRight(): THREE.Vector3 {
    const right = new THREE.Vector3(1, 0, 0);
    right.applyQuaternion(this.localQuaternion);
    return right;
  }

  /**
   * 获取上方向
   * @returns 上方向向量
   */
  public getUp(): THREE.Vector3 {
    const up = new THREE.Vector3(0, 1, 0);
    up.applyQuaternion(this.localQuaternion);
    return up;
  }

  /**
   * 缩放（乘法操作）
   * @param x X轴缩放因子或缩放向量
   * @param y Y轴缩放因子
   * @param z Z轴缩放因子
   */
  public scale3d(x: number | { x: number; y: number; z: number }, y?: number, z?: number): void {
    if (typeof x === 'object' && 'x' in x && 'y' in x && 'z' in x) {
      this.localScale.x *= x.x;
      this.localScale.y *= x.y;
      this.localScale.z *= x.z;
    } else {
      this.localScale.x *= x as number;
      this.localScale.y *= y !== undefined ? y : x as number;
      this.localScale.z *= z !== undefined ? z : x as number;
    }

    this.object3D.scale.copy(this.localScale);
    this.dirty = true;
    this.emit('scaleChanged', this.localScale);
  }

  /**
   * 销毁组件
   */
  public dispose(): void {
    // 从父变换中移除
    this.setParent(null);

    // 移除所有子变换
    while (this.children.length > 0) {
      this.children[0].setParent(null);
    }

    super.dispose();
  }
}
