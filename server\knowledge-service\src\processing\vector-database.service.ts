import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface VectorRecord {
  id: string;
  vector: number[];
  metadata: any;
}

export interface QueryOptions {
  vector: number[];
  namespace?: string;
  topK: number;
  filter?: any;
  includeMetadata: boolean;
}

export interface QueryResult {
  matches: VectorMatch[];
  namespace?: string;
}

export interface VectorMatch {
  id: string;
  score: number;
  metadata?: any;
}

export interface VectorDatabaseClient {
  upsert(namespace: string, records: VectorRecord[]): Promise<void>;
  query(options: QueryOptions): Promise<QueryResult>;
  delete(namespace: string, ids: string[]): Promise<void>;
  deleteNamespace(namespace: string): Promise<void>;
  getStats(namespace: string): Promise<any>;
}

@Injectable()
export class VectorDatabaseService {
  private client: VectorDatabaseClient;

  constructor(private readonly configService: ConfigService) {
    this.initializeClient();
  }

  /**
   * 初始化向量数据库客户端
   */
  private initializeClient(): void {
    const dbType = this.configService.get('VECTOR_DB_TYPE', 'chroma');

    switch (dbType) {
      case 'pinecone':
        this.client = new PineconeClient(this.configService);
        break;
      case 'chroma':
        this.client = new ChromaClient(this.configService);
        break;
      case 'milvus':
        this.client = new MilvusClient(this.configService);
        break;
      case 'weaviate':
        this.client = new WeaviateClient(this.configService);
        break;
      default:
        // 默认使用 Chroma 客户端
        this.client = new ChromaClient(this.configService);
    }
  }

  /**
   * 存储向量记录
   */
  async storeVectors(
    namespace: string,
    records: VectorRecord[],
  ): Promise<void> {
    if (!records || records.length === 0) {
      throw new Error('向量记录列表为空');
    }

    // 验证向量维度
    this.validateVectorDimensions(records);

    // 批量存储
    const batchSize = 100; // 每批处理100个向量
    for (let i = 0; i < records.length; i += batchSize) {
      const batch = records.slice(i, i + batchSize);
      await this.client.upsert(namespace, batch);
    }
  }

  /**
   * 查询相似向量
   */
  async queryVectors(options: QueryOptions): Promise<QueryResult> {
    if (!options.vector || options.vector.length === 0) {
      throw new Error('查询向量为空');
    }

    return await this.client.query(options);
  }

  /**
   * 删除向量记录
   */
  async deleteVectors(namespace: string, ids: string[]): Promise<void> {
    if (!ids || ids.length === 0) {
      return;
    }

    await this.client.delete(namespace, ids);
  }

  /**
   * 删除整个命名空间
   */
  async deleteNamespace(namespace: string): Promise<void> {
    await this.client.deleteNamespace(namespace);
  }

  /**
   * 获取命名空间统计信息
   */
  async getNamespaceStats(namespace: string): Promise<any> {
    return await this.client.getStats(namespace);
  }

  /**
   * 验证向量维度
   */
  private validateVectorDimensions(records: VectorRecord[]): void {
    const expectedDimension = this.configService.get('production.vectorDatabase.dimension');
    
    for (const record of records) {
      if (record.vector.length !== expectedDimension) {
        throw new Error(
          `向量维度不匹配: 期望 ${expectedDimension}, 实际 ${record.vector.length}`,
        );
      }
    }
  }
}

/**
 * Pinecone客户端
 */
class PineconeClient implements VectorDatabaseClient {
  private apiKey: string;
  private endpoint: string;
  private indexName: string;

  constructor(configService: ConfigService) {
    this.apiKey = configService.get('VECTOR_DB_API_KEY', '');
    this.endpoint = configService.get('VECTOR_DB_ENDPOINT', 'http://localhost:8000');
    this.indexName = configService.get('VECTOR_DB_INDEX', 'knowledge-base');
  }

  async upsert(namespace: string, records: VectorRecord[]): Promise<void> {
    const response = await fetch(`${this.endpoint}/vectors/upsert`, {
      method: 'POST',
      headers: {
        'Api-Key': this.apiKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        namespace,
        vectors: records.map(record => ({
          id: record.id,
          values: record.vector,
          metadata: record.metadata,
        })),
      }),
    });

    if (!response.ok) {
      throw new Error(`Pinecone upsert错误: ${response.statusText}`);
    }
  }

  async query(options: QueryOptions): Promise<QueryResult> {
    const response = await fetch(`${this.endpoint}/query`, {
      method: 'POST',
      headers: {
        'Api-Key': this.apiKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        namespace: options.namespace,
        vector: options.vector,
        topK: options.topK,
        filter: options.filter,
        includeMetadata: options.includeMetadata,
      }),
    });

    if (!response.ok) {
      throw new Error(`Pinecone query错误: ${response.statusText}`);
    }

    const data = await response.json();
    return {
      matches: data.matches.map((match: any) => ({
        id: match.id,
        score: match.score,
        metadata: match.metadata,
      })),
      namespace: options.namespace,
    };
  }

  async delete(namespace: string, ids: string[]): Promise<void> {
    const response = await fetch(`${this.endpoint}/vectors/delete`, {
      method: 'POST',
      headers: {
        'Api-Key': this.apiKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        namespace,
        ids,
      }),
    });

    if (!response.ok) {
      throw new Error(`Pinecone delete错误: ${response.statusText}`);
    }
  }

  async deleteNamespace(namespace: string): Promise<void> {
    const response = await fetch(`${this.endpoint}/vectors/delete`, {
      method: 'POST',
      headers: {
        'Api-Key': this.apiKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        namespace,
        deleteAll: true,
      }),
    });

    if (!response.ok) {
      throw new Error(`Pinecone deleteNamespace错误: ${response.statusText}`);
    }
  }

  async getStats(namespace: string): Promise<any> {
    const response = await fetch(`${this.endpoint}/describe_index_stats`, {
      method: 'POST',
      headers: {
        'Api-Key': this.apiKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        filter: namespace ? { namespace } : undefined,
      }),
    });

    if (!response.ok) {
      throw new Error(`Pinecone getStats错误: ${response.statusText}`);
    }

    return await response.json();
  }
}

/**
 * Chroma客户端
 */
class ChromaClient implements VectorDatabaseClient {
  private endpoint: string;
  private collectionName: string;

  constructor(configService: ConfigService) {
    this.endpoint = configService.get('VECTOR_DB_ENDPOINT', 'http://localhost:8000');
    this.collectionName = configService.get('VECTOR_DB_COLLECTION', 'knowledge_base');
  }

  async upsert(namespace: string, records: VectorRecord[]): Promise<void> {
    const collectionName = `${this.collectionName}_${namespace}`;

    const response = await fetch(`${this.endpoint}/api/v1/collections/${collectionName}/upsert`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ids: records.map(r => r.id),
        embeddings: records.map(r => r.vector),
        metadatas: records.map(r => r.metadata),
      }),
    });

    if (!response.ok) {
      throw new Error(`Chroma upsert错误: ${response.statusText}`);
    }
  }

  async query(options: QueryOptions): Promise<QueryResult> {
    const collectionName = `${this.collectionName}_${options.namespace}`;

    const response = await fetch(`${this.endpoint}/api/v1/collections/${collectionName}/query`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query_embeddings: [options.vector],
        n_results: options.topK,
        include: options.includeMetadata ? ['metadatas', 'distances'] : ['distances'],
        where: options.filter,
      }),
    });

    if (!response.ok) {
      throw new Error(`Chroma query错误: ${response.statusText}`);
    }

    const data = await response.json();
    const matches = data.ids[0].map((id: string, index: number) => ({
      id,
      score: 1 - data.distances[0][index], // 转换距离为相似度
      metadata: options.includeMetadata ? data.metadatas[0][index] : undefined,
    }));

    return { matches, namespace: options.namespace };
  }

  async delete(namespace: string, ids: string[]): Promise<void> {
    const collectionName = `${this.collectionName}_${namespace}`;

    const response = await fetch(`${this.endpoint}/api/v1/collections/${collectionName}/delete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ ids }),
    });

    if (!response.ok) {
      throw new Error(`Chroma delete错误: ${response.statusText}`);
    }
  }

  async deleteNamespace(namespace: string): Promise<void> {
    const collectionName = `${this.collectionName}_${namespace}`;

    const response = await fetch(`${this.endpoint}/api/v1/collections/${collectionName}`, {
      method: 'DELETE',
    });

    if (!response.ok && response.status !== 404) {
      throw new Error(`Chroma deleteNamespace错误: ${response.statusText}`);
    }
  }

  async getStats(namespace: string): Promise<any> {
    const collectionName = `${this.collectionName}_${namespace}`;

    const response = await fetch(`${this.endpoint}/api/v1/collections/${collectionName}`);

    if (!response.ok) {
      throw new Error(`Chroma getStats错误: ${response.statusText}`);
    }

    return await response.json();
  }
}

/**
 * Milvus客户端（简化实现）
 */
class MilvusClient implements VectorDatabaseClient {
  private endpoint: string;
  private collectionName: string;

  constructor(configService: ConfigService) {
    this.endpoint = configService.get('production.vectorDatabase.endpoint');
    this.collectionName = configService.get('production.vectorDatabase.indexName');
  }

  async upsert(namespace: string, records: VectorRecord[]): Promise<void> {
    // Milvus的具体实现需要使用官方SDK
    // 这里提供简化的HTTP API示例
    throw new Error('Milvus客户端需要使用官方SDK实现');
  }

  async query(options: QueryOptions): Promise<QueryResult> {
    throw new Error('Milvus客户端需要使用官方SDK实现');
  }

  async delete(namespace: string, ids: string[]): Promise<void> {
    throw new Error('Milvus客户端需要使用官方SDK实现');
  }

  async deleteNamespace(namespace: string): Promise<void> {
    throw new Error('Milvus客户端需要使用官方SDK实现');
  }

  async getStats(namespace: string): Promise<any> {
    throw new Error('Milvus客户端需要使用官方SDK实现');
  }
}

/**
 * Weaviate客户端（简化实现）
 */
class WeaviateClient implements VectorDatabaseClient {
  private endpoint: string;
  private className: string;

  constructor(configService: ConfigService) {
    this.endpoint = configService.get('production.vectorDatabase.endpoint');
    this.className = configService.get('production.vectorDatabase.indexName');
  }

  async upsert(namespace: string, records: VectorRecord[]): Promise<void> {
    // Weaviate的具体实现需要使用官方SDK
    // 这里提供简化的HTTP API示例
    throw new Error('Weaviate客户端需要使用官方SDK实现');
  }

  async query(options: QueryOptions): Promise<QueryResult> {
    throw new Error('Weaviate客户端需要使用官方SDK实现');
  }

  async delete(namespace: string, ids: string[]): Promise<void> {
    throw new Error('Weaviate客户端需要使用官方SDK实现');
  }

  async deleteNamespace(namespace: string): Promise<void> {
    throw new Error('Weaviate客户端需要使用官方SDK实现');
  }

  async getStats(namespace: string): Promise<any> {
    throw new Error('Weaviate客户端需要使用官方SDK实现');
  }
}
