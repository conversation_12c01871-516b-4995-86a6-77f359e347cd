import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  Index,
} from 'typeorm';
import { GenerationTask } from '../../tasks/entities/generation-task.entity';
import { SceneObject } from './scene-object.entity';
import { User } from '../../auth/entities/user.entity';

export enum SceneType {
  INDOOR = 'indoor',
  OUTDOOR = 'outdoor',
  MIXED = 'mixed',
  ABSTRACT = 'abstract',
}

export enum SceneStyle {
  REALISTIC = 'realistic',
  CARTOON = 'cartoon',
  MINIMALIST = 'minimalist',
  FUTURISTIC = 'futuristic',
  VINTAGE = 'vintage',
  FANTASY = 'fantasy',
}

export enum SceneStatus {
  DRAFT = 'draft',
  GENERATING = 'generating',
  READY = 'ready',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
}

@Entity('scenes')
@Index(['status', 'createdAt'])
@Index(['userId', 'createdAt'])
@Index(['type', 'style'])
export class Scene {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  @Index()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: SceneType,
    default: SceneType.INDOOR,
  })
  @Index()
  type: SceneType;

  @Column({
    type: 'enum',
    enum: SceneStyle,
    default: SceneStyle.REALISTIC,
  })
  @Index()
  style: SceneStyle;

  @Column({
    type: 'enum',
    enum: SceneStatus,
    default: SceneStatus.DRAFT,
  })
  @Index()
  status: SceneStatus;

  // 场景配置
  @Column({ type: 'json' })
  sceneConfig: Record<string, any>; // 场景配置数据

  @Column({ type: 'json', nullable: true })
  cameraSettings: Record<string, any>; // 相机设置

  @Column({ type: 'json', nullable: true })
  lightingSettings: Record<string, any>; // 光照设置

  @Column({ type: 'json', nullable: true })
  environmentSettings: Record<string, any>; // 环境设置

  @Column({ type: 'json', nullable: true })
  physicsSettings: Record<string, any>; // 物理设置

  @Column({ type: 'json', nullable: true })
  renderSettings: Record<string, any>; // 渲染设置

  // 场景边界
  @Column({ type: 'json', nullable: true })
  boundingBox: {
    min: { x: number; y: number; z: number };
    max: { x: number; y: number; z: number };
  };

  @Column({ name: 'scene_size', type: 'float', nullable: true })
  sceneSize: number; // 场景大小（立方米）

  // 文件信息
  @Column({ name: 'file_url', nullable: true })
  fileUrl: string; // 场景文件URL

  @Column({ name: 'file_format', default: 'gltf' })
  fileFormat: string; // 文件格式

  @Column({ name: 'file_size_mb', type: 'float', nullable: true })
  fileSizeMb: number; // 文件大小

  @Column({ name: 'thumbnail_url', nullable: true })
  thumbnailUrl: string; // 缩略图URL

  @Column({ name: 'preview_images', type: 'json', nullable: true })
  previewImages: string[]; // 预览图片URL数组

  // 质量和性能
  @Column({ name: 'quality_score', type: 'float', nullable: true })
  qualityScore: number; // 质量分数 (0-1)

  @Column({ name: 'complexity_score', type: 'float', nullable: true })
  complexityScore: number; // 复杂度分数 (0-1)

  @Column({ name: 'polygon_count', type: 'integer', nullable: true })
  polygonCount: number; // 多边形数量

  @Column({ name: 'texture_count', type: 'integer', nullable: true })
  textureCount: number; // 纹理数量

  @Column({ name: 'material_count', type: 'integer', nullable: true })
  materialCount: number; // 材质数量

  @Column({ name: 'object_count', type: 'integer', nullable: true })
  objectCount: number; // 对象数量

  // 性能指标
  @Column({ name: 'estimated_fps', type: 'float', nullable: true })
  estimatedFps: number; // 预估帧率

  @Column({ name: 'memory_usage_mb', type: 'float', nullable: true })
  memoryUsageMb: number; // 内存使用量

  @Column({ name: 'gpu_memory_mb', type: 'float', nullable: true })
  gpuMemoryMb: number; // GPU内存使用量

  // 标签和分类
  @Column({ type: 'text', array: true, default: [] })
  tags: string[];

  @Column({ type: 'json', nullable: true })
  categories: string[]; // 分类标签

  // 版本控制
  @Column({ default: 1 })
  version: number;

  @Column({ name: 'parent_scene_id', nullable: true })
  parentSceneId: string; // 父场景ID

  @Column({ name: 'is_template', default: false })
  isTemplate: boolean; // 是否为模板

  // 可见性和分享
  @Column({ name: 'is_public', default: false })
  isPublic: boolean;

  @Column({ name: 'is_featured', default: false })
  isFeatured: boolean;

  // 统计信息
  @Column({ name: 'view_count', default: 0 })
  viewCount: number;

  @Column({ name: 'download_count', default: 0 })
  downloadCount: number;

  @Column({ name: 'like_count', default: 0 })
  likeCount: number;

  @Column({ name: 'share_count', default: 0 })
  shareCount: number;

  // 关联关系
  @ManyToOne(() => GenerationTask, task => task.scenes, { nullable: true })
  generationTask: GenerationTask;

  @Column({ name: 'generation_task_id', nullable: true })
  @Index()
  generationTaskId: string;

  @ManyToOne(() => User, user => user.scenes)
  user: User;

  @Column({ name: 'user_id' })
  @Index()
  userId: string;

  @OneToMany(() => SceneObject, object => object.scene, { cascade: true })
  objects: SceneObject[];

  // 时间戳
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'published_at', nullable: true })
  publishedAt: Date;

  @Column({ name: 'last_viewed_at', nullable: true })
  lastViewedAt: Date;

  // 计算属性
  get isPublished(): boolean {
    return this.status === SceneStatus.PUBLISHED && this.publishedAt !== null;
  }

  get isReady(): boolean {
    return this.status === SceneStatus.READY;
  }

  get hasFile(): boolean {
    return !!this.fileUrl;
  }

  get displayName(): string {
    return this.name || `场景_${this.id.slice(0, 8)}`;
  }

  get typeDisplayName(): string {
    const typeNames = {
      [SceneType.INDOOR]: '室内',
      [SceneType.OUTDOOR]: '室外',
      [SceneType.MIXED]: '混合',
      [SceneType.ABSTRACT]: '抽象',
    };
    return typeNames[this.type] || this.type;
  }

  get styleDisplayName(): string {
    const styleNames = {
      [SceneStyle.REALISTIC]: '写实',
      [SceneStyle.CARTOON]: '卡通',
      [SceneStyle.MINIMALIST]: '简约',
      [SceneStyle.FUTURISTIC]: '未来',
      [SceneStyle.VINTAGE]: '复古',
      [SceneStyle.FANTASY]: '奇幻',
    };
    return styleNames[this.style] || this.style;
  }

  get statusDisplayName(): string {
    const statusNames = {
      [SceneStatus.DRAFT]: '草稿',
      [SceneStatus.GENERATING]: '生成中',
      [SceneStatus.READY]: '就绪',
      [SceneStatus.PUBLISHED]: '已发布',
      [SceneStatus.ARCHIVED]: '已归档',
    };
    return statusNames[this.status] || this.status;
  }

  get fileSizeFormatted(): string {
    if (!this.fileSizeMb) return '0 MB';
    
    if (this.fileSizeMb < 1) {
      return `${(this.fileSizeMb * 1024).toFixed(1)} KB`;
    } else if (this.fileSizeMb < 1024) {
      return `${this.fileSizeMb.toFixed(1)} MB`;
    } else {
      return `${(this.fileSizeMb / 1024).toFixed(1)} GB`;
    }
  }

  get complexityLevel(): string {
    if (!this.complexityScore) return '未知';
    
    if (this.complexityScore < 0.3) return '简单';
    if (this.complexityScore < 0.6) return '中等';
    if (this.complexityScore < 0.8) return '复杂';
    return '非常复杂';
  }

  get qualityLevel(): string {
    if (!this.qualityScore) return '未知';
    
    if (this.qualityScore < 0.3) return '低';
    if (this.qualityScore < 0.6) return '中';
    if (this.qualityScore < 0.8) return '高';
    return '极高';
  }

  // 增加查看次数
  incrementViewCount(): void {
    this.viewCount += 1;
    this.lastViewedAt = new Date();
    this.updatedAt = new Date();
  }

  // 增加下载次数
  incrementDownloadCount(): void {
    this.downloadCount += 1;
    this.updatedAt = new Date();
  }

  // 增加点赞次数
  incrementLikeCount(): void {
    this.likeCount += 1;
    this.updatedAt = new Date();
  }

  // 增加分享次数
  incrementShareCount(): void {
    this.shareCount += 1;
    this.updatedAt = new Date();
  }

  // 发布场景
  publish(): void {
    this.status = SceneStatus.PUBLISHED;
    this.publishedAt = new Date();
    this.updatedAt = new Date();
  }

  // 归档场景
  archive(): void {
    this.status = SceneStatus.ARCHIVED;
    this.updatedAt = new Date();
  }

  // 更新文件信息
  updateFileInfo(fileUrl: string, fileSizeMb: number, fileFormat?: string): void {
    this.fileUrl = fileUrl;
    this.fileSizeMb = fileSizeMb;
    if (fileFormat) {
      this.fileFormat = fileFormat;
    }
    this.updatedAt = new Date();
  }

  // 更新质量分数
  updateQualityScore(qualityScore: number, complexityScore?: number): void {
    this.qualityScore = Math.max(0, Math.min(1, qualityScore));
    if (complexityScore !== undefined) {
      this.complexityScore = Math.max(0, Math.min(1, complexityScore));
    }
    this.updatedAt = new Date();
  }
}
