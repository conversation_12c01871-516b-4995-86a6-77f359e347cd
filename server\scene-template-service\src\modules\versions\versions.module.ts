import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { VersionsService } from './versions.service';
import { TemplateVersion } from './entities/template-version.entity';
import { SceneTemplate } from '../templates/entities/scene-template.entity';
import { LoggerService } from '../../common/services/logger.service';

@Module({
  imports: [TypeOrmModule.forFeature([TemplateVersion, SceneTemplate])],
  providers: [
    VersionsService,
    LoggerService,
  ],
  exports: [VersionsService, TypeOrmModule],
})
export class VersionsModule {}
