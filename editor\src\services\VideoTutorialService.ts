/**
 * 视频教程服务
 * 负责管理编辑器的视频教程
 */
import { EventEmitter } from '../utils/EventEmitter';
//import  i18n  from '../i18n';
import { videoTutorials as defaultTutorials } from '../data/videoTutorials';

export interface VideoTutorial {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  duration: number; // 视频时长（分钟）
  thumbnailUrl: string;
  videoUrl: string;
  subtitlesUrl?: string;
  tags?: string[];
  relatedTutorials?: string[];
  prerequisites?: string[];
  chapters?: VideoTutorialChapter[];
}

export interface VideoTutorialChapter {
  id: string;
  title: string;
  startTime: number; // 开始时间（秒）
  endTime: number; // 结束时间（秒）
}

export class VideoTutorialService {
  private static instance: VideoTutorialService;
  private tutorials: VideoTutorial[] = [];
  private events = new EventEmitter();
  private watchedTutorials: Set<string> = new Set();
  private watchProgress: Map<string, number> = new Map(); // 教程ID -> 观看进度（秒）

  private constructor() {
    this.loadTutorials();
    this.loadUserProgress();
  }

  /**
   * 获取视频教程服务实例
   */
  public static getInstance(): VideoTutorialService {
    if (!VideoTutorialService.instance) {
      VideoTutorialService.instance = new VideoTutorialService();
    }
    return VideoTutorialService.instance;
  }

  /**
   * 加载视频教程数据
   */
  private loadTutorials(): void {
    // 使用默认视频教程数据
    this.tutorials = [...defaultTutorials];

    // 可以在这里添加从API或其他来源加载的视频教程
    // 例如：
    // const apiTutorials = await this.loadTutorialsFromAPI();
    // this.tutorials = [...this.tutorials, ...apiTutorials];
  }

  /**
   * 加载用户进度
   */
  private loadUserProgress(): void {
    try {
      // 从本地存储加载已观看的视频教程
      const watchedTutorialsJson = localStorage.getItem('watchedVideoTutorials');
      if (watchedTutorialsJson) {
        const watchedTutorialsArray = JSON.parse(watchedTutorialsJson);
        this.watchedTutorials = new Set(watchedTutorialsArray);
      }

      // 从本地存储加载视频教程进度
      const watchProgressJson = localStorage.getItem('videoTutorialProgress');
      if (watchProgressJson) {
        const watchProgressObj = JSON.parse(watchProgressJson);
        this.watchProgress = new Map(Object.entries(watchProgressObj));
      }
    } catch (error) {
      console.error('Failed to load video tutorial progress:', error);
    }
  }

  /**
   * 保存用户进度
   */
  private saveUserProgress(): void {
    try {
      // 保存已观看的视频教程到本地存储
      localStorage.setItem('watchedVideoTutorials', JSON.stringify([...this.watchedTutorials]));

      // 保存视频教程进度到本地存储
      const watchProgressObj = Object.fromEntries([...this.watchProgress]);
      localStorage.setItem('videoTutorialProgress', JSON.stringify(watchProgressObj));
    } catch (error) {
      console.error('Failed to save video tutorial progress:', error);
    }
  }

  /**
   * 获取所有视频教程
   */
  public getTutorials(): VideoTutorial[] {
    return this.tutorials;
  }

  /**
   * 根据ID获取视频教程
   */
  public getTutorialById(id: string): VideoTutorial | undefined {
    return this.tutorials.find(tutorial => tutorial.id === id);
  }

  /**
   * 根据类别获取视频教程
   */
  public getTutorialsByCategory(category: string): VideoTutorial[] {
    return this.tutorials.filter(tutorial => tutorial.category === category);
  }

  /**
   * 获取推荐视频教程
   */
  public getRecommendedTutorials(): VideoTutorial[] {
    // 根据用户观看情况推荐视频教程
    const watchedIds = [...this.watchedTutorials];
    return this.tutorials.filter(tutorial => {
      // 如果已观看，不推荐
      if (watchedIds.includes(tutorial.id)) {
        return false;
      }

      // 如果有前置条件，检查是否满足
      if (tutorial.prerequisites && tutorial.prerequisites.length > 0) {
        return tutorial.prerequisites.every(prereq => watchedIds.includes(prereq));
      }

      return true;
    });
  }

  /**
   * 播放视频教程
   */
  public playTutorial(tutorialId: string): void {
    const tutorial = this.getTutorialById(tutorialId);
    if (!tutorial) {
      return;
    }

    this.events.emit('tutorialPlay', tutorial);
  }

  /**
   * 打开视频教程
   */
  public openTutorial(tutorialId: string): void {
    const tutorial = this.getTutorialById(tutorialId);
    if (!tutorial) {
      return;
    }

    this.events.emit('tutorialOpen', tutorial);
  }

  /**
   * 暂停视频教程
   */
  public pauseTutorial(): void {
    this.events.emit('tutorialPause');
  }

  /**
   * 继续播放视频教程
   */
  public resumeTutorial(): void {
    this.events.emit('tutorialResume');
  }

  /**
   * 停止视频教程
   */
  public stopTutorial(): void {
    this.events.emit('tutorialStop');
  }

  /**
   * 跳转到视频教程的特定时间
   */
  public seekTutorial(time: number): void {
    this.events.emit('tutorialSeek', time);
  }

  /**
   * 跳转到视频教程的特定章节
   */
  public seekToChapter(tutorialId: string, chapterId: string): void {
    const tutorial = this.getTutorialById(tutorialId);
    if (!tutorial || !tutorial.chapters) {
      return;
    }

    const chapter = tutorial.chapters.find(chapter => chapter.id === chapterId);
    if (chapter) {
      this.seekTutorial(chapter.startTime);
    }
  }

  /**
   * 更新视频教程进度
   */
  public updateProgress(tutorialId: string, progress: number): void {
    this.watchProgress.set(tutorialId, progress);
    this.saveUserProgress();
    this.events.emit('progressUpdated', tutorialId, progress);
  }

  /**
   * 标记视频教程为已观看
   */
  public markAsWatched(tutorialId: string): void {
    this.watchedTutorials.add(tutorialId);
    this.saveUserProgress();
    this.events.emit('tutorialWatched', tutorialId);
  }

  /**
   * 检查视频教程是否已观看
   */
  public isTutorialWatched(tutorialId: string): boolean {
    return this.watchedTutorials.has(tutorialId);
  }

  /**
   * 获取视频教程进度
   */
  public getTutorialProgress(tutorialId: string): number {
    return this.watchProgress.get(tutorialId) || 0;
  }

  /**
   * 添加事件监听器
   */
  public on(event: string, listener: (...args: any[]) => void): void {
    this.events.on(event, listener);
  }

  /**
   * 移除事件监听器
   */
  public off(event: string, listener: (...args: any[]) => void): void {
    this.events.off(event, listener);
  }
}

export default VideoTutorialService.getInstance();
