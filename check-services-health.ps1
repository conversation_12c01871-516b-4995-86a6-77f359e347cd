# 数字人系统服务健康检查脚本

Write-Host "开始检查服务健康状态..." -ForegroundColor Green

# 检查服务状态
Write-Host "`n=== Docker Compose 服务状态 ===" -ForegroundColor Yellow
docker-compose -f docker-compose.windows.yml ps

# 定义服务列表和对应的健康检查端点
$services = @{
    "mysql" = @{
        "port" = 3306
        "healthCheck" = "mysql"
        "description" = "MySQL数据库"
    }
    "redis" = @{
        "port" = 6379
        "healthCheck" = "redis"
        "description" = "Redis缓存"
    }
    "minio" = @{
        "port" = 9000
        "healthCheck" = "http://localhost:9000/minio/health/live"
        "description" = "MinIO对象存储"
    }
    "elasticsearch" = @{
        "port" = 9200
        "healthCheck" = "http://localhost:9200/_cluster/health"
        "description" = "Elasticsearch搜索引擎"
    }
    "chroma" = @{
        "port" = 8000
        "healthCheck" = "http://localhost:8000/api/v1/heartbeat"
        "description" = "Chroma向量数据库"
    }
    "service-registry" = @{
        "port" = 4010
        "healthCheck" = "http://localhost:4010/api/health"
        "description" = "服务注册中心"
    }
    "api-gateway" = @{
        "port" = 3000
        "healthCheck" = "http://localhost:3000/api/health"
        "description" = "API网关"
    }
    "user-service" = @{
        "port" = 4001
        "healthCheck" = "http://localhost:4001/health"
        "description" = "用户服务"
    }
    "project-service" = @{
        "port" = 4002
        "healthCheck" = "http://localhost:4002/health"
        "description" = "项目服务"
    }
    "asset-service" = @{
        "port" = 4003
        "healthCheck" = "http://localhost:4003/health"
        "description" = "资产服务"
    }
    "render-service" = @{
        "port" = 4004
        "healthCheck" = "http://localhost:4004/health"
        "description" = "渲染服务"
    }
    "ai-model-service" = @{
        "port" = 3008
        "healthCheck" = "http://localhost:3008/api/v1/health"
        "description" = "AI模型服务"
    }
    "knowledge-service" = @{
        "port" = 8008
        "healthCheck" = "http://localhost:8008/api/health"
        "description" = "知识库服务"
    }
    "asset-library-service" = @{
        "port" = 8003
        "healthCheck" = "http://localhost:8003/health"
        "description" = "资源库服务"
    }
    "scene-template-service" = @{
        "port" = 8004
        "healthCheck" = "http://localhost:8004/health"
        "description" = "场景模板服务"
    }
    "scene-generation-service" = @{
        "port" = 8005
        "healthCheck" = "http://localhost:8005/health"
        "description" = "场景生成服务"
    }
    "binding-service" = @{
        "port" = 3011
        "healthCheck" = "http://localhost:3011/health"
        "description" = "绑定服务"
    }
    "monitoring-service" = @{
        "port" = 3012
        "healthCheck" = "http://localhost:3012/api/v1/health"
        "description" = "监控服务"
    }
}

Write-Host "`n=== 详细健康检查 ===" -ForegroundColor Yellow

foreach ($serviceName in $services.Keys) {
    $service = $services[$serviceName]
    $description = $service.description
    $port = $service.port
    
    Write-Host "`n检查 $description ($serviceName)..." -ForegroundColor Cyan
    
    # 检查容器状态
    $containerStatus = docker-compose -f docker-compose.windows.yml ps $serviceName
    if ($containerStatus -match "Up.*healthy") {
        Write-Host "  容器状态: 健康" -ForegroundColor Green
    } elseif ($containerStatus -match "Up.*unhealthy") {
        Write-Host "  容器状态: 不健康" -ForegroundColor Red
    } elseif ($containerStatus -match "Up.*starting") {
        Write-Host "  容器状态: 启动中" -ForegroundColor Yellow
    } elseif ($containerStatus -match "Up") {
        Write-Host "  容器状态: 运行中" -ForegroundColor Yellow
    } else {
        Write-Host "  容器状态: 未运行" -ForegroundColor Red
        continue
    }
    
    # 检查端口连通性
    try {
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $tcpClient.ConnectAsync("localhost", $port).Wait(3000)
        if ($tcpClient.Connected) {
            Write-Host "  端口 $port : 可连接" -ForegroundColor Green
            $tcpClient.Close()
        } else {
            Write-Host "  端口 $port : 无法连接" -ForegroundColor Red
        }
    } catch {
        Write-Host "  端口 $port : 无法连接" -ForegroundColor Red
    }
    
    # HTTP健康检查
    if ($service.healthCheck -like "http*") {
        try {
            $response = Invoke-WebRequest -Uri $service.healthCheck -TimeoutSec 5 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Host "  健康检查: 通过" -ForegroundColor Green
            } else {
                Write-Host "  健康检查: 失败 (状态码: $($response.StatusCode))" -ForegroundColor Red
            }
        } catch {
            Write-Host "  健康检查: 失败 ($($_.Exception.Message))" -ForegroundColor Red
        }
    }
}

# 检查数据库连接
Write-Host "`n=== 数据库连接检查 ===" -ForegroundColor Yellow
try {
    $databases = docker exec dl-engine-mysql-win mysql -u root -pDLEngine2024!@# -e "SHOW DATABASES;" 2>$null
    if ($databases -match "dl_engine_") {
        Write-Host "MySQL数据库连接: 成功" -ForegroundColor Green
        Write-Host "已创建的数据库:" -ForegroundColor Cyan
        $databases -split "`n" | Where-Object { $_ -match "dl_engine_" } | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
    } else {
        Write-Host "MySQL数据库连接: 失败" -ForegroundColor Red
    }
} catch {
    Write-Host "MySQL数据库连接: 失败" -ForegroundColor Red
}

# 总结
Write-Host "`n=== 检查总结 ===" -ForegroundColor Yellow
$allServices = docker-compose -f docker-compose.windows.yml ps
$healthyCount = ($allServices -split "`n" | Where-Object { $_ -match "healthy" }).Count
$unhealthyCount = ($allServices -split "`n" | Where-Object { $_ -match "unhealthy" }).Count
$startingCount = ($allServices -split "`n" | Where-Object { $_ -match "starting" }).Count

Write-Host "健康服务: $healthyCount" -ForegroundColor Green
Write-Host "不健康服务: $unhealthyCount" -ForegroundColor Red
Write-Host "启动中服务: $startingCount" -ForegroundColor Yellow

if ($unhealthyCount -gt 0) {
    Write-Host "`n建议操作:" -ForegroundColor Yellow
    Write-Host "1. 查看不健康服务的日志: docker-compose -f docker-compose.windows.yml logs [服务名]" -ForegroundColor Cyan
    Write-Host "2. 重启有问题的服务: docker-compose -f docker-compose.windows.yml restart [服务名]" -ForegroundColor Cyan
    Write-Host "3. 如果问题持续，可以尝试重新构建: docker-compose -f docker-compose.windows.yml up -d --build [服务名]" -ForegroundColor Cyan
}

Write-Host "`n健康检查完成！" -ForegroundColor Green
