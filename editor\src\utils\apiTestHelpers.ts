/**
 * API测试辅助函数
 * 用于在开发过程中测试API连接
 */

export const API_BASE_URL = 'http://localhost:3000/api';

/**
 * 测试API连接
 */
export async function testApiConnection(): Promise<boolean> {
  try {
    const response = await fetch(`${API_BASE_URL}/health`);
    return response.ok;
  } catch (error) {
    console.error('API连接测试失败:', error);
    return false;
  }
}

/**
 * 测试认证API
 */
export async function testAuthApi(token?: string): Promise<boolean> {
  try {
    const headers: HeadersInit = {};
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    
    const response = await fetch(`${API_BASE_URL}/auth/profile`, { headers });
    return response.ok;
  } catch (error) {
    console.error('认证API测试失败:', error);
    return false;
  }
}

/**
 * 获取API状态
 */
export async function getApiStatus(): Promise<{
  gateway: boolean;
  auth: boolean;
  projects: boolean;
  assets: boolean;
}> {
  const status = {
    gateway: false,
    auth: false,
    projects: false,
    assets: false
  };
  
  try {
    // 测试网关
    const gatewayResponse = await fetch(`${API_BASE_URL}/health`);
    status.gateway = gatewayResponse.ok;
    
    // 测试其他服务需要认证，这里只测试端点是否存在
    const authResponse = await fetch(`${API_BASE_URL}/auth/profile`);
    status.auth = authResponse.status !== 404;
    
    const projectsResponse = await fetch(`${API_BASE_URL}/projects`);
    status.projects = projectsResponse.status !== 404;
    
    const assetsResponse = await fetch(`${API_BASE_URL}/assets`);
    status.assets = assetsResponse.status !== 404;
    
  } catch (error) {
    console.error('API状态检查失败:', error);
  }
  
  return status;
}