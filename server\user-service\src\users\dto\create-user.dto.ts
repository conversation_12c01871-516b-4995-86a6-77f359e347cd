/**
 * 创建用户DTO
 */
import {
  IsString,
  IsEmail,
  IsNotEmpty,
  MinLength,
  IsOptional,
  IsBoolean,
  IsEnum,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { UserRole } from '../entities/user.entity';

export class CreateUserDto {
  @ApiProperty({ description: '用户名', example: 'johndoe' })
  @IsString()
  @IsNotEmpty()
  username: string;

  @ApiProperty({ description: '电子邮箱', example: '<EMAIL>' })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({ description: '密码', example: 'password123' })
  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  password: string;

  @ApiProperty({ description: '显示名称', required: false, example: 'John <PERSON>' })
  @IsString()
  @IsOptional()
  displayName?: string;

  @ApiProperty({ description: '是否已验证', required: false, default: false })
  @IsBoolean()
  @IsOptional()
  isVerified?: boolean;

  @ApiProperty({ description: '是否访客', required: false, default: false })
  @IsBoolean()
  @IsOptional()
  isGuest?: boolean;

  @ApiProperty({ description: '用户角色', required: false, enum: UserRole, default: UserRole.USER })
  @IsEnum(UserRole)
  @IsOptional()
  role?: UserRole;

  @ApiProperty({ description: '邀请码', required: false })
  @IsString()
  @IsOptional()
  inviteCode?: string;
}
