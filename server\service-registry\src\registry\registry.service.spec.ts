import { Test, TestingModule } from '@nestjs/testing';
import { RegistryService } from './registry.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ServiceEntity } from './entities/service.entity';
import { ServiceInstanceEntity } from './entities/service-instance.entity';
// import { EventBusService } from '@shared/event-bus';
import { RegisterServiceInstanceDto } from './dto/register-service.dto';
import { DiscoverServiceDto } from './dto/discover-service.dto';
import { HeartbeatDto } from './dto/heartbeat.dto';
import { NotFoundException } from '@nestjs/common';

describe('RegistryService', () => {
  let service: RegistryService;
  let serviceRepository;
  let instanceRepository;
  // let eventBusService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RegistryService,
        {
          provide: getRepositoryToken(ServiceEntity),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(ServiceInstanceEntity),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
          },
        },
        // {
        //   provide: EventBusService,
        //   useValue: {
        //     publish: jest.fn(),
        //   },
        // },
      ],
    }).compile();

    service = module.get<RegistryService>(RegistryService);
    serviceRepository = module.get(getRepositoryToken(ServiceEntity));
    instanceRepository = module.get(getRepositoryToken(ServiceInstanceEntity));
    // eventBusService = module.get<EventBusService>(EventBusService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('registerService', () => {
    it('should register a new service instance', async () => {
      // 准备测试数据
      const dto: RegisterServiceInstanceDto = {
        name: 'test-service',
        instanceId: 'test-instance-1',
        host: 'localhost',
        port: 3000,
        metadata: { version: '1.0.0' },
      };

      // 模拟数据库操作
      serviceRepository.findOne.mockResolvedValue(null);
      serviceRepository.create.mockReturnValue({ id: 'service-1', name: dto.name });
      serviceRepository.save.mockResolvedValue({ id: 'service-1', name: dto.name });
      
      instanceRepository.findOne.mockResolvedValue(null);
      instanceRepository.create.mockReturnValue({ 
        id: 'instance-1', 
        serviceId: 'service-1',
        instanceId: dto.instanceId,
        host: dto.host,
        port: dto.port,
        metadata: dto.metadata,
      });
      instanceRepository.save.mockResolvedValue({
        id: 'instance-1',
        serviceId: 'service-1',
        instanceId: dto.instanceId,
        host: dto.host,
        port: dto.port,
        metadata: dto.metadata,
      });

      // 执行测试
      const result = await service.registerService(dto);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.instanceId).toBe(dto.instanceId);
      expect(serviceRepository.findOne).toHaveBeenCalled();
      expect(instanceRepository.findOne).toHaveBeenCalled();
      // expect(eventBusService.publish).toHaveBeenCalled();
    });
  });

  describe('discoverService', () => {
    it('should discover service instances', async () => {
      // 准备测试数据
      const dto: DiscoverServiceDto = {
        name: 'test-service',
        onlyHealthy: true,
      };

      // 模拟数据库操作
      serviceRepository.findOne.mockResolvedValue({
        id: 'service-1',
        name: dto.name,
      });
      
      instanceRepository.find.mockResolvedValue([
        {
          id: 'instance-1',
          serviceId: 'service-1',
          instanceId: 'test-instance-1',
          host: 'localhost',
          port: 3000,
          isHealthy: true,
        },
      ]);

      // 执行测试
      const result = await service.discoverService(dto);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.length).toBe(1);
      expect(result[0].instanceId).toBe('test-instance-1');
    });

    it('should throw NotFoundException when service not found', async () => {
      // 准备测试数据
      const dto: DiscoverServiceDto = {
        name: 'non-existent-service',
        onlyHealthy: true,
      };

      // 模拟数据库操作
      serviceRepository.findOne.mockResolvedValue(null);

      // 执行测试并验证结果
      await expect(service.discoverService(dto)).rejects.toThrow(NotFoundException);
    });
  });

  describe('heartbeat', () => {
    it('should update instance heartbeat', async () => {
      // 准备测试数据
      const dto: HeartbeatDto = {
        name: 'test-service',
        instanceId: 'test-instance-1',
        status: { state: 'UP' },
      };

      // 模拟数据库操作
      serviceRepository.findOne.mockResolvedValue({
        id: 'service-1',
        name: dto.name,
      });
      
      instanceRepository.findOne.mockResolvedValue({
        id: 'instance-1',
        serviceId: 'service-1',
        instanceId: dto.instanceId,
        isHealthy: false,
      });
      
      instanceRepository.save.mockResolvedValue({
        id: 'instance-1',
        serviceId: 'service-1',
        instanceId: dto.instanceId,
        isHealthy: true,
        lastHeartbeat: expect.any(Date),
      });

      // 执行测试
      await service.heartbeat(dto);

      // 验证结果
      expect(instanceRepository.save).toHaveBeenCalledWith(expect.objectContaining({
        isHealthy: true,
        lastHeartbeat: expect.any(Date),
      }));
    });
  });
});
