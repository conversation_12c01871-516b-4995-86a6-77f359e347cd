import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TreeRepository } from 'typeorm';
import { Category } from './entities/category.entity';
import { CacheService } from '../../common/services/cache.service';
import { LoggerService } from '../../common/services/logger.service';

export interface CreateCategoryDto {
  name: string;
  description?: string;
  slug: string;
  icon?: string;
  parentId?: string;
  sortOrder?: number;
}

export interface UpdateCategoryDto {
  name?: string;
  description?: string;
  slug?: string;
  icon?: string;
  parentId?: string;
  sortOrder?: number;
  isActive?: boolean;
}

@Injectable()
export class CategoriesService {
  constructor(
    @InjectRepository(Category)
    private readonly categoryRepository: TreeRepository<Category>,
    private readonly cacheService: CacheService,
    private readonly logger: LoggerService,
  ) {}

  /**
   * 创建分类
   */
  async create(createCategoryDto: CreateCategoryDto): Promise<Category> {
    const { parentId, ...categoryData } = createCategoryDto;

    let parent: Category | null = null;
    if (parentId) {
      parent = await this.categoryRepository.findOne({
        where: { id: parentId },
      });
      if (!parent) {
        throw new NotFoundException('父分类不存在');
      }
    }

    const category = this.categoryRepository.create({
      ...categoryData,
      parent,
      level: parent ? parent.level + 1 : 0,
    });

    const savedCategory = await this.categoryRepository.save(category);
    
    // 更新路径
    await this.updateCategoryPath(savedCategory);
    
    // 清除缓存
    await this.clearCache();

    this.logger.log(`分类创建成功: ${savedCategory.id}`, 'CategoriesService');
    return savedCategory;
  }

  /**
   * 获取所有分类（树形结构）
   */
  async findAll(): Promise<Category[]> {
    const cacheKey = 'categories:tree';
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const categories = await this.categoryRepository.findTrees();
    
    // 缓存结果
    await this.cacheService.set(cacheKey, categories, 1800); // 30分钟缓存
    
    return categories;
  }

  /**
   * 获取根分类
   */
  async findRoots(): Promise<Category[]> {
    return await this.categoryRepository.findRoots();
  }

  /**
   * 根据ID查找分类
   */
  async findOne(id: string): Promise<Category> {
    const cacheKey = `category:${id}`;
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const category = await this.categoryRepository.findOne({
      where: { id },
      relations: ['children', 'parent'],
    });

    if (!category) {
      throw new NotFoundException('分类不存在');
    }

    // 缓存结果
    await this.cacheService.set(cacheKey, category, 600); // 10分钟缓存

    return category;
  }

  /**
   * 更新分类
   */
  async update(id: string, updateCategoryDto: UpdateCategoryDto): Promise<Category> {
    const category = await this.findOne(id);
    
    Object.assign(category, updateCategoryDto);
    
    const updatedCategory = await this.categoryRepository.save(category);
    
    // 清除缓存
    await this.clearCache();

    this.logger.log(`分类更新成功: ${updatedCategory.id}`, 'CategoriesService');
    return updatedCategory;
  }

  /**
   * 删除分类
   */
  async remove(id: string): Promise<void> {
    const category = await this.findOne(id);
    
    // 检查是否有子分类
    const children = await this.categoryRepository.findDescendants(category);
    if (children.length > 1) { // 包含自己
      throw new Error('不能删除有子分类的分类');
    }

    await this.categoryRepository.remove(category);
    
    // 清除缓存
    await this.clearCache();

    this.logger.log(`分类删除成功: ${id}`, 'CategoriesService');
  }

  /**
   * 更新分类路径
   */
  private async updateCategoryPath(category: Category): Promise<void> {
    const ancestors = await this.categoryRepository.findAncestors(category);
    const path = ancestors.map(c => c.name).join(' > ');
    
    category.path = path;
    await this.categoryRepository.save(category);
  }

  /**
   * 清除缓存
   */
  private async clearCache(): Promise<void> {
    await this.cacheService.delPattern('categories:*');
    await this.cacheService.delPattern('category:*');
  }
}
