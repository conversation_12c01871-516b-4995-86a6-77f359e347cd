# 开发模式启动脚本
# 用于前后端联调测试

Write-Host "🚀 启动开发模式微服务" -ForegroundColor Magenta
Write-Host "=" * 60 -ForegroundColor Magenta

# 检查Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js 版本: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js 未安装或不在PATH中" -ForegroundColor Red
    exit 1
}

# 启动数据库服务
Write-Host "`n🗄️ 启动数据库服务..." -ForegroundColor Cyan
try {
    docker-compose -f docker-compose.windows.yml up -d mysql redis
    Write-Host "✅ 数据库服务启动成功" -ForegroundColor Green
    Start-Sleep -Seconds 10
} catch {
    Write-Host "⚠️ 数据库服务启动失败，继续启动微服务" -ForegroundColor Yellow
}

# 定义要启动的服务
$services = @(
    @{ Name = "API网关"; Path = "server/api-gateway"; Port = 3000 },
    @{ Name = "用户服务"; Path = "server/user-service"; Port = 3001 },
    @{ Name = "项目服务"; Path = "server/project-service"; Port = 3002 }
)

# 启动微服务
$jobs = @()

foreach ($service in $services) {
    Write-Host "`n🔧 启动 $($service.Name)..." -ForegroundColor Cyan
    
    # 检查目录是否存在
    if (-not (Test-Path $service.Path)) {
        Write-Host "❌ 服务目录不存在: $($service.Path)" -ForegroundColor Red
        continue
    }
    
    # 检查package.json是否存在
    if (-not (Test-Path "$($service.Path)/package.json")) {
        Write-Host "❌ package.json不存在: $($service.Path)" -ForegroundColor Red
        continue
    }
    
    # 启动服务
    try {
        $job = Start-Job -ScriptBlock {
            param($servicePath, $serviceName)
            Set-Location $servicePath
            npm run start:dev 2>&1
        } -ArgumentList $service.Path, $service.Name
        
        $jobs += @{ Job = $job; Name = $service.Name; Port = $service.Port }
        Write-Host "✅ $($service.Name) 启动中... (端口: $($service.Port))" -ForegroundColor Green
    } catch {
        Write-Host "❌ $($service.Name) 启动失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 等待服务启动
Write-Host "`n⏳ 等待服务启动完成..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# 检查服务状态
Write-Host "`n📊 检查服务状态..." -ForegroundColor Cyan

function Test-ServiceHealth {
    param($port, $path = "/api/health")
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$port$path" -TimeoutSec 5 -UseBasicParsing
        return $response.StatusCode -eq 200
    } catch {
        return $false
    }
}

foreach ($serviceJob in $jobs) {
    $isHealthy = Test-ServiceHealth -port $serviceJob.Port
    if ($isHealthy) {
        Write-Host "✅ $($serviceJob.Name) - 运行正常 (http://localhost:$($serviceJob.Port))" -ForegroundColor Green
    } else {
        Write-Host "❌ $($serviceJob.Name) - 服务不可用" -ForegroundColor Red
        
        # 显示作业输出以便调试
        $output = Receive-Job -Job $serviceJob.Job -Keep
        if ($output) {
            Write-Host "   输出: $($output[-5..-1] -join "`n   ")" -ForegroundColor Gray
        }
    }
}

# 显示服务信息
Write-Host "`n🌐 服务访问地址:" -ForegroundColor Magenta
Write-Host "API网关: http://localhost:3000/api" -ForegroundColor Cyan
Write-Host "用户服务: http://localhost:3001/api/v1" -ForegroundColor Cyan
Write-Host "项目服务: http://localhost:3002/api/v1" -ForegroundColor Cyan

Write-Host "`n📝 下一步操作:" -ForegroundColor Magenta
Write-Host "1. 运行前后端联调测试: node frontend-backend-integration-test.js" -ForegroundColor Cyan
Write-Host "2. 启动前端开发服务器: cd editor && npm start" -ForegroundColor Cyan
Write-Host "3. 停止服务: 按 Ctrl+C 或运行 stop-dev-services.ps1" -ForegroundColor Cyan

# 保持脚本运行
Write-Host "`n⌨️ 按 Ctrl+C 停止所有服务..." -ForegroundColor Yellow

try {
    while ($true) {
        Start-Sleep -Seconds 5
        
        # 检查作业状态
        $runningJobs = $jobs | Where-Object { $_.Job.State -eq "Running" }
        if ($runningJobs.Count -eq 0) {
            Write-Host "⚠️ 所有服务已停止" -ForegroundColor Yellow
            break
        }
    }
} catch {
    Write-Host "`n🛑 正在停止服务..." -ForegroundColor Yellow
} finally {
    # 清理作业
    foreach ($serviceJob in $jobs) {
        if ($serviceJob.Job.State -eq "Running") {
            Stop-Job -Job $serviceJob.Job
            Remove-Job -Job $serviceJob.Job -Force
        }
    }
    Write-Host "✅ 所有服务已停止" -ForegroundColor Green
}
