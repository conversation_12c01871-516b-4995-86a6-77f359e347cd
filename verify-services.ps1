#!/usr/bin/env pwsh
# 服务验证脚本
# 验证所有服务是否正常启动并通过健康检查

param(
    [switch]$Verbose,           # 详细输出
    [switch]$WaitForServices,   # 等待服务启动
    [int]$Timeout = 300         # 超时时间（秒）
)

# 设置错误处理
$ErrorActionPreference = "Continue"

# 颜色输出函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput Cyan "ℹ️  $message"
}

function Write-Success($message) {
    Write-ColorOutput Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "❌ $message"
}

function Write-Header($message) {
    Write-Host ""
    Write-ColorOutput Magenta "🔍 $message"
    Write-Host "=" * 60
}

# 服务健康检查配置
$services = @(
    @{
        Name = "MySQL"
        Container = "dl-engine-mysql-win"
        HealthUrl = $null
        Port = 3306
        Type = "database"
    },
    @{
        Name = "Redis"
        Container = "dl-engine-redis-win"
        HealthUrl = $null
        Port = 6379
        Type = "cache"
    },
    @{
        Name = "MinIO"
        Container = "dl-engine-minio-win"
        HealthUrl = "http://localhost:9000/minio/health/live"
        Port = 9000
        Type = "storage"
    },
    @{
        Name = "Chroma"
        Container = "dl-engine-chroma-win"
        HealthUrl = "http://localhost:8000/api/v1/heartbeat"
        Port = 8000
        Type = "vector-db"
    },
    @{
        Name = "Elasticsearch"
        Container = "dl-engine-elasticsearch-win"
        HealthUrl = "http://localhost:9200/_cluster/health"
        Port = 9200
        Type = "search"
    },
    @{
        Name = "Service Registry"
        Container = "dl-engine-service-registry-win"
        HealthUrl = "http://localhost:4010/api/health"
        Port = 4010
        Type = "service"
    },
    @{
        Name = "API Gateway"
        Container = "dl-engine-api-gateway-win"
        HealthUrl = "http://localhost:3000/api/health"
        Port = 3000
        Type = "service"
    },
    @{
        Name = "User Service"
        Container = "dl-engine-user-service-win"
        HealthUrl = "http://localhost:4001/api/health"
        Port = 4001
        Type = "service"
    },
    @{
        Name = "Project Service"
        Container = "dl-engine-project-service-win"
        HealthUrl = "http://localhost:4002/api/health"
        Port = 4002
        Type = "service"
    },
    @{
        Name = "Asset Service"
        Container = "dl-engine-asset-service-win"
        HealthUrl = "http://localhost:4003/api/health"
        Port = 4003
        Type = "service"
    },
    @{
        Name = "Render Service"
        Container = "dl-engine-render-service-win"
        HealthUrl = "http://localhost:4004/api/health"
        Port = 4004
        Type = "service"
    },
    @{
        Name = "Knowledge Service"
        Container = "dl-engine-knowledge-service-win"
        HealthUrl = "http://localhost:8008/api/health"
        Port = 8008
        Type = "service"
    },
    @{
        Name = "RAG Engine"
        Container = "dl-engine-rag-engine-win"
        HealthUrl = "http://localhost:8009/health"
        Port = 8009
        Type = "service"
    },
    @{
        Name = "Game Server"
        Container = "dl-engine-game-server-win"
        HealthUrl = "http://localhost:3030/api/health"
        Port = 3030
        Type = "service"
    },
    @{
        Name = "Asset Library Service"
        Container = "dl-engine-asset-library-service-win"
        HealthUrl = "http://localhost:8003/health"
        Port = 8003
        Type = "service"
    },
    @{
        Name = "Binding Service"
        Container = "dl-engine-binding-service-win"
        HealthUrl = "http://localhost:3011/health"
        Port = 3011
        Type = "service"
    },
    @{
        Name = "Scene Generation Service"
        Container = "dl-engine-scene-generation-service-win"
        HealthUrl = "http://localhost:8005/health"
        Port = 8005
        Type = "service"
    },
    @{
        Name = "Scene Template Service"
        Container = "dl-engine-scene-template-service-win"
        HealthUrl = "http://localhost:8004/health"
        Port = 8004
        Type = "service"
    },
    @{
        Name = "Monitoring Service"
        Container = "dl-engine-monitoring-service-win"
        HealthUrl = "http://localhost:3012/api/v1/health"
        Port = 3012
        Type = "service"
    },
    @{
        Name = "Editor Frontend"
        Container = "dl-engine-editor-win"
        HealthUrl = "http://localhost:80"
        Port = 80
        Type = "frontend"
    }
)

# 检查Docker是否运行
function Test-DockerRunning {
    try {
        docker info | Out-Null
        return $true
    } catch {
        return $false
    }
}

# 检查容器状态
function Test-ContainerStatus($containerName) {
    try {
        $status = docker inspect --format='{{.State.Status}}' $containerName 2>$null
        return $status -eq "running"
    } catch {
        return $false
    }
}

# 检查端口是否开放
function Test-Port($port) {
    try {
        $connection = Test-NetConnection -ComputerName localhost -Port $port -WarningAction SilentlyContinue
        return $connection.TcpTestSucceeded
    } catch {
        return $false
    }
}

# 检查HTTP健康检查
function Test-HealthCheck($url) {
    try {
        $response = Invoke-WebRequest -Uri $url -TimeoutSec 10 -UseBasicParsing
        return $response.StatusCode -eq 200
    } catch {
        if ($Verbose) {
            Write-Warning "Health check failed for $url : $($_.Exception.Message)"
        }
        return $false
    }
}

# 等待服务启动
function Wait-ForService($service, $timeoutSeconds) {
    $elapsed = 0
    $interval = 5
    
    while ($elapsed -lt $timeoutSeconds) {
        # 检查容器状态
        if (Test-ContainerStatus $service.Container) {
            # 检查端口
            if (Test-Port $service.Port) {
                # 如果有健康检查URL，检查健康状态
                if ($service.HealthUrl) {
                    if (Test-HealthCheck $service.HealthUrl) {
                        return $true
                    }
                } else {
                    return $true
                }
            }
        }
        
        Start-Sleep -Seconds $interval
        $elapsed += $interval
        
        if ($Verbose) {
            Write-Info "等待 $($service.Name) 启动... ($elapsed/$timeoutSeconds 秒)"
        }
    }
    
    return $false
}

# 验证单个服务
function Test-Service($service) {
    $result = @{
        Name = $service.Name
        Container = $service.Container
        ContainerRunning = $false
        PortOpen = $false
        HealthCheck = $false
        Overall = $false
    }
    
    # 检查容器状态
    $result.ContainerRunning = Test-ContainerStatus $service.Container
    
    # 检查端口
    if ($result.ContainerRunning) {
        $result.PortOpen = Test-Port $service.Port
    }
    
    # 检查健康状态
    if ($result.PortOpen -and $service.HealthUrl) {
        $result.HealthCheck = Test-HealthCheck $service.HealthUrl
    } elseif ($result.PortOpen) {
        $result.HealthCheck = $true  # 没有健康检查URL的服务，端口开放即认为健康
    }
    
    # 总体状态
    $result.Overall = $result.ContainerRunning -and $result.PortOpen -and $result.HealthCheck
    
    return $result
}

# 主函数
function Main {
    Write-Header "DL Engine 服务验证"
    
    # 检查Docker状态
    if (-not (Test-DockerRunning)) {
        Write-Error "Docker Desktop 未运行，请先启动 Docker Desktop"
        exit 1
    }
    Write-Success "Docker Desktop 运行正常"
    
    # 如果需要等待服务启动
    if ($WaitForServices) {
        Write-Header "等待服务启动"
        foreach ($service in $services) {
            Write-Info "等待 $($service.Name) 启动..."
            $success = Wait-ForService $service $Timeout
            if ($success) {
                Write-Success "$($service.Name) 启动成功"
            } else {
                Write-Error "$($service.Name) 启动超时"
            }
        }
    }
    
    # 验证所有服务
    Write-Header "验证服务状态"
    
    $results = @()
    $totalServices = $services.Count
    $healthyServices = 0
    
    foreach ($service in $services) {
        $result = Test-Service $service
        $results += $result
        
        if ($result.Overall) {
            Write-Success "$($service.Name) - 健康"
            $healthyServices++
        } else {
            Write-Error "$($service.Name) - 不健康"
            if ($Verbose) {
                Write-Info "  容器运行: $($result.ContainerRunning)"
                Write-Info "  端口开放: $($result.PortOpen)"
                Write-Info "  健康检查: $($result.HealthCheck)"
            }
        }
    }
    
    # 显示汇总
    Write-Header "验证汇总"
    Write-Info "总服务数: $totalServices"
    Write-Info "健康服务数: $healthyServices"
    Write-Info "不健康服务数: $($totalServices - $healthyServices)"
    
    $healthPercentage = [math]::Round(($healthyServices / $totalServices) * 100, 2)
    Write-Info "健康率: $healthPercentage%"
    
    if ($healthyServices -eq $totalServices) {
        Write-Success "🎉 所有服务都运行正常！"
        exit 0
    } else {
        Write-Warning "⚠️  部分服务存在问题，请检查日志"
        
        # 显示问题服务
        Write-Header "问题服务详情"
        foreach ($result in $results) {
            if (-not $result.Overall) {
                Write-Error "$($result.Name):"
                Write-Info "  容器: $($result.Container) - $($result.ContainerRunning ? '运行' : '未运行')"
                Write-Info "  端口: $($result.PortOpen ? '开放' : '关闭')"
                Write-Info "  健康检查: $($result.HealthCheck ? '通过' : '失败')"
            }
        }
        
        exit 1
    }
}

# 执行主函数
try {
    Main
} catch {
    Write-Error "验证过程中发生错误: $($_.Exception.Message)"
    exit 1
}
