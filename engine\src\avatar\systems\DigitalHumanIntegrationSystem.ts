import { EventEmitter } from '../../utils/EventEmitter';
import { DigitalHumanComponent } from '../components/DigitalHumanComponent';
import { DigitalHumanPackageHeader } from '../formats/DigitalHumanPackage';
import { MinIOStorageService, MinIOConfig } from '../../storage/MinIOStorageService';
import { BIPIntegrationSystem } from './BIPIntegrationSystem';
import { DigitalHumanMarketplaceService } from '../marketplace/DigitalHumanMarketplaceService';
import {
  OptimizationSuggestion,
  ValidationResult,
  CompatibilityResult,
  ActionConflict,
  DigitalHumanCreationData,
  IntegrationResult,
  BatchOperationResult,
  PhotoCreationData,
  FileCreationData,
  BIPCreationData
} from '../types/IntegrationTypes';

/**
 * 系统状态接口
 */
interface SystemStatus {
  isInitialized: boolean;
  activeDigitalHumans: Map<string, DigitalHumanComponent>;
  systemHealth: 'healthy' | 'warning' | 'error';
  performanceMetrics: {
    renderFPS: number;
    memoryUsage: number;
    cpuUsage: number;
    networkLatency: number;
  };
}

/**
 * 系统集成配置接口
 */
interface IntegrationConfig {
  enablePerformanceMonitoring: boolean;
  enableAutoOptimization: boolean;
  enableCloudSync: boolean;
  maxConcurrentDigitalHumans: number;
  performanceThresholds: {
    maxMemoryUsage: number;
    minFPS: number;
    maxCPUUsage: number;
  };
}

/**
 * 系统集成结果接口
 */


interface SystemHealthCheck {
  overall: 'healthy' | 'warning' | 'error';
  components: any;
  lastCheck: Date;
  uptime: number;
  issues: any[];
}



interface HealthIssue {
  type: string;
  message: string;
  severity: 'low' | 'medium' | 'high';
}

/**
 * 数字人系统集成管理器
 * 负责整合所有数字人相关功能模块，提供统一的系统接口
 * 实现M4阶段的系统集成和优化功能
 */
export class DigitalHumanIntegrationSystem extends EventEmitter {
  private storageService: MinIOStorageService;
  private bipIntegrationSystem: BIPIntegrationSystem;
  private marketplaceService: DigitalHumanMarketplaceService;

  // 系统配置
  private config: IntegrationConfig = {
    enablePerformanceMonitoring: true,
    enableAutoOptimization: true,
    enableCloudSync: true,
    maxConcurrentDigitalHumans: 10,
    performanceThresholds: {
      maxMemoryUsage: 2048, // MB
      minFPS: 30,
      maxCPUUsage: 80 // %
    }
  };

  // 系统状态管理
  private systemStatus: SystemStatus = {
    isInitialized: false,
    activeDigitalHumans: new Map(),
    systemHealth: 'healthy',
    performanceMetrics: {
      renderFPS: 60,
      memoryUsage: 0,
      cpuUsage: 0,
      networkLatency: 0
    }
  };

  constructor(config?: Partial<IntegrationConfig>) {
    super(); // EventEmitter构造函数
    
    if (config) {
      this.config = { ...this.config, ...config };
    }
    
    this.initializeSubsystems();
  }

  /**
   * 初始化子系统
   */
  private async initializeSubsystems(): Promise<void> {
    try {
      console.log('初始化数字人集成系统...');

      // 初始化存储服务
      const defaultConfig: MinIOConfig = {
        endpoint: 'localhost',
        port: 9000,
        useSSL: false,
        accessKey: 'minioadmin',
        secretKey: 'minioadmin'
      };
      this.storageService = new MinIOStorageService(defaultConfig);
      await this.storageService.initialize();

      // 初始化BIP集成系统 - 需要world参数，暂时使用null
      this.bipIntegrationSystem = new BIPIntegrationSystem(null as any);

      // 初始化市场服务
      this.marketplaceService = new DigitalHumanMarketplaceService('http://localhost:3000', 'default-key');

      // 性能监控功能暂时禁用
      // if (this.config.enablePerformanceMonitoring) {
      //   this.performanceMonitor = new PerformanceMonitor();
      //   this.startPerformanceMonitoring();
      // }

      this.systemStatus.isInitialized = true;
      console.log('数字人集成系统初始化完成');

      // 发送初始化完成事件
      this.emit('systemInitialized', this.systemStatus);

    } catch (error) {
      console.error('数字人集成系统初始化失败:', error);
      this.systemStatus.systemHealth = 'error';
      throw error;
    }
  }

  /**
   * 启动性能监控
   */
  private startPerformanceMonitoring(): void {
    setInterval(() => {
      this.updatePerformanceMetrics();
      this.checkSystemHealth();
      
      if (this.config.enableAutoOptimization) {
        this.performAutoOptimization();
      }
    }, 1000); // 每秒更新一次
  }

  /**
   * 更新性能指标
   */
  private updatePerformanceMetrics(): void {
    // 性能监控功能暂时禁用
    // if (!this.performanceMonitor) return;

    // 使用模拟数据
    this.systemStatus.performanceMetrics = {
      renderFPS: 60,
      memoryUsage: 512,
      cpuUsage: 30,
      networkLatency: 50
    };
  }

  /**
   * 检查系统健康状态
   */
  private checkSystemHealth(): void {
    const { performanceMetrics } = this.systemStatus;
    const { performanceThresholds } = this.config;

    let healthStatus: 'healthy' | 'warning' | 'error' = 'healthy';

    // 检查内存使用
    if (performanceMetrics.memoryUsage > performanceThresholds.maxMemoryUsage) {
      healthStatus = 'error';
      console.warn(`内存使用过高: ${performanceMetrics.memoryUsage}MB`);
    }

    // 检查FPS
    if (performanceMetrics.renderFPS < performanceThresholds.minFPS) {
      healthStatus = healthStatus === 'error' ? 'error' : 'warning';
      console.warn(`渲染帧率过低: ${performanceMetrics.renderFPS}FPS`);
    }

    // 检查CPU使用
    if (performanceMetrics.cpuUsage > performanceThresholds.maxCPUUsage) {
      healthStatus = healthStatus === 'error' ? 'error' : 'warning';
      console.warn(`CPU使用率过高: ${performanceMetrics.cpuUsage}%`);
    }

    if (this.systemStatus.systemHealth !== healthStatus) {
      this.systemStatus.systemHealth = healthStatus;
      this.emit('healthStatusChanged', healthStatus);
    }
  }

  /**
   * 执行自动优化
   */
  private performAutoOptimization(): void {
    const { performanceMetrics } = this.systemStatus;
    const { performanceThresholds } = this.config;

    // 如果内存使用过高，清理不活跃的数字人
    if (performanceMetrics.memoryUsage > performanceThresholds.maxMemoryUsage * 0.8) {
      this.cleanupInactiveDigitalHumans();
    }

    // 如果FPS过低，降低渲染质量
    if (performanceMetrics.renderFPS < performanceThresholds.minFPS) {
      this.adjustRenderingQuality();
    }
  }

  /**
   * 清理不活跃的数字人
   */
  private cleanupInactiveDigitalHumans(): void {
    const now = Date.now();
    const inactiveThreshold = 5 * 60 * 1000; // 5分钟

    for (const [id, digitalHuman] of Array.from(this.systemStatus.activeDigitalHumans.entries())) {
      if (now - ((digitalHuman as any).lastActiveTime || now) > inactiveThreshold) {
        this.unloadDigitalHuman(id);
        console.log(`清理不活跃的数字人: ${id}`);
      }
    }
  }

  /**
   * 调整渲染质量
   */
  private adjustRenderingQuality(): void {
    // 实现渲染质量自动调整逻辑
    console.log('自动调整渲染质量以提升性能');
    // TODO: 实现具体的渲染质量调整逻辑
  }

  /**
   * 卸载数字人
   */
  private unloadDigitalHuman(digitalHumanId: string): void {
    const digitalHuman = this.systemStatus.activeDigitalHumans.get(digitalHumanId);
    if (digitalHuman) {
      (digitalHuman as any).dispose();
      this.systemStatus.activeDigitalHumans.delete(digitalHumanId);
      this.emit('digitalHumanUnloaded', digitalHumanId);
    }
  }

  /**
   * 获取系统状态
   */
  public getSystemStatus(): SystemStatus {
    return { ...this.systemStatus };
  }

  /**
   * 获取系统配置
   */
  public getConfig(): IntegrationConfig {
    return { ...this.config };
  }

  /**
   * 更新系统配置
   */
  public updateConfig(newConfig: Partial<IntegrationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('configUpdated', this.config);
  }

  // 移除了重写的EventEmitter方法，直接使用继承的方法

  /**
   * 创建数字人（统一入口）
   * 支持多种创建方式：照片、文件上传、市场下载、BIP骨骼
   */
  public async createDigitalHuman(
    creationData: DigitalHumanCreationData
  ): Promise<IntegrationResult> {
    try {
      console.log(`开始创建数字人: ${creationData.type}`);

      // 检查系统容量
      if (this.systemStatus.activeDigitalHumans.size >= this.config.maxConcurrentDigitalHumans) {
        throw new Error('已达到最大并发数字人数量限制');
      }

      let digitalHuman: DigitalHumanComponent;
      const warnings: string[] = [];

      switch (creationData.type) {
        case 'photo':
          digitalHuman = await this.createFromPhoto(creationData.photoData!);
          break;
        case 'upload':
          digitalHuman = await this.createFromUpload(creationData.fileData!);
          break;
        case 'marketplace':
          digitalHuman = await this.createFromMarketplace(creationData.marketplaceId!);
          break;
        case 'bip_skeleton':
          digitalHuman = await this.createFromBIPSkeleton(creationData.bipData!);
          break;
        default:
          throw new Error(`不支持的创建类型: ${creationData.type}`);
      }

      // 注册数字人到系统 - 使用entity的id
      const digitalHumanId = (digitalHuman as any).entity?.id || 'unknown';
      this.systemStatus.activeDigitalHumans.set(digitalHumanId, digitalHuman);

      // 版本控制功能暂时禁用
      // if (this.versionControl) {
      //   await this.versionControl.createSnapshot(digitalHumanId, '初始版本');
      // }

      // 如果启用云同步，上传到云端
      if (this.config.enableCloudSync) {
        await this.syncToCloud(digitalHuman);
      }

      console.log(`数字人创建成功: ${digitalHumanId}`);
      this.emit('digitalHumanCreated', digitalHuman);

      return {
        success: true,
        digitalHumanId: digitalHumanId,
        warnings,
        performanceImpact: {
          memoryDelta: 0, // digitalHuman.getMemoryUsage() 方法不存在
          renderingImpact: 0 // digitalHuman.getRenderingComplexity() 方法不存在
        }
      };

    } catch (error: any) {
      console.error('数字人创建失败:', error);
      return {
        success: false,
        errors: [error.message]
      };
    }
  }

  /**
   * 从照片创建数字人
   */
  private async createFromPhoto(photoData: PhotoCreationData): Promise<DigitalHumanComponent> {
    // 使用AI处理管道
    const aiManager = this.getAIProcessingManager();

    // 1. 人脸检测
    const faceFeatures = await aiManager.extractFaceFeatures(photoData.photo);

    // 2. 3D重建
    const faceMesh = await aiManager.generate3DMesh(faceFeatures);

    // 3. 纹理生成
    const texture = await aiManager.generateTexture(photoData.photo, faceMesh);

    // 4. 组装数字人
    const digitalHuman = await aiManager.assembleDigitalHuman(faceMesh, texture);

    return digitalHuman;
  }

  /**
   * 从上传文件创建数字人
   */
  private async createFromUpload(fileData: FileCreationData): Promise<DigitalHumanComponent> {
    // 1. 文件验证
    const validation = await this.validateDigitalHumanFile(fileData.file);
    if (!validation.isValid) {
      throw new Error(`文件验证失败: ${validation.errors.join(', ')}`);
    }

    // 2. 解析数字人包
    const packageManager = this.getPackageManager();
    const digitalHumanPackage = await packageManager.parsePackage(fileData.file);

    // 3. 兼容性检查
    const compatibility = await this.checkCompatibility(digitalHumanPackage);
    if (compatibility.hasErrors) {
      throw new Error(`兼容性检查失败: ${compatibility.errors.join(', ')}`);
    }

    // 4. 导入数字人
    const digitalHuman = await this.importDigitalHuman(digitalHumanPackage);

    return digitalHuman;
  }

  /**
   * 从市场下载创建数字人
   */
  private async createFromMarketplace(marketplaceId: string): Promise<DigitalHumanComponent> {
    // 1. 从市场下载
    const downloadResult = await this.marketplaceService.downloadDigitalHuman(marketplaceId, 'default-user');

    // 2. 解析下载的包
    const packageManager = this.getPackageManager();
    const digitalHumanPackage = await packageManager.parsePackageFromUrl(downloadResult.downloadUrl);

    // 3. 导入数字人
    const digitalHuman = await this.importDigitalHuman(digitalHumanPackage);

    return digitalHuman;
  }

  /**
   * 从BIP骨骼创建数字人
   */
  private async createFromBIPSkeleton(bipData: BIPCreationData): Promise<DigitalHumanComponent> {
    // 1. 导入BIP骨骼
    const importResult = await this.bipIntegrationSystem.importBIPSkeleton(
      bipData.bipFile,
      (bipData.targetDigitalHuman as any).entity || null
    );

    if (!importResult.success) {
      throw new Error(`BIP导入失败: ${importResult.error}`);
    }

    // 2. 多动作融合功能暂时禁用
    // if (bipData.additionalBipFiles && bipData.additionalBipFiles.length > 0) {
    //   const fusionResult = await this.multiActionManager.importMultipleBIPFiles([
    //     bipData.bipFile,
    //     ...bipData.additionalBipFiles
    //   ]);
    //   // ... 融合逻辑
    // }

    return bipData.targetDigitalHuman;
  }

  /**
   * 解决动作冲突
   */
  private async resolveActionConflicts(conflicts: ActionConflict[]): Promise<void> {
    // 动作冲突解决功能暂时禁用
    console.log(`检测到 ${conflicts.length} 个动作冲突，暂时跳过处理`);
  }

  /**
   * 获取AI处理管理器
   */
  private getAIProcessingManager(): any {
    // TODO: 实现AI处理管理器的获取逻辑
    return null;
  }

  /**
   * 获取包管理器
   */
  private getPackageManager(): any {
    // TODO: 实现包管理器的获取逻辑
    return null;
  }

  /**
   * 验证数字人文件
   */
  private async validateDigitalHumanFile(file: File): Promise<ValidationResult> {
    // TODO: 实现文件验证逻辑
    return { isValid: true, errors: [], warnings: [] };
  }

  /**
   * 检查兼容性
   */
  private async checkCompatibility(packageData: DigitalHumanPackageHeader): Promise<CompatibilityResult> {
    // TODO: 实现兼容性检查逻辑
    return {
      hasErrors: false,
      errors: [],
      warnings: []
    };
  }

  /**
   * 导入数字人
   */
  private async importDigitalHuman(packageData: DigitalHumanPackageHeader): Promise<DigitalHumanComponent> {
    // TODO: 实现数字人导入逻辑
    // 创建一个临时entity用于DigitalHumanComponent
    const tempEntity = { id: 'temp-' + Date.now() } as any;
    return new DigitalHumanComponent(tempEntity);
  }

  /**
   * 同步到云端
   */
  private async syncToCloud(digitalHuman: DigitalHumanComponent): Promise<void> {
    try {
      const packageData = await this.exportDigitalHuman(digitalHuman);
      const digitalHumanId = (digitalHuman as any).entity?.id || 'unknown';

      // 使用正确的方法名和参数
      const fileData = JSON.stringify(packageData);
      const blob = new Blob([fileData], { type: 'application/json' });
      await this.storageService.uploadDigitalHumanFile(
        'default-user', // userId
        digitalHumanId, // digitalHumanId
        'models' as any, // fileType
        blob as any, // file
        'package.json' // fileName
      );
      console.log(`数字人已同步到云端: ${digitalHumanId}`);
    } catch (error: any) {
      console.warn(`云端同步失败: ${error.message}`);
    }
  }

  /**
   * 导出数字人
   */
  private async exportDigitalHuman(digitalHuman: DigitalHumanComponent): Promise<any> {
    // TODO: 实现数字人导出逻辑
    return {};
  }

  /**
   * 批量创建数字人
   */
  public async batchCreateDigitalHumans(
    creationDataList: DigitalHumanCreationData[]
  ): Promise<BatchOperationResult<IntegrationResult>> {
    const startTime = Date.now();
    const results: Array<{
      id: string;
      success: boolean;
      result?: IntegrationResult;
      error?: string;
    }> = [];

    console.log(`开始批量创建 ${creationDataList.length} 个数字人`);

    for (let i = 0; i < creationDataList.length; i++) {
      const creationData = creationDataList[i];
      const operationId = `batch_${i}_${Date.now()}`;

      try {
        const result = await this.createDigitalHuman(creationData);
        results.push({
          id: operationId,
          success: result.success,
          result,
          error: result.errors?.[0]
        });
      } catch (error) {
        results.push({
          id: operationId,
          success: false,
          error: error.message
        });
      }
    }

    const duration = Date.now() - startTime;
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;

    const batchResult: BatchOperationResult<IntegrationResult> = {
      totalCount: creationDataList.length,
      successCount,
      failureCount,
      results,
      summary: {
        duration,
        averageTime: duration / creationDataList.length,
        errors: results.filter(r => !r.success).map(r => r.error!),
        warnings: []
      }
    };

    this.emit('batchOperationCompleted', batchResult);
    return batchResult;
  }

  /**
   * 获取系统健康检查
   */
  public async getSystemHealthCheck(): Promise<SystemHealthCheck> {
    const healthCheck: SystemHealthCheck = {
      overall: 'healthy',
      components: {
        storage: 'healthy',
        ai: 'healthy',
        bip: 'healthy',
        marketplace: 'healthy',
        versioning: 'healthy'
      },
      issues: [],
      lastCheck: new Date(),
      uptime: Date.now() - ((this.systemStatus as any).startTime || Date.now())
    };

    try {
      // 检查存储服务
      if (this.storageService) {
        const storageHealth = await this.checkStorageHealth();
        healthCheck.components.storage = storageHealth.status;
        if (storageHealth.issues) {
          healthCheck.issues.push(...storageHealth.issues);
        }
      }

      // 检查BIP集成系统
      if (this.bipIntegrationSystem) {
        const bipHealth = await this.checkBIPHealth();
        healthCheck.components.bip = bipHealth.status;
        if (bipHealth.issues) {
          healthCheck.issues.push(...bipHealth.issues);
        }
      }

      // 检查市场服务
      if (this.marketplaceService) {
        const marketplaceHealth = await this.checkMarketplaceHealth();
        healthCheck.components.marketplace = marketplaceHealth.status;
        if (marketplaceHealth.issues) {
          healthCheck.issues.push(...marketplaceHealth.issues);
        }
      }

      // 版本控制功能暂时禁用
      // if (this.versionControl) {
      //   const versionHealth = await this.checkVersionControlHealth();
      //   healthCheck.components.versioning = versionHealth.status;
      //   if (versionHealth.issues) {
      //     healthCheck.issues.push(...versionHealth.issues);
      //   }
      // }

      // 确定整体健康状态
      const componentStatuses = Object.values(healthCheck.components);
      if (componentStatuses.includes('error')) {
        healthCheck.overall = 'error';
      } else if (componentStatuses.includes('warning')) {
        healthCheck.overall = 'warning';
      }

    } catch (error) {
      console.error('健康检查失败:', error);
      healthCheck.overall = 'error';
      healthCheck.issues.push({
        component: 'system',
        severity: 'critical',
        message: `系统健康检查失败: ${error.message}`,
        timestamp: Date.now()
      });
    }

    return healthCheck;
  }

  /**
   * 获取优化建议
   */
  public getOptimizationSuggestions(): OptimizationSuggestion[] {
    const suggestions: OptimizationSuggestion[] = [];
    const { performanceMetrics } = this.systemStatus;
    const { performanceThresholds } = this.config;

    // 内存优化建议
    if (performanceMetrics.memoryUsage > performanceThresholds.maxMemoryUsage * 0.8) {
      suggestions.push({
        type: 'memory',
        priority: 'high',
        description: '内存使用接近限制',
        action: '清理不活跃的数字人或降低纹理质量',
        estimatedImpact: {
          memory: -200,
          performance: 0.1
        }
      });
    }

    // 性能优化建议
    if (performanceMetrics.renderFPS < performanceThresholds.minFPS * 1.2) {
      suggestions.push({
        type: 'performance',
        priority: 'medium',
        description: '渲染性能可以进一步优化',
        action: '启用LOD系统或降低渲染质量',
        estimatedImpact: {
          performance: 0.2,
          quality: -0.1
        }
      });
    }

    // 网络优化建议
    if (performanceMetrics.networkLatency > 100) {
      suggestions.push({
        type: 'network',
        priority: 'medium',
        description: '网络延迟较高',
        action: '启用CDN或优化资源压缩',
        estimatedImpact: {
          performance: 0.1
        }
      });
    }

    return suggestions;
  }

  /**
   * 应用优化建议
   */
  public async applyOptimizationSuggestion(suggestion: OptimizationSuggestion): Promise<boolean> {
    try {
      console.log(`应用优化建议: ${suggestion.action}`);

      switch (suggestion.type) {
        case 'memory':
          await this.applyMemoryOptimization();
          break;
        case 'performance':
          await this.applyPerformanceOptimization();
          break;
        case 'network':
          await this.applyNetworkOptimization();
          break;
        case 'quality':
          await this.applyQualityOptimization();
          break;
      }

      this.emit('optimizationApplied', { suggestion });
      return true;

    } catch (error) {
      console.error('优化建议应用失败:', error);
      return false;
    }
  }

  /**
   * 应用内存优化
   */
  private async applyMemoryOptimization(): Promise<void> {
    // 清理不活跃的数字人
    this.cleanupInactiveDigitalHumans();

    // 降低纹理质量
    // TODO: 实现纹理质量降低逻辑

    console.log('内存优化已应用');
  }

  /**
   * 应用性能优化
   */
  private async applyPerformanceOptimization(): Promise<void> {
    // 启用LOD系统
    // TODO: 实现LOD系统启用逻辑

    // 降低渲染质量
    // TODO: 实现渲染质量降低逻辑

    console.log('性能优化已应用');
  }

  /**
   * 应用网络优化
   */
  private async applyNetworkOptimization(): Promise<void> {
    // 启用资源压缩
    // TODO: 实现资源压缩逻辑

    // 优化CDN配置
    // TODO: 实现CDN优化逻辑

    console.log('网络优化已应用');
  }

  /**
   * 应用质量优化
   */
  private async applyQualityOptimization(): Promise<void> {
    // 提升渲染质量
    // TODO: 实现渲染质量提升逻辑

    // 启用高级特效
    // TODO: 实现高级特效启用逻辑

    console.log('质量优化已应用');
  }

  // 健康检查辅助方法
  private async checkStorageHealth(): Promise<{ status: 'healthy' | 'warning' | 'error'; issues?: HealthIssue[] }> {
    // TODO: 实现存储健康检查
    return { status: 'healthy' };
  }

  private async checkBIPHealth(): Promise<{ status: 'healthy' | 'warning' | 'error'; issues?: HealthIssue[] }> {
    // TODO: 实现BIP健康检查
    return { status: 'healthy' };
  }

  private async checkMarketplaceHealth(): Promise<{ status: 'healthy' | 'warning' | 'error'; issues?: HealthIssue[] }> {
    // TODO: 实现市场健康检查
    return { status: 'healthy' };
  }

  private async checkVersionControlHealth(): Promise<{ status: 'healthy' | 'warning' | 'error'; issues?: HealthIssue[] }> {
    // TODO: 实现版本控制健康检查
    return { status: 'healthy' };
  }

  /**
   * 系统更新处理
   */
  public update(deltaTime: number): void {
    // 性能监控和多动作管理器功能暂时禁用
    // if (this.performanceMonitor) {
    //   this.performanceMonitor.update(deltaTime);
    // }
    // if (this.multiActionManager) {
    //   this.multiActionManager.update(deltaTime);
    // }

    // 更新活跃数字人
    for (const digitalHuman of Array.from(this.systemStatus.activeDigitalHumans.values())) {
      digitalHuman.update(deltaTime);
    }
  }

  /**
   * 系统销毁
   */
  public dispose(): void {
    // 性能监控功能暂时禁用
    // if (this.performanceMonitor) {
    //   (this.performanceMonitor as any).dispose();
    // }

    // 清理所有数字人
    for (const digitalHuman of Array.from(this.systemStatus.activeDigitalHumans.values())) {
      (digitalHuman as any).dispose();
    }
    this.systemStatus.activeDigitalHumans.clear();

    // 移除所有事件监听器
    this.removeAllListeners();

    console.log('数字人集成系统已销毁');
  }
}
