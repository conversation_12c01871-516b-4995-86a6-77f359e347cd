/**
 * 场景生成AI模型管理器
 * 整合场景理解、布局生成、资产匹配等AI模型，提供统一的场景生成接口
 */
import { AIModelManager, AIModelManagerConfig } from '../AIModelManager';
import { type World } from '../../core/World';
import { SceneGenerationOptions, SceneGenerationResult } from './SceneGenerationTypes';
/**
 * 场景生成AI管理器配置
 */
export interface SceneGenerationAIManagerConfig extends AIModelManagerConfig {
    /** 场景理解模型配置 */
    sceneUnderstanding?: any;
    /** 布局生成模型配置 */
    layoutGeneration?: any;
    /** 资产匹配模型配置 */
    assetMatching?: any;
    /** 是否启用实时生成 */
    enableRealTimeGeneration?: boolean;
    /** 生成质量级别 */
    qualityLevel?: 'fast' | 'balanced' | 'high';
}
/**
 * 场景生成AI模型管理器
 */
export declare class SceneGenerationAIManager extends AIModelManager {
    private sceneUnderstandingModel;
    private layoutGenerationModel;
    private assetMatchingModel;
    private sceneGenerationConfig;
    private initialized;
    constructor(world: World, config?: SceneGenerationAIManagerConfig);
    /**
     * 初始化场景生成相关模型
     */
    private initializeSceneModels;
    /**
     * 生成场景
     */
    generateScene(description: string, options?: SceneGenerationOptions): Promise<SceneGenerationResult>;
    /**
     * 理解场景描述
     */
    private understandScene;
    /**
     * 生成布局
     */
    private generateLayout;
    /**
     * 匹配资产
     */
    private matchAssets;
    /**
     * 构建场景
     */
    private buildScene;
    /**
     * 计算总体置信度
     */
    private calculateOverallConfidence;
    /**
     * 实时生成场景（流式）
     */
    generateSceneStream(description: string, options?: SceneGenerationOptions): AsyncGenerator<Partial<SceneGenerationResult>, SceneGenerationResult, unknown>;
    /**
     * 优化现有场景
     */
    optimizeScene(currentScene: any, feedback: string, options?: SceneGenerationOptions): Promise<SceneGenerationResult>;
    /**
     * 获取场景生成建议
     */
    getGenerationSuggestions(partialDescription: string): Promise<string[]>;
    /**
     * 验证场景描述
     */
    validateDescription(description: string): Promise<{
        isValid: boolean;
        issues: string[];
        suggestions: string[];
    }>;
    /**
     * 获取支持的场景类型
     */
    getSupportedSceneTypes(): string[];
    /**
     * 获取支持的风格
     */
    getSupportedStyles(): string[];
    /**
     * 销毁管理器
     */
    destroy(): Promise<void>;
    /**
     * 获取管理器状态
     */
    getStatus(): {
        initialized: boolean;
        models: {
            sceneUnderstanding: boolean;
            layoutGeneration: boolean;
            assetMatching: boolean;
        };
        config: SceneGenerationAIManagerConfig;
    };
}
