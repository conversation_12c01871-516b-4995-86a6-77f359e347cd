import { Injectable, Logger } from '@nestjs/common';

export enum CircuitState {
  CLOSED = 'CLOSED',
  OPEN = 'OPEN',
  HALF_OPEN = 'HALF_OPEN',
}

export interface CircuitBreakerConfig {
  failureThreshold: number; // 失败阈值
  resetTimeout: number; // 重置超时时间（毫秒）
  monitoringPeriod: number; // 监控周期（毫秒）
  expectedExceptionPredicate?: (error: Error) => boolean; // 预期异常判断
}

export interface CircuitBreakerStats {
  state: CircuitState;
  failureCount: number;
  successCount: number;
  lastFailureTime?: Date;
  lastSuccessTime?: Date;
  nextAttemptTime?: Date;
}

class CircuitBreakerImpl {
  private state: CircuitState = CircuitState.CLOSED;
  private failureCount = 0;
  private successCount = 0;
  private lastFailureTime?: Date;
  private lastSuccessTime?: Date;
  private nextAttemptTime?: Date;

  constructor(
    private readonly name: string,
    private readonly config: CircuitBreakerConfig,
    private readonly logger: Logger,
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === CircuitState.OPEN) {
      if (this.shouldAttemptReset()) {
        this.state = CircuitState.HALF_OPEN;
        this.logger.log(`熔断器半开: ${this.name}`);
      } else {
        throw new Error(`熔断器开启，拒绝请求: ${this.name}`);
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure(error);
      throw error;
    }
  }

  private onSuccess(): void {
    this.successCount++;
    this.lastSuccessTime = new Date();

    if (this.state === CircuitState.HALF_OPEN) {
      this.state = CircuitState.CLOSED;
      this.failureCount = 0;
      this.logger.log(`熔断器关闭: ${this.name}`);
    }
  }

  private onFailure(error: Error): void {
    // 检查是否是预期异常
    if (this.config.expectedExceptionPredicate && this.config.expectedExceptionPredicate(error)) {
      return;
    }

    this.failureCount++;
    this.lastFailureTime = new Date();

    if (this.state === CircuitState.HALF_OPEN) {
      this.state = CircuitState.OPEN;
      this.nextAttemptTime = new Date(Date.now() + this.config.resetTimeout);
      this.logger.warn(`熔断器重新开启: ${this.name}`);
    } else if (this.failureCount >= this.config.failureThreshold) {
      this.state = CircuitState.OPEN;
      this.nextAttemptTime = new Date(Date.now() + this.config.resetTimeout);
      this.logger.warn(`熔断器开启: ${this.name}, 失败次数: ${this.failureCount}`);
    }
  }

  private shouldAttemptReset(): boolean {
    return this.nextAttemptTime ? new Date() >= this.nextAttemptTime : false;
  }

  getStats(): CircuitBreakerStats {
    return {
      state: this.state,
      failureCount: this.failureCount,
      successCount: this.successCount,
      lastFailureTime: this.lastFailureTime,
      lastSuccessTime: this.lastSuccessTime,
      nextAttemptTime: this.nextAttemptTime,
    };
  }

  reset(): void {
    this.state = CircuitState.CLOSED;
    this.failureCount = 0;
    this.successCount = 0;
    this.lastFailureTime = undefined;
    this.nextAttemptTime = undefined;
    this.logger.log(`熔断器已重置: ${this.name}`);
  }
}

@Injectable()
export class CircuitBreakerService {
  private readonly logger = new Logger(CircuitBreakerService.name);
  private readonly circuitBreakers = new Map<string, CircuitBreakerImpl>();

  /**
   * 执行操作，使用熔断器保护
   */
  async execute<T>(
    name: string,
    operation: () => Promise<T>,
    config?: Partial<CircuitBreakerConfig>,
  ): Promise<T> {
    const circuitBreaker = this.getOrCreateCircuitBreaker(name, config);
    return circuitBreaker.execute(operation);
  }

  /**
   * 获取熔断器统计信息
   */
  getStats(name: string): CircuitBreakerStats | undefined {
    const circuitBreaker = this.circuitBreakers.get(name);
    return circuitBreaker?.getStats();
  }

  /**
   * 获取所有熔断器统计信息
   */
  getAllStats(): Record<string, CircuitBreakerStats> {
    const stats: Record<string, CircuitBreakerStats> = {};
    for (const [name, circuitBreaker] of this.circuitBreakers) {
      stats[name] = circuitBreaker.getStats();
    }
    return stats;
  }

  /**
   * 重置熔断器
   */
  reset(name: string): void {
    const circuitBreaker = this.circuitBreakers.get(name);
    if (circuitBreaker) {
      circuitBreaker.reset();
    }
  }

  /**
   * 重置所有熔断器
   */
  resetAll(): void {
    for (const circuitBreaker of this.circuitBreakers.values()) {
      circuitBreaker.reset();
    }
    this.logger.log('所有熔断器已重置');
  }

  /**
   * 移除熔断器
   */
  remove(name: string): void {
    this.circuitBreakers.delete(name);
    this.logger.log(`熔断器已移除: ${name}`);
  }

  /**
   * 获取或创建熔断器
   */
  private getOrCreateCircuitBreaker(
    name: string,
    config?: Partial<CircuitBreakerConfig>,
  ): CircuitBreakerImpl {
    let circuitBreaker = this.circuitBreakers.get(name);
    
    if (!circuitBreaker) {
      const defaultConfig: CircuitBreakerConfig = {
        failureThreshold: 5,
        resetTimeout: 60000, // 1分钟
        monitoringPeriod: 10000, // 10秒
      };

      const finalConfig = { ...defaultConfig, ...config };
      circuitBreaker = new CircuitBreakerImpl(name, finalConfig, this.logger);
      this.circuitBreakers.set(name, circuitBreaker);
      
      this.logger.log(`创建熔断器: ${name}`, finalConfig);
    }

    return circuitBreaker;
  }
}

/**
 * 熔断器装饰器
 */
export function CircuitBreaker(
  name: string,
  config?: Partial<CircuitBreakerConfig>,
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const circuitBreakerService: CircuitBreakerService = this.circuitBreakerService;
      
      if (!circuitBreakerService) {
        throw new Error('CircuitBreakerService not found. Make sure to inject it in your class.');
      }

      return circuitBreakerService.execute(
        `${target.constructor.name}.${propertyName}`,
        () => method.apply(this, args),
        config,
      );
    };

    return descriptor;
  };
}
