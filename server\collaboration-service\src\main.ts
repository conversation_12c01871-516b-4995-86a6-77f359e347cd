/**
 * 协作服务入口文件
 */
import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppModule } from './app.module';
import { WsAdapter } from '@nestjs/platform-ws';

async function bootstrap() {
  const logger = new Logger('Bootstrap');
  
  // 创建NestJS应用
  const app = await NestFactory.create(AppModule);
  
  // 使用WebSocket适配器
  app.useWebSocketAdapter(new WsAdapter(app));
  
  // 获取配置服务
  const configService = app.get(ConfigService);
  
  // 获取端口配置
  const port = configService.get<number>('PORT', 3005);
  
  // 启动应用
  await app.listen(port);
  
  logger.log(`协作服务已启动，监听端口: ${port}`);
}

bootstrap();
