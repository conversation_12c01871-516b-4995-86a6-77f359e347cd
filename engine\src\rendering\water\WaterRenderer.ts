/**
 * 水体渲染器
 * 用于渲染水体，包括反射、折射、波动等效果
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import type { Camera   } from '../Camera';
import { Scene } from '../../scene/Scene';
import { WaterBodyComponent, WaterBodyType } from '../../physics/water/WaterBodyComponent';
import { WaterMaterial } from './WaterMaterial';
import { Debug } from '../../utils/Debug';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';

/**
 * 水体渲染器配置
 */
export interface WaterRendererConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 是否启用反射 */
  enableReflection?: boolean;
  /** 是否启用折射 */
  enableRefraction?: boolean;
  /** 是否启用因果波纹 */
  enableCaustics?: boolean;
  /** 是否启用水下雾效 */
  enableUnderwaterFog?: boolean;
  /** 是否启用水下扭曲 */
  enableUnderwaterDistortion?: boolean;
  /** 是否启用深度测试 */
  enableDepthTest?: boolean;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
  /** 反射贴图分辨率 */
  reflectionMapResolution?: number;
  /** 折射贴图分辨率 */
  refractionMapResolution?: number;
  /** 是否使用延迟渲染 */
  useDeferredRendering?: boolean;
  /** 是否使用屏幕空间反射 */
  useScreenSpaceReflection?: boolean;
}

/**
 * 水体渲染器
 */
export class WaterRenderer extends System {
  /** 配置 */
  private config: WaterRendererConfig;
  /** 水体组件映射 */
  private waterBodies: Map<string, WaterBodyComponent> = new Map();
  /** 水体材质映射 */
  private waterMaterials: Map<string, WaterMaterial> = new Map();
  /** 反射相机 */
  private reflectionCamera: THREE.PerspectiveCamera | null = null;
  /** 折射相机 */
  private refractionCamera: THREE.PerspectiveCamera | null = null;
  /** 反射渲染目标 */
  private reflectionRenderTarget: THREE.WebGLRenderTarget | null = null;
  /** 折射渲染目标 */
  private refractionRenderTarget: THREE.WebGLRenderTarget | null = null;
  /** 深度渲染目标 */
  private depthRenderTarget: THREE.WebGLRenderTarget | null = null;
  /** 因果波纹渲染目标 */
  private causticsRenderTarget: THREE.WebGLRenderTarget | null = null;
  /** 帧计数器 */
  private frameCount: number = 0;
  /** 活跃相机 */
  private activeCamera: Camera | null = null;
  /** 活跃场景 */
  private activeScene: Scene | null = null;
  /** 渲染器 */
  private renderer: THREE.WebGLRenderer | null = null;
  /** 性能监视器 */
  private performanceMonitor: PerformanceMonitor = PerformanceMonitor.getInstance();
  /** 反射平面 */
  private reflectionPlane: THREE.Plane = new THREE.Plane();
  /** 折射平面 */
  private refractionPlane: THREE.Plane = new THREE.Plane();
  /** 临时矩阵 */
  private tempMatrix: THREE.Matrix4 = new THREE.Matrix4();

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config: WaterRendererConfig = {}) {
    super();

    // 设置世界引用
    this.world = world;

    // 设置配置
    this.config = {
      enabled: config.enabled !== undefined ? config.enabled : true,
      autoUpdate: config.autoUpdate !== undefined ? config.autoUpdate : true,
      updateFrequency: config.updateFrequency || 1,
      enableReflection: config.enableReflection !== undefined ? config.enableReflection : true,
      enableRefraction: config.enableRefraction !== undefined ? config.enableRefraction : true,
      enableCaustics: config.enableCaustics !== undefined ? config.enableCaustics : false,
      enableUnderwaterFog: config.enableUnderwaterFog !== undefined ? config.enableUnderwaterFog : true,
      enableUnderwaterDistortion: config.enableUnderwaterDistortion !== undefined ? config.enableUnderwaterDistortion : true,
      enableDepthTest: config.enableDepthTest !== undefined ? config.enableDepthTest : true,
      enablePerformanceMonitoring: config.enablePerformanceMonitoring !== undefined ? config.enablePerformanceMonitoring : false,
      reflectionMapResolution: config.reflectionMapResolution || 512,
      refractionMapResolution: config.refractionMapResolution || 512,
      useDeferredRendering: config.useDeferredRendering !== undefined ? config.useDeferredRendering : false,
      useScreenSpaceReflection: config.useScreenSpaceReflection !== undefined ? config.useScreenSpaceReflection : false
    };
    
    // 设置世界
    this.setWorld(world);
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    // 如果启用了性能监控，启动性能监视器
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMonitor.start();
    }

    // 创建反射和折射相机
    this.createCameras();

    // 创建渲染目标
    this.createRenderTargets();
  }

  /**
   * 创建相机
   */
  private createCameras(): void {
    // 创建反射相机
    this.reflectionCamera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);

    // 创建折射相机
    this.refractionCamera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
  }

  /**
   * 创建渲染目标
   */
  private createRenderTargets(): void {
    // 创建反射渲染目标
    this.reflectionRenderTarget = new THREE.WebGLRenderTarget(
      this.config.reflectionMapResolution!,
      this.config.reflectionMapResolution!
    );

    // 创建折射渲染目标
    this.refractionRenderTarget = new THREE.WebGLRenderTarget(
      this.config.refractionMapResolution!,
      this.config.refractionMapResolution!
    );

    // 创建深度渲染目标
    this.depthRenderTarget = new THREE.WebGLRenderTarget(512, 512);

    // 创建因果波纹渲染目标
    this.causticsRenderTarget = new THREE.WebGLRenderTarget(512, 512);
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.config.enabled || !this.config.autoUpdate) {
      return;
    }
    
    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.config.updateFrequency !== 0) {
      return;
    }
    
    // 如果没有活跃相机或场景，尝试查找
    if (!this.activeCamera || !this.activeScene) {
      this.findActiveCamera();
      this.findActiveScene();
    }

    // 如果没有渲染器，尝试获取
    if (!this.renderer && this.world) {
      // 从全局对象获取渲染器
      if (typeof window !== 'undefined' && (window as any).renderer) {
        this.renderer = (window as any).renderer;
      }
    }

    // 如果缺少必要组件，则不更新
    if (!this.activeCamera || !this.activeScene || !this.renderer) {
      return;
    }

    // 如果启用了性能监控，开始计时
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMonitor.beginMeasure('waterRenderUpdate');
    }

    // 更新所有水体
    this.updateWaterBodies(deltaTime);

    // 如果启用了性能监控，结束计时
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMonitor.endMeasure('waterRenderUpdate');
    }
  }

  /**
   * 查找活跃相机
   */
  private findActiveCamera(): void {
    if (!this.world) return;

    // 从世界中查找相机实体
    const entities = this.world.getEntities();
    for (const [, entity] of entities) {
      const camera = entity.getComponent('Camera') as any;
      if (camera) {
        this.activeCamera = camera as Camera;
        break;
      }
    }
  }

  /**
   * 查找活跃场景
   */
  private findActiveScene(): void {
    if (!this.world) return;

    // 获取活跃场景
    this.activeScene = this.world.getActiveScene();
  }

  /**
   * 更新水体
   * @param deltaTime 帧间隔时间
   */
  private updateWaterBodies(deltaTime: number): void {
    // 遍历所有水体组件并更新
    for (const [, waterBody] of this.waterBodies) {
      // 更新水体物理
      if (waterBody.update) {
        waterBody.update(deltaTime);
      }
    }
  }

  /**
   * 创建水体材质
   * @param _component 水体组件
   * @returns 水体材质
   */
  private createWaterMaterial(_component: WaterBodyComponent): WaterMaterial {
    return new WaterMaterial({
      // 使用默认配置
    });
  }

  /**
   * 添加水体组件
   * @param entity 实体
   * @param component 水体组件
   */
  public addWaterBody(entity: Entity, component: WaterBodyComponent): void {
    this.waterBodies.set(entity.id, component);

    // 创建水体材质
    const material = this.createWaterMaterial(component);
    this.waterMaterials.set(entity.id, material);

    Debug.log('WaterRenderer', `添加水体组件: ${entity.id}`);
  }

  /**
   * 移除水体组件
   * @param entity 实体
   */
  public removeWaterBody(entity: Entity): void {
    this.waterBodies.delete(entity.id);
    this.waterMaterials.delete(entity.id);
    
    Debug.log('WaterRenderer', `移除水体组件: ${entity.id}`);
  }

  // 其他方法将在后续实现
}
