/**
 * 多动作融合管理器
 * 管理数字人的多个动作融合、冲突解决和状态切换
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { type Entity } from '../../core/Entity';
import { type World } from '../../core/World';
import { BatchImportResult, PlayActionOptions, ActionLibraryItem } from './MultiActionFusionTypes';
/**
 * 融合管理器配置
 */
export interface FusionManagerConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 最大并发处理数 */
    maxConcurrentProcessing?: number;
    /** 自动解决冲突 */
    autoResolveConflicts?: boolean;
}
/**
 * 多动作融合管理器
 */
export declare class MultiActionFusionManager extends EventEmitter {
    /** 关联的实体 */
    private entity;
    /** 世界实例 */
    private world;
    /** 配置 */
    private config;
    /** 动作库 */
    private actionLibrary;
    /** 动作库项目 */
    private actionLibraryItems;
    /** 动画状态机 */
    private animationStateMachine;
    /** 冲突解决器 */
    private conflictResolver;
    /** BIP解析器 */
    private bipParser;
    /** BIP映射器 */
    private bipMapper;
    /** 统一骨骼系统 */
    private unifiedSkeleton;
    /** 处理队列 */
    private processingQueue;
    /** 当前处理中的文件 */
    private activeProcessing;
    /**
     * 构造函数
     * @param entity 关联实体
     * @param world 世界实例
     * @param config 配置
     */
    constructor(entity: Entity, world: World, config?: FusionManagerConfig);
    /**
     * 设置事件监听器
     */
    private setupEventListeners;
    /**
     * 批量导入BIP文件
     * @param files BIP文件列表
     * @returns 批量导入结果
     */
    importMultipleBIPFiles(files: File[]): Promise<BatchImportResult>;
    /**
     * 解析BIP文件
     * @param file BIP文件
     * @returns BIP数据
     */
    private parseBIPFile;
    /**
     * 创建统一骨骼系统
     * @param bipDataList BIP数据列表
     * @returns 统一骨骼系统
     */
    private createUnifiedSkeleton;
    /**
     * 构建骨骼层级
     * @param skeletonData 骨骼数据
     * @returns 骨骼层级
     */
    private buildBoneHierarchy;
    /**
     * 合并骨骼层级
     * @param hierarchies 层级列表
     * @returns 合并后的层级
     */
    private mergeBoneHierarchies;
    /**
     * 创建统一骨骼映射
     * @param bones 骨骼列表
     * @returns 骨骼映射
     */
    private createUnifiedBoneMapping;
    /**
     * 重定向BIP动画
     * @param animations BIP动画列表
     * @param skeleton 统一骨骼系统
     * @returns 重定向后的动画
     */
    private retargetBIPAnimations;
    /**
     * 应用冲突解决方案
     * @param resolutions 解决方案列表
     */
    private applyConflictResolutions;
    /**
     * 应用单个解决方案
     * @param resolution 解决方案
     */
    private applyResolution;
    /**
     * 重命名动作
     * @param oldName 旧名称
     * @param newName 新名称
     */
    private renameAction;
    /**
     * 应用骨骼重映射
     * @param remapping 重映射表
     */
    private applyBoneRemapping;
    /**
     * 构建动画状态机
     */
    private buildAnimationStateMachine;
    /**
     * 获取动作库项目列表
     * @returns 动作库项目数组
     */
    getActionLibraryItems(): ActionLibraryItem[];
    /**
     * 播放指定动作
     * @param actionName 动作名称
     * @param options 播放选项
     */
    playAction(actionName: string, options?: PlayActionOptions): Promise<void>;
    /**
     * 销毁管理器
     */
    dispose(): void;
}
