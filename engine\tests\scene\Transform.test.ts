/**
 * Transform类单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import * as THREE from 'three';
import { Transform } from '../../src/scene/Transform';
import { Entity } from '../../src/core/Entity';
import { World } from '../../src/core/World';
import { Engine } from '../../src/core/Engine';

describe('Transform', () => {
  let engine: Engine;
  let world: World;
  let entity: Entity;
  let transform: Transform;
  
  // 在每个测试前创建一个新的变换实例
  beforeEach(() => {
    // 创建引擎和世界
    engine = new Engine({
      autoStart: false,
      debug: true
    });
    world = engine.getWorld();
    
    // 创建实体
    entity = new Entity(world);

    // 获取实体的变换组件
    transform = entity.getTransform();

    // 设置初始变换
    transform.setPosition({ x: 1, y: 2, z: 3 });
    transform.setRotation({ x: 0.1, y: 0.2, z: 0.3 });
    transform.setScale({ x: 2, y: 2, z: 2 });
  });
  
  // 在每个测试后销毁引擎实例
  afterEach(() => {
    if (engine) {
      engine.dispose();
    }
  });
  
  // 测试变换初始化
  it('应该正确初始化变换', () => {
    expect(transform).toBeDefined();
    expect(transform.getEntity()).toBe(entity);
    expect(transform.position).toEqual({ x: 1, y: 2, z: 3 });
    expect(transform.rotation.x).toBeCloseTo(0.1);
    expect(transform.rotation.y).toBeCloseTo(0.2);
    expect(transform.rotation.z).toBeCloseTo(0.3);
    expect(transform.scale).toEqual({ x: 2, y: 2, z: 2 });
  });
  
  // 测试设置位置
  it('应该能够设置位置', () => {
    // 设置新位置
    transform.setPosition(4, 5, 6);
    
    // 验证位置已更改
    expect(transform.position).toEqual({ x: 4, y: 5, z: 6 });
    
    // 设置另一个位置
    transform.setPosition({ x: 7, y: 8, z: 9 });
    
    // 验证位置已更改
    expect(transform.position).toEqual({ x: 7, y: 8, z: 9 });
  });
  
  // 测试设置旋转
  it('应该能够设置旋转', () => {
    // 设置新旋转
    transform.setRotation(0.4, 0.5, 0.6);
    
    // 验证旋转已更改
    expect(transform.rotation.x).toBeCloseTo(0.4);
    expect(transform.rotation.y).toBeCloseTo(0.5);
    expect(transform.rotation.z).toBeCloseTo(0.6);
    
    // 设置另一个旋转
    transform.setRotation({ x: 0.7, y: 0.8, z: 0.9 });
    
    // 验证旋转已更改
    expect(transform.rotation.x).toBeCloseTo(0.7);
    expect(transform.rotation.y).toBeCloseTo(0.8);
    expect(transform.rotation.z).toBeCloseTo(0.9);
  });
  
  // 测试设置缩放
  it('应该能够设置缩放', () => {
    // 设置新缩放
    transform.setScale(3, 3, 3);
    
    // 验证缩放已更改
    expect(transform.scale).toEqual({ x: 3, y: 3, z: 3 });
    
    // 设置另一个缩放
    transform.setScale({ x: 4, y: 4, z: 4 });
    
    // 验证缩放已更改
    expect(transform.scale).toEqual({ x: 4, y: 4, z: 4 });
  });
  
  // 测试平移
  it('应该能够平移', () => {
    // 初始位置
    expect(transform.position).toEqual({ x: 1, y: 2, z: 3 });
    
    // 平移
    transform.translate(2, 2, 2);
    
    // 验证位置已更改
    expect(transform.position).toEqual({ x: 3, y: 4, z: 5 });
    
    // 再次平移
    transform.translate({ x: 1, y: 1, z: 1 });
    
    // 验证位置已更改
    expect(transform.position).toEqual({ x: 4, y: 5, z: 6 });
  });
  
  // 测试旋转
  it('应该能够旋转', () => {
    // 初始旋转
    const initialRotation = { ...transform.rotation };
    
    // 旋转
    transform.rotate(0.1, 0.1, 0.1);
    
    // 验证旋转已更改
    expect(transform.rotation.x).toBeCloseTo(initialRotation.x + 0.1);
    expect(transform.rotation.y).toBeCloseTo(initialRotation.y + 0.1);
    expect(transform.rotation.z).toBeCloseTo(initialRotation.z + 0.1);
    
    // 再次旋转
    transform.rotate({ x: 0.2, y: 0.2, z: 0.2 });
    
    // 验证旋转已更改
    expect(transform.rotation.x).toBeCloseTo(initialRotation.x + 0.3);
    expect(transform.rotation.y).toBeCloseTo(initialRotation.y + 0.3);
    expect(transform.rotation.z).toBeCloseTo(initialRotation.z + 0.3);
  });
  
  // 测试缩放
  it('应该能够缩放', () => {
    // 初始缩放
    expect(transform.scale).toEqual({ x: 2, y: 2, z: 2 });
    
    // 缩放
    transform.scale3d(2, 2, 2);
    
    // 验证缩放已更改
    expect(transform.scale).toEqual({ x: 4, y: 4, z: 4 });
    
    // 再次缩放
    transform.scale3d({ x: 0.5, y: 0.5, z: 0.5 });
    
    // 验证缩放已更改
    expect(transform.scale).toEqual({ x: 2, y: 2, z: 2 });
  });
  
  // 测试朝向
  it('应该能够朝向目标', () => {
    // 设置初始位置和旋转
    transform.setPosition(0, 0, 0);
    transform.setRotation(0, 0, 0);
    
    // 朝向目标
    transform.lookAt(0, 0, -1);
    
    // 验证旋转已更改（应该朝向z轴负方向）
    expect(transform.rotation.x).toBeCloseTo(0);
    expect(transform.rotation.y).toBeCloseTo(Math.PI);
    expect(transform.rotation.z).toBeCloseTo(0);
    
    // 朝向另一个目标
    transform.lookAt({ x: 1, y: 0, z: 0 });
    
    // 验证旋转已更改（应该朝向x轴正方向）
    expect(transform.rotation.x).toBeCloseTo(0);
    expect(transform.rotation.y).toBeCloseTo(-Math.PI / 2);
    expect(transform.rotation.z).toBeCloseTo(0);
  });
  
  // 测试获取前方向
  it('应该能够获取前方向', () => {
    // 设置初始位置和旋转
    transform.setPosition(0, 0, 0);
    transform.setRotation(0, 0, 0);
    
    // 获取前方向
    const forward = transform.getForward();
    
    // 验证前方向（应该是z轴负方向）
    expect(forward.x).toBeCloseTo(0);
    expect(forward.y).toBeCloseTo(0);
    expect(forward.z).toBeCloseTo(-1);
    
    // 旋转90度
    transform.setRotation(0, Math.PI / 2, 0);
    
    // 获取前方向
    const forward2 = transform.getForward();
    
    // 验证前方向（应该是x轴负方向）
    expect(forward2.x).toBeCloseTo(-1);
    expect(forward2.y).toBeCloseTo(0);
    expect(forward2.z).toBeCloseTo(0);
  });
  
  // 测试获取右方向
  it('应该能够获取右方向', () => {
    // 设置初始位置和旋转
    transform.setPosition(0, 0, 0);
    transform.setRotation(0, 0, 0);
    
    // 获取右方向
    const right = transform.getRight();
    
    // 验证右方向（应该是x轴正方向）
    expect(right.x).toBeCloseTo(1);
    expect(right.y).toBeCloseTo(0);
    expect(right.z).toBeCloseTo(0);
    
    // 旋转90度（绕Y轴顺时针）
    transform.setRotation(0, Math.PI / 2, 0);

    // 获取右方向
    const right2 = transform.getRight();

    // 验证右方向（应该是z轴负方向）
    expect(right2.x).toBeCloseTo(0);
    expect(right2.y).toBeCloseTo(0);
    expect(right2.z).toBeCloseTo(-1);
  });
  
  // 测试获取上方向
  it('应该能够获取上方向', () => {
    // 设置初始位置和旋转
    transform.setPosition(0, 0, 0);
    transform.setRotation(0, 0, 0);
    
    // 获取上方向
    const up = transform.getUp();
    
    // 验证上方向（应该是y轴正方向）
    expect(up.x).toBeCloseTo(0);
    expect(up.y).toBeCloseTo(1);
    expect(up.z).toBeCloseTo(0);
    
    // 旋转90度（绕x轴）
    transform.setRotation(Math.PI / 2, 0, 0);
    
    // 获取上方向
    const up2 = transform.getUp();
    
    // 验证上方向（应该是z轴正方向）
    expect(up2.x).toBeCloseTo(0);
    expect(up2.y).toBeCloseTo(0);
    expect(up2.z).toBeCloseTo(1);
  });
  
  // 测试获取世界位置
  it('应该能够获取世界位置', () => {
    // 创建父实体
    const parentEntity = new Entity(world);
    const parentTransform = new Transform({
      position: { x: 10, y: 10, z: 10 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    });
    parentEntity.addComponent(parentTransform);
    
    // 设置父子关系
    entity.setParent(parentEntity);
    
    // 获取世界位置
    const worldPosition = transform.getWorldPosition();
    
    // 验证世界位置（应该是父位置 + 本地位置）
    expect(worldPosition.x).toBeCloseTo(11);
    expect(worldPosition.y).toBeCloseTo(12);
    expect(worldPosition.z).toBeCloseTo(13);
  });
  
  // 测试获取世界旋转
  it('应该能够获取世界旋转', () => {
    // 创建父实体
    const parentEntity = new Entity(world);
    const parentTransform = new Transform({
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0.1, y: 0.1, z: 0.1 },
      scale: { x: 1, y: 1, z: 1 }
    });
    parentEntity.addComponent(parentTransform);
    
    // 设置父子关系
    entity.setParent(parentEntity);
    
    // 获取世界旋转
    const worldRotation = transform.getWorldRotation();
    
    // 验证世界旋转（应该是父旋转 + 本地旋转）
    expect(worldRotation.x).toBeCloseTo(0.2);
    expect(worldRotation.y).toBeCloseTo(0.3);
    expect(worldRotation.z).toBeCloseTo(0.4);
  });
  
  // 测试获取世界缩放
  it('应该能够获取世界缩放', () => {
    // 创建父实体
    const parentEntity = new Entity(world);
    const parentTransform = new Transform({
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 2, y: 2, z: 2 }
    });
    parentEntity.addComponent(parentTransform);
    
    // 设置父子关系
    entity.setParent(parentEntity);
    
    // 获取世界缩放
    const worldScale = transform.getWorldScale();
    
    // 验证世界缩放（应该是父缩放 * 本地缩放）
    expect(worldScale.x).toBeCloseTo(4);
    expect(worldScale.y).toBeCloseTo(4);
    expect(worldScale.z).toBeCloseTo(4);
  });
});
