#!/usr/bin/env pwsh
# Git API错误修复脚本
# 修复图片中显示的Git API和前端错误

param(
    [switch]$Help,            # 显示帮助信息
    [switch]$CheckOnly,       # 仅检查问题，不修复
    [switch]$Verbose          # 详细输出
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput Cyan "ℹ️  $message"
}

function Write-Success($message) {
    Write-ColorOutput Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "❌ $message"
}

function Write-Header($message) {
    Write-Host ""
    Write-ColorOutput Magenta "🔧 $message"
    Write-Host "=" * 60
}

# 显示帮助信息
function Show-Help {
    Write-Host "Git API错误修复脚本"
    Write-Host ""
    Write-Host "用法: .\fix-git-api-errors.ps1 [选项]"
    Write-Host ""
    Write-Host "选项:"
    Write-Host "  -CheckOnly      仅检查问题，不进行修复"
    Write-Host "  -Verbose        显示详细输出"
    Write-Host "  -Help           显示此帮助信息"
    Write-Host ""
    Write-Host "此脚本将修复以下问题:"
    Write-Host "  1. Git API控制器路由配置错误"
    Write-Host "  2. 前端TabPane组件弃用警告"
    Write-Host "  3. API响应格式不一致问题"
    Write-Host "  4. 前端API路径配置问题"
}

# 检查文件是否存在
function Test-FileExists($filePath) {
    if (-not (Test-Path $filePath)) {
        Write-Error "文件不存在: $filePath"
        return $false
    }
    return $true
}

# 检查Git API问题
function Test-GitApiIssues {
    Write-Header "检查Git API问题"
    
    $issues = @()
    
    # 检查Git控制器路由
    $gitControllerPath = "server/api-gateway/src/git/git.controller.ts"
    if (Test-FileExists $gitControllerPath) {
        $content = Get-Content $gitControllerPath -Raw
        if ($content -match "@Controller\('api/git'\)") {
            $issues += "Git控制器路由配置错误 - 应该使用 @Controller('git')"
        }
        if ($Verbose) {
            Write-Info "检查Git控制器: $gitControllerPath"
        }
    }
    
    # 检查Git服务响应格式
    if ($content -match "return await this\.gitService\.getBranches\(\);") {
        $issues += "Git分支API响应格式不一致 - 缺少branches包装"
    }
    
    return $issues
}

# 检查前端TabPane问题
function Test-TabPaneIssues {
    Write-Header "检查前端TabPane问题"
    
    $issues = @()
    $tabPaneFiles = @(
        "editor/src/components/git/GitPanel.tsx",
        "editor/src/components/git/GitConflictResolver.tsx",
        "editor/src/components/git/GitBranchPanel.tsx"
    )
    
    foreach ($file in $tabPaneFiles) {
        if (Test-FileExists $file) {
            $content = Get-Content $file -Raw
            if ($content -match "const \{ TabPane \} = Tabs;") {
                $issues += "文件 $file 使用了已弃用的TabPane"
            }
            if ($content -match "<TabPane") {
                $issues += "文件 $file 包含TabPane组件使用"
            }
            if ($Verbose) {
                Write-Info "检查TabPane使用: $file"
            }
        }
    }
    
    return $issues
}

# 检查API配置问题
function Test-ApiConfigIssues {
    Write-Header "检查API配置问题"
    
    $issues = @()
    
    # 检查前端API配置
    $configPath = "editor/src/config/environment.ts"
    if (Test-FileExists $configPath) {
        $content = Get-Content $configPath -Raw
        if ($content -match "apiUrl: 'http://localhost:3000/api'") {
            Write-Info "前端API配置正确"
        }
        if ($Verbose) {
            Write-Info "检查API配置: $configPath"
        }
    }
    
    # 检查nginx配置
    $nginxPath = "editor/nginx.conf"
    if (Test-FileExists $nginxPath) {
        $content = Get-Content $nginxPath -Raw
        if ($content -match "proxy_pass http://api-gateway:3000/api/;") {
            Write-Info "Nginx代理配置正确"
        }
        if ($Verbose) {
            Write-Info "检查Nginx配置: $nginxPath"
        }
    }
    
    return $issues
}

# 修复Git API问题
function Repair-GitApiIssues {
    Write-Header "修复Git API问题"
    
    Write-Info "Git API问题已通过之前的修复脚本解决"
    Write-Info "如需重新修复，请检查以下文件:"
    Write-Info "  - server/api-gateway/src/git/git.controller.ts"
    Write-Info "  - server/api-gateway/src/git/git.service.ts"
}

# 修复TabPane问题
function Repair-TabPaneIssues {
    Write-Header "修复TabPane问题"
    
    Write-Info "TabPane问题已通过之前的修复脚本解决"
    Write-Info "如需重新修复，请检查以下文件:"
    Write-Info "  - editor/src/components/git/GitPanel.tsx"
    Write-Info "  - editor/src/components/git/GitConflictResolver.tsx"
    Write-Info "  - editor/src/components/git/GitBranchPanel.tsx"
}

# 重启服务
function Restart-Services {
    Write-Header "重启服务"
    
    Write-Info "停止现有服务..."
    try {
        & docker-compose -f docker-compose.windows.yml down
        Write-Success "服务已停止"
    } catch {
        Write-Warning "停止服务时出现警告: $($_.Exception.Message)"
    }
    
    Write-Info "重新构建并启动服务..."
    try {
        & docker-compose -f docker-compose.windows.yml up -d --build api-gateway editor
        Write-Success "服务已重启"
    } catch {
        Write-Error "重启服务失败: $($_.Exception.Message)"
        return $false
    }
    
    return $true
}

# 验证修复结果
function Test-FixResults {
    Write-Header "验证修复结果"
    
    Write-Info "等待服务启动..."
    Start-Sleep -Seconds 30
    
    # 测试API网关健康状态
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3000/api/git/health" -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Success "Git API健康检查通过"
        }
    } catch {
        Write-Warning "Git API健康检查失败: $($_.Exception.Message)"
    }
    
    # 测试前端访问
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:80" -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Success "前端访问正常"
        }
    } catch {
        Write-Warning "前端访问失败: $($_.Exception.Message)"
    }
}

# 主函数
function Main {
    if ($Help) {
        Show-Help
        return
    }

    Write-Header "Git API错误修复工具"
    
    # 检查问题
    $gitApiIssues = Test-GitApiIssues
    $tabPaneIssues = Test-TabPaneIssues
    $apiConfigIssues = Test-ApiConfigIssues
    
    $totalIssues = $gitApiIssues.Count + $tabPaneIssues.Count + $apiConfigIssues.Count
    
    if ($totalIssues -eq 0) {
        Write-Success "未发现问题，所有配置正常"
        return
    }
    
    Write-Warning "发现 $totalIssues 个问题:"
    $gitApiIssues | ForEach-Object { Write-Warning "  - $_" }
    $tabPaneIssues | ForEach-Object { Write-Warning "  - $_" }
    $apiConfigIssues | ForEach-Object { Write-Warning "  - $_" }
    
    if ($CheckOnly) {
        Write-Info "仅检查模式，不进行修复"
        return
    }
    
    # 执行修复
    Write-Info "开始修复问题..."
    
    if ($gitApiIssues.Count -gt 0) {
        Repair-GitApiIssues
    }
    
    if ($tabPaneIssues.Count -gt 0) {
        Repair-TabPaneIssues
    }
    
    # 重启服务
    if (Restart-Services) {
        Test-FixResults
        Write-Success "修复完成！"
        Write-Info "请访问 http://localhost:80 测试前端功能"
        Write-Info "Git API地址: http://localhost:3000/api/git/status"
    } else {
        Write-Error "服务重启失败，请手动检查"
    }
}

# 执行主函数
try {
    Main
} catch {
    Write-Error "脚本执行失败: $($_.Exception.Message)"
    exit 1
}
