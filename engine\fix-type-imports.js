#!/usr/bin/env node

/**
 * 修复错误的type导入
 * 将需要作为值使用的类型改回正常导入
 */

const fs = require('fs');
const path = require('path');

/**
 * 需要作为值使用的类型列表
 */
const valueTypes = ['Entity', 'Transform', 'World', 'PhysicsBody', 'Camera'];

/**
 * 修复文件中的type导入
 */
function fixTypeImports(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // 检查文件内容是否使用了这些类型作为值
  for (const type of valueTypes) {
    // 检查是否有 new Type() 的使用
    const newPattern = new RegExp(`new\\s+${type}\\s*\\(`, 'g');
    // 检查是否有 Type.type 的使用
    const staticPattern = new RegExp(`${type}\\.type`, 'g');
    // 检查是否有 instanceof Type 的使用
    const instanceofPattern = new RegExp(`instanceof\\s+${type}`, 'g');
    
    if (newPattern.test(content) || staticPattern.test(content) || instanceofPattern.test(content)) {
      // 将 import { type Type } 改为 import { Type }
      const typeImportPattern = new RegExp(`import\\s*{\\s*type\\s+${type}\\s*}\\s*from`, 'g');
      const newContent = content.replace(typeImportPattern, `import { ${type} } from`);
      
      if (newContent !== content) {
        content = newContent;
        modified = true;
        console.log(`修复 ${type} 的导入: ${path.relative(process.cwd(), filePath)}`);
      }
      
      // 也处理混合导入的情况，如 import { type Type, OtherType }
      const mixedPattern = new RegExp(`import\\s*{\\s*([^}]*?)\\btype\\s+${type}\\b([^}]*?)}\\s*from`, 'g');
      const newContent2 = content.replace(mixedPattern, (match, before, after) => {
        const cleanBefore = before.replace(/,\s*$/, '').trim();
        const cleanAfter = after.replace(/^\s*,/, '').trim();
        
        let imports = [];
        if (cleanBefore) imports.push(cleanBefore);
        imports.push(type);
        if (cleanAfter) imports.push(cleanAfter);
        
        return `import { ${imports.join(', ')} } from`;
      });
      
      if (newContent2 !== content) {
        content = newContent2;
        modified = true;
        console.log(`修复混合导入中的 ${type}: ${path.relative(process.cwd(), filePath)}`);
      }
    }
  }
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
  }
  
  return modified;
}

/**
 * 递归获取所有TypeScript文件
 */
function getAllTsFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      getAllTsFiles(fullPath, files);
    } else if (stat.isFile() && item.endsWith('.ts') && !item.endsWith('.d.ts')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

/**
 * 主函数
 */
function main() {
  const srcDir = path.join(__dirname, 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('src目录不存在');
    process.exit(1);
  }
  
  console.log('🔧 开始修复type导入错误...\n');
  
  const files = getAllTsFiles(srcDir);
  console.log(`📁 找到 ${files.length} 个TypeScript文件`);
  
  let fixedCount = 0;
  for (const file of files) {
    if (fixTypeImports(file)) {
      fixedCount++;
    }
  }
  
  console.log(`\n🎉 修复完成！`);
  console.log(`📊 已修复 ${fixedCount} 个文件`);
  
  if (fixedCount > 0) {
    console.log('\n💡 建议运行以下命令检查编译状态：');
    console.log('npm run build');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixTypeImports, getAllTsFiles };
