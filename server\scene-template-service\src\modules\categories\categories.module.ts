import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CategoriesService } from './categories.service';
import { CategoriesController } from './categories.controller';
import { TemplateCategory } from './entities/template-category.entity';
import { CacheService } from '../../common/services/cache.service';
import { LoggerService } from '../../common/services/logger.service';

@Module({
  imports: [TypeOrmModule.forFeature([TemplateCategory])],
  controllers: [CategoriesController],
  providers: [
    CategoriesService,
    CacheService,
    LoggerService,
  ],
  exports: [CategoriesService, TypeOrmModule],
})
export class CategoriesModule {}
