#!/usr/bin/env node

/**
 * 修复错误的super调用
 */

const fs = require('fs');
const path = require('path');

/**
 * 递归获取所有TypeScript文件
 */
function getAllTsFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      getAllTsFiles(fullPath, files);
    } else if (stat.isFile() && item.endsWith('.ts') && !item.endsWith('.d.ts')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

/**
 * 修复文件中的super调用
 */
function fixSuperCalls(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // 修复 (super as any).dispose() 为 super.dispose()
  const pattern = /\(super\s+as\s+any\)\.dispose\(\)/g;
  const newContent = content.replace(pattern, 'super.dispose()');
  
  if (newContent !== content) {
    fs.writeFileSync(filePath, newContent, 'utf8');
    console.log(`已修复: ${path.relative(process.cwd(), filePath)}`);
    modified = true;
  }
  
  return modified;
}

/**
 * 主函数
 */
function main() {
  const srcDir = path.join(__dirname, 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('src目录不存在');
    process.exit(1);
  }
  
  console.log('🔧 开始修复super调用错误...\n');
  
  const files = getAllTsFiles(srcDir);
  console.log(`📁 找到 ${files.length} 个TypeScript文件`);
  
  let fixedCount = 0;
  for (const file of files) {
    if (fixSuperCalls(file)) {
      fixedCount++;
    }
  }
  
  console.log(`\n🎉 修复完成！`);
  console.log(`📊 已修复 ${fixedCount} 个文件`);
  
  if (fixedCount > 0) {
    console.log('\n💡 建议运行以下命令检查编译状态：');
    console.log('npm run build');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixSuperCalls, getAllTsFiles };
