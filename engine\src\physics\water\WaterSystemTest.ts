/**
 * 水体系统完整测试
 * 测试水体预设、生成器和管理器的集成功能
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { WaterPresets, WaterPresetType } from './WaterPresets';
import { WaterGenerator } from './WaterGenerator';
import { WaterManager } from './WaterManager';

/**
 * 水体系统测试类
 */
export class WaterSystemTest {
  private static manager: WaterManager;
  private static scene: THREE.Scene;

  /**
   * 运行所有测试
   */
  public static async runAllTests(): Promise<void> {
    console.log('🌊 开始运行水体系统完整测试...\n');

    try {
      await this.setupTestEnvironment();
      await this.testWaterPresets();
      await this.testWaterGenerator();
      await this.testWaterManager();
      await this.testWaterScenes();
      await this.testConfigImportExport();
      
      console.log('🎉 所有水体系统测试通过！');
    } catch (error) {
      console.error('❌ 水体系统测试失败:', error);
    }
  }

  /**
   * 设置测试环境
   */
  private static async setupTestEnvironment(): Promise<void> {
    console.log('🔧 设置测试环境...');
    
    // 创建场景
    this.scene = new THREE.Scene();
    
    // 初始化管理器
    this.manager = WaterManager.getInstance();
    this.manager.initialize(this.scene);
    
    console.log('✅ 测试环境设置完成\n');
  }

  /**
   * 测试水体预设
   */
  private static async testWaterPresets(): Promise<void> {
    console.log('📋 测试水体预设系统...');
    
    // 测试所有预设类型
    const allTypes = WaterPresets.getAllPresetTypes();
    console.log(`   发现 ${allTypes.length} 个预设类型`);
    
    // 测试每个预设的配置
    for (const type of allTypes) {
      const config = WaterPresets.getPresetConfig(type);
      const displayName = WaterPresets.getPresetDisplayName(type);
      
      if (!config || !displayName) {
        throw new Error(`预设 ${type} 配置不完整`);
      }
      
      console.log(`   ✅ ${type} (${displayName}) - 配置完整`);
    }
    
    console.log('✅ 水体预设系统测试通过\n');
  }

  /**
   * 测试水体生成器
   */
  private static async testWaterGenerator(): Promise<void> {
    console.log('🏗️ 测试水体生成器...');
    
    // 测试基本生成功能
    const lakeResult = WaterGenerator.generateLake();
    if (!lakeResult.entity || !lakeResult.waterBody) {
      throw new Error('湖泊生成失败');
    }
    console.log('   ✅ 湖泊生成成功');
    
    const riverResult = WaterGenerator.generateRiver();
    if (!riverResult.entity || !riverResult.waterBody) {
      throw new Error('河流生成失败');
    }
    console.log('   ✅ 河流生成成功');
    
    const oceanResult = WaterGenerator.generateOcean();
    if (!oceanResult.entity || !oceanResult.waterBody) {
      throw new Error('海洋生成失败');
    }
    console.log('   ✅ 海洋生成成功');
    
    const poolResult = WaterGenerator.generatePool();
    if (!poolResult.entity || !poolResult.waterBody) {
      throw new Error('游泳池生成失败');
    }
    console.log('   ✅ 游泳池生成成功');
    
    // 测试自定义配置生成
    const customResult = WaterGenerator.generateWater({
      preset: WaterPresetType.LAKE,
      position: new THREE.Vector3(10, 0, 10),
      size: { width: 15, height: 2, depth: 15 },
      physicsParams: { density: 1100, viscosity: 1.2 },
      entityName: '自定义湖泊'
    });
    
    if (!customResult.entity || !customResult.waterBody) {
      throw new Error('自定义水体生成失败');
    }
    console.log('   ✅ 自定义水体生成成功');
    
    // 测试批量生成
    const batchConfigs = [
      { preset: WaterPresetType.LAKE, entityName: '批量湖泊1' },
      { preset: WaterPresetType.RIVER, entityName: '批量河流1' },
      { preset: WaterPresetType.POOL, entityName: '批量游泳池1' }
    ];
    
    const batchResults = WaterGenerator.generateMultipleWaters(batchConfigs);
    if (batchResults.length !== 3) {
      throw new Error('批量生成失败');
    }
    console.log('   ✅ 批量生成成功');
    
    // 测试场景生成
    const lakeScene = WaterGenerator.generateWaterScene('lake_scene');
    if (lakeScene.length === 0) {
      throw new Error('湖泊场景生成失败');
    }
    console.log('   ✅ 场景生成成功');
    
    console.log('✅ 水体生成器测试通过\n');
  }

  /**
   * 测试水体管理器
   */
  private static async testWaterManager(): Promise<void> {
    console.log('🎛️ 测试水体管理器...');
    
    // 清空现有水体
    this.manager.clearAllWaters();
    
    // 测试添加水体
    const waterInfo1 = this.manager.addWater({
      preset: WaterPresetType.LAKE,
      entityName: '管理器测试湖泊',
      position: new THREE.Vector3(0, 0, 0)
    });
    
    if (!waterInfo1) {
      throw new Error('添加水体失败');
    }
    console.log('   ✅ 添加水体成功');
    
    // 测试获取水体
    const retrievedWater = this.manager.getWater(waterInfo1.id);
    if (!retrievedWater || retrievedWater.id !== waterInfo1.id) {
      throw new Error('获取水体失败');
    }
    console.log('   ✅ 获取水体成功');
    
    // 测试更新水体
    const updateSuccess = this.manager.updateWater(waterInfo1.id, {
      size: { width: 25, height: 3, depth: 25 },
      physicsParams: { density: 1200 }
    });
    
    if (!updateSuccess) {
      throw new Error('更新水体失败');
    }
    console.log('   ✅ 更新水体成功');
    
    // 添加更多水体进行统计测试
    this.manager.addWater({
      preset: WaterPresetType.RIVER,
      entityName: '管理器测试河流'
    });
    
    this.manager.addWater({
      preset: WaterPresetType.POOL,
      entityName: '管理器测试游泳池'
    });
    
    // 测试统计功能
    const stats = this.manager.getStats();
    if (stats.totalWaterBodies !== 3) {
      throw new Error(`期望3个水体，实际${stats.totalWaterBodies}个`);
    }
    console.log('   ✅ 统计功能正常');
    console.log(`     总水体数: ${stats.totalWaterBodies}`);
    console.log(`     活跃水体数: ${stats.activeWaterBodies}`);
    console.log(`     总体积: ${stats.totalVolume.toFixed(2)}`);
    console.log(`     平均密度: ${stats.averageDensity.toFixed(2)}`);
    
    // 测试按类型获取
    const lakes = this.manager.getWatersByType(WaterPresetType.LAKE);
    if (lakes.length !== 1) {
      throw new Error('按类型获取水体失败');
    }
    console.log('   ✅ 按类型获取成功');
    
    // 测试移除水体
    const removeSuccess = this.manager.removeWater(waterInfo1.id);
    if (!removeSuccess) {
      throw new Error('移除水体失败');
    }
    console.log('   ✅ 移除水体成功');
    
    console.log('✅ 水体管理器测试通过\n');
  }

  /**
   * 测试水体场景
   */
  private static async testWaterScenes(): Promise<void> {
    console.log('🏞️ 测试水体场景...');
    
    // 清空现有水体
    this.manager.clearAllWaters();
    
    // 测试不同环境的推荐配置
    const environments = ['indoor', 'outdoor', 'underground', 'fantasy'] as const;
    
    for (const env of environments) {
      const recommendations = WaterGenerator.getRecommendedConfig(env);
      if (recommendations.length === 0) {
        throw new Error(`${env} 环境没有推荐配置`);
      }
      
      console.log(`   ✅ ${env} 环境推荐配置: ${recommendations.length} 个`);
      
      // 生成推荐的第一个水体
      const waterInfo = this.manager.addWater(recommendations[0]);
      if (!waterInfo) {
        throw new Error(`${env} 环境水体生成失败`);
      }
    }
    
    console.log('✅ 水体场景测试通过\n');
  }

  /**
   * 测试配置导入导出
   */
  private static async testConfigImportExport(): Promise<void> {
    console.log('💾 测试配置导入导出...');
    
    // 清空并添加一些测试水体
    this.manager.clearAllWaters();
    
    this.manager.addWater({
      preset: WaterPresetType.LAKE,
      entityName: '导出测试湖泊',
      position: new THREE.Vector3(5, 0, 5),
      size: { width: 20, height: 2, depth: 20 }
    });
    
    this.manager.addWater({
      preset: WaterPresetType.RIVER,
      entityName: '导出测试河流',
      position: new THREE.Vector3(-5, 0, -5),
      size: { width: 3, height: 1, depth: 30 }
    });
    
    // 导出配置
    const exportedConfig = this.manager.exportConfig();
    if (!exportedConfig || !exportedConfig.waters || exportedConfig.waters.length !== 2) {
      throw new Error('配置导出失败');
    }
    console.log('   ✅ 配置导出成功');
    
    // 导入配置
    this.manager.importConfig(exportedConfig);
    
    const stats = this.manager.getStats();
    if (stats.totalWaterBodies !== 2) {
      throw new Error('配置导入失败');
    }
    console.log('   ✅ 配置导入成功');
    
    console.log('✅ 配置导入导出测试通过\n');
  }

  /**
   * 性能测试
   */
  public static async performanceTest(): Promise<void> {
    console.log('⚡ 开始性能测试...');
    
    const manager = WaterManager.getInstance();
    manager.clearAllWaters();
    
    // 测试大量水体生成
    const startTime = performance.now();
    
    const configs = [];
    for (let i = 0; i < 100; i++) {
      configs.push({
        preset: WaterPresetType.LAKE,
        entityName: `性能测试湖泊_${i}`,
        position: new THREE.Vector3(
          Math.random() * 100 - 50,
          0,
          Math.random() * 100 - 50
        )
      });
    }
    
    const results = WaterGenerator.generateMultipleWaters(configs);
    
    const generationTime = performance.now() - startTime;
    console.log(`   生成100个水体耗时: ${generationTime.toFixed(2)}ms`);
    
    // 添加到管理器
    const addStartTime = performance.now();
    results.forEach(result => {
      manager.addWater({
        preset: WaterPresetType.LAKE,
        entityId: result.entity.id,
        entityName: result.entity.name
      });
    });
    
    const addTime = performance.now() - addStartTime;
    console.log(`   添加100个水体到管理器耗时: ${addTime.toFixed(2)}ms`);
    
    // 测试更新性能
    const updateStartTime = performance.now();
    manager.update(0.016); // 模拟60FPS
    const updateTime = performance.now() - updateStartTime;
    console.log(`   更新100个水体耗时: ${updateTime.toFixed(2)}ms`);
    
    // 清理
    manager.clearAllWaters();
    
    console.log('✅ 性能测试完成\n');
  }
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined' && typeof global !== 'undefined') {
  // Node.js 环境
  WaterSystemTest.runAllTests().then(() => {
    return WaterSystemTest.performanceTest();
  }).catch(console.error);
}
