/**
 * 动作捕捉姿势解算
 * 将关键点数据转换为骨骼旋转
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { LandmarkData, WorldLandmarkData, smoothLandmarks    } from '../types/LandmarkData';
import { LandmarkIndices } from '../constants/LandmarkIndices';
import { MotionCaptureComponent } from '../components/MotionCaptureComponent';
import { AvatarRigComponent } from '../../avatar/components/AvatarRigComponent';
import { VRMHumanBoneName } from '../../avatar/types/VRMHumanBoneName';
import { Debug } from '../../utils/Debug';

// 临时变量，避免重复创建
const tempVector = new THREE.Vector3();
const tempVector2 = new THREE.Vector3();
const tempVector3 = new THREE.Vector3();
// @ts-ignore: 预留功能，暂未使用
const tempQuaternion = new THREE.Quaternion();
// @ts-ignore: 预留功能，暂未使用
const tempQuaternion2 = new THREE.Quaternion();
const tempMatrix = new THREE.Matrix4();

// 旋转常量
// @ts-ignore: 预留功能，暂未使用
const Y_180_ROTATION = new THREE.Quaternion().setFromAxisAngle(new THREE.Vector3(0, 1, 0), Math.PI);
const X_90_ROTATION = new THREE.Quaternion().setFromAxisAngle(new THREE.Vector3(1, 0, 0), Math.PI / 2);

// 可见度阈值
const DEFAULT_VISIBILITY_THRESHOLD = 0.1;

/**
 * 解算动作捕捉姿势
 * @param entity 实体
 * @param worldLandmarks 世界坐标系关键点数据
 * @param screenLandmarks 屏幕坐标系关键点数据
 * @param smoothingFactor 平滑系数
 * @param visibilityThreshold 可见度阈值
 */
export function solveMotionCapturePose(
  entity: Entity,
  worldLandmarks: WorldLandmarkData[],
  screenLandmarks: LandmarkData[],
  smoothingFactor: number = 0.5,
  visibilityThreshold: number = DEFAULT_VISIBILITY_THRESHOLD
): void {
  // 获取动作捕捉组件
  const motionCaptureComponent = entity.getComponent<MotionCaptureComponent>(MotionCaptureComponent.TYPE);
  if (!motionCaptureComponent) {
    Debug.warn('solveMotionCapturePose', `Entity ${entity.id} does not have MotionCaptureComponent`);
    return;
  }

  // 获取骨骼组件
  const rigComponent = entity.getComponent<AvatarRigComponent>(AvatarRigComponent.type);
  if (!rigComponent) {
    Debug.warn('solveMotionCapturePose', `Entity ${entity.id} does not have AvatarRigComponent`);
    return;
  }

  // 平滑关键点数据
  const smoothedWorldLandmarks = smoothLandmarks(
    worldLandmarks,
    motionCaptureComponent.prevWorldLandmarks,
    smoothingFactor
  );

  const smoothedScreenLandmarks = smoothLandmarks(
    screenLandmarks,
    motionCaptureComponent.prevLandmarks,
    smoothingFactor
  );

  // 更新组件中的关键点数据
  motionCaptureComponent.setWorldLandmarks(smoothedWorldLandmarks);
  motionCaptureComponent.setLandmarks(smoothedScreenLandmarks);

  // 计算最低的Y坐标，用于地面对齐
  const lowestY = calculateLowestY(smoothedWorldLandmarks);

  // 检测是否应该解算下半身
  const shouldEstimateLowerBody = checkShouldEstimateLowerBody(smoothedWorldLandmarks, visibilityThreshold);
  motionCaptureComponent.setSolvingLowerBody(shouldEstimateLowerBody);

  // 计算地面脚部位置
  calculateGroundedFeet(smoothedWorldLandmarks, motionCaptureComponent);

  // 解算头部
  solveHead(entity, smoothedWorldLandmarks, motionCaptureComponent);

  // 解算脊柱
  solveSpine(entity, lowestY, smoothedWorldLandmarks, shouldEstimateLowerBody, motionCaptureComponent);

  // 解算左臂
  solveLeftArm(entity, lowestY, smoothedWorldLandmarks, motionCaptureComponent, visibilityThreshold);

  // 解算右臂
  solveRightArm(entity, lowestY, smoothedWorldLandmarks, motionCaptureComponent, visibilityThreshold);

  // 如果应该解算下半身
  if (shouldEstimateLowerBody) {
    // 解算左腿
    solveLeftLeg(entity, lowestY, smoothedWorldLandmarks, motionCaptureComponent, visibilityThreshold);

    // 解算右腿
    solveRightLeg(entity, lowestY, smoothedWorldLandmarks, motionCaptureComponent, visibilityThreshold);
  }
}

/**
 * 计算最低的Y坐标
 * @param landmarks 关键点数据
 * @returns 最低的Y坐标
 */
function calculateLowestY(landmarks: WorldLandmarkData[]): number {
  let lowestY = 0;
  for (const landmark of landmarks) {
    if (landmark.visibility && landmark.visibility > DEFAULT_VISIBILITY_THRESHOLD) {
      lowestY = Math.max(lowestY, landmark.y);
    }
  }
  return lowestY;
}

/**
 * 检查是否应该解算下半身
 * @param landmarks 关键点数据
 * @param threshold 可见度阈值
 * @returns 是否应该解算下半身
 */
function checkShouldEstimateLowerBody(landmarks: WorldLandmarkData[], threshold: number): boolean {
  // 检查下半身关键点的可见度
  const lowerBodyLandmarks = [
    landmarks[LandmarkIndices.LEFT_HIP],
    landmarks[LandmarkIndices.RIGHT_HIP],
    landmarks[LandmarkIndices.LEFT_KNEE],
    landmarks[LandmarkIndices.RIGHT_KNEE],
    landmarks[LandmarkIndices.LEFT_ANKLE],
    landmarks[LandmarkIndices.RIGHT_ANKLE]
  ];

  // 计算平均可见度
  let visibleCount = 0;
  let totalCount = 0;

  for (const landmark of lowerBodyLandmarks) {
    if (landmark && landmark.visibility !== undefined) {
      totalCount++;
      if (landmark.visibility > threshold) {
        visibleCount++;
      }
    }
  }

  // 如果超过一半的关键点可见，则解算下半身
  return visibleCount > totalCount / 2;
}

/**
 * 计算地面脚部位置
 * @param landmarks 关键点数据
 * @param component 动作捕捉组件
 */
function calculateGroundedFeet(landmarks: WorldLandmarkData[], component: MotionCaptureComponent): void {
  // 获取脚踝和脚尖的关键点
  const leftAnkle = landmarks[LandmarkIndices.LEFT_ANKLE];
  const rightAnkle = landmarks[LandmarkIndices.RIGHT_ANKLE];
  const leftFoot = landmarks[LandmarkIndices.LEFT_FOOT_INDEX];
  const rightFoot = landmarks[LandmarkIndices.RIGHT_FOOT_INDEX];

  // 计算脚部偏移
  let footOffset = 0;
  let validPoints = 0;

  if (leftAnkle && leftAnkle.visibility && leftAnkle.visibility > DEFAULT_VISIBILITY_THRESHOLD) {
    footOffset += leftAnkle.y;
    validPoints++;
  }

  if (rightAnkle && rightAnkle.visibility && rightAnkle.visibility > DEFAULT_VISIBILITY_THRESHOLD) {
    footOffset += rightAnkle.y;
    validPoints++;
  }

  if (leftFoot && leftFoot.visibility && leftFoot.visibility > DEFAULT_VISIBILITY_THRESHOLD) {
    footOffset += leftFoot.y;
    validPoints++;
  }

  if (rightFoot && rightFoot.visibility && rightFoot.visibility > DEFAULT_VISIBILITY_THRESHOLD) {
    footOffset += rightFoot.y;
    validPoints++;
  }

  // 计算平均偏移
  if (validPoints > 0) {
    footOffset /= validPoints;
    component.setFootOffset(footOffset);
  }
}

/**
 * 解算头部
 * @param entity 实体 - 预留参数
 * @param landmarks 关键点数据
 * @param component 动作捕捉组件
 */
function solveHead(entity: Entity, landmarks: WorldLandmarkData[], component: MotionCaptureComponent): void {
  // @ts-ignore: 预留参数，暂未使用
  entity;
  // 获取头部关键点
  const nose = landmarks[LandmarkIndices.NOSE];
  const leftEar = landmarks[LandmarkIndices.LEFT_EAR];
  const rightEar = landmarks[LandmarkIndices.RIGHT_EAR];

  // 检查可见度
  if (!nose || !leftEar || !rightEar ||
      !nose.visibility || !leftEar.visibility || !rightEar.visibility ||
      nose.visibility < DEFAULT_VISIBILITY_THRESHOLD ||
      leftEar.visibility < DEFAULT_VISIBILITY_THRESHOLD ||
      rightEar.visibility < DEFAULT_VISIBILITY_THRESHOLD) {
    return;
  }

  // 计算头部方向
  tempVector.set(-leftEar.x, -leftEar.y, -leftEar.z);
  tempVector2.set(-rightEar.x, -rightEar.y, -rightEar.z);
  tempVector3.set(-nose.x, -nose.y, -nose.z);

  // 计算头部旋转
  const headRotation = getQuaternionFromPoints(tempVector, tempVector2, tempVector3);

  // 应用额外旋转
  headRotation.multiply(X_90_ROTATION);

  // 设置头部骨骼旋转
  component.setBoneRotation(VRMHumanBoneName.Head, headRotation);
}

/**
 * 解算脊柱
 * @param entity 实体 - 预留参数
 * @param lowestY 最低Y坐标
 * @param landmarks 关键点数据
 * @param trackingLowerBody 是否跟踪下半身 - 预留参数
 * @param component 动作捕捉组件
 */
function solveSpine(
  entity: Entity,
  lowestY: number,
  landmarks: WorldLandmarkData[],
  trackingLowerBody: boolean,
  component: MotionCaptureComponent
): void {
  // @ts-ignore: 预留参数，暂未使用
  entity;
  // @ts-ignore: 预留参数，暂未使用
  trackingLowerBody;
  // 获取脊柱关键点
  const leftShoulder = landmarks[LandmarkIndices.LEFT_SHOULDER];
  const rightShoulder = landmarks[LandmarkIndices.RIGHT_SHOULDER];
  const leftHip = landmarks[LandmarkIndices.LEFT_HIP];
  const rightHip = landmarks[LandmarkIndices.RIGHT_HIP];

  // 检查可见度
  if (!leftShoulder || !rightShoulder || !leftHip || !rightHip ||
      !leftShoulder.visibility || !rightShoulder.visibility ||
      leftShoulder.visibility < DEFAULT_VISIBILITY_THRESHOLD ||
      rightShoulder.visibility < DEFAULT_VISIBILITY_THRESHOLD) {
    return;
  }

  // 计算脊柱方向
  tempVector.set(
    (leftShoulder.x + rightShoulder.x) / 2,
    lowestY - (leftShoulder.y + rightShoulder.y) / 2,
    -(leftShoulder.z + rightShoulder.z) / 2
  );

  tempVector2.set(
    (leftHip.x + rightHip.x) / 2,
    lowestY - (leftHip.y + rightHip.y) / 2,
    -(leftHip.z + rightHip.z) / 2
  );

  // 计算肩膀方向
  tempVector3.set(
    rightShoulder.x - leftShoulder.x,
    lowestY - rightShoulder.y - (lowestY - leftShoulder.y),
    -(rightShoulder.z - leftShoulder.z)
  ).normalize();

  // 计算脊柱旋转
  const spineRotation = new THREE.Quaternion();
  const shoulderRotation = new THREE.Quaternion();

  // 创建朝向矩阵
  tempMatrix.lookAt(tempVector, tempVector2, new THREE.Vector3(0, 1, 0));
  spineRotation.setFromRotationMatrix(tempMatrix);

  // 计算肩膀旋转
  tempMatrix.lookAt(
    new THREE.Vector3(0, 0, 0),
    tempVector3,
    new THREE.Vector3(0, 1, 0)
  );
  shoulderRotation.setFromRotationMatrix(tempMatrix);

  // 组合旋转
  spineRotation.multiply(shoulderRotation);

  // 设置脊柱骨骼旋转
  component.setBoneRotation(VRMHumanBoneName.Spine, spineRotation);
  component.setBoneRotation(VRMHumanBoneName.Chest, spineRotation);
  component.setBoneRotation(VRMHumanBoneName.UpperChest, spineRotation);

  // 设置髋关节位置
  component.setHipPosition(tempVector2);
}

/**
 * 解算左臂
 * @param entity 实体
 * @param lowestY 最低Y坐标
 * @param landmarks 关键点数据
 * @param component 动作捕捉组件
 * @param threshold 可见度阈值
 */
function solveLeftArm(
  entity: Entity,
  lowestY: number,
  landmarks: WorldLandmarkData[],
  component: MotionCaptureComponent,
  threshold: number
): void {
  // 获取左臂关键点
  const leftShoulder = landmarks[LandmarkIndices.LEFT_SHOULDER];
  const leftElbow = landmarks[LandmarkIndices.LEFT_ELBOW];
  const leftWrist = landmarks[LandmarkIndices.LEFT_WRIST];

  // 解算左上臂
  solveLimb(
    entity,
    leftShoulder,
    leftElbow,
    leftWrist,
    new THREE.Vector3(0, 0, 1),
    VRMHumanBoneName.LeftShoulder,
    VRMHumanBoneName.LeftUpperArm,
    VRMHumanBoneName.LeftLowerArm,
    lowestY,
    component,
    threshold
  );

  // 解算左手
  solveHand(
    entity,
    leftWrist,
    landmarks[LandmarkIndices.LEFT_INDEX],
    landmarks[LandmarkIndices.LEFT_PINKY],
    VRMHumanBoneName.LeftLowerArm,
    VRMHumanBoneName.LeftHand,
    lowestY,
    component,
    threshold
  );
}

/**
 * 解算右臂
 * @param entity 实体
 * @param lowestY 最低Y坐标
 * @param landmarks 关键点数据
 * @param component 动作捕捉组件
 * @param threshold 可见度阈值
 */
function solveRightArm(
  entity: Entity,
  lowestY: number,
  landmarks: WorldLandmarkData[],
  component: MotionCaptureComponent,
  threshold: number
): void {
  // 获取右臂关键点
  const rightShoulder = landmarks[LandmarkIndices.RIGHT_SHOULDER];
  const rightElbow = landmarks[LandmarkIndices.RIGHT_ELBOW];
  const rightWrist = landmarks[LandmarkIndices.RIGHT_WRIST];

  // 解算右上臂
  solveLimb(
    entity,
    rightShoulder,
    rightElbow,
    rightWrist,
    new THREE.Vector3(0, 0, 1),
    VRMHumanBoneName.RightShoulder,
    VRMHumanBoneName.RightUpperArm,
    VRMHumanBoneName.RightLowerArm,
    lowestY,
    component,
    threshold
  );

  // 解算右手
  solveHand(
    entity,
    rightWrist,
    landmarks[LandmarkIndices.RIGHT_INDEX],
    landmarks[LandmarkIndices.RIGHT_PINKY],
    VRMHumanBoneName.RightLowerArm,
    VRMHumanBoneName.RightHand,
    lowestY,
    component,
    threshold
  );
}

/**
 * 解算左腿
 * @param entity 实体
 * @param lowestY 最低Y坐标
 * @param landmarks 关键点数据
 * @param component 动作捕捉组件
 * @param threshold 可见度阈值
 */
function solveLeftLeg(
  entity: Entity,
  lowestY: number,
  landmarks: WorldLandmarkData[],
  component: MotionCaptureComponent,
  threshold: number
): void {
  // 获取左腿关键点
  const leftHip = landmarks[LandmarkIndices.LEFT_HIP];
  const leftKnee = landmarks[LandmarkIndices.LEFT_KNEE];
  const leftAnkle = landmarks[LandmarkIndices.LEFT_ANKLE];

  // 解算左大腿
  solveLimb(
    entity,
    leftHip,
    leftKnee,
    leftAnkle,
    new THREE.Vector3(0, 1, 0),
    VRMHumanBoneName.LeftUpperLeg,
    VRMHumanBoneName.LeftUpperLeg,
    VRMHumanBoneName.LeftLowerLeg,
    lowestY,
    component,
    threshold
  );

  // 解算左脚
  solveFoot(
    entity,
    leftAnkle,
    landmarks[LandmarkIndices.LEFT_HEEL],
    landmarks[LandmarkIndices.LEFT_FOOT_INDEX],
    VRMHumanBoneName.LeftLowerLeg,
    VRMHumanBoneName.LeftFoot,
    lowestY,
    component,
    threshold
  );
}

/**
 * 解算右腿
 * @param entity 实体
 * @param lowestY 最低Y坐标
 * @param landmarks 关键点数据
 * @param component 动作捕捉组件
 * @param threshold 可见度阈值
 */
function solveRightLeg(
  entity: Entity,
  lowestY: number,
  landmarks: WorldLandmarkData[],
  component: MotionCaptureComponent,
  threshold: number
): void {
  // 获取右腿关键点
  const rightHip = landmarks[LandmarkIndices.RIGHT_HIP];
  const rightKnee = landmarks[LandmarkIndices.RIGHT_KNEE];
  const rightAnkle = landmarks[LandmarkIndices.RIGHT_ANKLE];

  // 解算右大腿
  solveLimb(
    entity,
    rightHip,
    rightKnee,
    rightAnkle,
    new THREE.Vector3(0, 1, 0),
    VRMHumanBoneName.RightUpperLeg,
    VRMHumanBoneName.RightUpperLeg,
    VRMHumanBoneName.RightLowerLeg,
    lowestY,
    component,
    threshold
  );

  // 解算右脚
  solveFoot(
    entity,
    rightAnkle,
    landmarks[LandmarkIndices.RIGHT_HEEL],
    landmarks[LandmarkIndices.RIGHT_FOOT_INDEX],
    VRMHumanBoneName.RightLowerLeg,
    VRMHumanBoneName.RightFoot,
    lowestY,
    component,
    threshold
  );
}

/**
 * 解算肢体
 * @param entity 实体 - 预留参数
 * @param start 起始关键点
 * @param mid 中间关键点
 * @param end 结束关键点
 * @param axis 旋转轴
 * @param parentBoneName 父骨骼名称 - 预留参数
 * @param startBoneName 起始骨骼名称
 * @param midBoneName 中间骨骼名称
 * @param lowestY 最低Y坐标
 * @param component 动作捕捉组件
 * @param threshold 可见度阈值
 */
function solveLimb(
  entity: Entity,
  start: WorldLandmarkData,
  mid: WorldLandmarkData,
  end: WorldLandmarkData,
  axis: THREE.Vector3,
  parentBoneName: VRMHumanBoneName,
  startBoneName: VRMHumanBoneName,
  midBoneName: VRMHumanBoneName,
  lowestY: number,
  component: MotionCaptureComponent,
  threshold: number
): void {
  // @ts-ignore: 预留参数，暂未使用
  entity;
  // @ts-ignore: 预留参数，暂未使用
  parentBoneName;
  // 检查关键点
  if (!start || !mid || !end) return;

  // 检查可见度
  const avgVisibility = (
    (start.visibility || 0) +
    (mid.visibility || 0) +
    (end.visibility || 0)
  ) / 3;

  if (avgVisibility < threshold) return;

  // 转换坐标
  const startPoint = new THREE.Vector3(start.x, lowestY - start.y, -start.z);
  const midPoint = new THREE.Vector3(mid.x, lowestY - mid.y, -mid.z);
  const endPoint = new THREE.Vector3(end.x, lowestY - end.y, -end.z);

  // 计算方向向量 - 预留功能
  // @ts-ignore: 预留功能，暂未使用
  const startToMid = new THREE.Vector3().subVectors(midPoint, startPoint).normalize();
  // @ts-ignore: 预留功能，暂未使用
  const midToEnd = new THREE.Vector3().subVectors(endPoint, midPoint).normalize();

  // 计算上臂旋转
  const upperRotation = new THREE.Quaternion();
  tempMatrix.lookAt(startPoint, midPoint, axis);
  upperRotation.setFromRotationMatrix(tempMatrix);

  // 计算下臂旋转
  const lowerRotation = new THREE.Quaternion();
  tempMatrix.lookAt(midPoint, endPoint, axis);
  lowerRotation.setFromRotationMatrix(tempMatrix);

  // 设置骨骼旋转
  component.setBoneRotation(startBoneName, upperRotation);
  component.setBoneRotation(midBoneName, lowerRotation);
}

/**
 * 解算手部
 * @param entity 实体 - 预留参数
 * @param wrist 手腕关键点
 * @param index 食指关键点
 * @param pinky 小指关键点
 * @param parentBoneName 父骨骼名称 - 预留参数
 * @param handBoneName 手部骨骼名称
 * @param lowestY 最低Y坐标
 * @param component 动作捕捉组件
 * @param threshold 可见度阈值
 */
function solveHand(
  entity: Entity,
  wrist: WorldLandmarkData,
  index: WorldLandmarkData,
  pinky: WorldLandmarkData,
  parentBoneName: VRMHumanBoneName,
  handBoneName: VRMHumanBoneName,
  lowestY: number,
  component: MotionCaptureComponent,
  threshold: number
): void {
  // @ts-ignore: 预留参数，暂未使用
  entity;
  // @ts-ignore: 预留参数，暂未使用
  parentBoneName;
  // 检查关键点
  if (!wrist || !index || !pinky) return;

  // 检查可见度
  const avgVisibility = (
    (wrist.visibility || 0) +
    (index.visibility || 0) +
    (pinky.visibility || 0)
  ) / 3;

  if (avgVisibility < threshold) return;

  // 转换坐标
  const wristPoint = new THREE.Vector3(wrist.x, lowestY - wrist.y, -wrist.z);
  const indexPoint = new THREE.Vector3(index.x, lowestY - index.y, -index.z);
  const pinkyPoint = new THREE.Vector3(pinky.x, lowestY - pinky.y, -pinky.z);

  // 计算手掌法线
  const wristToIndex = new THREE.Vector3().subVectors(indexPoint, wristPoint).normalize();
  const wristToPinky = new THREE.Vector3().subVectors(pinkyPoint, wristPoint).normalize();
  const palmNormal = new THREE.Vector3().crossVectors(wristToPinky, wristToIndex).normalize();

  // 计算手部旋转
  const handRotation = new THREE.Quaternion();
  tempMatrix.lookAt(
    wristPoint,
    new THREE.Vector3().addVectors(wristPoint, wristToIndex),
    palmNormal
  );
  handRotation.setFromRotationMatrix(tempMatrix);

  // 设置骨骼旋转
  component.setBoneRotation(handBoneName, handRotation);
}

/**
 * 解算脚部
 * @param entity 实体 - 预留参数
 * @param ankle 踝关节关键点
 * @param heel 脚跟关键点
 * @param toe 脚尖关键点
 * @param parentBoneName 父骨骼名称 - 预留参数
 * @param footBoneName 脚部骨骼名称
 * @param lowestY 最低Y坐标
 * @param component 动作捕捉组件
 * @param threshold 可见度阈值
 */
function solveFoot(
  entity: Entity,
  ankle: WorldLandmarkData,
  heel: WorldLandmarkData,
  toe: WorldLandmarkData,
  parentBoneName: VRMHumanBoneName,
  footBoneName: VRMHumanBoneName,
  lowestY: number,
  component: MotionCaptureComponent,
  threshold: number
): void {
  // @ts-ignore: 预留参数，暂未使用
  entity;
  // @ts-ignore: 预留参数，暂未使用
  parentBoneName;
  // 检查关键点
  if (!ankle || !heel || !toe) return;

  // 检查可见度
  const avgVisibility = (
    (ankle.visibility || 0) +
    (heel.visibility || 0) +
    (toe.visibility || 0)
  ) / 3;

  if (avgVisibility < threshold) return;

  // 转换坐标
  const anklePoint = new THREE.Vector3(ankle.x, lowestY - ankle.y, -ankle.z);
  const heelPoint = new THREE.Vector3(heel.x, lowestY - heel.y, -heel.z);
  const toePoint = new THREE.Vector3(toe.x, lowestY - toe.y, -toe.z);

  // 计算脚部方向 - 预留功能
  // @ts-ignore: 预留功能，暂未使用
  const ankleToHeel = new THREE.Vector3().subVectors(heelPoint, anklePoint).normalize();
  // @ts-ignore: 预留功能，暂未使用
  const heelToToe = new THREE.Vector3().subVectors(toePoint, heelPoint).normalize();

  // 计算脚部旋转
  const footRotation = new THREE.Quaternion();
  tempMatrix.lookAt(
    anklePoint,
    toePoint,
    new THREE.Vector3(0, 1, 0)
  );
  footRotation.setFromRotationMatrix(tempMatrix);

  // 设置骨骼旋转
  component.setBoneRotation(footBoneName, footRotation);
}

/**
 * 从三个点计算四元数
 * @param a 点A
 * @param b 点B
 * @param c 点C
 * @returns 四元数
 */
function getQuaternionFromPoints(a: THREE.Vector3, b: THREE.Vector3, c: THREE.Vector3): THREE.Quaternion {
  // 计算平面法线
  const ab = new THREE.Vector3().subVectors(b, a).normalize();
  const ac = new THREE.Vector3().subVectors(c, a).normalize();
  const normal = new THREE.Vector3().crossVectors(ab, ac).normalize();

  // 计算朝向
  const direction = new THREE.Vector3().subVectors(c, new THREE.Vector3().addVectors(a, b).multiplyScalar(0.5)).normalize();

  // 创建旋转矩阵
  const matrix = new THREE.Matrix4().lookAt(
    new THREE.Vector3(0, 0, 0),
    direction,
    normal
  );

  // 返回四元数
  return new THREE.Quaternion().setFromRotationMatrix(matrix);
}
