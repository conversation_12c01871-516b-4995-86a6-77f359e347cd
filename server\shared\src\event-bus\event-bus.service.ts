import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Redis } from 'ioredis';

export interface DomainEvent {
  id: string;
  type: string;
  aggregateId: string;
  aggregateType: string;
  version: number;
  data: any;
  metadata: {
    timestamp: Date;
    userId?: string;
    correlationId?: string;
    causationId?: string;
    source: string;
  };
}

export interface EventHandler<T = any> {
  handle(event: DomainEvent): Promise<void>;
}

@Injectable()
export class EventBusService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(EventBusService.name);
  private redisClient: Redis;
  private subscriber: Redis;
  private readonly handlers = new Map<string, EventHandler[]>();
  private readonly serviceName: string;

  constructor(
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.serviceName = process.env.SERVICE_NAME || 'unknown-service';
  }

  async onModuleInit() {
    await this.initializeRedis();
    await this.setupSubscriptions();
    this.logger.log('事件总线服务初始化完成');
  }

  async onModuleDestroy() {
    if (this.redisClient) {
      await this.redisClient.disconnect();
    }
    if (this.subscriber) {
      await this.subscriber.disconnect();
    }
    this.logger.log('事件总线服务已关闭');
  }

  /**
   * 发布领域事件
   */
  async publish(event: Omit<DomainEvent, 'id' | 'metadata'>): Promise<void> {
    const domainEvent: DomainEvent = {
      ...event,
      id: this.generateEventId(),
      metadata: {
        timestamp: new Date(),
        source: this.serviceName,
        correlationId: this.generateCorrelationId(),
      },
    };

    try {
      // 本地发布
      this.eventEmitter.emit(event.type, domainEvent);

      // 远程发布到Redis
      const channel = `events:${event.type}`;
      await this.redisClient.publish(channel, JSON.stringify(domainEvent));

      // 存储事件到事件存储
      await this.storeEvent(domainEvent);

      this.logger.debug(`事件已发布: ${event.type}`, {
        eventId: domainEvent.id,
        aggregateId: event.aggregateId,
        type: event.type,
      });

    } catch (error) {
      this.logger.error(`发布事件失败: ${event.type}`, error);
      throw error;
    }
  }

  /**
   * 订阅事件类型
   */
  subscribe(eventType: string, handler: EventHandler): void {
    if (!this.handlers.has(eventType)) {
      this.handlers.set(eventType, []);
    }
    this.handlers.get(eventType)!.push(handler);

    // 本地订阅
    this.eventEmitter.on(eventType, async (event: DomainEvent) => {
      await this.handleEvent(event, handler);
    });

    this.logger.log(`已订阅事件类型: ${eventType}`);
  }

  /**
   * 批量发布事件
   */
  async publishBatch(events: Array<Omit<DomainEvent, 'id' | 'metadata'>>): Promise<void> {
    const domainEvents = events.map(event => ({
      ...event,
      id: this.generateEventId(),
      metadata: {
        timestamp: new Date(),
        source: this.serviceName,
        correlationId: this.generateCorrelationId(),
      },
    }));

    try {
      // 使用Redis管道批量发布
      const pipeline = this.redisClient.multi();
      
      for (const event of domainEvents) {
        const channel = `events:${event.type}`;
        pipeline.publish(channel, JSON.stringify(event));
        
        // 本地发布
        this.eventEmitter.emit(event.type, event);
      }

      await pipeline.exec();

      // 批量存储事件
      await this.storeEventBatch(domainEvents);

      this.logger.debug(`批量发布 ${events.length} 个事件`);

    } catch (error) {
      this.logger.error('批量发布事件失败', error);
      throw error;
    }
  }

  /**
   * 获取聚合的事件流
   */
  async getEventStream(
    aggregateId: string,
    fromVersion?: number,
    toVersion?: number,
  ): Promise<DomainEvent[]> {
    try {
      const key = `event_stream:${aggregateId}`;
      const start = fromVersion ? fromVersion - 1 : 0;
      const end = toVersion ? toVersion - 1 : -1;

      const eventData = await this.redisClient.lrange(key, start, end);
      return eventData.map(data => JSON.parse(data));

    } catch (error) {
      this.logger.error(`获取事件流失败: ${aggregateId}`, error);
      throw error;
    }
  }

  /**
   * 重放事件
   */
  async replayEvents(
    aggregateId: string,
    fromVersion?: number,
    toVersion?: number,
  ): Promise<void> {
    const events = await this.getEventStream(aggregateId, fromVersion, toVersion);
    
    for (const event of events) {
      this.eventEmitter.emit(event.type, event);
    }

    this.logger.log(`重放了 ${events.length} 个事件`, { aggregateId });
  }

  /**
   * 获取事件统计
   */
  async getEventStats(): Promise<{
    totalEvents: number;
    eventsByType: Record<string, number>;
    recentEvents: DomainEvent[];
  }> {
    try {
      const totalEvents = await this.redisClient.get('event_stats:total') || '0';
      const eventTypes = await this.redisClient.hgetall('event_stats:by_type');
      const recentEventsData = await this.redisClient.lrange('recent_events', 0, 9);
      
      const recentEvents = recentEventsData.map(data => JSON.parse(data));

      return {
        totalEvents: parseInt(totalEvents),
        eventsByType: Object.fromEntries(
          Object.entries(eventTypes).map(([type, count]) => [type, parseInt(count as string)])
        ),
        recentEvents,
      };

    } catch (error) {
      this.logger.error('获取事件统计失败', error);
      throw error;
    }
  }

  /**
   * 初始化Redis连接
   */
  private async initializeRedis(): Promise<void> {
    const redisHost = process.env.REDIS_HOST || 'localhost';
    const redisPort = parseInt(process.env.REDIS_PORT || '6379');
    const redisPassword = process.env.REDIS_PASSWORD || '';

    this.redisClient = new Redis({
      host: redisHost,
      port: redisPort,
      password: redisPassword || undefined,
    });

    this.subscriber = new Redis({
      host: redisHost,
      port: redisPort,
      password: redisPassword || undefined,
    });

    this.logger.log('Redis连接已建立');
  }

  /**
   * 设置订阅
   */
  private async setupSubscriptions(): Promise<void> {
    // 订阅所有事件频道
    this.subscriber.psubscribe('events:*');
    this.subscriber.on('pmessage', (pattern, channel, message) => {
      this.handleRemoteEvent(message, channel);
    });

    this.logger.log('事件订阅已设置');
  }

  /**
   * 处理远程事件
   */
  private async handleRemoteEvent(message: string, channel: string): Promise<void> {
    try {
      const event: DomainEvent = JSON.parse(message);
      const eventType = channel.replace('events:', '');

      // 避免处理自己发布的事件
      if (event.metadata.source === this.serviceName) {
        return;
      }

      const handlers = this.handlers.get(eventType) || [];
      for (const handler of handlers) {
        await this.handleEvent(event, handler);
      }

    } catch (error) {
      this.logger.error(`处理远程事件失败: ${channel}`, error);
    }
  }

  /**
   * 处理事件
   */
  private async handleEvent(event: DomainEvent, handler: EventHandler): Promise<void> {
    try {
      await handler.handle(event);
      this.logger.debug(`事件处理成功: ${event.type}`, {
        eventId: event.id,
        handler: handler.constructor.name,
      });

    } catch (error) {
      this.logger.error(`事件处理失败: ${event.type}`, {
        eventId: event.id,
        handler: handler.constructor.name,
        error: error.message,
      });

      // 可以在这里实现重试逻辑或死信队列
      await this.handleFailedEvent(event, handler, error);
    }
  }

  /**
   * 处理失败的事件
   */
  private async handleFailedEvent(
    event: DomainEvent,
    handler: EventHandler,
    error: Error,
  ): Promise<void> {
    const failedEvent = {
      ...event,
      failureInfo: {
        handler: handler.constructor.name,
        error: error.message,
        timestamp: new Date(),
        retryCount: 0,
      },
    };

    // 存储到失败事件队列
    await this.redisClient.lpush('failed_events', JSON.stringify(failedEvent));
  }

  /**
   * 存储事件
   */
  private async storeEvent(event: DomainEvent): Promise<void> {
    const pipeline = this.redisClient.multi();

    // 存储到聚合事件流
    const streamKey = `event_stream:${event.aggregateId}`;
    pipeline.rpush(streamKey, JSON.stringify(event));

    // 更新统计
    pipeline.incr('event_stats:total');
    pipeline.hincrby('event_stats:by_type', event.type, 1);

    // 存储到最近事件列表
    pipeline.lpush('recent_events', JSON.stringify(event));
    pipeline.ltrim('recent_events', 0, 99); // 保留最近100个事件

    await pipeline.exec();
  }

  /**
   * 批量存储事件
   */
  private async storeEventBatch(events: DomainEvent[]): Promise<void> {
    const pipeline = this.redisClient.multi();

    for (const event of events) {
      const streamKey = `event_stream:${event.aggregateId}`;
      pipeline.rpush(streamKey, JSON.stringify(event));
      pipeline.hincrby('event_stats:by_type', event.type, 1);
      pipeline.lpush('recent_events', JSON.stringify(event));
    }

    pipeline.incrby('event_stats:total', events.length);
    pipeline.ltrim('recent_events', 0, 99);

    await pipeline.exec();
  }

  /**
   * 生成事件ID
   */
  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成关联ID
   */
  private generateCorrelationId(): string {
    return `corr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
