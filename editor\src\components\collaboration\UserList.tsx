/**
 * 协作用户列表组件
 */
import React from 'react';
import { List, Avatar, Badge, Tooltip, Space, Typography } from 'antd';
import { UserOutlined, CrownOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { CollaborationRole } from '../../services/PermissionService';
import { CollaborationUser } from '../../services/CollaborationService';

const { Text } = Typography;

// 角色图标映射
const roleIcons = {
  [CollaborationRole.OWNER]: <CrownOutlined style={{ color: '#faad14' }} />,
  [CollaborationRole.ADMIN]: <CrownOutlined style={{ color: '#52c41a' }} />,
  [CollaborationRole.EDITOR]: <EditOutlined style={{ color: '#1890ff' }} />,
  [CollaborationRole.VIEWER]: <EyeOutlined style={{ color: '#8c8c8c' }} />};

// 角色名称映射
const roleNames = {
  [CollaborationRole.OWNER]: 'collaboration.roles.owner',
  [CollaborationRole.ADMIN]: 'collaboration.roles.admin',
  [CollaborationRole.EDITOR]: 'collaboration.roles.editor',
  [CollaborationRole.VIEWER]: 'collaboration.roles.viewer'};

interface UserListProps {
  users: CollaborationUser[];
  currentUserId: string;
}

/**
 * 用户列表组件
 */
const UserList: React.FC<UserListProps> = ({ users, currentUserId }) => {
  const { t } = useTranslation();

  return (
    <List
      className="collaboration-user-list"
      itemLayout="horizontal"
      dataSource={users}
      renderItem={(user) => {
        const isCurrentUser = user.id === currentUserId;
        
        return (
          <List.Item>
            <List.Item.Meta
              avatar={
                <Badge
                  dot
                  status={user.isActive ? 'success' : 'default'}
                  offset={[-5, 32]}
                >
                  <Avatar
                    style={{ 
                      backgroundColor: user.color,
                      border: isCurrentUser ? '2px solid #1890ff' : 'none'
                    }}
                    icon={<UserOutlined />}
                    src={user.avatar}
                  />
                </Badge>
              }
              title={
                <Space>
                  <Text strong={isCurrentUser}>
                    {user.name} {isCurrentUser && `(${t('collaboration.you')})`}
                  </Text>
                  <Tooltip title={t(roleNames[user.role])}>
                    {roleIcons[user.role]}
                  </Tooltip>
                </Space>
              }
              description={
                <Text type="secondary">
                  {user.isActive 
                    ? t('collaboration.status.active') 
                    : t('collaboration.status.inactive')}
                </Text>
              }
            />
          </List.Item>
        );
      }}
    />
  );
};

export default UserList;
