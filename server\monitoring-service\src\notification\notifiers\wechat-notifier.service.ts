import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { NotificationChannelEntity } from '../entities/notification-channel.entity';
import { NotificationHistoryEntity } from '../entities/notification-history.entity';
import { AlertEntity, AlertSeverity } from '../../alert/entities/alert.entity';

/**
 * 企业微信通知服务
 * 负责向企业微信发送通知消息
 */
@Injectable()
export class WeChatNotifierService {
  private readonly logger = new Logger(WeChatNotifierService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 发送企业微信通知
   */
  async send(
    channel: NotificationChannelEntity,
    alert: AlertEntity,
    notification: NotificationHistoryEntity,
  ): Promise<void> {
    try {
      // 获取企业微信配置
      const config = channel.config;

      if (!config.webhookUrl) {
        throw new Error('未配置企业微信 Webhook URL');
      }

      const payload = {
        msgtype: 'markdown',
        markdown: {
          content: this.formatAlertMarkdown(alert, notification),
          mentioned_list: config.mentionedList || [],
          mentioned_mobile_list: config.mentionedMobileList || [],
        },
      };

      const response = await firstValueFrom(this.httpService.post(config.webhookUrl, payload, { timeout: 10000 }));

      if (response.data.errcode !== 0) {
        throw new Error(`企业微信API返回错误: ${response.data.errmsg}`);
      }

      this.logger.debug(`企业微信通知已发送: ${notification.subject}`);
    } catch (error) {
      this.logger.error(`发送企业微信通知失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 发送企业微信通知（兼容旧接口）
   */
  async sendNotification(data: {
    title: string;
    message: string;
    type: 'info' | 'warning' | 'error' | 'success';
    webhookUrl: string;
    mentionedList?: string[];
    mentionedMobileList?: string[];
    metadata?: any;
  }): Promise<boolean> {
    try {
      const payload = {
        msgtype: 'markdown',
        markdown: {
          content: this.formatMarkdownMessage(data),
          mentioned_list: data.mentionedList || [],
          mentioned_mobile_list: data.mentionedMobileList || [],
        },
      };

      const response = await firstValueFrom(this.httpService.post(data.webhookUrl, payload));

      if (response.data.errcode === 0) {
        this.logger.log(`企业微信通知发送成功: ${data.title}`);
        return true;
      } else {
        this.logger.error(`企业微信通知发送失败: ${response.data.errmsg}`);
        return false;
      }
    } catch (error) {
      this.logger.error(`发送企业微信通知时出错: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 发送文本通知
   */
  async sendTextNotification(data: {
    content: string;
    webhookUrl: string;
    mentionedList?: string[];
    mentionedMobileList?: string[];
  }): Promise<boolean> {
    try {
      const payload = {
        msgtype: 'text',
        text: {
          content: data.content,
          mentioned_list: data.mentionedList || [],
          mentioned_mobile_list: data.mentionedMobileList || [],
        },
      };

      const response = await firstValueFrom(this.httpService.post(data.webhookUrl, payload));

      if (response.data.errcode === 0) {
        this.logger.log('企业微信文本通知发送成功');
        return true;
      } else {
        this.logger.error(`企业微信文本通知发送失败: ${response.data.errmsg}`);
        return false;
      }
    } catch (error) {
      this.logger.error(`发送企业微信文本通知时出错: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 测试企业微信连接
   */
  async testConnection(webhookUrl: string): Promise<boolean> {
    try {
      const testPayload = {
        msgtype: 'text',
        text: {
          content: '这是一条测试消息，用于验证企业微信通知配置是否正确。',
        },
      };

      const response = await firstValueFrom(this.httpService.post(webhookUrl, testPayload));

      return response.data.errcode === 0;
    } catch (error) {
      this.logger.error(`测试企业微信连接失败: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 格式化Markdown消息
   */
  private formatMarkdownMessage(data: { title: string; message: string; type: string; metadata?: any }): string {
    const emoji = this.getEmojiByType(data.type);
    const color = this.getColorByType(data.type);

    let markdown = `## <font color="${color}">${emoji} ${data.title}</font>\n\n`;
    markdown += `${data.message}\n\n`;

    if (data.metadata) {
      markdown += '**详细信息:**\n\n';
      for (const [key, value] of Object.entries(data.metadata)) {
        markdown += `> **${key}:** ${typeof value === 'object' ? JSON.stringify(value) : value}\n`;
      }
    }

    markdown += `\n---\n*发送时间: ${new Date().toLocaleString('zh-CN')}*`;

    return markdown;
  }

  /**
   * 根据类型获取表情符号
   */
  private getEmojiByType(type: string): string {
    const emojis = {
      info: 'ℹ️',
      warning: '⚠️',
      error: '❌',
      success: '✅',
    };

    return emojis[type] || emojis.info;
  }

  /**
   * 根据类型获取颜色
   */
  private getColorByType(type: string): string {
    const colors = {
      info: 'info',
      warning: 'warning',
      error: 'comment',
      success: 'info',
    };

    return colors[type] || colors.info;
  }

  /**
   * 获取严重性对应的表情符号
   */
  private getSeverityEmoji(severity: AlertSeverity): string {
    switch (severity) {
      case AlertSeverity.INFO:
        return 'ℹ️';
      case AlertSeverity.WARNING:
        return '⚠️';
      case AlertSeverity.ERROR:
        return '❌';
      case AlertSeverity.CRITICAL:
        return '🚨';
      default:
        return '❓';
    }
  }

  /**
   * 获取严重性对应的颜色
   */
  private getSeverityColor(severity: AlertSeverity): string {
    switch (severity) {
      case AlertSeverity.INFO:
        return 'info';
      case AlertSeverity.WARNING:
        return 'warning';
      case AlertSeverity.ERROR:
        return 'comment';
      case AlertSeverity.CRITICAL:
        return 'comment';
      default:
        return 'info';
    }
  }

  /**
   * 格式化告警Markdown消息
   */
  private formatAlertMarkdown(alert: AlertEntity, _notification: NotificationHistoryEntity): string {
    const emoji = this.getSeverityEmoji(alert.severity);
    const color = this.getSeverityColor(alert.severity);

    let markdown = `## <font color="${color}">${emoji} ${alert.name}</font>\n\n`;
    markdown += `${alert.description}\n\n`;

    markdown += '**告警详情:**\n\n';
    markdown += `> **严重性:** ${this.getSeverityText(alert.severity)}\n`;
    markdown += `> **状态:** ${alert.status}\n`;
    markdown += `> **开始时间:** ${alert.startTime.toLocaleString('zh-CN')}\n`;

    if (alert.serviceType) {
      markdown += `> **服务类型:** ${alert.serviceType}\n`;
    }

    if (alert.hostname) {
      markdown += `> **主机:** ${alert.hostname}\n`;
    }

    if (alert.labels && Object.keys(alert.labels).length > 0) {
      markdown += '\n**标签:**\n\n';
      for (const [key, value] of Object.entries(alert.labels)) {
        markdown += `> **${key}:** ${value}\n`;
      }
    }

    if (alert.value && Object.keys(alert.value).length > 0) {
      markdown += '\n**值:**\n\n';
      for (const [key, value] of Object.entries(alert.value)) {
        markdown += `> **${key}:** ${value}\n`;
      }
    }

    markdown += `\n---\n*发送时间: ${new Date().toLocaleString('zh-CN')}*`;

    return markdown;
  }

  /**
   * 获取严重性文本
   */
  private getSeverityText(severity: AlertSeverity): string {
    switch (severity) {
      case AlertSeverity.INFO:
        return '信息';
      case AlertSeverity.WARNING:
        return '警告';
      case AlertSeverity.ERROR:
        return '错误';
      case AlertSeverity.CRITICAL:
        return '严重';
      default:
        return severity;
    }
  }
}
