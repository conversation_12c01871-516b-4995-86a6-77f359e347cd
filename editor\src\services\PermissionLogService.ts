/**
 * 权限日志服务
 * 负责记录权限变更历史
 */
import { EventEmitter } from '../utils/EventEmitter';
import { store } from '../store';
import { addPermissionLog } from '../store/collaboration/permissionSlice';
import { Permission } from './PermissionService';
import { CollaborationRole } from './PermissionService';
// 本地用户角色定义（避免依赖外部模块）
export enum UserRole {
  /** 访客 */
  GUEST = 'guest',
  /** 用户 */
  USER = 'user',
  /** 管理员 */
  ADMIN = 'admin',
  /** 超级管理员 */
  SUPER_ADMIN = 'super_admin'}

// 权限日志类型
export enum PermissionLogType {
  ROLE_CHANGED = 'role_changed',
  PERMISSION_GRANTED = 'permission_granted',
  PERMISSION_REVOKED = 'permission_revoked',
  ROLE_PERMISSIONS_CHANGED = 'role_permissions_changed',
  PERMISSION_CHECK_FAILED = 'permission_check_failed',
  PERMISSION_CHECK_ENABLED = 'permission_check_enabled',
  PERMISSION_CHECK_DISABLED = 'permission_check_disabled',
  USER_SESSION_CREATED = 'user_session_created',
  USER_SESSION_REMOVED = 'user_session_removed',
  USER_SESSION_UPDATED = 'user_session_updated',
  USER_PERMISSION_DENIED = 'user_permission_denied',
  ORGANIZATION_NODE_ADDED = 'organization_node_added',
  ORGANIZATION_NODE_REMOVED = 'organization_node_removed',
  ORGANIZATION_NODE_UPDATED = 'organization_node_updated',
  ORGANIZATION_PERMISSIONS_ENABLED = 'organization_permissions_enabled',
  ORGANIZATION_PERMISSIONS_DISABLED = 'organization_permissions_disabled',
  PERMISSION_INHERITANCE_CHANGED = 'permission_inheritance_changed',
  PERMISSION_POLICY_CREATED = 'permission_policy_created',
  PERMISSION_POLICY_UPDATED = 'permission_policy_updated',
  PERMISSION_POLICY_DELETED = 'permission_policy_deleted',
  PERMISSION_POLICY_APPLIED = 'permission_policy_applied'}

// 权限日志接口
export interface PermissionLog {
  id: string;
  timestamp: number;
  type: PermissionLogType;
  userId: string;
  targetUserId?: string;
  role?: CollaborationRole;
  permission?: Permission;
  permissions?: Permission[];
  details?: any;
}

/**
 * 权限日志服务类
 */
class PermissionLogService extends EventEmitter {
  private logs: PermissionLog[] = [];
  private enabled: boolean = true;
  private maxLogs: number = 1000;

  /**
   * 设置是否启用日志记录
   * @param enabled 是否启用
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }

  /**
   * 设置最大日志数量
   * @param maxLogs 最大日志数量
   */
  public setMaxLogs(maxLogs: number): void {
    this.maxLogs = maxLogs;
  }

  /**
   * 添加日志
   * @param log 日志对象
   */
  public addLog(log: Omit<PermissionLog, 'id' | 'timestamp'>): void {
    if (!this.enabled) {
      return;
    }

    // 创建完整日志对象
    const fullLog: PermissionLog = {
      ...log,
      id: this.generateId(),
      timestamp: Date.now()};

    // 添加到日志列表
    this.logs.push(fullLog);

    // 如果日志数量超过最大值，删除最旧的日志
    if (this.logs.length > this.maxLogs) {
      this.logs.splice(0, this.logs.length - this.maxLogs);
    }

    // 更新Redux状态
    store.dispatch(addPermissionLog(fullLog));

    // 触发事件
    this.emit('logAdded', fullLog);
  }

  /**
   * 记录角色变更日志
   * @param userId 操作用户ID
   * @param targetUserId 目标用户ID
   * @param role 新角色
   * @param oldRole 旧角色
   */
  public logRoleChanged(userId: string, targetUserId: string, role: CollaborationRole, oldRole?: CollaborationRole): void {
    this.addLog({
      type: PermissionLogType.ROLE_CHANGED,
      userId,
      targetUserId,
      role,
      details: { oldRole }});
  }

  /**
   * 记录权限授予日志
   * @param userId 操作用户ID
   * @param targetUserId 目标用户ID
   * @param permission 权限
   */
  public logPermissionGranted(userId: string, targetUserId: string, permission: Permission): void {
    this.addLog({
      type: PermissionLogType.PERMISSION_GRANTED,
      userId,
      targetUserId,
      permission});
  }

  /**
   * 记录权限撤销日志
   * @param userId 操作用户ID
   * @param targetUserId 目标用户ID
   * @param permission 权限
   */
  public logPermissionRevoked(userId: string, targetUserId: string, permission: Permission): void {
    this.addLog({
      type: PermissionLogType.PERMISSION_REVOKED,
      userId,
      targetUserId,
      permission});
  }

  /**
   * 记录角色权限变更日志
   * @param userId 操作用户ID
   * @param role 角色
   * @param permissions 新权限列表
   * @param oldPermissions 旧权限列表
   */
  public logRolePermissionsChanged(userId: string, role: CollaborationRole, permissions: Permission[], oldPermissions?: Permission[]): void {
    this.addLog({
      type: PermissionLogType.ROLE_PERMISSIONS_CHANGED,
      userId,
      role,
      permissions,
      details: { oldPermissions }});
  }

  /**
   * 记录权限检查失败日志
   * @param userId 用户ID
   * @param permission 权限
   * @param context 上下文信息
   */
  public logPermissionCheckFailed(userId: string, permission: Permission, context?: any): void {
    this.addLog({
      type: PermissionLogType.PERMISSION_CHECK_FAILED,
      userId,
      permission,
      details: { context }});
  }

  /**
   * 获取所有日志
   * @returns 日志列表
   */
  public getLogs(): PermissionLog[] {
    return [...this.logs];
  }

  /**
   * 获取指定用户的日志
   * @param userId 用户ID
   * @returns 日志列表
   */
  public getUserLogs(userId: string): PermissionLog[] {
    return this.logs.filter(log => log.userId === userId || log.targetUserId === userId);
  }

  /**
   * 获取指定类型的日志
   * @param type 日志类型
   * @returns 日志列表
   */
  public getLogsByType(type: PermissionLogType): PermissionLog[] {
    return this.logs.filter(log => log.type === type);
  }

  /**
   * 获取指定时间范围的日志
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @returns 日志列表
   */
  public getLogsByTimeRange(startTime: number, endTime: number): PermissionLog[] {
    return this.logs.filter(log => log.timestamp >= startTime && log.timestamp <= endTime);
  }

  /**
   * 记录用户会话创建日志
   * @param userId 用户ID
   * @param role 角色
   */
  public logUserSessionCreated(userId: string, role: UserRole): void {
    this.addLog({
      type: PermissionLogType.USER_SESSION_CREATED,
      userId,
      role: role as unknown as CollaborationRole});
  }

  /**
   * 记录用户会话移除日志
   * @param userId 用户ID
   */
  public logUserSessionRemoved(userId: string): void {
    this.addLog({
      type: PermissionLogType.USER_SESSION_REMOVED,
      userId});
  }

  /**
   * 记录用户会话更新日志
   * @param userId 用户ID
   * @param details 更新详情
   */
  public logUserSessionUpdated(userId: string, details: any): void {
    this.addLog({
      type: PermissionLogType.USER_SESSION_UPDATED,
      userId,
      details});
  }

  /**
   * 记录用户权限拒绝日志
   * @param userId 操作用户ID
   * @param targetUserId 目标用户ID
   * @param permission 权限
   */
  public logUserPermissionDenied(userId: string, targetUserId: string, permission: Permission): void {
    this.addLog({
      type: PermissionLogType.USER_PERMISSION_DENIED,
      userId,
      targetUserId,
      permission});
  }

  /**
   * 记录组织节点添加日志
   * @param userId 操作用户ID
   * @param nodeId 节点ID
   * @param nodeType 节点类型
   * @param details 节点详情
   */
  public logOrganizationNodeAdded(userId: string, nodeId: string, nodeType: string, details?: any): void {
    this.addLog({
      type: PermissionLogType.ORGANIZATION_NODE_ADDED,
      userId,
      details: {
        nodeId,
        nodeType,
        ...details}});
  }

  /**
   * 记录组织节点移除日志
   * @param userId 操作用户ID
   * @param nodeId 节点ID
   * @param nodeType 节点类型
   */
  public logOrganizationNodeRemoved(userId: string, nodeId: string, nodeType: string): void {
    this.addLog({
      type: PermissionLogType.ORGANIZATION_NODE_REMOVED,
      userId,
      details: {
        nodeId,
        nodeType}});
  }

  /**
   * 记录组织节点更新日志
   * @param userId 操作用户ID
   * @param nodeId 节点ID
   * @param updates 更新内容
   */
  public logOrganizationNodeUpdated(userId: string, nodeId: string, updates: any): void {
    this.addLog({
      type: PermissionLogType.ORGANIZATION_NODE_UPDATED,
      userId,
      details: {
        nodeId,
        updates}});
  }

  /**
   * 记录组织权限启用日志
   * @param userId 操作用户ID
   */
  public logOrganizationPermissionsEnabled(userId: string): void {
    this.addLog({
      type: PermissionLogType.ORGANIZATION_PERMISSIONS_ENABLED,
      userId});
  }

  /**
   * 记录组织权限禁用日志
   * @param userId 操作用户ID
   */
  public logOrganizationPermissionsDisabled(userId: string): void {
    this.addLog({
      type: PermissionLogType.ORGANIZATION_PERMISSIONS_DISABLED,
      userId});
  }

  /**
   * 记录权限继承变更日志
   * @param userId 操作用户ID
   * @param strategy 继承策略
   * @param oldStrategy 旧继承策略
   */
  public logPermissionInheritanceChanged(userId: string, strategy: string, oldStrategy?: string): void {
    this.addLog({
      type: PermissionLogType.PERMISSION_INHERITANCE_CHANGED,
      userId,
      details: {
        strategy,
        oldStrategy}});
  }

  /**
   * 记录权限策略创建日志
   * @param userId 操作用户ID
   * @param policyId 策略ID
   * @param policyName 策略名称
   * @param details 策略详情
   */
  public logPermissionPolicyCreated(userId: string, policyId: string, policyName: string, details?: any): void {
    this.addLog({
      type: PermissionLogType.PERMISSION_POLICY_CREATED,
      userId,
      details: {
        policyId,
        policyName,
        ...details}});
  }

  /**
   * 记录权限策略更新日志
   * @param userId 操作用户ID
   * @param policyId 策略ID
   * @param updates 更新内容
   */
  public logPermissionPolicyUpdated(userId: string, policyId: string, updates: any): void {
    this.addLog({
      type: PermissionLogType.PERMISSION_POLICY_UPDATED,
      userId,
      details: {
        policyId,
        updates}});
  }

  /**
   * 记录权限策略删除日志
   * @param userId 操作用户ID
   * @param policyId 策略ID
   * @param policyName 策略名称
   */
  public logPermissionPolicyDeleted(userId: string, policyId: string, policyName: string): void {
    this.addLog({
      type: PermissionLogType.PERMISSION_POLICY_DELETED,
      userId,
      details: {
        policyId,
        policyName}});
  }

  /**
   * 记录权限策略应用日志
   * @param userId 操作用户ID
   * @param policyId 策略ID
   * @param targetId 目标ID
   * @param targetType 目标类型
   */
  public logPermissionPolicyApplied(userId: string, policyId: string, targetId: string, targetType: string): void {
    this.addLog({
      type: PermissionLogType.PERMISSION_POLICY_APPLIED,
      userId,
      details: {
        policyId,
        targetId,
        targetType}});
  }

  /**
   * 清空日志
   */
  public clearLogs(): void {
    this.logs = [];
  }

  /**
   * 生成唯一ID
   * @returns 唯一ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }
}

// 创建权限日志服务实例
export const permissionLogService = new PermissionLogService();
