/**
 * 地形系统
 * 负责地形的创建、更新和渲染
 */
import * as THREE from 'three';
import { System } from '../core/System';
import type { Entity } from '../core/Entity';
import { TerrainComponent } from './components/TerrainComponent';
import { TerrainMaterial } from './materials/TerrainMaterial';
import { TerrainUtils } from './utils/TerrainUtils';
import { TerrainPhysicsHelper, TerrainPhysicsMaterialProps } from './utils/TerrainPhysicsHelper';
import { PhysicsSystem } from '../physics/PhysicsSystem';
import { Debug } from '../utils/Debug';

/**
 * 地形系统
 */
export class TerrainSystem extends System {
  /** 系统类型 */
  public static readonly TYPE = 'TerrainSystem';
  /** 地形实体列表 */
  private terrainEntities: Map<Entity, TerrainComponent>;
  /** 地形工具 */
  private terrainUtils: TerrainUtils;
  /** 地形物理助手 */
  private terrainPhysicsHelper: TerrainPhysicsHelper | null;
  /** 物理系统 */
  private physicsSystem: PhysicsSystem | null;

  /**
   * 创建地形系统
   */
  constructor() {
    super();
    this.terrainEntities = new Map();
    this.terrainUtils = new TerrainUtils();
    this.terrainPhysicsHelper = null;
    this.physicsSystem = null;
  }

  /**
   * 设置物理系统
   * @param physicsSystem 物理系统
   */
  public setPhysicsSystem(physicsSystem: PhysicsSystem): void {
    this.physicsSystem = physicsSystem;
    this.terrainPhysicsHelper = new TerrainPhysicsHelper(physicsSystem);
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return TerrainSystem.TYPE;
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    // 系统初始化逻辑
  }

  /**
   * 添加地形实体
   * @param entity 实体
   * @param component 地形组件
   */
  public addTerrainEntity(entity: Entity, component: TerrainComponent): void {
    this.terrainEntities.set(entity, component);
    this.initializeTerrain(entity, component);
  }

  /**
   * 移除地形实体
   * @param entity 实体
   */
  public removeTerrainEntity(entity: Entity): void {
    const component = this.terrainEntities.get(entity);
    if (component) {
      this.disposeTerrain(component);
      this.terrainEntities.delete(entity);
    }
  }

  /**
   * 初始化地形
   * @param entity 实体
   * @param component 地形组件
   */
  private initializeTerrain(entity: Entity, component: TerrainComponent): void {
    if (component.initialized) {
      return;
    }

    // 创建地形几何体
    component.geometry = this.createTerrainGeometry(component);

    // 创建地形材质
    component.material = this.createTerrainMaterial(component);

    // 创建地形网格
    component.mesh = new THREE.Mesh(component.geometry, component.material);
    component.mesh.receiveShadow = true;
    component.mesh.castShadow = true;
    component.mesh.name = `Terrain_${entity.id}`;

    // 添加到实体的Transform组件
    const transform = entity.getComponent('Transform') as any;
    if (transform) {
      (transform as any).getObject3D().add(component.mesh);
    }

    // 初始化物理
    if (component.usePhysics) {
      this.initializeTerrainPhysics(entity, component);
    }

    component.initialized = true;
    component.needsUpdate = false;
    component.needsPhysicsUpdate = false;
  }

  /**
   * 创建地形几何体
   * @param component 地形组件
   * @returns 地形几何体
   */
  private createTerrainGeometry(component: TerrainComponent): THREE.BufferGeometry {
    const geometry = new THREE.PlaneGeometry(
      component.width,
      component.height,
      component.resolution - 1,
      component.resolution - 1
    );
    geometry.rotateX(-Math.PI / 2);

    // 更新顶点高度
    const positions = geometry.attributes.position.array as Float32Array;
    for (let i = 0, j = 0; i < positions.length; i += 3, j++) {
      positions[i + 1] = component.heightData[j] * component.maxHeight;
    }

    // 计算法线和切线
    geometry.computeVertexNormals();
    geometry.computeTangents();

    // 存储法线数据
    const normals = geometry.attributes.normal.array as Float32Array;
    for (let i = 0; i < normals.length; i++) {
      component.normalData[i] = normals[i];
    }

    return geometry;
  }

  /**
   * 创建地形材质
   * @param component 地形组件
   * @returns 地形材质
   */
  private createTerrainMaterial(component: TerrainComponent): THREE.Material {
    return new TerrainMaterial({
      layers: component.layers,
      resolution: component.resolution,
      width: component.width,
      height: component.height,
      maxHeight: component.maxHeight
    });
  }

  /**
   * 初始化地形物理
   * @param entity 实体
   * @param component 地形组件
   */
  private initializeTerrainPhysics(entity: Entity, component: TerrainComponent): void {
    if (!this.terrainPhysicsHelper || !this.physicsSystem) {
      Debug.warn('无法初始化地形物理：物理系统未设置');
      return;
    }

    // 创建物理材质属性
    const materialProps: TerrainPhysicsMaterialProps = {
      friction: component.physicsFriction,
      restitution: component.physicsRestitution,
      density: component.physicsDensity
    };

    // 创建地形物理
    const success = this.terrainPhysicsHelper.createTerrainPhysics(entity, component, materialProps);

    if (success) {
      Debug.log(`地形物理初始化成功: ${entity.id}`);

      // 更新调试可视化
      if (component.showPhysicsDebug) {
        this.terrainPhysicsHelper.updateDebugVisuals(entity, component, true);
      }
    } else {
      Debug.warn(`地形物理初始化失败: ${entity.id}`);
    }
  }

  /**
   * 更新地形
   * @param component 地形组件
   */
  private updateTerrain(component: TerrainComponent): void {
    if (!component.needsUpdate || !component.geometry || !component.mesh) {
      return;
    }

    // 更新几何体顶点高度
    const positions = component.geometry.attributes.position.array as Float32Array;
    for (let i = 0, j = 0; i < positions.length; i += 3, j++) {
      positions[i + 1] = component.heightData[j] * component.maxHeight;
    }

    // 更新几何体
    component.geometry.attributes.position.needsUpdate = true;
    component.geometry.computeVertexNormals();
    component.geometry.computeTangents();

    // 更新法线数据
    const normals = component.geometry.attributes.normal.array as Float32Array;
    for (let i = 0; i < normals.length; i++) {
      component.normalData[i] = normals[i];
    }

    component.needsUpdate = false;
  }

  /**
   * 更新地形物理
   * @param entity 实体
   * @param component 地形组件
   */
  private updateTerrainPhysics(entity: Entity, component: TerrainComponent): void {
    if (!component.needsPhysicsUpdate || !component.usePhysics) {
      return;
    }

    if (!this.terrainPhysicsHelper || !this.physicsSystem) {
      Debug.warn('无法更新地形物理：物理系统未设置');
      component.needsPhysicsUpdate = false;
      return;
    }

    // 创建物理材质属性
    const materialProps: TerrainPhysicsMaterialProps = {
      friction: component.physicsFriction,
      restitution: component.physicsRestitution,
      density: component.physicsDensity
    };

    // 更新地形物理
    const success = this.terrainPhysicsHelper.updateTerrainPhysics(entity, component, materialProps);

    if (success) {
      Debug.log(`地形物理更新成功: ${entity.id}`);

      // 更新调试可视化
      this.terrainPhysicsHelper.updateDebugVisuals(entity, component, component.showPhysicsDebug);
    } else {
      Debug.warn(`地形物理更新失败: ${entity.id}`);
    }

    component.needsPhysicsUpdate = false;
  }

  /**
   * 释放地形资源
   * @param component 地形组件
   */
  private disposeTerrain(component: TerrainComponent): void {
    if (component.geometry) {
      (component.geometry as any).dispose();
      component.geometry = null;
    }

    if (component.material) {
      if (Array.isArray(component.material)) {
        component.material.forEach(mat => (mat as any).dispose());
      } else {
        (component.material as any).dispose();
      }
      component.material = null;
    }

    if (component.mesh) {
      component.mesh.removeFromParent();
      component.mesh = null;
    }

    component.initialized = false;
  }

  /**
   * 更新系统
   * @param _deltaTime 时间增量（当前未使用）
   */
  public update(_deltaTime: number): void {
    // 更新所有地形
    this.terrainEntities.forEach((component, entity) => {
      if (component.needsUpdate) {
        this.updateTerrain(component);
      }

      if (component.needsPhysicsUpdate && component.usePhysics) {
        this.updateTerrainPhysics(entity, component);
      }
    });
  }
}
