/**
 * Git状态面板组件
 * 显示Git工作区状态和文件变更
 */
import React, { useState } from 'react';
import { List, Button, Checkbox, Tooltip, Space, Tag, Modal, message, Input } from 'antd';
import {
  FileOutlined,
  EditOutlined,
  DeleteOutlined,
  FileUnknownOutlined,
  ExclamationCircleOutlined,
  SaveOutlined,
  UndoOutlined,
  DiffOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  SyncOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import { GitFileStatus } from '../../services/GitService';
import './GitStatusPanel.less';

const { TextArea } = Input;

/**
 * Git状态面板组件
 */
const GitStatusPanel: React.FC = () => {
  const { t } = useTranslation();

  // 安全地获取Git状态
  const gitState = useSelector((state: RootState) => {
    try {
      return state.git;
    } catch (error) {
      console.warn('Git state access error:', error);
      return {
        unstagedFiles: [],
        stagedFiles: [],
        isLoading: false
      };
    }
  });

  const { unstagedFiles, stagedFiles, isLoading } = gitState;

  const [selectedUnstagedFiles, setSelectedUnstagedFiles] = useState<string[]>([]);
  const [selectedStagedFiles, setSelectedStagedFiles] = useState<string[]>([]);
  const [commitMessage, setCommitMessage] = useState<string>('');
  const [isCommitModalVisible, setIsCommitModalVisible] = useState<boolean>(false);

  // 处理暂存文件
  const handleStageFiles = () => {
    // 这里应该调用gitService的stageFiles方法
    message.success(t('git.stageSuccess'));
    setSelectedUnstagedFiles([]);
  };

  // 处理取消暂存文件
  const handleUnstageFiles = () => {
    // 这里应该调用gitService的unstageFiles方法
    message.success(t('git.unstageSuccess'));
    setSelectedStagedFiles([]);
  };

  // 处理提交
  const handleCommit = () => {
    setIsCommitModalVisible(true);
  };

  // 处理确认提交
  const handleConfirmCommit = () => {
    if (!commitMessage.trim()) {
      message.error(t('git.commitMessageRequired'));
      return;
    }

    // 这里应该调用gitService的commit方法
    message.success(t('git.commitSuccess'));
    setIsCommitModalVisible(false);
    setCommitMessage('');
  };

  // 处理取消提交
  const handleCancelCommit = () => {
    setIsCommitModalVisible(false);
  };

  // 处理拉取
  const handlePull = () => {
    // 这里应该调用gitService的pull方法
    message.success(t('git.pullSuccess'));
  };

  // 处理推送
  const handlePush = () => {
    // 这里应该调用gitService的push方法
    message.success(t('git.pushSuccess'));
  };

  // 处理同步
  const handleSync = () => {
    // 这里应该调用gitService的sync方法
    message.success(t('git.syncSuccess'));
  };

  // 处理查看差异
  const handleViewDiff = (file: GitFileStatus) => {
    // 这里应该调用gitService的viewDiff方法
    message.info(`${t('git.viewingDiff')}: ${file.path}`);
  };

  // 渲染文件图标
  const renderFileIcon = (file: GitFileStatus) => {
    switch (file.status) {
      case 'added':
        return <FileOutlined style={{ color: '#52c41a' }} />;
      case 'modified':
        return <EditOutlined style={{ color: '#1890ff' }} />;
      case 'deleted':
        return <DeleteOutlined style={{ color: '#f5222d' }} />;
      case 'conflicted':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      default:
        return <FileUnknownOutlined />;
    }
  };

  // 渲染文件状态标签
  const renderFileStatusTag = (file: GitFileStatus) => {
    switch (file.status) {
      case 'added':
        return <Tag color="success">{t('git.added')}</Tag>;
      case 'modified':
        return <Tag color="processing">{t('git.modified')}</Tag>;
      case 'deleted':
        return <Tag color="error">{t('git.deleted')}</Tag>;
      case 'conflicted':
        return <Tag color="warning">{t('git.conflicted')}</Tag>;
      default:
        return <Tag>{file.status}</Tag>;
    }
  };

  // 渲染未暂存文件列表
  const renderUnstagedFiles = () => {
    const files = unstagedFiles || [];
    if (files.length === 0) {
      return (
        <div className="git-empty-list">
          <p>{t('git.noUnstagedFiles')}</p>
        </div>
      );
    }

    return (
      <List
        size="small"
        dataSource={files}
        renderItem={(file) => (
          <List.Item
            key={file.path}
            actions={[
              <Tooltip title={t('git.viewDiff')} key="diff">
                <Button
                  icon={<DiffOutlined />}
                  size="small"
                  onClick={() => handleViewDiff(file)}
                />
              </Tooltip>,
            ]}
          >
            <Checkbox
              checked={selectedUnstagedFiles.includes(file.path)}
              onChange={(e) => {
                if (e.target.checked) {
                  setSelectedUnstagedFiles([...selectedUnstagedFiles, file.path]);
                } else {
                  setSelectedUnstagedFiles((prevFiles) => {
                    const safeFiles = Array.isArray(prevFiles) ? prevFiles : [];
                    return safeFiles.filter(path => path !== file.path);
                  });
                }
              }}
            />
            <Space className="git-file-item">
              {renderFileIcon(file)}
              <span className="git-file-path">{file.path}</span>
              {renderFileStatusTag(file)}
            </Space>
          </List.Item>
        )}
      />
    );
  };

  // 渲染已暂存文件列表
  const renderStagedFiles = () => {
    const files = stagedFiles || [];
    if (files.length === 0) {
      return (
        <div className="git-empty-list">
          <p>{t('git.noStagedFiles')}</p>
        </div>
      );
    }

    return (
      <List
        size="small"
        dataSource={files}
        renderItem={(file) => (
          <List.Item
            key={file.path}
            actions={[
              <Tooltip title={t('git.viewDiff')} key="diff">
                <Button
                  icon={<DiffOutlined />}
                  size="small"
                  onClick={() => handleViewDiff(file)}
                />
              </Tooltip>,
            ]}
          >
            <Checkbox
              checked={selectedStagedFiles.includes(file.path)}
              onChange={(e) => {
                if (e.target.checked) {
                  setSelectedStagedFiles([...selectedStagedFiles, file.path]);
                } else {
                  setSelectedStagedFiles((prevFiles) => {
                    const safeFiles = Array.isArray(prevFiles) ? prevFiles : [];
                    return safeFiles.filter(path => path !== file.path);
                  });
                }
              }}
            />
            <Space className="git-file-item">
              {renderFileIcon(file)}
              <span className="git-file-path">{file.path}</span>
              {renderFileStatusTag(file)}
            </Space>
          </List.Item>
        )}
      />
    );
  };

  return (
    <div className="git-status-panel">
      {/* 操作按钮 */}
      <div className="git-actions">
        <Space>
          <Tooltip title={t('git.pull')}>
            <Button
              icon={<ArrowDownOutlined />}
              onClick={handlePull}
              loading={isLoading}
            >
              {t('git.pull')}
            </Button>
          </Tooltip>
          <Tooltip title={t('git.push')}>
            <Button
              icon={<ArrowUpOutlined />}
              onClick={handlePush}
              loading={isLoading}
            >
              {t('git.push')}
            </Button>
          </Tooltip>
          <Tooltip title={t('git.sync')}>
            <Button
              icon={<SyncOutlined />}
              onClick={handleSync}
              loading={isLoading}
            >
              {t('git.sync')}
            </Button>
          </Tooltip>
        </Space>
      </div>

      {/* 未暂存文件 */}
      <div className="git-section">
        <div className="git-section-header">
          <h3>{t('git.unstagedChanges')}</h3>
          <Space>
            <Button
              size="small"
              icon={<SaveOutlined />}
              onClick={handleStageFiles}
              disabled={selectedUnstagedFiles.length === 0}
            >
              {t('git.stage')}
            </Button>
          </Space>
        </div>
        {renderUnstagedFiles()}
      </div>

      {/* 已暂存文件 */}
      <div className="git-section">
        <div className="git-section-header">
          <h3>{t('git.stagedChanges')}</h3>
          <Space>
            <Button
              size="small"
              icon={<UndoOutlined />}
              onClick={handleUnstageFiles}
              disabled={selectedStagedFiles.length === 0}
            >
              {t('git.unstage')}
            </Button>
            <Button
              type="primary"
              size="small"
              icon={<SaveOutlined />}
              onClick={handleCommit}
              disabled={stagedFiles.length === 0}
            >
              {t('git.commit')}
            </Button>
          </Space>
        </div>
        {renderStagedFiles()}
      </div>

      {/* 提交对话框 */}
      <Modal
        title={t('git.commit')}
        open={isCommitModalVisible}
        onOk={handleConfirmCommit}
        onCancel={handleCancelCommit}
        okText={t('git.commit')}
        cancelText={t('common.cancel')}
      >
        <div className="git-commit-form">
          <p>{t('git.commitMessagePrompt')}</p>
          <TextArea
            rows={4}
            value={commitMessage}
            onChange={(e) => setCommitMessage(e.target.value)}
            placeholder={t('git.commitMessagePlaceholder') as string}
          />
        </div>
      </Modal>
    </div>
  );
};

export default GitStatusPanel;
