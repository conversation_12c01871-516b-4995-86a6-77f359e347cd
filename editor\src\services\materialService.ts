/**
 * 材质服务
 */
import axios from 'axios';
import { Material, MaterialType } from '../store/materials/materialsSlice';

const API_URL = 'http://localhost:3000/api/materials';

// 材质缓存
const materialCache = new Map<string, Material>();
const cacheTimeout = 5 * 60 * 1000; // 5分钟缓存

export const materialService = {
  /**
   * 获取所有材质
   */
  async getMaterials(params?: {
    page?: number;
    pageSize?: number;
    type?: MaterialType;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) {
    try {
      const response = await axios.get(API_URL, { params });
      return response.data;
    } catch (error) {
      console.error('获取材质列表失败:', error);
      throw error;
    }
  },

  /**
   * 获取单个材质
   */
  async getMaterial(materialId: string, useCache = true) {
    // 检查缓存
    if (useCache && materialCache.has(materialId)) {
      const cached = materialCache.get(materialId)!;
      const now = Date.now();
      if (now - new Date(cached.updatedAt).getTime() < cacheTimeout) {
        return cached;
      }
    }

    try {
      const response = await axios.get(`${API_URL}/${materialId}`);
      const material = response.data;

      // 更新缓存
      if (useCache) {
        materialCache.set(materialId, material);
      }

      return material;
    } catch (error) {
      console.error('获取材质失败:', error);
      throw error;
    }
  },

  /**
   * 创建材质
   */
  async createMaterial(materialData: Omit<Material, 'id' | 'createdAt' | 'updatedAt'>) {
    try {
      const response = await axios.post(API_URL, {
        ...materialData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });

      const newMaterial = response.data;

      // 更新缓存
      materialCache.set(newMaterial.id, newMaterial);

      return newMaterial;
    } catch (error) {
      console.error('创建材质失败:', error);
      throw error;
    }
  },

  /**
   * 更新材质
   */
  async updateMaterial(materialId: string, materialData: Partial<Material>) {
    try {
      const response = await axios.patch(`${API_URL}/${materialId}`, {
        ...materialData,
        updatedAt: new Date().toISOString()
      });

      const updatedMaterial = response.data;

      // 更新缓存
      materialCache.set(materialId, updatedMaterial);

      return updatedMaterial;
    } catch (error) {
      console.error('更新材质失败:', error);
      throw error;
    }
  },

  /**
   * 删除材质
   */
  async deleteMaterial(materialId: string) {
    try {
      await axios.delete(`${API_URL}/${materialId}`);

      // 清除缓存
      materialCache.delete(materialId);
    } catch (error) {
      console.error('删除材质失败:', error);
      throw error;
    }
  },

  /**
   * 批量删除材质
   */
  async deleteMaterials(materialIds: string[]) {
    try {
      await axios.delete(API_URL, { data: { ids: materialIds } });

      // 清除缓存
      materialIds.forEach(id => materialCache.delete(id));
    } catch (error) {
      console.error('批量删除材质失败:', error);
      throw error;
    }
  },

  /**
   * 导出材质
   */
  async exportMaterial(materialId: string, format: 'json' | 'mtl' | 'gltf' = 'json') {
    try {
      const response = await axios.get(`${API_URL}/${materialId}/export`, {
        params: { format },
        responseType: format === 'json' ? 'json' : 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('导出材质失败:', error);
      throw error;
    }
  },

  /**
   * 批量导出材质
   */
  async exportMaterials(materialIds: string[], format: 'json' | 'zip' = 'json') {
    try {
      const response = await axios.post(`${API_URL}/export`,
        { ids: materialIds, format },
        { responseType: format === 'zip' ? 'blob' : 'json' }
      );
      return response.data;
    } catch (error) {
      console.error('批量导出材质失败:', error);
      throw error;
    }
  },

  /**
   * 导入材质
   */
  async importMaterial(materialData: any, options?: {
    overwrite?: boolean;
    validateOnly?: boolean;
  }) {
    try {
      const response = await axios.post(`${API_URL}/import`, {
        ...materialData,
        options
      });

      const importedMaterial = response.data;

      // 更新缓存
      if (importedMaterial.id) {
        materialCache.set(importedMaterial.id, importedMaterial);
      }

      return importedMaterial;
    } catch (error) {
      console.error('导入材质失败:', error);
      throw error;
    }
  },

  /**
   * 复制材质
   */
  async duplicateMaterial(materialId: string, newName?: string) {
    try {
      const response = await axios.post(`${API_URL}/${materialId}/duplicate`, {
        name: newName || `${materialId}_copy_${Date.now()}`
      });

      const duplicatedMaterial = response.data;

      // 更新缓存
      materialCache.set(duplicatedMaterial.id, duplicatedMaterial);

      return duplicatedMaterial;
    } catch (error) {
      console.error('复制材质失败:', error);
      throw error;
    }
  },

  /**
   * 上传纹理文件
   */
  async uploadTexture(file: File, options?: {
    compress?: boolean;
    generateMipmaps?: boolean;
    maxSize?: number;
  }) {
    try {
      const formData = new FormData();
      formData.append('texture', file);

      if (options) {
        formData.append('options', JSON.stringify(options));
      }

      const response = await axios.post(`${API_URL}/textures/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 1));
          console.log(`上传进度: ${percentCompleted}%`);
        }
      });

      return response.data;
    } catch (error) {
      console.error('上传纹理失败:', error);
      throw error;
    }
  },

  /**
   * 验证材质数据
   */
  async validateMaterial(materialData: Partial<Material>) {
    try {
      const response = await axios.post(`${API_URL}/validate`, materialData);
      return response.data;
    } catch (error) {
      console.error('验证材质失败:', error);
      throw error;
    }
  },

  /**
   * 获取材质预设
   */
  async getMaterialPresets(category?: string) {
    try {
      const response = await axios.get(`${API_URL}/presets`, {
        params: { category }
      });
      return response.data;
    } catch (error) {
      console.error('获取材质预设失败:', error);
      throw error;
    }
  },

  /**
   * 清除缓存
   */
  clearCache() {
    materialCache.clear();
  },

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return {
      size: materialCache.size,
      keys: [...materialCache.keys()]
    };
  }
};
