import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { NotificationService } from './notification.service';
import { NotificationController } from './notification.controller';
import { NotificationChannelEntity } from './entities/notification-channel.entity';
import { NotificationHistoryEntity } from './entities/notification-history.entity';
import { EmailNotifierService } from './notifiers/email-notifier.service';
import { WebhookNotifierService } from './notifiers/webhook-notifier.service';
import { SlackNotifierService } from './notifiers/slack-notifier.service';
import { DingTalkNotifierService } from './notifiers/dingtalk-notifier.service';
import { WeChatNotifierService } from './notifiers/wechat-notifier.service';

@Module({
  imports: [TypeOrmModule.forFeature([NotificationChannelEntity, NotificationHistoryEntity]), HttpModule],
  controllers: [NotificationController],
  providers: [
    NotificationService,
    EmailNotifierService,
    WebhookNotifierService,
    SlackNotifierService,
    DingTalkNotifierService,
    WeChatNotifierService,
  ],
  exports: [NotificationService],
})
export class NotificationModule {}
