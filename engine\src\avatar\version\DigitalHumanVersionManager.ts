import { EventEmitter } from 'events';
import { DigitalHumanComponent } from '../components/DigitalHumanComponent';
import { type Entity  } from '../../core/Entity';

/**
 * 版本类型
 */
export enum VersionType {
  MAJOR = 'major',
  MINOR = 'minor',
  PATCH = 'patch',
  SNAPSHOT = 'snapshot',
  BRANCH = 'branch'
}

/**
 * 版本状态
 */
export enum VersionStatus {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
  DELETED = 'deleted',
  MERGED = 'merged'
}

/**
 * 变更类型
 */
export enum ChangeType {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  MOVE = 'move',
  RENAME = 'rename',
  PROPERTY_CHANGE = 'property_change'
}

/**
 * 版本信息
 */
export interface VersionInfo {
  /** 版本ID */
  id: string;
  /** 版本号 */
  version: string;
  /** 版本类型 */
  type: VersionType;
  /** 版本状态 */
  status: VersionStatus;
  /** 父版本ID */
  parentId?: string;
  /** 分支名称 */
  branchName?: string;
  /** 版本描述 */
  description: string;
  /** 创建者 */
  author: string;
  /** 创建时间 */
  createdAt: Date;
  /** 标签 */
  tags: string[];
  /** 元数据 */
  metadata: Map<string, any>;
}

/**
 * 变更记录
 */
export interface ChangeRecord {
  /** 变更ID */
  id: string;
  /** 变更类型 */
  type: ChangeType;
  /** 目标对象 */
  target: string;
  /** 属性路径 */
  propertyPath?: string;
  /** 旧值 */
  oldValue?: any;
  /** 新值 */
  newValue?: any;
  /** 时间戳 */
  timestamp: Date;
  /** 描述 */
  description: string;
}

/**
 * 版本快照
 */
export interface VersionSnapshot {
  /** 快照ID */
  id: string;
  /** 版本ID */
  versionId: string;
  /** 数字人数据 */
  digitalHumanData: any;
  /** 变更记录 */
  changes: ChangeRecord[];
  /** 创建时间 */
  createdAt: Date;
  /** 数据大小 */
  dataSize: number;
  /** 压缩后大小 */
  compressedSize?: number;
}

/**
 * 分支信息
 */
export interface BranchInfo {
  /** 分支ID */
  id: string;
  /** 分支名称 */
  name: string;
  /** 基础版本ID */
  baseVersionId: string;
  /** 当前版本ID */
  currentVersionId: string;
  /** 分支描述 */
  description: string;
  /** 创建者 */
  author: string;
  /** 创建时间 */
  createdAt: Date;
  /** 是否活跃 */
  isActive: boolean;
  /** 标签 */
  tags: string[];
}

/**
 * 合并冲突
 */
export interface MergeConflict {
  /** 冲突ID */
  id: string;
  /** 属性路径 */
  propertyPath: string;
  /** 源分支值 */
  sourceValue: any;
  /** 目标分支值 */
  targetValue: any;
  /** 冲突类型 */
  conflictType: 'value' | 'structure' | 'deletion';
  /** 解决方案 */
  resolution?: 'source' | 'target' | 'custom';
  /** 自定义值 */
  customValue?: any;
}

/**
 * 版本管理器配置
 */
export interface VersionManagerConfig {
  /** 最大版本数量 */
  maxVersions: number;
  /** 最大快照数量 */
  maxSnapshots: number;
  /** 是否启用自动快照 */
  autoSnapshot: boolean;
  /** 自动快照间隔（秒） */
  autoSnapshotInterval: number;
  /** 是否启用压缩 */
  enableCompression: boolean;
  /** 是否启用调试 */
  debug: boolean;
}

/**
 * 数字人版本管理器
 * 实现数字人的版本控制和历史管理，支持撤销重做、快照保存和分支管理
 */
export class DigitalHumanVersionManager extends EventEmitter {
  /** 配置 */
  private config: VersionManagerConfig;

  /** 版本存储 */
  private versions: Map<string, VersionInfo>;

  /** 快照存储 */
  private snapshots: Map<string, VersionSnapshot>;

  /** 分支存储 */
  private branches: Map<string, BranchInfo>;

  /** 实体版本映射 */
  private entityVersions: Map<string, string>;

  /** 当前分支映射 */
  private entityBranches: Map<string, string>;

  /** 变更历史 */
  private changeHistory: Map<string, ChangeRecord[]>;

  /** 撤销栈 */
  private undoStack: Map<string, VersionSnapshot[]>;

  /** 重做栈 */
  private redoStack: Map<string, VersionSnapshot[]>;

  /** 自动快照定时器 */
  private autoSnapshotTimers: Map<string, NodeJS.Timeout>;

  /** 版本计数器 */
  private versionCounter: number = 1;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: Partial<VersionManagerConfig> = {}) {
    super();

    this.config = {
      maxVersions: 100,
      maxSnapshots: 50,
      autoSnapshot: true,
      autoSnapshotInterval: 300, // 5分钟
      enableCompression: true,
      debug: false,
      ...config
    };

    this.versions = new Map();
    this.snapshots = new Map();
    this.branches = new Map();
    this.entityVersions = new Map();
    this.entityBranches = new Map();
    this.changeHistory = new Map();
    this.undoStack = new Map();
    this.redoStack = new Map();
    this.autoSnapshotTimers = new Map();

    this.initializeDefaultBranch();
  }

  /**
   * 初始化默认分支
   */
  private initializeDefaultBranch(): void {
    const defaultBranch: BranchInfo = {
      id: 'main',
      name: 'main',
      baseVersionId: '',
      currentVersionId: '',
      description: '主分支',
      author: 'system',
      createdAt: new Date(),
      isActive: true,
      tags: ['default']
    };

    this.branches.set('main', defaultBranch);

    if (this.config.debug) {
      console.log('DigitalHumanVersionManager: 初始化默认分支');
    }
  }

  // ==================== 版本管理 ====================

  /**
   * 创建版本
   * @param entityId 实体ID
   * @param type 版本类型
   * @param description 描述
   * @param author 作者
   * @returns 版本ID
   */
  public createVersion(
    entityId: string,
    type: VersionType = VersionType.MINOR,
    description: string = '',
    author: string = 'user'
  ): string {
    const versionId = this.generateVersionId();
    const currentBranch = this.getCurrentBranch(entityId);
    const parentVersionId = this.entityVersions.get(entityId);

    const version: VersionInfo = {
      id: versionId,
      version: this.generateVersionNumber(type, parentVersionId),
      type,
      status: VersionStatus.ACTIVE,
      parentId: parentVersionId,
      branchName: currentBranch.name,
      description,
      author,
      createdAt: new Date(),
      tags: [],
      metadata: new Map()
    };

    this.versions.set(versionId, version);
    this.entityVersions.set(entityId, versionId);

    // 更新分支当前版本
    currentBranch.currentVersionId = versionId;

    // 创建快照
    this.createSnapshot(entityId, versionId);

    // 清理旧版本
    this.cleanupOldVersions();

    this.emit('versionCreated', entityId, version);

    if (this.config.debug) {
      console.log(`创建版本: ${version.version} for 实体 ${entityId}`);
    }

    return versionId;
  }

  /**
   * 切换到指定版本
   * @param entityId 实体ID
   * @param versionId 版本ID
   * @returns 是否成功切换
   */
  public switchToVersion(entityId: string, versionId: string): boolean {
    const version = this.versions.get(versionId);
    if (!version) {
      console.warn(`版本不存在: ${versionId}`);
      return false;
    }

    const snapshot = this.findSnapshotByVersion(versionId);
    if (!snapshot) {
      console.warn(`版本快照不存在: ${versionId}`);
      return false;
    }

    // 应用快照数据
    const success = this.applySnapshot(entityId, snapshot);
    if (success) {
      this.entityVersions.set(entityId, versionId);
      this.emit('versionSwitched', entityId, version);

      if (this.config.debug) {
        console.log(`切换到版本: ${version.version} for 实体 ${entityId}`);
      }
    }

    return success;
  }

  /**
   * 获取版本历史
   * @param entityId 实体ID
   * @returns 版本列表
   */
  public getVersionHistory(entityId: string): VersionInfo[] {
    const currentBranch = this.getCurrentBranch(entityId);
    const versions: VersionInfo[] = [];

    // 收集当前分支的所有版本
    for (const version of this.versions.values()) {
      if (version.branchName === currentBranch.name) {
        versions.push(version);
      }
    }

    // 按创建时间排序
    return versions.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  /**
   * 删除版本
   * @param versionId 版本ID
   * @returns 是否成功删除
   */
  public deleteVersion(versionId: string): boolean {
    const version = this.versions.get(versionId);
    if (!version) return false;

    // 标记为已删除而不是直接删除
    version.status = VersionStatus.DELETED;

    // 删除相关快照
    const snapshot = this.findSnapshotByVersion(versionId);
    if (snapshot) {
      this.snapshots.delete(snapshot.id);
    }

    this.emit('versionDeleted', version);

    if (this.config.debug) {
      console.log(`删除版本: ${version.version}`);
    }

    return true;
  }

  // ==================== 快照管理 ====================

  /**
   * 创建快照
   * @param entityId 实体ID
   * @param versionId 版本ID
   * @returns 快照ID
   */
  public createSnapshot(entityId: string, versionId: string): string {
    const snapshotId = this.generateSnapshotId();
    
    // 获取数字人数据
    const digitalHumanData = this.captureDigitalHumanData(entityId);
    const changes = this.changeHistory.get(entityId) || [];

    const snapshot: VersionSnapshot = {
      id: snapshotId,
      versionId,
      digitalHumanData,
      changes: [...changes],
      createdAt: new Date(),
      dataSize: JSON.stringify(digitalHumanData).length
    };

    // 压缩数据（如果启用）
    if (this.config.enableCompression) {
      snapshot.compressedSize = this.compressData(digitalHumanData);
    }

    this.snapshots.set(snapshotId, snapshot);

    // 添加到撤销栈
    this.addToUndoStack(entityId, snapshot);

    // 清理旧快照
    this.cleanupOldSnapshots();

    this.emit('snapshotCreated', entityId, snapshot);

    if (this.config.debug) {
      console.log(`创建快照: ${snapshotId} for 版本 ${versionId}`);
    }

    return snapshotId;
  }

  /**
   * 应用快照
   * @param entityId 实体ID
   * @param snapshot 快照
   * @returns 是否成功应用
   */
  private applySnapshot(entityId: string, snapshot: VersionSnapshot): boolean {
    try {
      // 这里应该实现将快照数据应用到数字人实体的逻辑
      // 实际实现需要根据具体的数字人组件结构来处理
      
      this.emit('snapshotApplied', entityId, snapshot);
      return true;
    } catch (error) {
      console.error('应用快照失败:', error);
      return false;
    }
  }

  /**
   * 捕获数字人数据
   * @param entityId 实体ID
   * @returns 数字人数据
   */
  private captureDigitalHumanData(entityId: string): any {
    // 这里应该实现捕获数字人完整状态的逻辑
    // 包括几何体、材质、动画、表情等所有数据
    
    return {
      entityId,
      timestamp: Date.now(),
      // 实际数据结构需要根据DigitalHumanComponent来定义
      data: {}
    };
  }

  // ==================== 分支管理 ====================

  /**
   * 创建分支
   * @param entityId 实体ID
   * @param branchName 分支名称
   * @param description 描述
   * @param author 作者
   * @returns 分支ID
   */
  public createBranch(
    entityId: string,
    branchName: string,
    description: string = '',
    author: string = 'user'
  ): string {
    const branchId = this.generateBranchId();
    const currentVersionId = this.entityVersions.get(entityId) || '';

    const branch: BranchInfo = {
      id: branchId,
      name: branchName,
      baseVersionId: currentVersionId,
      currentVersionId,
      description,
      author,
      createdAt: new Date(),
      isActive: true,
      tags: []
    };

    this.branches.set(branchId, branch);
    this.entityBranches.set(entityId, branchId);

    this.emit('branchCreated', entityId, branch);

    if (this.config.debug) {
      console.log(`创建分支: ${branchName} for 实体 ${entityId}`);
    }

    return branchId;
  }

  /**
   * 切换分支
   * @param entityId 实体ID
   * @param branchId 分支ID
   * @returns 是否成功切换
   */
  public switchBranch(entityId: string, branchId: string): boolean {
    const branch = this.branches.get(branchId);
    if (!branch) {
      console.warn(`分支不存在: ${branchId}`);
      return false;
    }

    // 切换到分支的当前版本
    const success = this.switchToVersion(entityId, branch.currentVersionId);
    if (success) {
      this.entityBranches.set(entityId, branchId);
      this.emit('branchSwitched', entityId, branch);

      if (this.config.debug) {
        console.log(`切换到分支: ${branch.name} for 实体 ${entityId}`);
      }
    }

    return success;
  }

  /**
   * 合并分支
   * @param entityId 实体ID
   * @param sourceBranchId 源分支ID
   * @param targetBranchId 目标分支ID
   * @returns 合并结果
   */
  public mergeBranch(
    entityId: string,
    sourceBranchId: string,
    targetBranchId: string
  ): { success: boolean; conflicts?: MergeConflict[] } {
    const sourceBranch = this.branches.get(sourceBranchId);
    const targetBranch = this.branches.get(targetBranchId);

    if (!sourceBranch || !targetBranch) {
      return { success: false };
    }

    // 检测合并冲突
    const conflicts = this.detectMergeConflicts(sourceBranch, targetBranch);

    if (conflicts.length > 0) {
      this.emit('mergeConflicts', entityId, conflicts);
      return { success: false, conflicts };
    }

    // 执行合并
    const mergeVersionId = this.createVersion(
      entityId,
      VersionType.MINOR,
      `合并分支 ${sourceBranch.name} 到 ${targetBranch.name}`,
      'system'
    );

    // 标记源分支为已合并
    sourceBranch.isActive = false;
    
    this.emit('branchMerged', entityId, sourceBranch, targetBranch, mergeVersionId);

    if (this.config.debug) {
      console.log(`合并分支: ${sourceBranch.name} -> ${targetBranch.name}`);
    }

    return { success: true };
  }

  /**
   * 获取当前分支
   * @param entityId 实体ID
   * @returns 当前分支
   */
  private getCurrentBranch(entityId: string): BranchInfo {
    const branchId = this.entityBranches.get(entityId) || 'main';
    return this.branches.get(branchId) || this.branches.get('main')!;
  }

  // 其他辅助方法将在下一部分实现...

  /**
   * 生成版本ID
   */
  private generateVersionId(): string {
    return `v_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成快照ID
   */
  private generateSnapshotId(): string {
    return `s_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成分支ID
   */
  private generateBranchId(): string {
    return `b_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成版本号
   */
  private generateVersionNumber(type: VersionType, parentVersionId?: string): string {
    // 简化的版本号生成逻辑
    return `${this.versionCounter++}.0.0`;
  }

  /**
   * 压缩数据
   */
  private compressData(data: any): number {
    // 简化的压缩实现
    return JSON.stringify(data).length * 0.7; // 假设压缩率70%
  }

  /**
   * 清理旧版本
   */
  private cleanupOldVersions(): void {
    // 实现版本清理逻辑
  }

  /**
   * 清理旧快照
   */
  private cleanupOldSnapshots(): void {
    // 实现快照清理逻辑
  }

  /**
   * 查找版本快照
   */
  private findSnapshotByVersion(versionId: string): VersionSnapshot | undefined {
    for (const snapshot of this.snapshots.values()) {
      if (snapshot.versionId === versionId) {
        return snapshot;
      }
    }
    return undefined;
  }

  /**
   * 添加到撤销栈
   */
  private addToUndoStack(entityId: string, snapshot: VersionSnapshot): void {
    let stack = this.undoStack.get(entityId);
    if (!stack) {
      stack = [];
      this.undoStack.set(entityId, stack);
    }

    stack.push(snapshot);

    // 限制栈大小
    if (stack.length > 20) {
      stack.shift();
    }

    // 清空重做栈
    this.redoStack.set(entityId, []);
  }

  /**
   * 检测合并冲突
   */
  private detectMergeConflicts(sourceBranch: BranchInfo, targetBranch: BranchInfo): MergeConflict[] {
    // 简化的冲突检测实现
    return [];
  }

  // ==================== 撤销重做系统 ====================

  /**
   * 撤销操作
   * @param entityId 实体ID
   * @returns 是否成功撤销
   */
  public undo(entityId: string): boolean {
    const undoStack = this.undoStack.get(entityId);
    if (!undoStack || undoStack.length === 0) {
      return false;
    }

    // 保存当前状态到重做栈
    const currentSnapshot = this.createCurrentSnapshot(entityId);
    let redoStack = this.redoStack.get(entityId);
    if (!redoStack) {
      redoStack = [];
      this.redoStack.set(entityId, redoStack);
    }
    redoStack.push(currentSnapshot);

    // 恢复上一个状态
    const previousSnapshot = undoStack.pop()!;
    const success = this.applySnapshot(entityId, previousSnapshot);

    if (success) {
      this.emit('undoPerformed', entityId, previousSnapshot);

      if (this.config.debug) {
        console.log(`撤销操作 for 实体 ${entityId}`);
      }
    }

    return success;
  }

  /**
   * 重做操作
   * @param entityId 实体ID
   * @returns 是否成功重做
   */
  public redo(entityId: string): boolean {
    const redoStack = this.redoStack.get(entityId);
    if (!redoStack || redoStack.length === 0) {
      return false;
    }

    // 保存当前状态到撤销栈
    const currentSnapshot = this.createCurrentSnapshot(entityId);
    let undoStack = this.undoStack.get(entityId);
    if (!undoStack) {
      undoStack = [];
      this.undoStack.set(entityId, undoStack);
    }
    undoStack.push(currentSnapshot);

    // 恢复重做状态
    const redoSnapshot = redoStack.pop()!;
    const success = this.applySnapshot(entityId, redoSnapshot);

    if (success) {
      this.emit('redoPerformed', entityId, redoSnapshot);

      if (this.config.debug) {
        console.log(`重做操作 for 实体 ${entityId}`);
      }
    }

    return success;
  }

  /**
   * 创建当前快照
   * @param entityId 实体ID
   * @returns 当前快照
   */
  private createCurrentSnapshot(entityId: string): VersionSnapshot {
    const snapshotId = this.generateSnapshotId();
    const currentVersionId = this.entityVersions.get(entityId) || '';
    const digitalHumanData = this.captureDigitalHumanData(entityId);
    const changes = this.changeHistory.get(entityId) || [];

    return {
      id: snapshotId,
      versionId: currentVersionId,
      digitalHumanData,
      changes: [...changes],
      createdAt: new Date(),
      dataSize: JSON.stringify(digitalHumanData).length
    };
  }

  // ==================== 变更追踪 ====================

  /**
   * 记录变更
   * @param entityId 实体ID
   * @param type 变更类型
   * @param target 目标对象
   * @param propertyPath 属性路径
   * @param oldValue 旧值
   * @param newValue 新值
   * @param description 描述
   */
  public recordChange(
    entityId: string,
    type: ChangeType,
    target: string,
    propertyPath?: string,
    oldValue?: any,
    newValue?: any,
    description: string = ''
  ): void {
    const changeId = this.generateChangeId();

    const change: ChangeRecord = {
      id: changeId,
      type,
      target,
      propertyPath,
      oldValue,
      newValue,
      timestamp: new Date(),
      description
    };

    let changes = this.changeHistory.get(entityId);
    if (!changes) {
      changes = [];
      this.changeHistory.set(entityId, changes);
    }

    changes.push(change);

    // 限制变更历史大小
    if (changes.length > 1000) {
      changes.shift();
    }

    this.emit('changeRecorded', entityId, change);

    if (this.config.debug) {
      console.log(`记录变更: ${type} - ${target} for 实体 ${entityId}`);
    }
  }

  /**
   * 获取变更历史
   * @param entityId 实体ID
   * @param limit 限制数量
   * @returns 变更记录列表
   */
  public getChangeHistory(entityId: string, limit: number = 50): ChangeRecord[] {
    const changes = this.changeHistory.get(entityId) || [];
    return changes.slice(-limit).reverse();
  }

  /**
   * 清除变更历史
   * @param entityId 实体ID
   */
  public clearChangeHistory(entityId: string): void {
    this.changeHistory.delete(entityId);
    this.emit('changeHistoryCleared', entityId);

    if (this.config.debug) {
      console.log(`清除变更历史 for 实体 ${entityId}`);
    }
  }

  // ==================== 自动快照 ====================

  /**
   * 启用自动快照
   * @param entityId 实体ID
   */
  public enableAutoSnapshot(entityId: string): void {
    if (!this.config.autoSnapshot) return;

    // 清除现有定时器
    this.disableAutoSnapshot(entityId);

    const timer = setInterval(() => {
      const currentVersionId = this.entityVersions.get(entityId);
      if (currentVersionId) {
        this.createSnapshot(entityId, currentVersionId);
      }
    }, this.config.autoSnapshotInterval * 1000);

    this.autoSnapshotTimers.set(entityId, timer);

    if (this.config.debug) {
      console.log(`启用自动快照 for 实体 ${entityId}`);
    }
  }

  /**
   * 禁用自动快照
   * @param entityId 实体ID
   */
  public disableAutoSnapshot(entityId: string): void {
    const timer = this.autoSnapshotTimers.get(entityId);
    if (timer) {
      clearInterval(timer);
      this.autoSnapshotTimers.delete(entityId);

      if (this.config.debug) {
        console.log(`禁用自动快照 for 实体 ${entityId}`);
      }
    }
  }

  // ==================== 标签管理 ====================

  /**
   * 添加版本标签
   * @param versionId 版本ID
   * @param tag 标签
   */
  public addVersionTag(versionId: string, tag: string): boolean {
    const version = this.versions.get(versionId);
    if (!version) return false;

    if (!version.tags.includes(tag)) {
      version.tags.push(tag);
      this.emit('versionTagAdded', version, tag);

      if (this.config.debug) {
        console.log(`添加版本标签: ${tag} to ${version.version}`);
      }
    }

    return true;
  }

  /**
   * 移除版本标签
   * @param versionId 版本ID
   * @param tag 标签
   */
  public removeVersionTag(versionId: string, tag: string): boolean {
    const version = this.versions.get(versionId);
    if (!version) return false;

    const index = version.tags.indexOf(tag);
    if (index !== -1) {
      version.tags.splice(index, 1);
      this.emit('versionTagRemoved', version, tag);

      if (this.config.debug) {
        console.log(`移除版本标签: ${tag} from ${version.version}`);
      }
    }

    return true;
  }

  /**
   * 按标签搜索版本
   * @param tag 标签
   * @returns 版本列表
   */
  public findVersionsByTag(tag: string): VersionInfo[] {
    const results: VersionInfo[] = [];

    for (const version of this.versions.values()) {
      if (version.tags.includes(tag)) {
        results.push(version);
      }
    }

    return results.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  // ==================== 统计和查询 ====================

  /**
   * 获取版本统计信息
   * @param entityId 实体ID
   * @returns 统计信息
   */
  public getVersionStats(entityId: string): {
    totalVersions: number;
    totalSnapshots: number;
    totalBranches: number;
    currentVersion: string;
    currentBranch: string;
    diskUsage: number;
  } {
    const currentBranch = this.getCurrentBranch(entityId);
    const currentVersionId = this.entityVersions.get(entityId) || '';
    const currentVersion = this.versions.get(currentVersionId);

    // 计算分支版本数
    let branchVersions = 0;
    for (const version of this.versions.values()) {
      if (version.branchName === currentBranch.name) {
        branchVersions++;
      }
    }

    // 计算快照数
    let branchSnapshots = 0;
    let diskUsage = 0;
    for (const snapshot of this.snapshots.values()) {
      const version = this.versions.get(snapshot.versionId);
      if (version && version.branchName === currentBranch.name) {
        branchSnapshots++;
        diskUsage += snapshot.compressedSize || snapshot.dataSize;
      }
    }

    return {
      totalVersions: branchVersions,
      totalSnapshots: branchSnapshots,
      totalBranches: this.branches.size,
      currentVersion: currentVersion?.version || '',
      currentBranch: currentBranch.name,
      diskUsage
    };
  }

  /**
   * 比较两个版本
   * @param versionId1 版本1 ID
   * @param versionId2 版本2 ID
   * @returns 比较结果
   */
  public compareVersions(versionId1: string, versionId2: string): {
    differences: Array<{
      path: string;
      type: 'added' | 'removed' | 'modified';
      oldValue?: any;
      newValue?: any;
    }>;
    similarity: number;
  } {
    const snapshot1 = this.findSnapshotByVersion(versionId1);
    const snapshot2 = this.findSnapshotByVersion(versionId2);

    if (!snapshot1 || !snapshot2) {
      return { differences: [], similarity: 0 };
    }

    // 简化的比较实现
    // 实际实现需要深度比较两个快照的数据
    return {
      differences: [],
      similarity: 0.95 // 假设95%相似度
    };
  }

  // ==================== 清理和销毁 ====================

  /**
   * 清理实体相关数据
   * @param entityId 实体ID
   */
  public cleanupEntity(entityId: string): void {
    // 禁用自动快照
    this.disableAutoSnapshot(entityId);

    // 清理映射
    this.entityVersions.delete(entityId);
    this.entityBranches.delete(entityId);
    this.changeHistory.delete(entityId);
    this.undoStack.delete(entityId);
    this.redoStack.delete(entityId);

    this.emit('entityCleaned', entityId);

    if (this.config.debug) {
      console.log(`清理实体数据: ${entityId}`);
    }
  }

  /**
   * 销毁版本管理器
   */
  public dispose(): void {
    // 清理所有定时器
    for (const timer of this.autoSnapshotTimers.values()) {
      clearInterval(timer);
    }

    // 清理所有数据
    this.versions.clear();
    this.snapshots.clear();
    this.branches.clear();
    this.entityVersions.clear();
    this.entityBranches.clear();
    this.changeHistory.clear();
    this.undoStack.clear();
    this.redoStack.clear();
    this.autoSnapshotTimers.clear();

    // 移除所有事件监听器
    this.removeAllListeners();

    if (this.config.debug) {
      console.log('DigitalHumanVersionManager: 已销毁');
    }
  }

  /**
   * 生成变更ID
   */
  private generateChangeId(): string {
    return `c_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
