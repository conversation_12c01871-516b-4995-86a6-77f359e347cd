/**
 * 数字人系统
 * 管理数字人的生成、编辑和渲染
 */
import { System } from '../../core/System';
import { Entity } from '../../core/Entity';
import { type World } from '../../core/World';
import { DigitalHumanSource, ClothingSlotType } from '../components/DigitalHumanComponent';
import { MultiActionFusionManager } from '../animation/MultiActionFusionManager';
/**
 * 数字人系统配置
 */
export interface DigitalHumanSystemConfig {
    /** 是否启用调试模式 */
    debug?: boolean;
    /** 最大并发生成数量 */
    maxConcurrentGenerations?: number;
    /** 自动保存间隔（毫秒） */
    autoSaveInterval?: number;
    /** 是否启用自动优化 */
    enableAutoOptimization?: boolean;
}
/**
 * 数字人生成请求
 */
export interface DigitalHumanGenerationRequest {
    /** 请求ID */
    id: string;
    /** 用户ID */
    userId: string;
    /** 生成类型 */
    source: DigitalHumanSource;
    /** 源数据 */
    sourceData: {
        photoUrl?: string;
        fileUrl?: string;
        marketplaceId?: string;
    };
    /** 生成选项 */
    options: {
        name?: string;
        tags?: string[];
        licenseType?: string;
        generateClothing?: boolean;
        generateAnimations?: boolean;
    };
    /** 回调函数 */
    onProgress?: (progress: number) => void;
    /** 完成回调 */
    onComplete?: (entity: Entity) => void;
    /** 错误回调 */
    onError?: (error: Error) => void;
}
/**
 * 数字人系统
 */
export declare class DigitalHumanSystem extends System {
    /** 系统优先级 */
    static readonly PRIORITY = 5;
    /** 配置 */
    private config;
    /** 事件发射器 */
    private eventEmitter;
    /** 数字人实体映射 */
    private digitalHumans;
    /** 生成队列 */
    private generationQueue;
    /** 当前生成中的请求 */
    private activeGenerations;
    /** 自动保存定时器 */
    private autoSaveTimer?;
    /**
     * 构造函数
     * @param world 世界实例
     * @param config 系统配置
     */
    constructor(world: World, config?: DigitalHumanSystemConfig);
    /**
     * 系统初始化
     */
    initialize(): void;
    /**
     * 系统更新
     * @param deltaTime 帧间隔时间
     */
    update(deltaTime: number): void;
    /**
     * 注册数字人
     * @param entity 实体
     */
    registerDigitalHuman(entity: Entity): void;
    /**
     * 注销数字人
     * @param entity 实体
     */
    unregisterDigitalHuman(entity: Entity): void;
    /**
     * 创建数字人
     * @param request 生成请求
     * @returns Promise<Entity>
     */
    createDigitalHuman(request: DigitalHumanGenerationRequest): Promise<Entity>;
    /**
     * 获取数字人
     * @param entityId 实体ID
     * @returns 数字人实体
     */
    getDigitalHuman(entityId: string): Entity | undefined;
    /**
     * 获取所有数字人
     * @returns 数字人实体数组
     */
    getAllDigitalHumans(): Entity[];
    /**
     * 删除数字人
     * @param entityId 实体ID
     * @returns 是否成功删除
     */
    deleteDigitalHuman(entityId: string): boolean;
    /**
     * 更新数字人外观
     * @param entityId 实体ID
     * @param slotType 插槽类型
     * @param itemId 服装项ID
     * @param itemUrl 服装项URL
     */
    updateDigitalHumanClothing(entityId: string, slotType: ClothingSlotType, itemId: string, itemUrl: string): void;
    /**
     * 处理生成队列
     */
    private processGenerationQueue;
    /**
     * 处理生成请求
     * @param request 生成请求
     */
    private processGenerationRequest;
    /**
     * 根据来源生成数字人
     * @param entity 实体
     * @param request 生成请求
     */
    private generateDigitalHumanBySource;
    /**
     * 从照片生成数字人
     * @param entity 实体
     * @param request 生成请求
     */
    private generateFromPhoto;
    /**
     * 从上传文件生成数字人
     * @param entity 实体
     * @param request 生成请求
     */
    private generateFromUpload;
    /**
     * 从市场下载生成数字人
     * @param entity 实体
     * @param request 生成请求
     */
    private generateFromMarketplace;
    /**
     * 手动创建数字人
     * @param entity 实体
     * @param request 生成请求
     */
    private generateManually;
    /**
     * 模拟生成过程
     * @param onProgress 进度回调
     */
    private simulateGeneration;
    /**
     * 更新数字人
     * @param entity 实体
     * @param deltaTime 帧间隔时间
     */
    private updateDigitalHuman;
    /**
     * 执行自动优化
     */
    private performAutoOptimization;
    /**
     * 自动保存
     */
    private autoSave;
    /**
     * 保存数字人
     * @param entity 实体
     */
    private saveDigitalHuman;
    /**
     * 获取数字人的多动作融合管理器
     * @param entity 数字人实体
     * @returns 多动作融合管理器
     */
    getMultiActionFusionManager(entity: Entity): MultiActionFusionManager | null;
    /**
     * 销毁系统
     */
    dispose(): void;
}
