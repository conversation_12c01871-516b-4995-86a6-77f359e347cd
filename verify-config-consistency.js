#!/usr/bin/env node

/**
 * 配置文件一致性验证脚本
 * 检查.env、docker-compose.windows.yml、start-windows.ps1等配置文件的一致性
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 验证配置文件一致性...\n');

// 测试1: 检查.env文件
function testEnvFile() {
  console.log('📋 测试1: 检查.env文件');
  
  const envPath = path.join(__dirname, '.env');
  
  if (!fs.existsSync(envPath)) {
    console.log('❌ .env文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(envPath, 'utf8');
  
  // 检查关键配置项
  const requiredConfigs = [
    'NODE_ENV=production',
    'MYSQL_ROOT_PASSWORD=',
    'REACT_APP_API_URL=http://localhost:3000/api',
    'REACT_APP_COLLABORATION_SERVER_URL=ws://localhost:3007',
    'API_GATEWAY_PORT=3000',
    'COLLABORATION_LB_PORT=3007'
  ];
  
  let allPresent = true;
  for (const config of requiredConfigs) {
    if (!content.includes(config.split('=')[0] + '=')) {
      console.log(`   ❌ 缺少配置: ${config.split('=')[0]}`);
      allPresent = false;
    }
  }
  
  if (allPresent) {
    console.log('✅ .env文件配置完整');
    return true;
  } else {
    console.log('❌ .env文件配置不完整');
    return false;
  }
}

// 测试2: 检查docker-compose.windows.yml
function testDockerCompose() {
  console.log('📋 测试2: 检查docker-compose.windows.yml');
  
  const dockerComposePath = path.join(__dirname, 'docker-compose.windows.yml');
  
  if (!fs.existsSync(dockerComposePath)) {
    console.log('❌ docker-compose.windows.yml文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(dockerComposePath, 'utf8');
  
  // 检查关键服务
  const requiredServices = [
    'mysql:',
    'redis:',
    'api-gateway:',
    'collaboration-load-balancer:',
    'editor:'
  ];
  
  let allPresent = true;
  for (const service of requiredServices) {
    if (!content.includes(service)) {
      console.log(`   ❌ 缺少服务: ${service.replace(':', '')}`);
      allPresent = false;
    }
  }
  
  // 检查编辑器端口配置
  if (!content.includes("'80:80'")) {
    console.log('   ❌ 编辑器端口配置错误');
    allPresent = false;
  }
  
  // 检查环境变量
  const envVars = [
    'REACT_APP_API_URL=http://localhost:3000/api',
    'REACT_APP_COLLABORATION_SERVER_URL=ws://localhost:3007'
  ];
  
  for (const envVar of envVars) {
    if (!content.includes(envVar)) {
      console.log(`   ❌ 缺少环境变量: ${envVar.split('=')[0]}`);
      allPresent = false;
    }
  }
  
  if (allPresent) {
    console.log('✅ docker-compose.windows.yml配置正确');
    return true;
  } else {
    console.log('❌ docker-compose.windows.yml配置有问题');
    return false;
  }
}

// 测试3: 检查编辑器Dockerfile
function testEditorDockerfile() {
  console.log('📋 测试3: 检查编辑器Dockerfile');
  
  const dockerfilePath = path.join(__dirname, 'editor/Dockerfile');
  
  if (!fs.existsSync(dockerfilePath)) {
    console.log('❌ editor/Dockerfile文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(dockerfilePath, 'utf8');
  
  // 检查关键步骤
  const requiredSteps = [
    'FROM node:22-alpine AS builder',
    'COPY engine ./engine',
    'npm run build',
    'FROM nginx:alpine',
    'COPY --from=builder /app/dist /usr/share/nginx/html',
    'EXPOSE 80'
  ];
  
  let allPresent = true;
  for (const step of requiredSteps) {
    if (!content.includes(step)) {
      console.log(`   ❌ 缺少构建步骤: ${step}`);
      allPresent = false;
    }
  }
  
  if (allPresent) {
    console.log('✅ editor/Dockerfile配置正确');
    return true;
  } else {
    console.log('❌ editor/Dockerfile配置有问题');
    return false;
  }
}

// 测试4: 检查nginx配置
function testNginxConfig() {
  console.log('📋 测试4: 检查nginx配置');
  
  const nginxConfigPath = path.join(__dirname, 'editor/nginx.conf');
  
  if (!fs.existsSync(nginxConfigPath)) {
    console.log('❌ editor/nginx.conf文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(nginxConfigPath, 'utf8');
  
  // 检查关键配置
  const requiredConfigs = [
    'listen 80;',
    'proxy_pass http://api-gateway:3000/api/;',
    'try_files $uri $uri/ /index.html;'
  ];
  
  let allPresent = true;
  for (const config of requiredConfigs) {
    if (!content.includes(config)) {
      console.log(`   ❌ 缺少配置: ${config}`);
      allPresent = false;
    }
  }
  
  if (allPresent) {
    console.log('✅ nginx配置正确');
    return true;
  } else {
    console.log('❌ nginx配置有问题');
    return false;
  }
}

// 测试5: 检查启动脚本
function testStartScript() {
  console.log('📋 测试5: 检查启动脚本');
  
  const startScriptPath = path.join(__dirname, 'start-windows.ps1');
  
  if (!fs.existsSync(startScriptPath)) {
    console.log('❌ start-windows.ps1文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(startScriptPath, 'utf8');
  
  // 检查关键功能
  const requiredFeatures = [
    'docker-compose',
    'docker-compose.windows.yml',
    'Write-Success',
    'Write-Error'
  ];
  
  let allPresent = true;
  for (const feature of requiredFeatures) {
    if (!content.includes(feature)) {
      console.log(`   ❌ 缺少功能: ${feature}`);
      allPresent = false;
    }
  }
  
  if (allPresent) {
    console.log('✅ 启动脚本配置正确');
    return true;
  } else {
    console.log('❌ 启动脚本配置有问题');
    return false;
  }
}

// 运行所有测试
function runAllTests() {
  const tests = [
    testEnvFile,
    testDockerCompose,
    testEditorDockerfile,
    testNginxConfig,
    testStartScript
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      if (test()) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.log(`❌ 测试执行失败: ${error.message}`);
      failed++;
    }
    console.log('');
  }
  
  console.log('📊 测试结果总结:');
  console.log(`✅ 通过: ${passed}/${tests.length}`);
  console.log(`❌ 失败: ${failed}/${tests.length}`);
  
  if (failed === 0) {
    console.log('\n🎉 所有配置文件一致性检查通过！');
    console.log('\n💡 建议下一步操作:');
    console.log('   1. 运行 .\\start-windows.ps1 启动所有服务');
    console.log('   2. 访问 http://localhost:80 测试编辑器');
    console.log('   3. 检查所有服务是否正常运行');
  } else {
    console.log('\n⚠️  部分配置文件检查失败，请检查上述错误信息');
  }
}

// 执行测试
runAllTests();
