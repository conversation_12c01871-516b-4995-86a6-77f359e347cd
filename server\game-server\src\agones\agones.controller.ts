import { Controller, Get, Post, Logger } from '@nestjs/common';
import { AgonesService } from './agones.service';

@Controller('agones')
export class AgonesController {
  private readonly logger = new Logger(AgonesController.name);

  constructor(private readonly agonesService: AgonesService) {}

  /**
   * 获取游戏服务器信息
   */
  @Get('info')
  async getGameServerInfo() {
    const gameServer = await this.agonesService.getGameServer();
    return {
      success: !!gameServer,
      data: gameServer,
    };
  }

  /**
   * 分配游戏服务器
   */
  @Post('allocate')
  async allocateGameServer() {
    const success = await this.agonesService.allocate();
    return {
      success,
      message: success ? '游戏服务器已分配' : '游戏服务器分配失败',
    };
  }

  /**
   * 关闭游戏服务器
   */
  @Post('shutdown')
  async shutdownGameServer() {
    const success = await this.agonesService.shutdown();
    return {
      success,
      message: success ? '游戏服务器已关闭' : '游戏服务器关闭失败',
    };
  }
}
