/**
 * 场景性能优化器
 * 优化场景生成和渲染性能，包括LOD、几何体合并、纹理优化等
 */
import * as THREE from 'three';
import { Scene } from '../../scene/Scene';
import { type Entity  } from '../../core/Entity';
import { MeshComponent } from '../../rendering/MeshComponent';

/**
 * 性能配置
 */
export interface PerformanceConfig {
  /** 目标FPS */
  targetFPS: number;
  /** 最大多边形数 */
  maxPolygons: number;
  /** 最大纹理尺寸 */
  maxTextureSize: number;
  /** 启用LOD */
  enableLOD: boolean;
  /** 启用几何体合并 */
  enableGeometryMerging: boolean;
  /** 启用纹理压缩 */
  enableTextureCompression: boolean;
  /** 启用实例化渲染 */
  enableInstancing: boolean;
}

/**
 * 性能统计
 */
export interface PerformanceStats {
  /** 当前FPS */
  currentFPS: number;
  /** 多边形数量 */
  polygonCount: number;
  /** 纹理内存使用 */
  textureMemory: number;
  /** 绘制调用次数 */
  drawCalls: number;
  /** 优化建议 */
  suggestions: string[];
}

/**
 * LOD管理器
 */
export class LODManager {
  private lodLevels: Map<string, THREE.LOD> = new Map();
  private distanceThresholds: number[] = [10, 25, 50];

  /**
   * 创建LOD对象
   */
  createLOD(entity: Entity, meshes: THREE.Mesh[]): THREE.LOD {
    const lod = new THREE.LOD();
    
    meshes.forEach((mesh, index) => {
      const distance = this.distanceThresholds[index] || this.distanceThresholds[this.distanceThresholds.length - 1];
      lod.addLevel(mesh, distance);
    });

    this.lodLevels.set(entity.id, lod);
    return lod;
  }

  /**
   * 更新LOD
   */
  updateLOD(camera: THREE.Camera): void {
    this.lodLevels.forEach(lod => {
      lod.update(camera);
    });
  }

  /**
   * 移除LOD
   */
  removeLOD(entityId: string): void {
    this.lodLevels.delete(entityId);
  }
}

/**
 * 几何体合并器
 */
export class GeometryMerger {
  private mergedGeometries: Map<string, THREE.BufferGeometry> = new Map();

  /**
   * 合并几何体
   */
  mergeGeometries(entities: Entity[]): THREE.BufferGeometry[] {
    const geometriesByMaterial = new Map<string, { geometry: THREE.BufferGeometry; matrix: THREE.Matrix4 }[]>();

    // 按材质分组
    entities.forEach(entity => {
      const meshRenderer = entity.getComponent('MeshComponent') as any;
      const transform = entity.getComponent('Transform') as any;
      
      if (meshRenderer && transform) {
        const mesh = (meshRenderer as any).mesh;
        if (mesh && mesh.geometry) {
          const materialKey = this.getMaterialKey(mesh.material);
          
          if (!geometriesByMaterial.has(materialKey)) {
            geometriesByMaterial.set(materialKey, []);
          }
          
          geometriesByMaterial.get(materialKey)!.push({
            geometry: mesh.geometry,
            matrix: (transform as any).getWorldMatrix()
          });
        }
      }
    });

    // 合并每组几何体
    const mergedGeometries: THREE.BufferGeometry[] = [];
    geometriesByMaterial.forEach((geometries, materialKey) => {
      if (geometries.length > 1) {
        const merged = this.mergeGeometryGroup(geometries);
        if (merged) {
          this.mergedGeometries.set(materialKey, merged);
          mergedGeometries.push(merged);
        }
      }
    });

    return mergedGeometries;
  }

  /**
   * 合并几何体组
   */
  private mergeGeometryGroup(geometries: { geometry: THREE.BufferGeometry; matrix: THREE.Matrix4 }[]): THREE.BufferGeometry | null {
    try {
      const mergedGeometry = new THREE.BufferGeometry();
      const positions: number[] = [];
      const normals: number[] = [];
      const uvs: number[] = [];
      const indices: number[] = [];
      
      let vertexOffset = 0;

      geometries.forEach(({ geometry, matrix }) => {
        const positionAttribute = geometry.getAttribute('position');
        const normalAttribute = geometry.getAttribute('normal');
        const uvAttribute = geometry.getAttribute('uv');
        const indexAttribute = geometry.getIndex();

        if (positionAttribute) {
          // 变换顶点位置
          const tempVector = new THREE.Vector3();
          for (let i = 0; i < positionAttribute.count; i++) {
            tempVector.fromBufferAttribute(positionAttribute, i);
            tempVector.applyMatrix4(matrix);
            positions.push(tempVector.x, tempVector.y, tempVector.z);
          }

          // 变换法线
          if (normalAttribute) {
            const normalMatrix = new THREE.Matrix3().getNormalMatrix(matrix);
            for (let i = 0; i < normalAttribute.count; i++) {
              tempVector.fromBufferAttribute(normalAttribute, i);
              tempVector.applyMatrix3(normalMatrix).normalize();
              normals.push(tempVector.x, tempVector.y, tempVector.z);
            }
          }

          // 复制UV坐标
          if (uvAttribute) {
            for (let i = 0; i < uvAttribute.count; i++) {
              uvs.push(uvAttribute.getX(i), uvAttribute.getY(i));
            }
          }

          // 调整索引
          if (indexAttribute) {
            for (let i = 0; i < indexAttribute.count; i++) {
              indices.push(indexAttribute.getX(i) + vertexOffset);
            }
          }

          vertexOffset += positionAttribute.count;
        }
      });

      // 设置属性
      mergedGeometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
      if (normals.length > 0) {
        mergedGeometry.setAttribute('normal', new THREE.Float32BufferAttribute(normals, 3));
      }
      if (uvs.length > 0) {
        mergedGeometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
      }
      if (indices.length > 0) {
        mergedGeometry.setIndex(indices);
      }

      return mergedGeometry;
    } catch (error) {
      console.error('几何体合并失败:', error);
      return null;
    }
  }

  /**
   * 获取材质键
   */
  private getMaterialKey(material: THREE.Material | THREE.Material[]): string {
    if (Array.isArray(material)) {
      return material.map(m => m.uuid).join('_');
    }
    return material.uuid;
  }
}

/**
 * 纹理优化器
 */
export class TextureOptimizer {
  private maxTextureSize: number = 1024;
  private compressionFormat: string = 'DXT1';

  constructor(maxSize: number = 1024) {
    this.maxTextureSize = maxSize;
  }

  /**
   * 优化纹理
   */
  async optimizeTexture(texture: THREE.Texture): Promise<THREE.Texture> {
    // 调整纹理尺寸
    if (texture.image) {
      const resizedImage = await this.resizeImage(texture.image, this.maxTextureSize);
      texture.image = resizedImage;
    }

    // 设置纹理参数
    texture.generateMipmaps = true;
    texture.minFilter = THREE.LinearMipmapLinearFilter;
    texture.magFilter = THREE.LinearFilter;
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.RepeatWrapping;

    return texture;
  }

  /**
   * 调整图像尺寸
   */
  private async resizeImage(image: HTMLImageElement | HTMLCanvasElement, maxSize: number): Promise<HTMLCanvasElement> {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;

    // 计算新尺寸
    let { width, height } = image;
    if (width > maxSize || height > maxSize) {
      const ratio = Math.min(maxSize / width, maxSize / height);
      width *= ratio;
      height *= ratio;
    }

    // 确保尺寸是2的幂
    width = this.nearestPowerOfTwo(width);
    height = this.nearestPowerOfTwo(height);

    canvas.width = width;
    canvas.height = height;

    // 绘制调整后的图像
    ctx.drawImage(image, 0, 0, width, height);

    return canvas;
  }

  /**
   * 获取最近的2的幂
   */
  private nearestPowerOfTwo(value: number): number {
    return Math.pow(2, Math.round(Math.log(value) / Math.log(2)));
  }

  /**
   * 压缩纹理
   */
  compressTexture(texture: THREE.Texture): THREE.CompressedTexture | THREE.Texture {
    // 这里应该实现实际的纹理压缩
    // 目前返回原纹理
    return texture;
  }
}

/**
 * 实例化渲染器
 */
export class InstancedRenderer {
  private instancedMeshes: Map<string, THREE.InstancedMesh> = new Map();

  /**
   * 创建实例化网格
   */
  createInstancedMesh(
    geometry: THREE.BufferGeometry,
    material: THREE.Material,
    count: number,
    transforms: THREE.Matrix4[]
  ): THREE.InstancedMesh {
    const instancedMesh = new THREE.InstancedMesh(geometry, material, count);

    // 设置实例变换矩阵
    transforms.forEach((matrix, index) => {
      instancedMesh.setMatrixAt(index, matrix);
    });

    instancedMesh.instanceMatrix.needsUpdate = true;

    const key = `${geometry.uuid}_${material.uuid}`;
    this.instancedMeshes.set(key, instancedMesh);

    return instancedMesh;
  }

  /**
   * 更新实例
   */
  updateInstance(key: string, index: number, matrix: THREE.Matrix4): void {
    const instancedMesh = this.instancedMeshes.get(key);
    if (instancedMesh) {
      instancedMesh.setMatrixAt(index, matrix);
      instancedMesh.instanceMatrix.needsUpdate = true;
    }
  }

  /**
   * 移除实例化网格
   */
  removeInstancedMesh(key: string): void {
    this.instancedMeshes.delete(key);
  }
}

/**
 * 场景性能优化器
 */
export class ScenePerformanceOptimizer {
  private config: PerformanceConfig;
  private lodManager: LODManager;
  private geometryMerger: GeometryMerger;
  private textureOptimizer: TextureOptimizer;
  private instancedRenderer: InstancedRenderer;
  private performanceMonitor: PerformanceMonitor;

  constructor(config: Partial<PerformanceConfig> = {}) {
    this.config = {
      targetFPS: 60,
      maxPolygons: 100000,
      maxTextureSize: 1024,
      enableLOD: true,
      enableGeometryMerging: true,
      enableTextureCompression: true,
      enableInstancing: true,
      ...config
    };

    this.lodManager = new LODManager();
    this.geometryMerger = new GeometryMerger();
    this.textureOptimizer = new TextureOptimizer(this.config.maxTextureSize);
    this.instancedRenderer = new InstancedRenderer();
    this.performanceMonitor = new PerformanceMonitor();
  }

  /**
   * 优化场景
   */
  async optimizeScene(scene: Scene): Promise<void> {
    const entities = scene.getEntities();

    // 1. 几何体合并
    if (this.config.enableGeometryMerging) {
      await this.optimizeGeometry(entities);
    }

    // 2. 纹理优化
    if (this.config.enableTextureCompression) {
      await this.optimizeTextures(entities);
    }

    // 3. LOD设置
    if (this.config.enableLOD) {
      await this.setupLOD(entities);
    }

    // 4. 实例化渲染
    if (this.config.enableInstancing) {
      await this.setupInstancing(entities);
    }

    // 5. 清理未使用的资源
    await this.cleanupResources(scene);
  }

  /**
   * 优化几何体
   */
  private async optimizeGeometry(entities: Entity[]): Promise<void> {
    // 识别可合并的几何体
    const mergeableEntities = entities.filter(entity => {
      const meshRenderer = entity.getComponent('MeshComponent') as any;
      return meshRenderer && this.isMergeable(meshRenderer);
    });

    // 合并几何体
    if (mergeableEntities.length > 1) {
      this.geometryMerger.mergeGeometries(mergeableEntities);
    }
  }

  /**
   * 检查是否可合并
   */
  private isMergeable(meshRenderer: any): boolean {
    const mesh = (meshRenderer as any).mesh;
    if (!mesh) return false;

    // 检查几何体复杂度
    const geometry = mesh.geometry;
    const vertexCount = geometry.getAttribute('position')?.count || 0;
    
    return vertexCount < 1000; // 只合并简单几何体
  }

  /**
   * 优化纹理
   */
  private async optimizeTextures(entities: Entity[]): Promise<void> {
    const textures = new Set<THREE.Texture>();

    // 收集所有纹理
    entities.forEach(entity => {
      const meshRenderer = entity.getComponent('MeshComponent') as any;
      if (meshRenderer) {
        const mesh = (meshRenderer as any).mesh;
        if (mesh && mesh.material) {
          this.collectTextures(mesh.material, textures);
        }
      }
    });

    // 优化每个纹理
    const optimizationPromises = Array.from(textures).map(texture => 
      this.textureOptimizer.optimizeTexture(texture)
    );

    await Promise.all(optimizationPromises);
  }

  /**
   * 收集材质中的纹理
   */
  private collectTextures(material: THREE.Material | THREE.Material[], textures: Set<THREE.Texture>): void {
    const materials = Array.isArray(material) ? material : [material];

    materials.forEach(mat => {
      if (mat instanceof THREE.MeshStandardMaterial) {
        if (mat.map) textures.add(mat.map);
        if (mat.normalMap) textures.add(mat.normalMap);
        if (mat.roughnessMap) textures.add(mat.roughnessMap);
        if (mat.metalnessMap) textures.add(mat.metalnessMap);
      }
    });
  }

  /**
   * 设置LOD
   */
  private async setupLOD(entities: Entity[]): Promise<void> {
    entities.forEach(entity => {
      const meshRenderer = entity.getComponent('MeshComponent') as any;
      if (meshRenderer && this.shouldUseLOD(meshRenderer)) {
        const lodMeshes = this.generateLODMeshes(meshRenderer);
        this.lodManager.createLOD(entity, lodMeshes);
      }
    });
  }

  /**
   * 检查是否应该使用LOD
   */
  private shouldUseLOD(meshRenderer: any): boolean {
    const mesh = (meshRenderer as any).mesh;
    if (!mesh) return false;

    const geometry = mesh.geometry;
    const vertexCount = geometry.getAttribute('position')?.count || 0;
    
    return vertexCount > 500; // 复杂几何体使用LOD
  }

  /**
   * 生成LOD网格
   */
  private generateLODMeshes(meshRenderer: any): THREE.Mesh[] {
    const originalMesh = (meshRenderer as any).mesh;
    if (!originalMesh) return [];

    const meshes: THREE.Mesh[] = [originalMesh];

    // 生成简化版本
    // 这里应该实现几何体简化算法
    // 目前只是复制原网格
    const simplifiedMesh1 = originalMesh.clone();
    const simplifiedMesh2 = originalMesh.clone();

    meshes.push(simplifiedMesh1, simplifiedMesh2);

    return meshes;
  }

  /**
   * 设置实例化渲染
   */
  private async setupInstancing(entities: Entity[]): Promise<void> {
    // 按几何体和材质分组
    const groups = new Map<string, Entity[]>();

    entities.forEach(entity => {
      const meshRenderer = entity.getComponent('MeshComponent') as any;
      if (meshRenderer) {
        const mesh = (meshRenderer as any).mesh;
        if (mesh) {
          const key = `${mesh.geometry.uuid}_${this.getMaterialKey(mesh.material)}`;
          if (!groups.has(key)) {
            groups.set(key, []);
          }
          groups.get(key)!.push(entity);
        }
      }
    });

    // 为重复的几何体创建实例化渲染
    groups.forEach((groupEntities, key) => {
      if (groupEntities.length > 3) { // 至少3个实例才使用实例化渲染
        this.createInstancedGroup(groupEntities);
      }
    });
  }

  /**
   * 创建实例化组
   */
  private createInstancedGroup(entities: Entity[]): void {
    if (entities.length === 0) return;

    const firstEntity = entities[0];
    const meshRenderer = firstEntity.getComponent('MeshComponent') as any;
    if (!meshRenderer) return;

    const mesh = (meshRenderer as any).mesh;
    if (!mesh) return;

    // 收集变换矩阵
    const transforms: THREE.Matrix4[] = [];
    entities.forEach(entity => {
      const transform = entity.getComponent('Transform') as any;
      if (transform) {
        transforms.push((transform as any).getWorldMatrix());
      }
    });

    // 创建实例化网格
    this.instancedRenderer.createInstancedMesh(
      mesh.geometry,
      mesh.material,
      entities.length,
      transforms
    );
  }

  /**
   * 获取材质键
   */
  private getMaterialKey(material: THREE.Material | THREE.Material[]): string {
    if (Array.isArray(material)) {
      return material.map(m => m.uuid).join('_');
    }
    return material.uuid;
  }

  /**
   * 清理资源
   */
  private async cleanupResources(scene: Scene): Promise<void> {
    // 清理未使用的几何体和材质
    // 这里应该实现资源引用计数和清理逻辑
  }

  /**
   * 更新LOD
   */
  updateLOD(camera: THREE.Camera): void {
    this.lodManager.updateLOD(camera);
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats(): PerformanceStats {
    return this.performanceMonitor.getStats();
  }

  /**
   * 设置性能配置
   */
  setConfig(config: Partial<PerformanceConfig>): void {
    this.config = { ...this.config, ...config };
  }
}

/**
 * 性能监控器
 */
export class PerformanceMonitor {
  private frameCount: number = 0;
  private lastTime: number = 0;
  private fps: number = 0;

  /**
   * 更新性能统计
   */
  update(): void {
    this.frameCount++;
    const currentTime = performance.now();
    
    if (currentTime - this.lastTime >= 1000) {
      this.fps = this.frameCount;
      this.frameCount = 0;
      this.lastTime = currentTime;
    }
  }

  /**
   * 获取统计信息
   */
  getStats(): PerformanceStats {
    return {
      currentFPS: this.fps,
      polygonCount: 0, // 需要从渲染器获取
      textureMemory: 0, // 需要从渲染器获取
      drawCalls: 0, // 需要从渲染器获取
      suggestions: this.generateSuggestions()
    };
  }

  /**
   * 生成优化建议
   */
  private generateSuggestions(): string[] {
    const suggestions: string[] = [];

    if (this.fps < 30) {
      suggestions.push('FPS过低，建议启用LOD和几何体合并');
    }

    if (this.fps < 60) {
      suggestions.push('可以考虑降低纹理质量或减少多边形数量');
    }

    return suggestions;
  }
}
