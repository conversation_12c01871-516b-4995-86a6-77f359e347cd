import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { MonitoringService } from './monitoring.service';
import { MonitoringController } from './monitoring.controller';
import { MetricsEntity } from './entities/metrics.entity';
import { ServiceMetricsEntity } from './entities/service-metrics.entity';
import { SystemMetricsEntity } from './entities/system-metrics.entity';
import { MetricsCollectorService } from './metrics-collector.service';
import { PrometheusExporterService } from './prometheus-exporter.service';
import { MetricsAggregatorService } from './metrics-aggregator.service';
import { MetricsStorageService } from './metrics-storage.service';

@Module({
  imports: [TypeOrmModule.forFeature([MetricsEntity, ServiceMetricsEntity, SystemMetricsEntity]), HttpModule],
  controllers: [MonitoringController],
  providers: [
    MonitoringService,
    MetricsCollectorService,
    PrometheusExporterService,
    MetricsAggregatorService,
    MetricsStorageService,
  ],
  exports: [MonitoringService, MetricsCollectorService, PrometheusExporterService],
})
export class MonitoringModule {}
