/**
 * 数字人系统
 * 管理数字人的生成、编辑和渲染
 */
import { System } from '../../core/System';
import { Entity } from '../../core/Entity';
import { type World  } from '../../core/World';
import { EventEmitter } from '../../utils/EventEmitter';
import { DigitalHumanComponent, DigitalHumanSource, ClothingSlotType } from '../components/DigitalHumanComponent';
import { AvatarRigComponent } from '../components/AvatarRigComponent';
import { Transform } from '../../scene/Transform';
import { MultiActionFusionManager } from '../animation/MultiActionFusionManager';

/**
 * 数字人系统配置
 */
export interface DigitalHumanSystemConfig {
  /** 是否启用调试模式 */
  debug?: boolean;
  /** 最大并发生成数量 */
  maxConcurrentGenerations?: number;
  /** 自动保存间隔（毫秒） */
  autoSaveInterval?: number;
  /** 是否启用自动优化 */
  enableAutoOptimization?: boolean;
}

/**
 * 数字人生成请求
 */
export interface DigitalHumanGenerationRequest {
  /** 请求ID */
  id: string;
  /** 用户ID */
  userId: string;
  /** 生成类型 */
  source: DigitalHumanSource;
  /** 源数据 */
  sourceData: {
    photoUrl?: string;
    fileUrl?: string;
    marketplaceId?: string;
  };
  /** 生成选项 */
  options: {
    name?: string;
    tags?: string[];
    licenseType?: string;
    generateClothing?: boolean;
    generateAnimations?: boolean;
  };
  /** 回调函数 */
  onProgress?: (progress: number) => void;
  /** 完成回调 */
  onComplete?: (entity: Entity) => void;
  /** 错误回调 */
  onError?: (error: Error) => void;
}

/**
 * 数字人系统
 */
export class DigitalHumanSystem extends System {
  /** 系统优先级 */
  public static readonly PRIORITY = 5;

  /** 配置 */
  private config: DigitalHumanSystemConfig;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 数字人实体映射 */
  private digitalHumans: Map<string, Entity> = new Map();

  /** 生成队列 */
  private generationQueue: DigitalHumanGenerationRequest[] = [];

  /** 当前生成中的请求 */
  private activeGenerations: Map<string, DigitalHumanGenerationRequest> = new Map();

  /** 自动保存定时器 */
  private autoSaveTimer?: NodeJS.Timeout;

  /**
   * 构造函数
   * @param world 世界实例
   * @param config 系统配置
   */
  constructor(world: World, config: DigitalHumanSystemConfig = {}) {
    super(DigitalHumanSystem.PRIORITY);

    this.config = {
      debug: false,
      maxConcurrentGenerations: 3,
      autoSaveInterval: 30000, // 30秒
      enableAutoOptimization: true,
      ...config
    };

    // 设置自动保存
    if (this.config.autoSaveInterval && this.config.autoSaveInterval > 0) {
      this.autoSaveTimer = setInterval(() => {
        this.autoSave();
      }, this.config.autoSaveInterval);
    }
  }

  /**
   * 系统初始化
   */
  public initialize(): void {
    if (this.config.debug) {
      console.log('[DigitalHumanSystem] 系统初始化');
    }

    // 监听实体添加事件
    this.world.on('entityAdded', (entity: Entity) => {
      const digitalHumanComponent = entity.getComponent<DigitalHumanComponent>(DigitalHumanComponent.TYPE);
      if (digitalHumanComponent) {
        this.registerDigitalHuman(entity);
      }
    });

    // 监听实体移除事件
    this.world.on('entityRemoved', (entity: Entity) => {
      this.unregisterDigitalHuman(entity);
    });
  }

  /**
   * 系统更新
   * @param deltaTime 帧间隔时间
   */
  public update(deltaTime: number): void {
    // 处理生成队列
    this.processGenerationQueue();

    // 更新所有数字人
    for (const entity of this.digitalHumans.values()) {
      this.updateDigitalHuman(entity, deltaTime);
    }

    // 自动优化
    if (this.config.enableAutoOptimization) {
      this.performAutoOptimization();
    }
  }

  /**
   * 注册数字人
   * @param entity 实体
   */
  public registerDigitalHuman(entity: Entity): void {
    const digitalHumanComponent = entity.getComponent<DigitalHumanComponent>(DigitalHumanComponent.TYPE);
    if (!digitalHumanComponent) {
      if (this.config.debug) {
        console.warn('[DigitalHumanSystem] 实体缺少DigitalHumanComponent', entity);
      }
      return;
    }

    // 添加到映射
    this.digitalHumans.set(entity.id, entity);

    // 发出事件
    this.eventEmitter.emit('digitalHumanRegistered', entity);

    if (this.config.debug) {
      console.log('[DigitalHumanSystem] 注册数字人', entity.id);
    }
  }

  /**
   * 注销数字人
   * @param entity 实体
   */
  public unregisterDigitalHuman(entity: Entity): void {
    if (this.digitalHumans.has(entity.id)) {
      this.digitalHumans.delete(entity.id);
      this.eventEmitter.emit('digitalHumanUnregistered', entity);

      if (this.config.debug) {
        console.log('[DigitalHumanSystem] 注销数字人', entity.id);
      }
    }
  }

  /**
   * 创建数字人
   * @param request 生成请求
   * @returns Promise<Entity>
   */
  public async createDigitalHuman(request: DigitalHumanGenerationRequest): Promise<Entity> {
    return new Promise((resolve, reject) => {
      // 设置回调
      request.onComplete = resolve;
      request.onError = reject;

      // 添加到队列
      this.generationQueue.push(request);

      if (this.config.debug) {
        console.log('[DigitalHumanSystem] 添加生成请求到队列', request.id);
      }
    });
  }

  /**
   * 获取数字人
   * @param entityId 实体ID
   * @returns 数字人实体
   */
  public getDigitalHuman(entityId: string): Entity | undefined {
    return this.digitalHumans.get(entityId);
  }

  /**
   * 获取所有数字人
   * @returns 数字人实体数组
   */
  public getAllDigitalHumans(): Entity[] {
    return Array.from(this.digitalHumans.values());
  }

  /**
   * 删除数字人
   * @param entityId 实体ID
   * @returns 是否成功删除
   */
  public deleteDigitalHuman(entityId: string): boolean {
    const entity = this.digitalHumans.get(entityId);
    if (entity) {
      this.world.removeEntity(entity);
      return true;
    }
    return false;
  }

  /**
   * 更新数字人外观
   * @param entityId 实体ID
   * @param slotType 插槽类型
   * @param itemId 服装项ID
   * @param itemUrl 服装项URL
   */
  public updateDigitalHumanClothing(
    entityId: string,
    slotType: ClothingSlotType,
    itemId: string,
    itemUrl: string
  ): void {
    const entity = this.digitalHumans.get(entityId);
    if (entity) {
      const digitalHumanComponent = entity.getComponent<DigitalHumanComponent>(DigitalHumanComponent.TYPE);
      if (digitalHumanComponent) {
        digitalHumanComponent.setClothingItem(slotType, itemId, itemUrl);
        this.eventEmitter.emit('digitalHumanClothingUpdated', entity, slotType, itemId);
      }
    }
  }

  /**
   * 处理生成队列
   */
  private processGenerationQueue(): void {
    // 检查是否可以处理新的生成请求
    while (
      this.generationQueue.length > 0 &&
      this.activeGenerations.size < (this.config.maxConcurrentGenerations || 3)
    ) {
      const request = this.generationQueue.shift()!;
      this.activeGenerations.set(request.id, request);
      this.processGenerationRequest(request);
    }
  }

  /**
   * 处理生成请求
   * @param request 生成请求
   */
  private async processGenerationRequest(request: DigitalHumanGenerationRequest): Promise<void> {
    try {
      if (this.config.debug) {
        console.log('[DigitalHumanSystem] 开始处理生成请求', request.id);
      }

      // 创建实体
      const entity = new Entity('数字人');
      entity.name = request.options.name || `数字人_${Date.now()}`;

      // 添加Transform组件
      const transform = new Transform();
      entity.addComponent(transform);

      // 创建数字人组件
      const digitalHumanComponent = new DigitalHumanComponent(entity, {
        source: request.source,
        sourcePhotoUrl: request.sourceData.photoUrl,
        sourceFileUrl: request.sourceData.fileUrl,
        name: entity.name,
        userId: request.userId,
        tags: request.options.tags || [],
        licenseType: request.options.licenseType || 'private'
      });

      // 设置生成状态
      digitalHumanComponent.setGenerationState(true, 0);
      entity.addComponent(digitalHumanComponent);

      // 添加骨骼组件
      const rigComponent = new AvatarRigComponent();
      entity.addComponent(rigComponent);

      // 添加到世界
      this.world.addEntity(entity);

      // 根据来源类型进行不同的处理
      await this.generateDigitalHumanBySource(entity, request);

      // 完成生成
      digitalHumanComponent.setGenerationState(false, 1);
      this.activeGenerations.delete(request.id);

      // 调用完成回调
      if (request.onComplete) {
        request.onComplete(entity);
      }

      this.eventEmitter.emit('digitalHumanGenerated', entity);

    } catch (error) {
      // 处理错误
      this.activeGenerations.delete(request.id);

      if (request.onError) {
        request.onError(error as Error);
      }

      this.eventEmitter.emit('digitalHumanGenerationError', request.id, error);

      if (this.config.debug) {
        console.error('[DigitalHumanSystem] 生成失败', request.id, error);
      }
    }
  }

  /**
   * 根据来源生成数字人
   * @param entity 实体
   * @param request 生成请求
   */
  private async generateDigitalHumanBySource(
    entity: Entity,
    request: DigitalHumanGenerationRequest
  ): Promise<void> {
    const digitalHumanComponent = entity.getComponent<DigitalHumanComponent>(DigitalHumanComponent.TYPE);
    if (!digitalHumanComponent) return;

    switch (request.source) {
      case DigitalHumanSource.PHOTO:
        await this.generateFromPhoto(entity, request);
        break;
      case DigitalHumanSource.UPLOAD:
        await this.generateFromUpload(entity, request);
        break;
      case DigitalHumanSource.MARKETPLACE:
        await this.generateFromMarketplace(entity, request);
        break;
      case DigitalHumanSource.MANUAL:
        await this.generateManually(entity, request);
        break;
      default:
        throw new Error(`不支持的生成来源: ${request.source}`);
    }
  }

  /**
   * 从照片生成数字人
   * @param entity 实体
   * @param request 生成请求
   */
  private async generateFromPhoto(
    entity: Entity,
    request: DigitalHumanGenerationRequest
  ): Promise<void> {
    const digitalHumanComponent = entity.getComponent<DigitalHumanComponent>(DigitalHumanComponent.TYPE);
    if (!digitalHumanComponent || !request.sourceData.photoUrl) return;

    // 更新进度
    if (request.onProgress) request.onProgress(0.1);

    // TODO: 集成AI处理服务
    // 1. 人脸检测和特征提取
    // 2. 3D人脸重建
    // 3. 纹理生成
    // 4. 身体模型生成
    // 5. 骨骼绑定

    // 模拟生成过程
    await this.simulateGeneration(request.onProgress);

    if (this.config.debug) {
      console.log('[DigitalHumanSystem] 从照片生成数字人完成', entity.id);
    }
  }

  /**
   * 从上传文件生成数字人
   * @param entity 实体
   * @param request 生成请求
   */
  private async generateFromUpload(
    entity: Entity,
    request: DigitalHumanGenerationRequest
  ): Promise<void> {
    const digitalHumanComponent = entity.getComponent<DigitalHumanComponent>(DigitalHumanComponent.TYPE);
    if (!digitalHumanComponent || !request.sourceData.fileUrl) return;

    // 更新进度
    if (request.onProgress) request.onProgress(0.1);

    // TODO: 实现文件解析和导入
    // 1. 解析数字人文件格式
    // 2. 验证文件完整性
    // 3. 导入模型和纹理
    // 4. 重建骨骼结构
    // 5. 应用动画数据

    // 模拟导入过程
    await this.simulateGeneration(request.onProgress);

    if (this.config.debug) {
      console.log('[DigitalHumanSystem] 从文件导入数字人完成', entity.id);
    }
  }

  /**
   * 从市场下载生成数字人
   * @param entity 实体
   * @param request 生成请求
   */
  private async generateFromMarketplace(
    entity: Entity,
    request: DigitalHumanGenerationRequest
  ): Promise<void> {
    const digitalHumanComponent = entity.getComponent<DigitalHumanComponent>(DigitalHumanComponent.TYPE);
    if (!digitalHumanComponent || !request.sourceData.marketplaceId) return;

    // 更新进度
    if (request.onProgress) request.onProgress(0.1);

    // TODO: 实现市场下载
    // 1. 验证下载权限
    // 2. 下载数字人包
    // 3. 解析和导入
    // 4. 应用许可限制

    // 模拟下载过程
    await this.simulateGeneration(request.onProgress);

    if (this.config.debug) {
      console.log('[DigitalHumanSystem] 从市场下载数字人完成', entity.id);
    }
  }

  /**
   * 手动创建数字人
   * @param entity 实体
   * @param request 生成请求
   */
  private async generateManually(
    entity: Entity,
    request: DigitalHumanGenerationRequest
  ): Promise<void> {
    const digitalHumanComponent = entity.getComponent<DigitalHumanComponent>(DigitalHumanComponent.TYPE);
    if (!digitalHumanComponent) return;

    // 更新进度
    if (request.onProgress) request.onProgress(0.5);

    // 创建默认数字人
    // TODO: 加载默认模型和纹理

    // 完成
    if (request.onProgress) request.onProgress(1.0);

    if (this.config.debug) {
      console.log('[DigitalHumanSystem] 手动创建数字人完成', entity.id);
    }
  }

  /**
   * 模拟生成过程
   * @param onProgress 进度回调
   */
  private async simulateGeneration(onProgress?: (progress: number) => void): Promise<void> {
    const steps = [0.2, 0.4, 0.6, 0.8, 1.0];

    for (const progress of steps) {
      await new Promise(resolve => setTimeout(resolve, 500)); // 模拟处理时间
      if (onProgress) onProgress(progress);
    }
  }

  /**
   * 更新数字人
   * @param entity 实体
   * @param deltaTime 帧间隔时间
   */
  private updateDigitalHuman(entity: Entity, deltaTime: number): void {
    const digitalHumanComponent = entity.getComponent<DigitalHumanComponent>(DigitalHumanComponent.TYPE);
    if (!digitalHumanComponent) return;

    // 更新数字人状态
    // TODO: 实现具体的更新逻辑
  }

  /**
   * 执行自动优化
   */
  private performAutoOptimization(): void {
    // TODO: 实现自动优化逻辑
    // 1. LOD优化
    // 2. 纹理压缩
    // 3. 几何简化
    // 4. 动画优化
  }

  /**
   * 自动保存
   */
  private autoSave(): void {
    if (this.config.debug) {
      console.log('[DigitalHumanSystem] 执行自动保存');
    }

    // TODO: 实现自动保存逻辑
    for (const entity of this.digitalHumans.values()) {
      const digitalHumanComponent = entity.getComponent(DigitalHumanComponent.TYPE) as DigitalHumanComponent;
      if (digitalHumanComponent && digitalHumanComponent.isLoaded) {
        // 保存数字人状态
        this.saveDigitalHuman(entity);
      }
    }
  }

  /**
   * 保存数字人
   * @param entity 实体
   */
  private saveDigitalHuman(entity: Entity): void {
    // TODO: 实现保存逻辑
    if (this.config.debug) {
      console.log('[DigitalHumanSystem] 保存数字人', entity.id);
    }
  }

  /**
   * 获取数字人的多动作融合管理器
   * @param entity 数字人实体
   * @returns 多动作融合管理器
   */
  public getMultiActionFusionManager(entity: Entity): MultiActionFusionManager | null {
    const digitalHumanComponent = entity.getComponent<DigitalHumanComponent>(DigitalHumanComponent.TYPE);
    if (!digitalHumanComponent) return null;

    // 如果还没有创建，则创建一个新的
    if (!digitalHumanComponent.multiActionFusionManager) {
      digitalHumanComponent.multiActionFusionManager = new MultiActionFusionManager(entity, this.world);
    }

    return digitalHumanComponent.multiActionFusionManager;
  }

  // 移除了重写的EventEmitter方法，直接使用继承的方法

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 清理自动保存定时器
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
      this.autoSaveTimer = undefined;
    }

    // 清理所有数字人
    this.digitalHumans.clear();

    // 清理生成队列
    this.generationQueue.length = 0;
    this.activeGenerations.clear();

    // 清理事件监听器
    this.eventEmitter.removeAllListeners();

    if (this.config.debug) {
      console.log('[DigitalHumanSystem] 系统已销毁');
    }
  }
}
