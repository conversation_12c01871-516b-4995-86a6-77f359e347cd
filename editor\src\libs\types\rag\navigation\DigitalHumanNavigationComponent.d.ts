/**
 * 数字人导航组件
 * 实现数字人沿路径移动、停留点处理、状态管理
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
import { type Entity } from '../../core/Entity';
import { DigitalHumanPathEditor, StopPoint } from './DigitalHumanPathEditor';
/**
 * 导航状态枚举
 */
export declare enum NavigationState {
    IDLE = "idle",
    MOVING = "moving",
    STOPPED = "stopped",
    PAUSED = "paused",
    WAITING = "waiting",
    INTERACTING = "interacting"
}
/**
 * 导航事件类型
 */
export declare enum NavigationEventType {
    STATE_CHANGED = "stateChanged",
    REACHED_STOP_POINT = "reachedStopPoint",
    PATH_COMPLETED = "pathCompleted",
    MOVEMENT_STARTED = "movementStarted",
    MOVEMENT_PAUSED = "movementPaused",
    MOVEMENT_RESUMED = "movementResumed"
}
/**
 * 导航配置接口
 */
export interface NavigationConfig {
    moveSpeed: number;
    rotationSpeed: number;
    stopThreshold: number;
    lookAhead: number;
    smoothRotation: boolean;
    autoStart: boolean;
    debug: boolean;
}
/**
 * 导航事件数据
 */
export interface NavigationEventData {
    state: NavigationState;
    progress: number;
    currentStopPoint?: StopPoint;
    position: THREE.Vector3;
    direction: THREE.Vector3;
}
/**
 * 数字人导航组件
 */
export declare class DigitalHumanNavigationComponent extends Component {
    static readonly TYPE = "DigitalHumanNavigation";
    private currentPath;
    private currentProgress;
    private state;
    private currentStopPoint;
    private waitTimer;
    private lastPosition;
    private targetRotation;
    private config;
    onStateChanged?: (data: NavigationEventData) => void;
    onReachedStopPoint?: (stopPoint: StopPoint) => void;
    onPathCompleted?: () => void;
    constructor(entity: Entity, config?: Partial<NavigationConfig>);
    /**
     * 设置路径
     */
    setPath(path: DigitalHumanPathEditor): void;
    /**
     * 开始沿路径移动
     */
    startMoving(): void;
    /**
     * 停止移动
     */
    stopMoving(): void;
    /**
     * 暂停移动
     */
    pauseMoving(): void;
    /**
     * 恢复移动
     */
    resumeMoving(): void;
    /**
     * 设置移动速度
     */
    setMoveSpeed(speed: number): void;
    /**
     * 获取移动速度
     */
    getMoveSpeed(): number;
    /**
     * 获取当前状态
     */
    getState(): NavigationState;
    /**
     * 获取当前进度 (0-1)
     */
    getProgress(): number;
    /**
     * 设置进度
     */
    setProgress(progress: number): void;
    /**
     * 跳转到指定停留点
     */
    jumpToStopPoint(stopPointId: string): boolean;
    /**
     * 更新组件
     */
    update(deltaTime: number): void;
    /**
     * 更新移动
     */
    private updateMovement;
    /**
     * 更新实体变换
     */
    private updateEntityTransform;
    /**
     * 检查停留点
     */
    private checkStopPoints;
    /**
     * 处理停留点
     */
    private handleStopPoint;
    /**
     * 触发停留点动作
     */
    private triggerStopPointActions;
    /**
     * 触发动作
     */
    private triggerAction;
    /**
     * 设置状态
     */
    private setState;
    /**
     * 获取当前路径
     */
    getCurrentPath(): DigitalHumanPathEditor | null;
    /**
     * 获取当前停留点
     */
    getCurrentStopPoint(): StopPoint | null;
    /**
     * 获取剩余等待时间
     */
    getRemainingWaitTime(): number;
    /**
     * 跳过当前等待
     */
    skipWait(): void;
    /**
     * 获取到下一个停留点的距离
     */
    getDistanceToNextStopPoint(): number;
    /**
     * 获取配置
     */
    getConfig(): NavigationConfig;
    /**
     * 更新配置
     */
    updateConfig(config: Partial<NavigationConfig>): void;
    /**
     * 重置导航
     */
    reset(): void;
    /**
     * 获取导航信息
     */
    getNavigationInfo(): any;
    /**
     * 销毁组件
     */
    dispose(): void;
}
