// 简单的启动测试脚本
const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('./dist/app.module');

async function testStartup() {
  try {
    console.log('正在测试RAG引擎启动...');
    
    // 设置基本环境变量
    process.env.NODE_ENV = 'test';
    process.env.PORT = '3009';
    process.env.REDIS_HOST = 'localhost';
    process.env.REDIS_PORT = '6379';
    process.env.VECTOR_DB_TYPE = 'chroma';
    process.env.VECTOR_DB_ENDPOINT = 'http://localhost:8000';
    process.env.LLM_PROVIDER = 'openai';
    process.env.LLM_API_KEY = 'test-key';
    process.env.EMBEDDING_PROVIDER = 'openai';
    process.env.EMBEDDING_API_KEY = 'test-key';
    
    const app = await NestFactory.create(AppModule, {
      logger: ['error', 'warn'],
    });
    
    console.log('✅ RAG引擎模块创建成功');
    
    // 测试基本配置
    const configService = app.get('ConfigService');
    console.log('✅ 配置服务获取成功');
    
    await app.close();
    console.log('✅ RAG引擎测试完成，所有基本功能正常');
    
  } catch (error) {
    console.error('❌ RAG引擎启动测试失败:', error.message);
    console.error('详细错误:', error.stack);
    process.exit(1);
  }
}

testStartup();
