import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  ParseUUIDPipe,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiConsumes,
  ApiBody,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { AssetsService, AssetSearchResult } from './assets.service';
import { CreateAssetDto, UpdateAssetDto, AssetQueryDto } from './dto/create-asset.dto';
import { Asset } from './entities/asset.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('assets')
@Controller('assets')
export class AssetsController {
  constructor(private readonly assetsService: AssetsService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建资产' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '资产创建成功',
    type: Asset,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: '未授权',
  })
  async create(
    @Body() createAssetDto: CreateAssetDto,
    @Request() req: any,
  ): Promise<Asset> {
    return await this.assetsService.create(createAssetDto, req.user.id);
  }

  @Get()
  @ApiOperation({ summary: '获取资产列表' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '资产列表获取成功',
  })
  @ApiQuery({ name: 'search', required: false, description: '搜索关键词' })
  @ApiQuery({ name: 'type', required: false, description: '资产类型' })
  @ApiQuery({ name: 'categoryId', required: false, description: '分类ID' })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  async findAll(@Query() query: AssetQueryDto): Promise<AssetSearchResult> {
    return await this.assetsService.findAll(query);
  }

  @Get('popular')
  @ApiOperation({ summary: '获取热门资产' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '热门资产获取成功',
    type: [Asset],
  })
  @ApiQuery({ name: 'limit', required: false, description: '数量限制' })
  async getPopular(@Query('limit') limit?: number): Promise<Asset[]> {
    return await this.assetsService.getPopularAssets(limit);
  }

  @Get('latest')
  @ApiOperation({ summary: '获取最新资产' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '最新资产获取成功',
    type: [Asset],
  })
  @ApiQuery({ name: 'limit', required: false, description: '数量限制' })
  async getLatest(@Query('limit') limit?: number): Promise<Asset[]> {
    return await this.assetsService.getLatestAssets(limit);
  }

  @Get('recommended')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取推荐资产' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '推荐资产获取成功',
    type: [Asset],
  })
  @ApiQuery({ name: 'limit', required: false, description: '数量限制' })
  async getRecommended(
    @Request() req: any,
    @Query('limit') limit?: number,
  ): Promise<Asset[]> {
    return await this.assetsService.getRecommendedAssets(req.user.id, limit);
  }

  @Get('search')
  @ApiOperation({ summary: '搜索资产' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '搜索结果',
    type: [Asset],
  })
  @ApiQuery({ name: 'q', required: true, description: '搜索关键词' })
  @ApiQuery({ name: 'type', required: false, description: '资产类型过滤' })
  @ApiQuery({ name: 'categoryId', required: false, description: '分类过滤' })
  async search(
    @Query('q') query: string,
    @Query('type') type?: string,
    @Query('categoryId') categoryId?: string,
  ): Promise<Asset[]> {
    const filters: any = {};
    if (type) filters.type = type;
    if (categoryId) filters.categoryId = categoryId;
    
    return await this.assetsService.search(query, filters);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取资产详情' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '资产详情获取成功',
    type: Asset,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '资产不存在',
  })
  @ApiParam({ name: 'id', description: '资产ID' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Asset> {
    return await this.assetsService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新资产' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '资产更新成功',
    type: Asset,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '资产不存在',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: '无权限修改',
  })
  @ApiParam({ name: 'id', description: '资产ID' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateAssetDto: UpdateAssetDto,
    @Request() req: any,
  ): Promise<Asset> {
    return await this.assetsService.update(id, updateAssetDto, req.user.id);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除资产' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: '资产删除成功',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '资产不存在',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: '无权限删除',
  })
  @ApiParam({ name: 'id', description: '资产ID' })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<void> {
    await this.assetsService.remove(id, req.user.id);
  }

  @Post(':id/download')
  @ApiOperation({ summary: '下载资产' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '下载链接获取成功',
  })
  @ApiParam({ name: 'id', description: '资产ID' })
  async download(@Param('id', ParseUUIDPipe) id: string): Promise<{ downloadUrl: string }> {
    // 增加下载次数
    await this.assetsService.incrementDownloadCount(id);
    
    // 这里应该生成临时下载链接
    // 实际实现中需要集成存储服务
    return {
      downloadUrl: `/api/files/download/${id}`,
    };
  }

  @Post(':id/upload')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: '上传资产文件' })
  @ApiBody({
    description: '资产文件',
    type: 'multipart/form-data',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '文件上传成功',
  })
  @ApiParam({ name: 'id', description: '资产ID' })
  async uploadFile(
    @Param('id', ParseUUIDPipe) id: string,
    @UploadedFile() file: Express.Multer.File,
    @Request() _req: any,
  ): Promise<{ message: string; filePath: string }> {
    // 这里应该实现文件上传逻辑
    // 实际实现中需要集成存储服务和文件验证
    return {
      message: '文件上传成功',
      filePath: `/uploads/assets/${id}/${file.originalname}`,
    };
  }
}
