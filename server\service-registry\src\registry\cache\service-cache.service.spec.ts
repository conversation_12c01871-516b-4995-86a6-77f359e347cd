import { Test, TestingModule } from '@nestjs/testing';
import { ServiceCacheService, CacheLevel } from './service-cache.service';
import { ConfigService } from '@nestjs/config';
// import { EventBusService } from '@shared/event-bus';
import { Redis } from 'ioredis';

// 模拟Redis
jest.mock('ioredis', () => {
  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => ({
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      flushall: jest.fn(),
      on: jest.fn(),
    })),
  };
});

describe('ServiceCacheService', () => {
  let service: ServiceCacheService;
  let configService: ConfigService;
  // let eventBusService: EventBusService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ServiceCacheService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockImplementation((key, defaultValue) => {
              if (key === 'SERVICE_CACHE_ENABLED') return true;
              if (key === 'SERVICE_CACHE_LEVELS') return [CacheLevel.MEMORY, CacheLevel.REDIS];
              if (key === 'SERVICE_CACHE_MEMORY_TTL') return 60000;
              if (key === 'SERVICE_CACHE_REDIS_TTL') return 300000;
              if (key === 'SERVICE_CACHE_MAX_ENTRIES') return 1000;
              if (key === 'SERVICE_CACHE_REDIS_HOST') return 'localhost';
              if (key === 'SERVICE_CACHE_REDIS_PORT') return 6379;
              return defaultValue;
            }),
          },
        },
        // {
        //   provide: EventBusService,
        //   useValue: {
        //     subscribe: jest.fn(),
        //   },
        // },
      ],
    }).compile();

    service = module.get<ServiceCacheService>(ServiceCacheService);
    configService = module.get<ConfigService>(ConfigService);
    // eventBusService = module.get<EventBusService>(EventBusService);

    // Redis mock 已在文件顶部设置
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('get', () => {
    it('should return data from memory cache if available', async () => {
      // 准备测试数据
      const key = 'test-key';
      const expectedData = { id: 1, name: 'Test' };
      
      // 手动设置缓存
      await service.set(key, expectedData);

      // 执行测试
      const fetchFn = jest.fn().mockResolvedValue({ id: 2, name: 'New Test' });
      const result = await service.get(key, fetchFn);

      // 验证结果
      expect(result).toEqual(expectedData);
      expect(fetchFn).not.toHaveBeenCalled();
    });

    it('should call fetchFn and cache result if not in cache', async () => {
      // 准备测试数据
      const key = 'test-key-2';
      const expectedData = { id: 2, name: 'Test 2' };
      
      // 执行测试
      const fetchFn = jest.fn().mockResolvedValue(expectedData);
      const result = await service.get(key, fetchFn);

      // 验证结果
      expect(result).toEqual(expectedData);
      expect(fetchFn).toHaveBeenCalled();

      // 验证数据已被缓存
      const result2 = await service.get(key, jest.fn().mockResolvedValue({ id: 3, name: 'Test 3' }));
      expect(result2).toEqual(expectedData);
    });
  });

  describe('set', () => {
    it('should set data in memory cache', async () => {
      // 准备测试数据
      const key = 'test-key-3';
      const data = { id: 3, name: 'Test 3' };
      
      // 执行测试
      await service.set(key, data);

      // 验证结果（通过get方法验证）
      const result = await service.get(key, jest.fn().mockResolvedValue(null));
      expect(result).toEqual(data);
    });
  });

  describe('invalidate', () => {
    it('should invalidate cache for a key', async () => {
      // 准备测试数据
      const key = 'test-key-4';
      const data = { id: 4, name: 'Test 4' };
      
      // 设置缓存
      await service.set(key, data);
      
      // 验证缓存存在
      const result1 = await service.get(key, jest.fn().mockResolvedValue(null));
      expect(result1).toEqual(data);
      
      // 执行测试（失效缓存）
      await service.invalidate(key);
      
      // 验证缓存已失效
      const newData = { id: 5, name: 'Test 5' };
      const fetchFn = jest.fn().mockResolvedValue(newData);
      const result2 = await service.get(key, fetchFn);
      
      expect(fetchFn).toHaveBeenCalled();
      expect(result2).toEqual(newData);
    });
  });

  describe('getStats', () => {
    it('should return cache statistics', async () => {
      // 执行一些缓存操作
      await service.set('test-key-5', { id: 5, name: 'Test 5' });
      await service.get('test-key-5', jest.fn());
      
      // 获取统计信息
      const stats = service.getStats();
      
      // 验证统计信息
      expect(stats).toBeDefined();
      expect(stats.hits).toBeGreaterThan(0);
      expect(stats.size).toBeGreaterThan(0);
    });
  });
});
