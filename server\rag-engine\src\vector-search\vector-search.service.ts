import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CacheService } from '../cache/cache.service';
import axios from 'axios';

export interface SearchOptions {
  vector: number[];
  namespace: string;
  topK: number;
  includeMetadata: boolean;
  filter?: any;
  threshold?: number;
}

export interface SearchResult {
  matches: VectorMatch[];
  namespace: string;
  searchTime: number;
}

export interface VectorMatch {
  id: string;
  score: number;
  metadata?: any;
}

@Injectable()
export class VectorSearchService {
  private vectorDbClient: any;

  constructor(
    private readonly configService: ConfigService,
    private readonly cacheService: CacheService,
  ) {
    this.initializeVectorDatabase();
  }

  /**
   * 初始化向量数据库客户端
   */
  private initializeVectorDatabase(): void {
    const dbType = this.configService.get('rag.vectorDatabase.type');

    switch (dbType) {
      case 'pinecone':
        this.vectorDbClient = new PineconeSearchClient(this.configService);
        break;
      case 'chroma':
        this.vectorDbClient = new ChromaSearchClient(this.configService);
        break;
      case 'milvus':
        this.vectorDbClient = new MilvusSearchClient(this.configService);
        break;
      case 'weaviate':
        this.vectorDbClient = new WeaviateSearchClient(this.configService);
        break;
      default:
        throw new Error(`不支持的向量数据库类型: ${dbType}`);
    }
  }

  /**
   * 执行向量搜索
   */
  async search(options: SearchOptions): Promise<SearchResult> {
    const startTime = Date.now();

    try {
      // 检查缓存
      const cacheKey = this.generateCacheKey(options);
      const cached = await this.cacheService.get<SearchResult>(cacheKey);

      if (cached) {
        return {
          ...cached,
          searchTime: Date.now() - startTime,
        };
      }

      // 执行搜索
      const result = await this.vectorDbClient.search(options);

      // 过滤低分结果
      if (options.threshold) {
        result.matches = result.matches.filter((match) => match.score >= options.threshold);
      }

      const searchResult: SearchResult = {
        matches: result.matches,
        namespace: options.namespace,
        searchTime: Date.now() - startTime,
      };

      // 缓存结果（短时间缓存）
      await this.cacheService.set(cacheKey, searchResult, 300); // 5分钟缓存

      return searchResult;
    } catch (error) {
      console.error('向量搜索失败:', error);
      throw new Error(`向量搜索失败: ${error.message}`);
    }
  }

  /**
   * 批量搜索多个命名空间
   */
  async batchSearch(
    vector: number[],
    namespaces: string[],
    topK: number,
    includeMetadata: boolean = true,
  ): Promise<SearchResult[]> {
    const searchPromises = namespaces.map((namespace) =>
      this.search({
        vector,
        namespace,
        topK,
        includeMetadata,
      }),
    );

    return await Promise.all(searchPromises);
  }

  /**
   * 混合搜索（向量搜索 + 关键词搜索）
   */
  async hybridSearch(vector: number[], keywords: string[], namespace: string, topK: number): Promise<SearchResult> {
    // 向量搜索
    const vectorResults = await this.search({
      vector,
      namespace,
      topK: topK * 2, // 获取更多结果用于重排序
      includeMetadata: true,
    });

    // 关键词过滤和重排序
    const hybridResults = this.combineVectorAndKeywordResults(vectorResults.matches, keywords);

    return {
      matches: hybridResults.slice(0, topK),
      namespace,
      searchTime: vectorResults.searchTime,
    };
  }

  /**
   * 结合向量和关键词结果
   */
  private combineVectorAndKeywordResults(vectorMatches: VectorMatch[], keywords: string[]): VectorMatch[] {
    return vectorMatches
      .map((match) => {
        let keywordScore = 0;
        const content = match.metadata?.content?.toLowerCase() || '';

        // 计算关键词匹配分数
        for (const keyword of keywords) {
          const keywordLower = keyword.toLowerCase();
          const occurrences = (content.match(new RegExp(keywordLower, 'g')) || []).length;
          keywordScore += occurrences * 0.1; // 每次匹配加0.1分
        }

        // 组合向量分数和关键词分数
        const combinedScore = match.score * 0.8 + keywordScore * 0.2;

        return {
          ...match,
          score: Math.min(combinedScore, 1.0), // 确保分数不超过1
        };
      })
      .sort((a, b) => b.score - a.score);
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(options: SearchOptions): string {
    const vectorHash = this.hashVector(options.vector);
    const filterHash = options.filter ? JSON.stringify(options.filter) : '';

    return `vector_search:${options.namespace}:${vectorHash}:${options.topK}:${filterHash}`;
  }

  /**
   * 计算向量哈希
   */
  private hashVector(vector: number[]): string {
    // 简化的向量哈希，实际应用中可能需要更复杂的哈希算法
    const sum = vector.reduce((acc, val) => acc + val, 0);
    const avg = sum / vector.length;
    return Math.round(avg * 1000).toString();
  }

  /**
   * 获取搜索统计信息
   */
  async getSearchStats(namespace: string): Promise<any> {
    try {
      return await this.vectorDbClient.getStats(namespace);
    } catch (error) {
      console.error('获取搜索统计失败:', error);
      return null;
    }
  }
}

/**
 * Pinecone搜索客户端
 */
class PineconeSearchClient {
  private apiKey: string;
  private endpoint: string;

  constructor(configService: ConfigService) {
    this.apiKey = configService.get('rag.vectorDatabase.apiKey');
    this.endpoint = configService.get('rag.vectorDatabase.endpoint');
  }

  async search(options: SearchOptions): Promise<any> {
    const response = await axios.post(
      `${this.endpoint}/query`,
      {
        namespace: options.namespace,
        vector: options.vector,
        topK: options.topK,
        filter: options.filter,
        includeMetadata: options.includeMetadata,
      },
      {
        headers: {
          'Api-Key': this.apiKey,
          'Content-Type': 'application/json',
        },
      },
    );

    const data = response.data;
    return {
      matches: data.matches.map((match: any) => ({
        id: match.id,
        score: match.score,
        metadata: match.metadata,
      })),
    };
  }

  async getStats(namespace: string): Promise<any> {
    const response = await axios.post(
      `${this.endpoint}/describe_index_stats`,
      {
        filter: namespace ? { namespace } : undefined,
      },
      {
        headers: {
          'Api-Key': this.apiKey,
          'Content-Type': 'application/json',
        },
      },
    );

    return response.data;
  }
}

/**
 * Chroma搜索客户端
 */
class ChromaSearchClient {
  private endpoint: string;
  private collectionName: string;

  constructor(configService: ConfigService) {
    this.endpoint = configService.get('rag.vectorDatabase.endpoint');
    this.collectionName = configService.get('rag.vectorDatabase.indexName');
  }

  async search(options: SearchOptions): Promise<any> {
    const collectionName = `${this.collectionName}_${options.namespace}`;

    const response = await axios.post(
      `${this.endpoint}/api/v1/collections/${collectionName}/query`,
      {
        query_embeddings: [options.vector],
        n_results: options.topK,
        include: options.includeMetadata ? ['metadatas', 'distances'] : ['distances'],
        where: options.filter,
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );

    const data = response.data;
    const matches = data.ids[0].map((id: string, index: number) => ({
      id,
      score: 1 - data.distances[0][index], // 转换距离为相似度
      metadata: options.includeMetadata ? data.metadatas[0][index] : undefined,
    }));

    return { matches };
  }

  async getStats(namespace: string): Promise<any> {
    const collectionName = `${this.collectionName}_${namespace}`;

    const response = await axios.get(`${this.endpoint}/api/v1/collections/${collectionName}`);

    return response.data;
  }
}

/**
 * Milvus搜索客户端（简化实现）
 */
class MilvusSearchClient {
  constructor(_configService: ConfigService) {
    // Milvus需要使用官方SDK
  }

  async search(_options: SearchOptions): Promise<any> {
    throw new Error('Milvus搜索客户端需要使用官方SDK实现');
  }

  async getStats(_namespace: string): Promise<any> {
    throw new Error('Milvus统计客户端需要使用官方SDK实现');
  }
}

/**
 * Weaviate搜索客户端（简化实现）
 */
class WeaviateSearchClient {
  constructor(_configService: ConfigService) {
    // Weaviate需要使用官方SDK
  }

  async search(_options: SearchOptions): Promise<any> {
    throw new Error('Weaviate搜索客户端需要使用官方SDK实现');
  }

  async getStats(_namespace: string): Promise<any> {
    throw new Error('Weaviate统计客户端需要使用官方SDK实现');
  }
}
