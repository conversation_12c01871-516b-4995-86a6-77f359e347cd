import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RatingsService } from './ratings.service';
import { TemplateRating } from './entities/template-rating.entity';
import { SceneTemplate } from '../templates/entities/scene-template.entity';
import { LoggerService } from '../../common/services/logger.service';

@Module({
  imports: [TypeOrmModule.forFeature([TemplateRating, SceneTemplate])],
  providers: [
    RatingsService,
    LoggerService,
  ],
  exports: [RatingsService, TypeOrmModule],
})
export class RatingsModule {}
