import { Injectable, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisClientType } from 'redis';

@Injectable()
export class CacheService {
  private readonly defaultTTL: number;
  private readonly memoryCache: Map<string, { value: any; expiry: number }> = new Map();

  constructor(
    @Inject('REDIS_CLIENT') private readonly redisClient: RedisClientType | null,
    private readonly configService: ConfigService,
  ) {
    this.defaultTTL = this.configService.get('rag.cache.ttl', 3600);

    // 如果Redis不可用，启动内存缓存清理定时器
    if (!this.redisClient) {
      console.log('使用内存缓存模式');
      this.startMemoryCacheCleanup();
    }
  }

  /**
   * 设置缓存
   */
  async set(key: string, value: any, ttl?: number): Promise<void> {
    const expireTime = ttl || this.defaultTTL;

    if (this.redisClient) {
      try {
        const serializedValue = JSON.stringify(value);
        await this.redisClient.setEx(key, expireTime, serializedValue);
      } catch (error) {
        console.warn('Redis设置缓存失败，使用内存缓存:', error.message);
        this.setMemoryCache(key, value, expireTime);
      }
    } else {
      this.setMemoryCache(key, value, expireTime);
    }
  }

  /**
   * 获取缓存
   */
  async get<T>(key: string): Promise<T | null> {
    if (this.redisClient) {
      try {
        const value = await this.redisClient.get(key);
        if (!value) {
          return null;
        }
        return JSON.parse(value) as T;
      } catch (error) {
        console.warn('Redis获取缓存失败，尝试内存缓存:', error.message);
        return this.getMemoryCache<T>(key);
      }
    } else {
      return this.getMemoryCache<T>(key);
    }
  }

  /**
   * 删除缓存
   */
  async del(key: string): Promise<void> {
    if (this.redisClient) {
      try {
        await this.redisClient.del(key);
      } catch (error) {
        console.warn('Redis删除缓存失败，删除内存缓存:', error.message);
        this.memoryCache.delete(key);
      }
    } else {
      this.memoryCache.delete(key);
    }
  }

  /**
   * 批量删除缓存
   */
  async delPattern(pattern: string): Promise<void> {
    if (this.redisClient) {
      try {
        const keys = await this.redisClient.keys(pattern);
        if (keys.length > 0) {
          await this.redisClient.del(keys);
        }
      } catch (error) {
        console.warn('Redis批量删除缓存失败:', error.message);
        this.delMemoryCachePattern(pattern);
      }
    } else {
      this.delMemoryCachePattern(pattern);
    }
  }

  /**
   * 检查缓存是否存在
   */
  async exists(key: string): Promise<boolean> {
    if (this.redisClient) {
      try {
        const result = await this.redisClient.exists(key);
        return result === 1;
      } catch (error) {
        console.warn('Redis检查缓存失败，检查内存缓存:', error.message);
        return this.memoryCache.has(key) && this.memoryCache.get(key)!.expiry > Date.now();
      }
    } else {
      return this.memoryCache.has(key) && this.memoryCache.get(key)!.expiry > Date.now();
    }
  }

  /**
   * 设置缓存过期时间
   */
  async expire(key: string, ttl: number): Promise<void> {
    if (this.redisClient) {
      try {
        await this.redisClient.expire(key, ttl);
      } catch (error) {
        console.warn('Redis设置过期时间失败:', error.message);
        const cached = this.memoryCache.get(key);
        if (cached) {
          cached.expiry = Date.now() + ttl * 1000;
        }
      }
    } else {
      const cached = this.memoryCache.get(key);
      if (cached) {
        cached.expiry = Date.now() + ttl * 1000;
      }
    }
  }

  /**
   * 获取缓存剩余过期时间
   */
  async ttl(key: string): Promise<number> {
    if (this.redisClient) {
      try {
        return await this.redisClient.ttl(key);
      } catch (error) {
        console.warn('Redis获取TTL失败:', error.message);
        const cached = this.memoryCache.get(key);
        if (cached) {
          return Math.max(0, Math.floor((cached.expiry - Date.now()) / 1000));
        }
        return -1;
      }
    } else {
      const cached = this.memoryCache.get(key);
      if (cached) {
        return Math.max(0, Math.floor((cached.expiry - Date.now()) / 1000));
      }
      return -1;
    }
  }

  /**
   * 内存缓存设置
   */
  private setMemoryCache(key: string, value: any, ttlSeconds: number): void {
    const expiry = Date.now() + ttlSeconds * 1000;
    this.memoryCache.set(key, { value, expiry });
  }

  /**
   * 内存缓存获取
   */
  private getMemoryCache<T>(key: string): T | null {
    const cached = this.memoryCache.get(key);
    if (!cached) {
      return null;
    }

    if (cached.expiry <= Date.now()) {
      this.memoryCache.delete(key);
      return null;
    }

    return cached.value as T;
  }

  /**
   * 内存缓存模式匹配删除
   */
  private delMemoryCachePattern(pattern: string): void {
    const regex = new RegExp(pattern.replace(/\*/g, '.*'));
    for (const key of this.memoryCache.keys()) {
      if (regex.test(key)) {
        this.memoryCache.delete(key);
      }
    }
  }

  /**
   * 启动内存缓存清理定时器
   */
  private startMemoryCacheCleanup(): void {
    setInterval(() => {
      const now = Date.now();
      for (const [key, cached] of this.memoryCache.entries()) {
        if (cached.expiry <= now) {
          this.memoryCache.delete(key);
        }
      }
    }, 60000); // 每分钟清理一次过期缓存
  }
}
