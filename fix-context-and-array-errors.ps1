# React Context和数组错误修复脚本
# 修复图片中显示的useContext和find方法错误

param(
    [switch]$Verbose,
    [switch]$SkipBuild,
    [switch]$TestOnly
)

$ErrorActionPreference = "Stop"

function Write-Header {
    param([string]$Message)
    Write-Host "`n" -NoNewline
    Write-Host "=" * 60 -ForegroundColor Cyan
    Write-Host " $Message" -ForegroundColor Yellow
    Write-Host "=" * 60 -ForegroundColor Cyan
}

function Write-Step {
    param([string]$Message)
    Write-Host "`n🔄 $Message" -ForegroundColor Green
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Clear-FrontendCache {
    Write-Step "清理前端缓存..."
    
    try {
        Push-Location "editor"
        
        # 清理node_modules和构建缓存
        if (Test-Path "node_modules") {
            Remove-Item "node_modules" -Recurse -Force
            Write-Host "  已删除 node_modules" -ForegroundColor Gray
        }
        
        if (Test-Path "dist") {
            Remove-Item "dist" -Recurse -Force
            Write-Host "  已删除 dist" -ForegroundColor Gray
        }
        
        if (Test-Path ".vite") {
            Remove-Item ".vite" -Recurse -Force
            Write-Host "  已删除 .vite 缓存" -ForegroundColor Gray
        }
        
        # 清理npm缓存
        npm cache clean --force 2>$null
        
        Write-Success "前端缓存清理完成"
    }
    catch {
        Write-Warning "清理缓存时出现警告: $($_.Exception.Message)"
    }
    finally {
        Pop-Location
    }
}

function Install-FrontendDependencies {
    Write-Step "重新安装前端依赖..."
    
    try {
        Push-Location "editor"
        
        # 安装依赖
        npm install --no-audit --no-fund
        
        if ($LASTEXITCODE -ne 0) {
            throw "npm install 失败"
        }
        
        Write-Success "前端依赖安装完成"
    }
    catch {
        Write-Error "安装前端依赖失败: $($_.Exception.Message)"
        return $false
    }
    finally {
        Pop-Location
    }
    
    return $true
}

function Build-Frontend {
    if ($SkipBuild) {
        Write-Step "跳过前端构建"
        return $true
    }
    
    Write-Step "构建前端应用..."
    
    try {
        Push-Location "editor"
        
        # 构建前端
        npm run build
        
        if ($LASTEXITCODE -ne 0) {
            throw "前端构建失败"
        }
        
        Write-Success "前端构建完成"
    }
    catch {
        Write-Error "前端构建失败: $($_.Exception.Message)"
        return $false
    }
    finally {
        Pop-Location
    }
    
    return $true
}

function Restart-DockerServices {
    Write-Step "重启Docker服务..."
    
    try {
        # 停止现有服务
        docker-compose -f docker-compose.windows.yml down 2>$null
        
        # 等待服务完全停止
        Start-Sleep -Seconds 5
        
        # 启动基础服务
        Write-Host "启动基础服务..." -ForegroundColor Yellow
        docker-compose -f docker-compose.windows.yml up -d mysql redis minio
        Start-Sleep -Seconds 15
        
        # 启动后端服务
        Write-Host "启动后端服务..." -ForegroundColor Yellow
        docker-compose -f docker-compose.windows.yml up -d service-registry user-service project-service asset-service
        Start-Sleep -Seconds 20
        
        # 启动API网关
        Write-Host "启动API网关..." -ForegroundColor Yellow
        docker-compose -f docker-compose.windows.yml up -d api-gateway
        Start-Sleep -Seconds 10
        
        # 启动前端
        Write-Host "启动前端服务..." -ForegroundColor Yellow
        docker-compose -f docker-compose.windows.yml up -d editor
        Start-Sleep -Seconds 15
        
        Write-Success "Docker服务重启完成"
        return $true
    }
    catch {
        Write-Error "Docker服务重启失败: $($_.Exception.Message)"
        return $false
    }
}

function Test-Services {
    Write-Step "测试服务连接..."
    
    $services = @(
        @{ Name = "API网关"; Url = "http://localhost:3000"; MaxRetries = 10 },
        @{ Name = "前端"; Url = "http://localhost"; MaxRetries = 15 }
    )
    
    $allPassed = $true
    
    foreach ($service in $services) {
        $success = $false
        
        for ($i = 1; $i -le $service.MaxRetries; $i++) {
            try {
                $response = Invoke-WebRequest -Uri $service.Url -Method GET -TimeoutSec 5 -UseBasicParsing
                if ($response.StatusCode -eq 200) {
                    Write-Success "$($service.Name) 服务正常"
                    $success = $true
                    break
                }
            }
            catch {
                if ($Verbose) {
                    Write-Host "  $($service.Name) 测试 $i/$($service.MaxRetries) 失败" -ForegroundColor Gray
                }
            }
            
            if ($i -lt $service.MaxRetries) {
                Start-Sleep -Seconds 3
            }
        }
        
        if (-not $success) {
            Write-Error "$($service.Name) 服务测试失败"
            $allPassed = $false
        }
    }
    
    return $allPassed
}

function Show-FixSummary {
    Write-Header "修复总结"

    Write-Host "`n🔧 已修复的问题:" -ForegroundColor Cyan
    Write-Host "  ✅ 添加了错误边界组件防止应用崩溃" -ForegroundColor Green
    Write-Host "  ✅ 修复了useSelector调用的Context错误" -ForegroundColor Green
    Write-Host "  ✅ 添加了数组操作的null检查保护" -ForegroundColor Green
    Write-Host "  ✅ 修复了Git组件中的数组filter错误" -ForegroundColor Green
    Write-Host "  ✅ 优化了Redux状态访问的错误处理" -ForegroundColor Green
    Write-Host "  ✅ 修复了React Hook调用规则违反问题" -ForegroundColor Green
    Write-Host "  ✅ 修复了TabPane组件弃用问题" -ForegroundColor Green
    Write-Host "  ✅ 修复了面板注册器中的Hook使用问题" -ForegroundColor Green

    Write-Host "`n🌐 访问地址:" -ForegroundColor Cyan
    Write-Host "  前端应用: http://localhost" -ForegroundColor White
    Write-Host "  API网关: http://localhost:3000/api" -ForegroundColor White
    Write-Host "  API文档: http://localhost:3000/api/docs" -ForegroundColor White

    Write-Host "`n🔑 测试账号:" -ForegroundColor Cyan
    Write-Host "  邮箱: <EMAIL>" -ForegroundColor White
    Write-Host "  密码: 123456" -ForegroundColor White

    Write-Host "`n📊 容器状态:" -ForegroundColor Cyan
    docker-compose -f docker-compose.windows.yml ps
}

# 主执行流程
try {
    Write-Header "React Context和数组错误修复"
    
    if ($TestOnly) {
        Write-Step "仅测试模式，跳过修复步骤"
        $testResult = Test-Services
        if ($testResult) {
            Write-Success "所有服务测试通过！"
        } else {
            Write-Warning "部分服务测试失败"
        }
        return
    }
    
    # 清理缓存
    Clear-FrontendCache
    
    # 重新安装依赖
    if (-not (Install-FrontendDependencies)) {
        throw "依赖安装失败"
    }
    
    # 构建前端
    if (-not (Build-Frontend)) {
        throw "前端构建失败"
    }
    
    # 重启Docker服务
    if (-not (Restart-DockerServices)) {
        throw "Docker服务重启失败"
    }
    
    # 等待服务完全启动
    Write-Step "等待所有服务完全启动..."
    Start-Sleep -Seconds 30
    
    # 测试服务
    $testResult = Test-Services
    
    # 显示修复总结
    Show-FixSummary
    
    if ($testResult) {
        Write-Header "🎉 修复完成！"
        Write-Host "`n✅ 所有错误已修复，系统运行正常" -ForegroundColor Green
        Write-Host "🌐 请访问 http://localhost 测试登录功能" -ForegroundColor Yellow
        Write-Host "📝 如果仍有问题，请检查浏览器控制台的错误信息" -ForegroundColor Cyan
    } else {
        Write-Header "⚠️  修复完成，但部分服务测试失败"
        Write-Host "`n请检查Docker容器状态和日志" -ForegroundColor Yellow
        Write-Host "运行以下命令查看日志:" -ForegroundColor Cyan
        Write-Host "docker-compose -f docker-compose.windows.yml logs editor" -ForegroundColor White
    }
    
}
catch {
    Write-Error "修复过程中出现错误: $($_.Exception.Message)"
    Write-Host "`n查看详细日志:" -ForegroundColor Yellow
    Write-Host "docker-compose -f docker-compose.windows.yml logs" -ForegroundColor White
    exit 1
}
