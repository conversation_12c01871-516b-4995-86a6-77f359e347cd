/**
 * 水体预设测试
 * 用于测试水体预设系统的功能
 */
import { WaterPresets, WaterPresetType, WaterPresetConfig } from './WaterPresets';
import type { Entity } from '../../core/Entity';

/**
 * 水体预设测试类
 */
export class WaterPresetsTest {
  /**
   * 运行所有测试
   */
  public static runAllTests(): void {
    console.log('开始运行水体预设测试...');
    
    try {
      this.testGetAllPresetTypes();
      this.testGetPresetDisplayName();
      this.testGetPresetConfig();
      this.testCreatePreset();
      
      console.log('✅ 所有水体预设测试通过！');
    } catch (error) {
      console.error('❌ 水体预设测试失败:', error);
    }
  }

  /**
   * 测试获取所有预设类型
   */
  private static testGetAllPresetTypes(): void {
    console.log('测试获取所有预设类型...');
    
    const types = WaterPresets.getAllPresetTypes();
    
    if (!Array.isArray(types)) {
      throw new Error('getAllPresetTypes 应该返回数组');
    }
    
    if (types.length === 0) {
      throw new Error('预设类型数组不应该为空');
    }
    
    if (!types.includes(WaterPresetType.LAKE)) {
      throw new Error('预设类型应该包含 LAKE');
    }
    
    console.log(`✅ 获取到 ${types.length} 个预设类型`);
  }

  /**
   * 测试获取预设显示名称
   */
  private static testGetPresetDisplayName(): void {
    console.log('测试获取预设显示名称...');
    
    const lakeName = WaterPresets.getPresetDisplayName(WaterPresetType.LAKE);
    if (lakeName !== '湖泊') {
      throw new Error(`湖泊预设名称应该是 '湖泊'，实际是 '${lakeName}'`);
    }
    
    const riverName = WaterPresets.getPresetDisplayName(WaterPresetType.RIVER);
    if (riverName !== '河流') {
      throw new Error(`河流预设名称应该是 '河流'，实际是 '${riverName}'`);
    }
    
    console.log('✅ 预设显示名称正确');
  }

  /**
   * 测试获取预设配置
   */
  private static testGetPresetConfig(): void {
    console.log('测试获取预设配置...');
    
    // 测试湖泊预设
    const lakeConfig = WaterPresets.getPresetConfig(WaterPresetType.LAKE);
    if (!lakeConfig) {
      throw new Error('湖泊预设配置不应该为空');
    }
    
    if (lakeConfig.type !== WaterPresetType.LAKE) {
      throw new Error('湖泊预设类型不正确');
    }
    
    if (!lakeConfig.size) {
      throw new Error('湖泊预设应该有尺寸配置');
    }
    
    if (!lakeConfig.waveParams) {
      throw new Error('湖泊预设应该有波动参数');
    }
    
    if (!lakeConfig.flowParams) {
      throw new Error('湖泊预设应该有流动参数');
    }
    
    if (!lakeConfig.physicsParams) {
      throw new Error('湖泊预设应该有物理参数');
    }
    
    // 测试河流预设
    const riverConfig = WaterPresets.getPresetConfig(WaterPresetType.RIVER);
    if (!riverConfig) {
      throw new Error('河流预设配置不应该为空');
    }
    
    if (riverConfig.type !== WaterPresetType.RIVER) {
      throw new Error('河流预设类型不正确');
    }
    
    // 验证河流的流速应该比湖泊快
    if (riverConfig.flowParams!.speed <= lakeConfig.flowParams!.speed) {
      throw new Error('河流的流速应该比湖泊快');
    }
    
    console.log('✅ 预设配置正确');
  }

  /**
   * 测试创建预设
   */
  private static testCreatePreset(): void {
    console.log('测试创建预设...');
    
    // 创建模拟实体
    const mockEntity = {
      id: 'test-entity',
      name: 'TestEntity',
      getComponent: (name: string) => null,
      addComponent: (component: any) => {},
      removeComponent: (name: string) => {},
      hasComponent: (name: string) => false
    } as any as Entity;
    
    // 测试创建湖泊预设
    const lakeConfig: WaterPresetConfig = {
      type: WaterPresetType.LAKE
    };
    
    const lakeWaterBody = WaterPresets.createPreset(mockEntity, lakeConfig);
    if (!lakeWaterBody) {
      throw new Error('创建湖泊水体失败');
    }
    
    // 测试创建河流预设
    const riverConfig: WaterPresetConfig = {
      type: WaterPresetType.RIVER,
      size: { width: 10, height: 1, depth: 100 }
    };
    
    const riverWaterBody = WaterPresets.createPreset(mockEntity, riverConfig);
    if (!riverWaterBody) {
      throw new Error('创建河流水体失败');
    }
    
    console.log('✅ 预设创建成功');
  }

  /**
   * 测试预设配置的完整性
   */
  public static testPresetCompleteness(): void {
    console.log('测试预设配置完整性...');
    
    const allTypes = WaterPresets.getAllPresetTypes();
    
    for (const type of allTypes) {
      try {
        const config = WaterPresets.getPresetConfig(type);
        const displayName = WaterPresets.getPresetDisplayName(type);
        
        if (!config) {
          throw new Error(`预设类型 ${type} 没有配置`);
        }
        
        if (!displayName) {
          throw new Error(`预设类型 ${type} 没有显示名称`);
        }
        
        console.log(`✅ ${type} (${displayName}) 配置完整`);
      } catch (error) {
        console.error(`❌ 预设类型 ${type} 配置不完整:`, error);
      }
    }
    
    console.log('✅ 所有预设配置完整性检查通过');
  }
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined' && typeof global !== 'undefined') {
  // Node.js 环境
  WaterPresetsTest.runAllTests();
  WaterPresetsTest.testPresetCompleteness();
}
